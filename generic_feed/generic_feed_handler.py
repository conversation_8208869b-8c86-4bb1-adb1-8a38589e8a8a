import asyncio
import c<PERSON><PERSON><PERSON>le
import json
from abc import ABCMeta, abstractmethod

from flask import request
from flask.views import MethodView

from paraty import Config
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_promotions_of_hotel, get_rooms_of_hotel, get_boards_of_hotel, get_multirates_of_hotel
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.generic_feed.feed_constants import FeedSearch, SearchResponse, US_ENGINE, PRESTIGE_ENGINE, INNSIST_ENGINE, QUARANTINE_ENGINE_CONFIGURATIONS, EUROPE_ENGINE
from paraty_commons_3.generic_feed.feed_utils import build_hotel_chunks, build_special_servers_dict, build_search_string, _process_engine_in_quarantine
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.http.http_utils import call_urls_async
from paraty_commons_3.language_utils import get_language_in_manager_based_on_locale
import logging
from flask import Response

ENGINE_IN_QUARANTINE = "Engine in quarantine"

from paraty_commons_3.redis.redis_communicator import build_redis_client


def _get_quarantine_engine_configuration(engine):
    seconds_for_quarantine = QUARANTINE_ENGINE_CONFIGURATIONS.get(engine, {}).get("seconds_for_quarantine", 60)
    max_calls = QUARANTINE_ENGINE_CONFIGURATIONS.get(engine, {}).get("max_calls", 30)
    return seconds_for_quarantine, max_calls


def _take_quarantine_into_account(cached, non_cached):

    for current_request in non_cached:

        request_string = current_request['search_url']

        server_engine = None
        if PRESTIGE_ENGINE in request_string:
            # note that we have to add our own offers to prestige prices
            request_string = request_string.replace(PRESTIGE_ENGINE, EUROPE_ENGINE)
            request_string += "&useAsProxy=%s" % PRESTIGE_ENGINE
            current_request['search_url'] = request_string
            server_engine = PRESTIGE_ENGINE

        if INNSIST_ENGINE in request_string:
            # note that we have to add our own offers to prestige prices
            myServerUrl = US_ENGINE
            # request_string += "&useAsProxy=%s" % INNSIST_ENGINE
            server_engine = INNSIST_ENGINE

        if server_engine:
            seconds_for_quarantine, max_calls = _get_quarantine_engine_configuration(server_engine)
            engine_is_in_quarantine = _process_engine_in_quarantine(server_engine, seconds_for_quarantine=seconds_for_quarantine, max_calls=max_calls)
            if engine_is_in_quarantine:
                current_request['search_url'] = ENGINE_IN_QUARANTINE


def _split_cached_and_non_cached_searches(requests_to_perform):

    if not Config.REDIS_HOST:
        return [], requests_to_perform

    my_redis_client = build_redis_client(Config.REDIS_HOST, password=Config.REDIS_PASSWORD, port=Config.REDIS_PORT)

    cached = []
    non_cached = []
    for current_request in requests_to_perform:
        cached_result = my_redis_client.get(current_request['search_url'])
        if cached_result:
            current_request['result'] = cached_result
            cached.append(current_request)
        else:
            non_cached.append(current_request)

    _take_quarantine_into_account(cached, non_cached)

    return cached, non_cached


def _build_search(chunk, search):
    special_servers = build_special_servers_dict(chunk)
    server_url = special_servers.get(chunk[0], US_ENGINE)
    hotel_code_multi = ",".join(chunk)

    path = build_search_string(hotel_code_multi, search)

    return server_url + path


def _build_all_requests_url(feed_searches):
    result = []
    for search in feed_searches:
        hotel_chunks = build_hotel_chunks(search.hotels)
        for chunk in hotel_chunks:
            result.append({"feed_search": search,
                           "search_url": _build_search(chunk, search),
                           "chunk": chunk})

    return result


# @persistent_timed_cache(days=1, keyGenerator=lambda x: f"_get_all_names_for_hotel_code_{x[0]}_{x[1]}")
@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='RoomType,Rate,Regimen,Promotion', ttl_seconds=24*3600)
def _get_all_names_for_hotel_code(hotel_code, language_code):
    language = get_language_in_manager_based_on_locale(language_code)

    hotel = get_hotel_by_application_id(hotel_code)

    rates = get_rates_of_hotel(hotel, language, only_enabled=True) + get_multirates_of_hotel(hotel, language, only_enabled=True)
    promotions = get_promotions_of_hotel(hotel, language)
    rooms = get_rooms_of_hotel(hotel, language)
    boards = get_boards_of_hotel(hotel, language)

    result = {}
    result.update({x.get('key'): x.get('name') for x in rates})
    result.update({x.get('key'): x.get('name') for x in promotions})
    result.update({x.get('key'): x.get('name') for x in rooms})
    result.update({x.get('key'): x.get('name') for x in boards})
    return result



class GenericFeedHandler(MethodView, metaclass=ABCMeta):

    @abstractmethod
    def build_searches(self) -> list[FeedSearch]:
        '''
        This method should return a List of FeedSearch objects. Note that sometimes one request might generate multiple search, i.e. 4-0-0, 1-0-0
        '''
        pass

    def get_all_names_of_entities(self):
        return True

    def validate_request(self):
        # By default, we suppose the request is valid
        return True

    @abstractmethod
    def parse_response(self, feed_response: list[SearchResponse]):
        '''
        Receives a list of SearchResponse and builds the response string
        '''
        pass

    def get(self):
        return self.post()

    def post(self):
        if request.values.get('profile'):
            result = cProfile.runctx('self.real_post()', globals(), locals(), sort='cumulative')
            return 'OK! (Result can not be returned as we are profiling it)'
        else:
            return self.real_post()

    def _build_search_responses(self, server_responses):
        result = []
        for i, response in enumerate(server_responses):

            if not response.get('result'):
                logging.error("No result for search %s" % response['chunk'])
                continue

            new_search_response = SearchResponse()
            new_search_response.search = response['feed_search']

            # Note that we are going to separate each hotel in a different search in order to be able to cache it
            if len(response['chunk']) > 1:
                new_search_response.results = {k: v for k, v in json.loads(response['result']).items() if k in response['chunk']}
            else:
                new_search_response.results = {response['chunk'][0]: json.loads(response['result'])}

            new_search_response.status = 200

            new_search_response.names = {}
            for hotel_code in new_search_response.results:
                if self.get_all_names_of_entities():
                    new_search_response.names.update(_get_all_names_for_hotel_code(hotel_code, new_search_response.search.language))
            result.append(new_search_response)
        return result

    def real_post(self):

        if not self.validate_request():
            logging.info("Request not valid")
            return Response("Unauthorized access", status=401)

        feed_searches = self.build_searches()

        for search in feed_searches:
            search.validate()

        requests_to_perform = _build_all_requests_url(feed_searches)

        cached_searches, non_cached_searches = _split_cached_and_non_cached_searches(requests_to_perform)

        non_cached_search_url = [x['search_url'] for x in non_cached_searches if x['search_url'] != ENGINE_IN_QUARANTINE]

        logging.info("Requests to perform: %s " % non_cached_search_url)

        results = asyncio.run(call_urls_async(non_cached_search_url, headers={"Authorization": "Basic cGFjbzpwYWNv"}))

        if Config.REDIS_HOST:
            my_redis_client = build_redis_client(Config.REDIS_HOST, password=Config.REDIS_PASSWORD, port=Config.REDIS_PORT)

        for i, result in enumerate(results):
            # datastore_cache_with_ttl.set_value(non_cached_searches[i][1], results[i], self.cache_search_seconds())
            if Config.REDIS_HOST and self.cache_search_seconds() > 0:
                my_redis_client.setex(non_cached_searches[i]['search_url'], self.cache_search_seconds(), results[i])

            non_cached_searches[i]['result'] = results[i]

        server_responses = cached_searches + non_cached_searches

        try:
            result = self._build_search_responses(server_responses)
        except Exception as e:
            logging.exception("Error building response")
            return Response("Error building response", status=500)

        return self.parse_response(result)

    def cache_search_seconds(self) -> int:
        return 14400
