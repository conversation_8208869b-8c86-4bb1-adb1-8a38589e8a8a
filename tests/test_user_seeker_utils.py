import unittest

from unittest.mock import patch, MagicMock
from paraty import app
from paraty_commons_3.user_seeker.interfaces import UserSeekerLoginResponse
from paraty_commons_3.user_seeker.user_seeker_utils import UserSeekerAuth


class TestUserSeekerUtils(unittest.TestCase):

    _test_username = 'foo'
    _test_password = 'foo'
    _two_factor_code = 'foo'

    def setUp(self):
        self.user_seeker_auth = UserSeekerAuth()

    def test_login_success(self):
        with app.test_request_context():
            with patch('requests.post') as mock_post:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    'cookie': 'g34rf3w4ertgf34rtg3e4w5rtg4te5rthe4edsfgethbC16eHXt6BIBb8O2GBHmrrmF9UokJ5g-beUWlg==',
                    'status': 'login_ok',
                    'success': True,
                    'user': {
                        'accesibleApplications': [4001],
                        'configurationMap': ['email_password @@ <EMAIL>', 'allowCancelReservation @@ False'],
                        'description': None,
                        'enabled': True,
                        'lastPasswordChange': '2025-04-03 12:00:30',
                        'name': 'foo',
                        'permission': 'admin;noReservations'
                    }
                }
                mock_post.return_value = mock_response

                response: UserSeekerLoginResponse = self.user_seeker_auth.login(self._test_username, self._test_password, self._two_factor_code)
                
                self.assertTrue(response['success'])
                self.assertIsNotNone(response['user'])
                self.assertEqual(response['message'], 'Login Successful')
                self.assertFalse(response['requires_2fa'])

                # Verify user data structure
                self.assertTrue(response['user']['enabled'])

    def test_login_invalid_credentials(self):
        with app.test_request_context():
            with patch('requests.post') as mock_post:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    'status': 'user_not_found',
                    'success': False,
                    'user': None
                }
                mock_post.return_value = mock_response

                response = self.user_seeker_auth.login('invalid_user', 'invalid_pass')
                self.assertFalse(response['success'])
                self.assertFalse(response['requires_2fa'])
                self.assertIsNotNone(response['message'])
                self.assertIsNone(response['user'])

    def test_login_2fa_required(self):
        with app.test_request_context():
            with patch('requests.post') as mock_post:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    'status': 'two_factor_required',
                    'success': False,
                    'user': None
                }
                mock_post.return_value = mock_response

                response = self.user_seeker_auth.login(self._test_username, self._test_password)
                
                self.assertFalse(response['success'])
                self.assertTrue(response['requires_2fa'])
                self.assertEqual(response['message'], 'Two Factor Authentication required')
                self.assertIsNone(response['user'])

    def test_login_server_error(self):
        with app.test_request_context():
            with patch('requests.post') as mock_post:
                mock_response = MagicMock()
                mock_response.status_code = 500
                mock_post.return_value = mock_response

                response = self.user_seeker_auth.login(self._test_username, self._test_password)
                
                self.assertFalse(response['success'])
                self.assertFalse(response['requires_2fa'])
                self.assertEqual(response['message'], 'Server Error')
                self.assertIsNone(response['user'])

    def test_user_disabled(self):
        with app.test_request_context():
            with patch('requests.post') as mock_post:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    'cookie': 'g34rf3w4ertgf34rtg3e4w5rtg4te5rthe4edsfgethbC16eHXt6BIBb8O2GBHmrrmF9UokJ5g-beUWlg==',
                    'status': 'login_ok',
                    'success': True,
                    'user': {
                        'accesibleApplications': [],
                        'configurationMap': ['email_password @@ <EMAIL>', 'allowCancelReservation @@ False'],
                        'description': None,
                        'enabled': False,
                        'lastPasswordChange': '2025-04-03 12:00:30',
                        'name': 'foo',
                        'permission': 'admin;noReservations'
                    }
                }
                mock_post.return_value = mock_response

                response: UserSeekerLoginResponse = self.user_seeker_auth.login(self._test_username,
                                                                                self._test_password,
                                                                                self._two_factor_code)

                self.assertEqual(response['message'], 'User disabled')
                self.assertIsNone(response['user'])