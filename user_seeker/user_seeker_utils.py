import requests
import logging
from flask import request
from requests.auth import HTTP<PERSON>asic<PERSON><PERSON>

from paraty_commons_3.security_utils import get_secret
from paraty_commons_3.user_seeker.interfaces import UserSeekerLoginResponse, UserSeekerResponseRequest, ManagerUser
from paraty_commons_3.audit_utils import make_traceback

try:
    from paraty import Config

    project = Config.PROJECT
except:
    project = 'unknown_project'


class UserSeekerAuth:
    """Utility class for handling authentication with User Seeker API"""

    BASE_URL = "https://user-seeker.ew.r.appspot.com"
    LOGIN_ENDPOINT = "/login/login_user"

    def login(self, username: str, password: str, two_factor_code: str = "") -> UserSeekerLoginResponse:
        """
        Authenticate a user against the User Seeker API

        Args:
            username (str): The username to authenticate
            password (str): The user's password
            two_factor_code (str, optional): Two-factor authentication code if required

        Returns:
            UserSeekerLoginResponse: Response containing:
                - success (bool): Whether the login was successful
                - requires_2fa (bool): Whether 2FA is required
                - message (str): Error message if any
                - user (User): User data if login successful
        """
        # Prepare the login request data
        login_data = {
            "user": username,
            "two_factor_code": two_factor_code,
            "password": password,
            "cookie": "",
            "ip": request.remote_addr,
            "footprint": request.headers.get('User-Agent', ''),
            "application": project,
            "dontAskAgain": False
        }

        try:
            login_api_auth_user, login_api_auth_password = self._retrieve_security_headers()

            # Make the POST request to the API endpoint
            api_auth = HTTPBasicAuth(login_api_auth_user, login_api_auth_password)
            target_endpoint = f"{self.BASE_URL}{self.LOGIN_ENDPOINT}"
            response = requests.post(target_endpoint, auth=api_auth, json=login_data)

            if response.status_code != 200:
                logging.error(f"Login failed for user {username}: {response.status_code}")
                return self._build_response_dict(False, False, 'Server Error', None)

            response_data: UserSeekerResponseRequest = response.json()

            # Check if 2FA is required
            if response_data['status'] == 'two_factor_required':
                return self._build_response_dict(False, True, 'Two Factor Authentication required', None)

            # Check if login was successful
            if not response_data['success']:
                return self._build_response_dict(False, False, 'Wrong Credentials', None)

            # User not enabled
            if not (response_data.get('user') and response_data['user']['enabled']):
                return self._build_response_dict(False, False, 'User disabled', None)

            return self._build_response_dict(True, False, 'Login Successful', response_data['user'])

        except Exception as e:
            logging.error(make_traceback())
            logging.error(f"Error during login for user {username}: {str(e)}")
            return self._build_response_dict(False, False, 'Server Error', None)

    @staticmethod
    def _build_response_dict(success: bool, requires_2fa:bool, message: str , user: ManagerUser | None) -> UserSeekerLoginResponse:
        return {
            'success': success,
            'requires_2fa': requires_2fa,
            'message': message,
            'user': user
        }

    @staticmethod
    def _retrieve_security_headers() -> tuple[str, str]:
        """
        Return:
             user, password
        """
        try:
            user_and_password = get_secret('security-seeker', 'user-seeker')
            login_api_auth_user = user_and_password.split(":")[0]
            login_api_auth_password = user_and_password.split(":")[1]
            return login_api_auth_user, login_api_auth_password
        except Exception as e:
            logging.error(make_traceback())
            logging.error(f"Error retrieving security headers: {str(e)}")
            return '', ''
