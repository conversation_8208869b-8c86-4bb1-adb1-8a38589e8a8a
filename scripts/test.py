import json

from paraty.utilities.manager_utils import get_configuration_property_value

from paraty.pages.cobrador.cobrador import get_rules_all, get_rules_to_apply_for_reservation

from paraty.pages.cobrador.cobrador_constants import PAYMENT_TYPE_IN_WEB, PAYMENT_TYPE_EARLY_PAYMENT, \
    PAYMENT_TYPE_CREATE_TOKEN, PAYMENT_TYPE_FLIGHT
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_rooms_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_hotel_by_application_id
from paraty import app

@app.route("/test", methods=['GET'])
def test_asasd():
	reservas = {}

	for hotel_code in get_all_hotels():
		try:
			if not get_configuration_property_value(hotel_code, "Gateway rules by cobrador"):
				continue
			hotel_code = 'hovima-santamaria'
			# reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
			#                                                                        search_params=[
			# 	                                                                       ('timestamp', '>=',
			# 	                                                                        "2025-02-19 17:30:00")]))
			reservations = list(datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code,
			                                                                       search_params=[('identifier', '=', '29750955')]))

		except:
			reservations = []

		for reservation in reservations:
			if not get_nothing_paid(json.loads(reservation.get('extraInfo'))):
				continue
			if avoid(reservation.get('identifier')):
				continue


			if sadf(hotel_code, reservation):
				if reservas.get(hotel_code):
					reservas[hotel_code].append(reservation.get('identifier'))
				else:
					reservas[hotel_code] = [reservation.get('identifier')]
			pass

		pass
	pass



def sadf(hotel_code, reservation):

		hotel = get_hotel_by_application_id(hotel_code)
		all_rates = get_rates_of_hotel(hotel, include_removed=True)
		all_rates_map = {x.get("key"): x for x in all_rates}

		all_rules = get_rules_all(hotel_code)
		type_rules_filter = [PAYMENT_TYPE_IN_WEB, PAYMENT_TYPE_EARLY_PAYMENT, PAYMENT_TYPE_CREATE_TOKEN]
		if reservation.get("price_only_flight"):
			type_rules_filter = [PAYMENT_TYPE_FLIGHT]
		filtered_rules = [rule for rule in all_rules if rule.get("type_rule", "") in type_rules_filter]

		all_rooms = get_rooms_of_hotel(hotel, include_removed=True)
		all_rooms_map = {x.get("key"): x for x in all_rooms}
		rules_to_apply_filtered = get_rules_to_apply_for_reservation(reservation, filtered_rules, all_rates_map, all_rooms_map, hotel_code)


def avoid(identifier):
	if identifier in "{'flashhotelbenidorm': ['60277953'], 'cortijo-zahara': ['84054406'], 'port-benidorm': ['38577321'], 'hotansa-grand-pas': ['90947389'], 'port-jardin': ['68329703'], 'oasishoteles-grandpalm': ['R65892210', 'R32325940'], 'ibersol-torremolinosbeach': ['64191525'], 'port-elche': ['67215492', '64411301'], 'ap-sinerama': ['47E214A4E', '54CBE3C62'], 'oasishoteles-tulum': ['R42216030'], 'oasishoteles-smart': ['R76415520'], 'port-vista': ['46974754'], 'ibersol-almunecar': ['26438199'], 'q10-cuevas': ['31893821', '17211387', '26735425', '71591840', '53747669'], 'q10-etxarri': ['20707812'], 'q10-ampolla': ['95881629', '57734833', '36174984', '92901780', '79722961'], 'oasishoteles-pyramid': ['R96592630'], 'oasishoteles-grandcancun': ['R70270830', 'R86165950', 'R44429490', 'R24709840', 'R11565760'], 'oasishoteles-senstulum': ['R21742490'], 'port-azafata': ['64331019'], 'port-denia': ['78955955', '13892187', '89483277'], 'port-huerto': ['65453942', '59834315', '50161466', '52215502'], 'port-europa': ['16175428'], 'villa-flamenca': ['R76346297'], 'solvasa-valencia': ['66753338'], 'port-fiesta': ['15711265', '21653774', '30987060', '94904580'], 'taiga-valdevaqueros': ['80815275', '22039843', '87198981'], 'q10-conil': ['49635101', '21806577', '38832696'], 'taiga-dunas': ['40620356', '92096033', '75469553', '89294221', '58950016', '71804067', '80568146', '61723423'], 'port-alicante': ['41565401', '45300299', '40859562', '28997668'], 'puntazo-2': ['52544748']}":
		return True

def get_nothing_paid(extra_info):
	return not float(extra_info.get("payed", 0) or 0) and not extra_info.get("payed_by_tpv_link") and not extra_info.get('payed_by_cobrador')

def test_123():
	order = {}.get("order", "")
	if isinstance("order", str):
		order = order.upper()
	pass
