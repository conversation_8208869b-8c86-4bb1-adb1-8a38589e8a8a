1. Install Docker
2. Make sure it's not enabled "Use Docker Compose V2" in the app
3. Docker app must be opened
4. Execute "build_docker.sh"
5. Again, make sure it's not enabled "Use Docker Compose V2" in the Docker app
6. Add "docker-compose.yml" to pycharm and set "python3" as "Python interpreter path"
7. It will create a python interpreter option to use for the run/debug in the project
8. Check which port are you using in the "main.py", it has to be the same port inside "ports" and "expose" variables in "docker-compose.yml"
9. To execute compass watch, you have to run first the server (with the compose configuration) and after that you have to run the file "execute_compass.sh"