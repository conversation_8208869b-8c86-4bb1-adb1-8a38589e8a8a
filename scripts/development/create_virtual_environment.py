import logging
import subprocess

def _print(message):
	logging.info(message)


def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def print_line(message):
	print()
	print("-------------------------------------------------------------")
	print(message.upper())
	print("-------------------------------------------------------------")


def _create_new_virtual_env_3_7():

	execute_command("cd %s/" % VIRTUAL_ENV_FOLDER)

	command = 'python3 -m venv %s/%s' % (VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME)
	execute_command(command)


def _add_required_libraries():

	upgrade_command = '%s/%s/bin/pip3 install --upgrade pip' % (VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME)
	execute_command(upgrade_command)

	command = '%s/%s/bin/pip3 install -r ../../src/requirements.txt' % (VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME)
	execute_command(command)


#Change this to a different path if you want to create the virtual environment in a different location
VIRTUAL_ENV_FOLDER = '../..'
VIRTUAL_LOCATION_NAME = 'venv'


if __name__ == '__main__':

	_create_new_virtual_env_3_7()
	_add_required_libraries()


