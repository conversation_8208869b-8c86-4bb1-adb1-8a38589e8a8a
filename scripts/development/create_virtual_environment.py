import logging
import subprocess
import platform


def _print(message):
    logging.info(message)


def _get_architecture_prefix():
    if 'arm' in platform.machine():
        return 'arch -arm64 '
    else:
        return ''


def execute_command(cmd, print_it=True):
    if print_it:
        print(cmd)

    subprocess.check_call(cmd, shell=True)


def print_line(message):
    print()
    print("-------------------------------------------------------------")
    print(message.upper())
    print("-------------------------------------------------------------")


def _create_new_virtual_env_3_7():
    execute_command("cd %s/" % VIRTUAL_ENV_FOLDER)

    command = '%spython3.11 -m venv %s/%s' % (_get_architecture_prefix(), VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME)
    execute_command(command)


def _add_required_libraries():
    # force_reinstall = True  # Set to true In case you run into incompatibilities with the libraries in mac M1, you can force a reinstall here

    reinstall_command = ''
    # if force_reinstall:
    # 	reinstall_command = '--ignore-installed --no-cache-dir'

    upgrade_command = '%s%s/%s/bin/pip3 install --upgrade pip  %s' % (_get_architecture_prefix(), VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME, reinstall_command)
    execute_command(upgrade_command)

    command = '%s%s/%s/bin/pip3 install -r ../../src/requirements.txt  %s' % (_get_architecture_prefix(), VIRTUAL_ENV_FOLDER, VIRTUAL_LOCATION_NAME, reinstall_command)
    execute_command(command)


# Change this to a different path if you want to create the virtual environment in a different location
VIRTUAL_ENV_FOLDER = '../..'
VIRTUAL_LOCATION_NAME = 'venv'

if __name__ == '__main__':
    _create_new_virtual_env_3_7()
    _add_required_libraries()
