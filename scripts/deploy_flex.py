import logging
import subprocess

def _print(message):
	logging.info(message)


def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)


def print_line(message):
	print()
	print("-------------------------------------------------------------")
	print(message.upper())
	print("-------------------------------------------------------------")


def deploy_flex():
	# execute_command("cd ../src")

	command = 'cd ../src;gcloud config set app/cloud_build_timeout 30000s;gcloud app deploy app_flexible.yaml --project payment-seeker --no-promote'
	execute_command(command)


if __name__ == '__main__':
	deploy_flex()
