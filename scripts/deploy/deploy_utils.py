import datetime
import json
import os
import subprocess
from urllib.parse import urlencode

import requests



def execute_command(cmd):
    print(cmd)
    subprocess.check_call(cmd, shell=True)



def comunicate_upload_to_development_dpto(integration_name, base_integration_branch, adapter_branch, short_description):
    # SLACK

    user = subprocess.check_output(["git", "config", "user.name"])
    user = user.decode().replace("\n", "")

    message = f"usuario:    {user}\n" \
              f"target project:     [{integration_name}]" \
              f"\nBASE INTEGRATION Branch:     [{base_integration_branch}]" \
              f"\nADAPTER Branch:     [{adapter_branch}]" \
              f"\nDescription:\n{short_description}"

    data_to_send = {'message': message,
                    'chat_id': 'C06ALTFNJL8'}
    header = {"content-type": "application/json"}
    requests.post('https://europe-west1-web-seeker.cloudfunctions.net/paraty-slack-notify', headers=header,
                  data=json.dumps(data_to_send))

    # Checklist
    user_email = subprocess.check_output(["git", "config", "user.email"]) or ""
    user_email = user_email.decode("utf-8").replace("\n", "")

    form_autofill = {
        "entry.17072800": integration_name,
        "emailAddress": user_email
    }
    query = urlencode(form_autofill, safe="@")

    os.system(
        'open "https://docs.google.com/forms/d/e/1FAIpQLSf3nLeuAmSVSgzNem4gdJ5b4EgSkC38HP4x4pagLLvLWYSgkQ/viewform?%s"' % query)
