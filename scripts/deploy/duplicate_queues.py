"""
This script creates queues to work with cloud run
This allow to use queues without overwrite the target

Ie. (queue.yaml)
Original:
- processor0
    target: background-module

- processor1
    target: background-module

After script:
- processor0
    target: background-module

- processor1
    target: background-module

- processor0-run
- processor1-run

"""
import os.path
import re

SRC_PATH = os.path.join(os.path.dirname(__file__), '../../src')

def get_original_queue_file() -> str:
    queue_file_path = os.path.join(SRC_PATH, 'queue.yaml')
    with open(queue_file_path) as f:
        return f.read()

def create_new_queues(original_queue_file: str) -> str:
    original_queue_file = original_queue_file.replace('queue:', '')
    original_queue_file = re.sub('target:.*', '', original_queue_file)
    original_queue_file = re.sub(r'name:(.*)\n', r'name:\g<1>-cloud-run\n', original_queue_file)

    return original_queue_file


def save_new_queues_file(original_queue_file: str, new_queues: str):
    queue_file_path = os.path.join(SRC_PATH, 'queue.yaml')
    with open(queue_file_path, 'w') as f:
        f.write(original_queue_file)
        f.write(new_queues)


if __name__ == '__main__':
    original_queue_file = get_original_queue_file()
    new_queues = create_new_queues(original_queue_file)

    save_new_queues_file(original_queue_file, new_queues)
