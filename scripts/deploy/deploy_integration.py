import json
import os
import re
import subprocess

import requests

from scripts.deploy.deploy_utils import comunicate_upload_to_development_dpto

# Build is located in project build-tools-2
BUILD_URL = "https://cloudbuild.googleapis.com/v1/projects/build-tools-2/triggers/adapter-deploy-webhook:webhook?key=AIzaSyDoDavVcHHMLcipc5kr6HQDOQgTS93F02M&secret=zs__ob8xoCU9nvwTvWVILRGA5SnkL79s"

DEFAULT_BRANCH_OPTIONS = {
    'yieldplanet': 'python3-flexible',
    'bookingexpert': 'master',
    'base-integration': 'python3-flexible',
    'parity': 'master',
    'verticalbooking': 'master',
    'availpro2': 'main',
    'cloudbeds': 'master',
    'avalon': 'master',
    'dingus': 'feature/python3-migration',
    'siteminder': 'feature/python3-migration',
    'host': 'python3-flexible',
    'duetto': 'master',
    'omnibeespull': 'master'
}

DEFAULT_APPENGINE_MACHINE_TYPE = {
    'yieldplanet': 'F4',
    'bookingexpert': 'F2',
    'parity': 'F4',
    'verticalbooking': 'F4',
    'availpro2': 'F4',
    'cloudbeds': 'F2',
    'avalon': 'F2',
    'dingus': 'F4',
    'siteminder': 'F4',
    'host':  'F2',
    'duetto': 'F2',
    'omnibeespull': 'F2'
}

DEFAULT_ADAPTERS_LOCATIONS = {
    'yieldplanet': 'us-central1',
    'bookingexpert': 'us-east1',
    'parity': 'europe-west1',
    'verticalbooking': 'europe-west1',
    'availpro2': 'europe-west1',
    'cloudbeds': 'us-central1',
    'avalon': 'us-central1',
    'dingus': 'europe-west1',
    'siteminder': 'europe-west1',
    'host': 'europe-west1',
    'duetto': 'europe-west1',
    'omnibeespull': 'us-central1'
}

DEFAULT_ADAPTER_GOOGLE_PROJECTS = {
}

DEFAULT_ADAPTER_GIT_REPOS = {
    'verticalbooking': 'vertical-booking-adapter',
}

def separator_message_decorator(func):
    def wrapper(*args, **kwargs):
        for i in range(500):
            print()
        return func(*args, **kwargs)
    return wrapper

def welcome_message():
    print("======= DEPLOY INTEGRATION =======")
    print()
    print("This script is used to deploy a new integration to the cloud.")
    print()

@separator_message_decorator
def request_integration_name():
    integrations_list = list(DEFAULT_ADAPTERS_LOCATIONS.keys())
    print("The available integrations are:")
    for i, integration in enumerate(integrations_list):
        print(f"{i}) {integration}")

    target_integration = input("Please select the integration: ")

    if "5" in target_integration and "15" not in target_integration:
        print("¡Con premio!")

    target_integration = integrations_list[int(target_integration)]
    print(f"Selected integration: {target_integration}")
    return target_integration

@separator_message_decorator
def request_custom_branch(integration_name):
    print("Custom branch options:")
    use_custom_branch = ''
    while use_custom_branch not in ['y', 'n']:
        use_custom_branch = input("Do you want to deploy a custom branch? (y/n): ")

    if use_custom_branch == 'n':
        return DEFAULT_BRANCH_OPTIONS.get('base-integration', 'master'), DEFAULT_BRANCH_OPTIONS.get(integration_name)

    else:
        adapter_branch_name = input("Adapter branch name: ") or DEFAULT_BRANCH_OPTIONS.get(integration_name)
        base_integration_branch_name = input("Base integration branch name: ") or DEFAULT_BRANCH_OPTIONS.get('base-integration')
        return base_integration_branch_name, adapter_branch_name

def get_developer_email(global_config=False):
    project_config = "git config user.email"
    project_global_config = "git config --global user.email"
    target_command = project_config if not global_config else project_global_config
    proc = subprocess.Popen(target_command, stdout=subprocess.PIPE, shell=True)
    (out, err) = proc.communicate()
    return out.decode('utf-8').strip()



@separator_message_decorator
def request_short_description():
    short_description = ""
    while not short_description:
        short_description = input("Short description / commit / clickup: ")

    return short_description


@separator_message_decorator
def ask_to_deploy_background() -> str|None:
    deploy_background = ''
    while deploy_background not in ['y', 'n', 'only']:
        deploy_background = input("Do you want to deploy the integration in the background also? (y/n/only): ")

    return deploy_background


@separator_message_decorator
def ask_to_customize_version_name() -> str|None:
    custom_version_name = ''
    while custom_version_name not in ['y', 'n']:
        custom_version_name = input("Do you want to customize the version name? (y/n): ")

    if custom_version_name == 'y':
        return input("Please enter the version name: ")

    return ''



@separator_message_decorator
def confirm_deployment(integration_name, base_integration_branch, adapter_branch, deploy_background):
    machine_type = DEFAULT_APPENGINE_MACHINE_TYPE.get(integration_name, 'F4')

    custom_adapter_version_name = ask_to_customize_version_name()
    short_description = request_short_description()

    post_data = {
        'adapter_name': integration_name,
        'adapter_location': DEFAULT_ADAPTERS_LOCATIONS[integration_name],
        'base_integration_branch': base_integration_branch,
        'adapter_branch': adapter_branch,
        'machine_type': machine_type,
        'deploy_background': deploy_background or '',
        'developer_email': re.sub('([.@])', '_', get_developer_email()).lower(),
        'google_project': DEFAULT_ADAPTER_GOOGLE_PROJECTS.get(integration_name, f'{integration_name}-adapter'),
        'git_repository': DEFAULT_ADAPTER_GIT_REPOS.get(integration_name, f'{integration_name}-adapter'),
        'custom_adapter_version_name': custom_adapter_version_name
    }

    print("Will be deployed with the following options:")
    print("Integration to deploy: ", integration_name)
    print("Base integration branch: ", base_integration_branch)
    print("Adapter branch: ", adapter_branch)
    print("Appengine machine type: ", machine_type)
    print("Deploy background: ", deploy_background)
    if custom_adapter_version_name:
        print("Appengine version name: ", custom_adapter_version_name)
    print("Short description: ", short_description)

    is_correct = ''
    while is_correct not in ['y', 'n']:
        is_correct = input("Is this correct? (y/n): ")

    if is_correct == 'n':
        print("Exiting...")
        return

    else:
        requests.post(
            BUILD_URL,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(post_data)
        )

        print("Deployment started. Check the status in the Cloud Build console at build-tools2.")
        os.system(
            'open "https://console.cloud.google.com/cloud-build/builds?referrer=search&inv=1&invt=AbkoMw&project=build-tools-2"')

        comunicate_upload_to_development_dpto(integration_name, base_integration_branch, adapter_branch, short_description)


if __name__ == '__main__':
    welcome_message()

    integration_name = request_integration_name()
    base_integration_branch, adapter_branch = request_custom_branch(integration_name)
    deploy_background = ask_to_deploy_background()

    confirm_deployment(integration_name, base_integration_branch, adapter_branch, deploy_background)