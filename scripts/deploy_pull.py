import datetime
import json
import os
import subprocess
from urllib.parse import urlencode
import unittest

import requests

SERVICES_TO_UPDATE = {
    "3": {
        "yaml": "app_front.yaml",
        "service": "front",
        "type": "front"
    },
    "1": {
        "yaml": "app_flexible.yaml",
        "service": "default",
        "type": "backend"
    },
    "2": {
        "yaml": "app_failover.yaml",
        "service": "failover",
        "type": "failover"
    },
    "4": {
        "yaml": "app.yaml",
        "service": "default",
        "type": "standard"
    }
}

def execute_command(cmd):
    print(cmd)
    subprocess.check_call(cmd, shell=True)


def execute_test():
    loader = unittest.TestLoader()
    start_dir = '../src/test/gateways/'
    suite = loader.discover(os.path.join(os.path.dirname(__file__), start_dir))

    runner = unittest.TextTestRunner()
    result_test = runner.run(suite)
    return result_test.wasSuccessful()


def loop_version():
    custom_version = input("Version's name: ")
    if not custom_version:
        return loop_version()

    return custom_version


if __name__ == "__main__":
    # print("Ejecutando tests")
    # loader = unittest.TestLoader()
    # start_dir = '../src/test/gateways/'
    # suite = loader.discover(os.path.join(os.path.dirname(__file__), start_dir))
    #
    # runner = unittest.TextTestRunner()
    # result_test = runner.run(suite)
    # print()
    # if not result_test.wasSuccessful():
    #     print("Los test han fallado, hay que revisar que ha ocurrido")
    #     exit()
    project_name = "payment-seeker"
    print("Uploading project")
    user = os.getcwd().split("/")[2]
    path_tmp = "/Users/<USER>/tmp" % user

    if not os.path.exists(path_tmp):
        os.mkdir(path_tmp)
        print("tmp folder created")

    path_projects = "%s/projects" % path_tmp
    if not os.path.exists(path_projects):
        os.mkdir(path_projects)
        print("tmp projects folder created")

    current_date = datetime.datetime.now()

    project_build = "%s_%s" % (project_name, current_date.strftime("%Y%m%d_%H%M%S"))
    project_build_path = "%s/%s" % (path_projects, project_build)
    option_selected = input("Select an option:\n1. Production\n2. Failover\n3. Front\n4. Standard\n")
    if int(option_selected) < 1 and int(option_selected) > 4:
        print("Error")
        exit(1)

    option_selected = SERVICES_TO_UPDATE[option_selected]

    custom_branch = input("Custom branch (empty for master): ")

    gcloud_version = current_date.strftime("%Y%m%d%H%M%S")
    if option_selected.get("type") == "standard":
        gcloud_version = loop_version()

    short_description = input("Short description: ")

    execute_command(f"cd {path_projects} && <NAME_EMAIL>:paraty/{project_name}.git {project_build}")
    execute_command(f"cd {project_build_path} && git submodule init && git submodule update && cd src/paraty_commons_3 && git checkout master")
    if custom_branch:
        execute_command(f"cd {project_build_path} && git checkout {custom_branch}")

    update_command = f"gcloud config set app/cloud_build_timeout 30000s;gcloud app deploy {option_selected['yaml']} --project {project_name} --no-promote -q"

    update_command += f" --version={gcloud_version}"

    # SLACK
    user = subprocess.check_output(["git", "config", "user.name"])
    user = user.decode().replace("\n", "")

    message = f"usuario:    {user}\ntarget project:     [payment-seeker]\ntarget service:     [{option_selected['type']}]\ntarget version:     [{gcloud_version}]\n{short_description}"
    if option_selected.get("type") == "standard":
        message += f"\nTest version deployed"
    data_to_send = {'message': message,
                    'chat_id': 'C06ALTFNJL8'}
    header = {"content-type": "application/json"}

    command_run = f"gcloud run deploy {option_selected['type']} --source . --platform managed --region europe-west1 --allow-unauthenticated --project payment-seeker --tag={option_selected['type']}{gcloud_version} --service-account=<EMAIL>"
    execute_command(f"cd {project_build_path}/src && {update_command}")

    requests.post('https://europe-west1-web-seeker.cloudfunctions.net/paraty-slack-notify', headers=header,
                  data=json.dumps(data_to_send))

    user_email = subprocess.check_output(["git", "config", "user.email"]) or ""
    user_email = user_email.decode("utf-8").replace("\n", "")

    form_autofill = {
        "entry.********": project_name,
        "emailAddress": user_email
    }
    query = urlencode(form_autofill, safe="@")

    os.system(f'open https://{gcloud_version}-dot-payment-seeker.appspot.com/healthcheck')
    os.system('open "https://docs.google.com/forms/d/e/1FAIpQLSf3nLeuAmSVSgzNem4gdJ5b4EgSkC38HP4x4pagLLvLWYSgkQ/viewform?%s"' % query)
