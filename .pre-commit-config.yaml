repos:
  - repo: local
    hooks:
      - id: pylint
        name: pylint
        entry: bash -c 'pylint "$@"'
        language: system
        verbose: true
        types: [python]
        args:
          [
            "-rn",
            "-sn",
            "--rcfile=pylintrc",
            "--jobs=0",
            "--fail-on=R,C,W,E,F",
            "--output-format=colorized",
            "--msg-template='{msg_id}:{line:4d}: [{obj}] {msg}'"
          ]
        exclude: tests(/\w*)*/functional/|tests/input|tests(/\w*)*data/|doc/
  - repo: *****************:paraty/check_with_last_score.git
    rev: 8ea4db3906439f58c6f408203139a030328780d9
    verbose: true
    hooks:
      - id: check_with_last_score
        stages: [commit]
        verbose: true
        additional_dependencies: [pylint==2.15.10, GitPython, colorama, rich]

