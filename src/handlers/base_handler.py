import cProfile
import functools
from abc import abstractmethod
import hashlib
import uuid
import logging

from flask import request, Response
from flask.views import MethodView

from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE
from model.configuration_model import IntegrationUser
from paraty import Config, app
from paraty.audit.audit_utils import audit
from paraty.exceptions import InvalidCredentialsException
from paraty.utils.rendering import render_result
from paraty.utils.xml_utils import XmlFinder
from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.decorators.timerdecorator import timeit
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels

SHOW_EXCEPTION_MESSAGE_IN_RESPONSE = True
MASTER_NAME_USER = "paraty_master"
MASTER_PASS = "P4r4ty M4st3r B@ck3nd"


@app.errorhandler(Exception)
def handle_exception(exception):
	# Log error
	message = make_traceback()

	# BusinessEntityNotFound is not really an error but a configuration issue
	if message and ('BusinessEntityNotFoundException' in message) or ('WrongParamsException' in message):
		logging.warning(message)
	else:
		logging.exception(message)

	# Let specific implementation decide what to do
	return SPECIFIC_INTEGRATION_INTERFACE._handle_exception(None, exception, None)

def handle_base_integration_exception(method_handler):
	@functools.wraps(method_handler)
	def wrapper(*args, **kwargs):
		try:
			return method_handler(*args, **kwargs)
		except Exception as e:
			message_traceback = make_traceback()
			words_to_avoid = ['Hotel not enabled', 'InvalidCredentialsException', 'errorCODE']
			if any(word in message_traceback for word in words_to_avoid):
				logging.info(message_traceback)
			else:
				logging.error(message_traceback)

			return SPECIFIC_INTEGRATION_INTERFACE._handle_exception(args[0], e, None)

	return wrapper

class BaseIntegrationHandler(MethodView):

	@timeit
	@handle_base_integration_exception
	def get(self):
		return self.post()

	@timeit
	@handle_base_integration_exception
	def post(self):

		request_id = str(uuid.uuid4())

		logging.info('REQUEST_ID=%s' % request_id)
		# logging.info('HEADERS=%s' % str(request.headers))

		if 'gzip' in request.headers.get('User-Agent', '').lower():
			logging.info('User-Agent contains gzip. Request content will be decoded')
			request_body = request.data.decode("zlib")
		else:
			request_body = request.data.decode('utf8')

		xml_finder = XmlFinder(request_body)

		self.__validate_credentials(xml_finder)

		#Note that we redefined get function in order to enable application profiler
		profile_param = request.args.get('profile', 'false').lower() == 'true'
		if profile_param and Config.DEV:
			# Create a StringIO object to capture the profiler output
			import io
			import pstats
			from flask import Response
			s = io.StringIO()
			
			# Run the profiler
			profiler = cProfile.Profile()
			result = profiler.runctx('self._do_post(xml_finder, request_id, request_body)', globals(), locals())
			
			# Get stats and limit to top 100 entries by cumulative time
			ps = pstats.Stats(profiler, stream=s).sort_stats('cumulative')
			ps.print_stats(200)  # Only show top 200 results
			
			# Return the profiler output as a string with application/text content type
			return Response(s.getvalue(), mimetype='application/text')
		else:
			return self._do_post(xml_finder, request_id, request_body)


	def _get_extra_error_params(self):
		return {}



	@abstractmethod
	def _do_post(self, xml_finder: XmlFinder, request_id, request_body: str):
		raise NotImplemented

	def __validate_credentials(self, xml_finder):

		name, password = self._get_username_and_password(xml_finder)

		if name == MASTER_NAME_USER and (password == None or password == MASTER_PASS):
			self.user = get_master_user()
			logging.warning("master user used!!")
		else:

			self.user = get_user_by_name(name)

			if not self.user or not self.user.enabled:
				logging.info("User not found or not enabled")
				raise InvalidCredentialsException(name)

			if password == None:
				#This indicates that we should ignore the password in this integration
				logging.debug('User: username=%s, enabled=%s, hotels=%s', self.user.name, self.user.enabled, self.user.hotels)
				return

			hashed_password = hashlib.md5(password.encode('utf-8')).hexdigest()

			if self.user.password and self.user.password != hashed_password:
				logging.info("Wrong password")
				raise InvalidCredentialsException(name)

			if not Config.DEV:
				logging.debug('User: username=%s, enabled=%s, hotels=%s', self.user.name, self.user.enabled, self.user.hotels)

	def _get_username_and_password(self, xml_finder):
		return SPECIFIC_INTEGRATION_INTERFACE._get_username_and_password(xml_finder)

	def _write_response(self, data, template, request_id="", request_body="", audited_enabled=False, hotel=None, logging_enabled=False, minimize=False, to_one_line=False, operation=""):

		#For robustness
		if data == None:
			data = {}

		if request_body == None:
			request_body =""

		if callable(getattr(SPECIFIC_INTEGRATION_INTERFACE, "_custom_request_body", None)):
			request_body = SPECIFIC_INTEGRATION_INTERFACE._custom_request_body(self)

		response_body = render_result(data, template)

		# Save response to file
		with open('77748612.xml', 'w') as f:
			f.write(response_body)

		if audited_enabled:
			audit(request_id, request.path, request_body, response_body, hotel, operation=operation)

		if minimize:
			response_body = response_body.replace("\n\n", "\n")
		if to_one_line:
			response_body = response_body.replace("  ", "").replace("\t", "").replace("\n", "")


		if logging_enabled:
			logging.info("Request response:")
			logging.info(response_body)

		return Response(response=response_body, status=200, headers={'Content-Type': SPECIFIC_INTEGRATION_INTERFACE.get_content_type()})




@timed_cache(hours=24)
def get_user_by_name(name):
	return IntegrationUser.query(IntegrationUser.name == name).get()


@timed_cache(hours=24)
def get_all_integration_credentials():
	return IntegrationUser.query(IntegrationUser.enabled == True)

def get_master_user():

	master_user = IntegrationUser()
	master_user.name = MASTER_NAME_USER
	master_user.password = ''
	master_user.hotels = [x['applicationId'] for x in get_all_hotels().values() if x.get('enabled', False)]
	master_user.enabled = True

	return master_user