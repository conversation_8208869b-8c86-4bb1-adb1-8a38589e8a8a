import logging

from google.cloud import logging as logging_cloud
from flask.views import MethodView
from google.cloud.logging import DESCENDING

from paraty import Config
from paraty.utils import email_utils
from paraty.utils.email_utils import EMAILS_SUPPORT

LOG_MIN_LEVEL = 'ERROR' #Everything else will not be taken into account

app_name = Config.PROJECT

DEFAULT_TITLE = '[%s] Error Finder summary' % app_name


#TODO, MIGRATE LOGSERVICE
#TODO, https://cloud.google.com/logging/docs/reference/libraries#client-libraries-install-python

#See
#https://pypi.org/project/google-cloud-logging/


class ErrorFinderHandler(MethodView):

	def get(self):
		html = ''
		report_needed = False
		errors_found = False

		client = logging_cloud.Client(project=Config.PROJECT)
		FILTER = 'SEVERITY >= %s' % (LOG_MIN_LEVEL)
		for req_log in client.list_entries(filter_=FILTER, order_by=DESCENDING):  # API call(s)
			report_needed = True
			errors_found = True
			html = html + '<b>Timestamp:</b> %s<br />' % req_log.timestamp
			try:
				if req_log.http_request.get("requestUrl"):
					html += "<b>Route:</b> %s<br />" % req_log.http_request.get("requestUrl")
			except:
				logging.info("No requestUrl")

			html = html + '<b>Message:</b> %s<br />' % req_log.payload
			html = html + '<br /><br />'

		if report_needed and errors_found:
			email_utils.sendEmail(EMAILS_SUPPORT, DEFAULT_TITLE, "", html)


		#Useful so we can call this manually also
		return "OK"

	def valid_error_filter(self, req_log):
		valid_error = True

		if app_name == "temairazu-adapter":
			if "tema000" in req_log.resource:
				valid_error = False

		return valid_error
