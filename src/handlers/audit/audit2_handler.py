'''
In charge or managing filters and launching new Search
'''
import json
import requests
import logging

from flask import Response, request
from flask.views import MethodView
from werkzeug.utils import redirect

import interface_to_implement
from paraty import Config
from paraty.constants.audit import AUDIT_ASSISTANT_PERFORM_SEARCH
from paraty.utils.rendering import render_result
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_boards_of_hotel, get_rooms_of_hotel
from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.hotel_manager import hotel_manager_utils


def _build_search_filter(request):
	max_days = int(request.values.get('max_days', 5))

	if max_days > 30:
		raise Exception("Max days: 30")

	rates = request.form.getlist('rates')
	rooms = request.form.getlist('rooms')
	boards = request.form.getlist('boards')
	price_day = request.values.get('priceday')
	price_day_to = request.values.get('priceday-to')
	only_last = request.values.get('only_last')
	capacities = request.values.get('capacities')

	#Required when executing this locally
	integration = request.values.get('integration')

	result = {
		'max_days': max_days,
		'rates': rates,
		'boards': boards,
		'rooms': rooms,
		'price_day': price_day,
		'price_day_to': price_day_to,
		'only_last': only_last,
		'production_url': 'https://%s-adapter.appspot.com/' % integration,
		'capacities': capacities,
		'price_property': request.values.get('price_property'),
		'min_stay_property': request.values.get('min_stay_property'),
		'available_property': request.values.get('available_property'),
		'status_property': request.values.get('status_property'),
		'max_stay_property': request.values.get('max_stay_property'),
		'release_property': request.values.get('release_property'),
	}

	logging.info("Search Filter: %s", json.dumps(result))

	if Config.DEV:
		result['production_url'] = 'https://yieldplanet-adapter.appspot.com/'

	return result


def _get_room_capacities(r):
		if 'capacities' not in r:
			return []

		capacities = r['capacities']

		return capacities


class Audit2Handler(MethodView):
	def get(self):

		hotel_code = request.values.get('hotel_code')

		if not hotel_code:
			return Response(response='Missing hotel_code', status=400)

		hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)

		all_rates = [{'id': datastore_utils.alphanumeric_to_id(x['key']), 'name': x.get('localName')} for x in get_rates_of_hotel(hotel, include_removed=True) if x.get('localName')]
		all_boards = [{'id': datastore_utils.alphanumeric_to_id(x['key']), 'name': x.get('name')} for x in get_boards_of_hotel(hotel, include_removed=True) if x.get('name')]
		all_rooms = [{'id': datastore_utils.alphanumeric_to_id(x['key']), 'name': x.get('name')} for x in get_rooms_of_hotel(hotel, include_removed=True) if x.get('name')]


		all_rates.sort(key=lambda x:x['name'].lower())
		all_boards.sort(key=lambda x:x['name'].lower())
		all_rooms.sort(key=lambda x:x['name'].lower())

		valid_capacities = set()
		for current_room in get_rooms_of_hotel(hotel, include_removed=True):
			for valid_capacity in _get_room_capacities(current_room):
				valid_capacities.add(valid_capacity)

		sorted_capacities = sorted(valid_capacities)

		template = "audit/new_search.html"

		params = {
			'rooms': all_rooms,
			'rates': all_rates,
			'boards': all_boards,
			'capacities': sorted_capacities
		}

		response_body = render_result(params, template)
		return Response(response=response_body, status=200)

	def post(self):

		# Trigger a new search and redirect to the results page
		hotel = request.values.get('hotel_code')
		search_filter = _build_search_filter(request)
		headers = {'Content-Type': 'application/json'}
		params = {
			'hotel_code': hotel,
			'integration': interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
			'modify_url': interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_modify_url(),
			'filter': search_filter
		}
		logging.info('[AUDIT] Audit search for hotel {} with params {}'.format(hotel, params))
		response = requests.post(AUDIT_ASSISTANT_PERFORM_SEARCH, data=json.dumps(params), headers=headers)

		if response.status_code == 200:
			audit_id = response.content.decode('utf-8')
			logging.info('[AUDIT] [{}] Response from audit assistant: {}'.format(response.status_code, audit_id))
			return redirect("/audit3?audit_id={}&hotel_code={}&page=0".format(audit_id, hotel))
		else:
			msg = '[AUDIT] [{}] Bad response from audit assistant: {}'.format(response.status_code, response.content)
			logging.info(msg)
			return Response(response=msg, status=200)
