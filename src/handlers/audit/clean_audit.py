import datetime
import logging

from flask.views import MethodView
from google.cloud import ndb

from model.audit import EndpointCallAuditEvent
from paraty import Config
from paraty.utils.queues.queues_utils import defer

DELETE_LOGS_MONTHS = 9
SIZE_PAGE = 5000

def clean_old_audits(max_to_delete=90000):

	# I substract especified months from today like limit
	months_limit = datetime.datetime.today() - datetime.timedelta(days=(30 * DELETE_LOGS_MONTHS))

	logging.info("[Clean Audit]: All old audits will be cleaned")
	query = EndpointCallAuditEvent.query(EndpointCallAuditEvent.timestamp <= months_limit)

	more = True
	next_curs = None


	keys_to_delete = []
	while more:
		if next_curs:
			operations, next_curs, more = query.fetch_page(SIZE_PAGE, keys_only=True, start_cursor=next_curs)
		else:
			operations, next_curs, more = query.fetch_page(SIZE_PAGE, keys_only=True)

		keys_to_delete.extend(operations)

		logging.info("[Clean Audit]: {} old audits have been cleaned".format(len(keys_to_delete)))

		if len(keys_to_delete) >= max_to_delete:
			more = False

	deleted_result = ndb.delete_multi_async(keys_to_delete)

	logging.info("[Clean Audit]:TOTAL audits cleaned: {}".format(len(keys_to_delete)))


class CleanAuditsHandler(MethodView):

	def get(self):
		#This might take quite a while, so better to do it on background and in a few steps
		if Config.DEV:
			clean_old_audits(90000)
			return 'OK'

		defer(clean_old_audits)

		return 'OK'
		# deferred.defer(clean_old_audits)

