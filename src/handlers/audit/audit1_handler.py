'''
In charge of choosing hotel and redirecting to the next step in the proper integration
'''
from flask import Response, request
from flask.views import MethodView
from werkzeug.utils import redirect

from paraty import Config

from paraty.utils.rendering import render_result
from paraty_commons_3.hotel_manager import hotel_manager_utils


class Audit1Handler(MethodView):
	def get(self):

		error_in_previous_search = request.values.get('error', '')

		all_hotels = hotel_manager_utils.get_all_hotels().values()

		all_hotels = [{'code':x['applicationId'], 'name': x['name']} for x in all_hotels if x.get('enabled')]

		all_hotels = sorted(all_hotels, key=lambda x: x['name'])

		template = 'audit/choose_hotel.html'
		params = {'hotels': all_hotels, 'error': error_in_previous_search}

		response_body = render_result(params, template)
		return Response(response=response_body, status=200)


	def post(self):

		hotel_name = request.values.get('hotel_name')
		all_hotels = {x['name'].lower(): x['applicationId'] for x in hotel_manager_utils.get_all_hotels().values() if x.get('enabled', 'false')}
		hotel_code = all_hotels.get(hotel_name.lower())

		from paraty.development import new_audit_service
		integrated_here, integrations = new_audit_service.is_hotel_integrated(hotel_code)

		SELECT_HOTEL_URL = '/audit1'
		OTHER_STUFF = ['tripadvisor', 'paritymaker', 'sermepa', 'hotelads', 'final_prices', 'donpancho', 'bird']

		if not integrated_here:

			hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)

			valid_integrations = []
			if integrations:
				valid_integrations = [x for x in integrations if x.lower() not in OTHER_STUFF]

			if not valid_integrations:
				#Redirect back to search
				message = "El Hotel '%s' no parece estar integrado" % hotel.get('name')
				return redirect(SELECT_HOTEL_URL + "?error=%s" % message)


			elif len(valid_integrations) > 1:
				#Redirect back to search
				message = "El Hotel '%s' no parece estar integrado en este servidor, integraciones configuradas: %s" % (hotel.get('name'), ", ".join([x for x in valid_integrations]))
				return redirect(SELECT_HOTEL_URL + '?error=%s' % message)

			else:
				#Go to the corresponding integration audit filter
				integration_url = "https://%s-adapter.appspot.com/audit2" % valid_integrations[0].lower()

				if Config.DEV:
					integration_url = '/audit2'

				return redirect(str("%s?hotel_code=%s&integration=%s" % (integration_url, hotel_code, valid_integrations[0].lower())))

		return redirect("/audit2?hotel_code=%s" % hotel_code, code=301)

