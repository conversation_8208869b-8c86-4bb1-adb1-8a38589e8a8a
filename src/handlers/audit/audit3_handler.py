'''
Handler in charge or showing the results for a given audit
'''
import json
import logging

import requests
from flask import Response, request
from flask.views import MethodView

from paraty.constants.audit import AUDIT_ASSISTANT_GET_ITEMS
from paraty.utils.rendering import render_result
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_boards_of_hotel, get_rooms_of_hotel
from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.hotel_manager import hotel_manager_utils


class Audit3Handler(MethodView):

	def get(self):

		requested_audit_id = request.values.get("audit_id")
		hotel_code = request.values.get("hotel_code")
		page = int(request.values.get('page', 0))

		payload = {
			'request_id': requested_audit_id,
			'hotel_code': hotel_code,
			'page': page,
		}

		response = requests.get(AUDIT_ASSISTANT_GET_ITEMS, params=payload, timeout=30)
		search_result = None
		pages = []
		if response.status_code == 200:
			search_result = json.loads(response.content)
			if not search_result['finished'] and not search_result['results']:
				my_audit_result = None  # still looking for avails
			elif search_result['results']:
				search_result = json.loads(response.content)
				my_audit_result = [json.loads(res) for res in search_result.get('results', '').split(',\n') if res]
				pages = list(range(int(search_result.get('pages', 1))))
				page = int(search_result.get('page', 0))
			else:
				search_result = {
					'finished': True,
				}
				my_audit_result = []
				page = 0
		else:
			my_audit_result = None

		#We already have results available at the datastore!!!!!
		if my_audit_result is not None:
			hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
			if my_audit_result:
				from paraty.utils.checker_utils import validate_against_production
				validate_against_production(my_audit_result, hotel_code)
				_convert_to_human_names(hotel, my_audit_result, "SPANISH")
				
			params = {
				'finished': search_result['finished'],
				'items': my_audit_result,
				'title': '%s - %s' % (request.values.get('priceDate'), hotel_code),
				'hotel_code': hotel_code,
				'show_date': True,
				'validated': True,
				'pages': pages,
				'page': page,
				'audit_id': requested_audit_id,
			}
			template = 'audit/audit_result.html'

		#Nothing yet, let's keep waiting
		else:
			params = {
				"url_post_audit": AUDIT_ASSISTANT_GET_ITEMS,
				"payload": json.dumps(payload)
			}
			template = 'audit/waiting.html'

		response_body = render_result(params, template)
		return Response(response=response_body, status=200)


def _convert_to_human_names(hotel, availability_items, language):

	all_rates = {datastore_utils.alphanumeric_to_id(x['key']): x for x in get_rates_of_hotel(hotel, language=language, include_removed=True)}
	all_boards = {datastore_utils.alphanumeric_to_id(x['key']): x for x in get_boards_of_hotel(hotel, language=language, include_removed=True)}
	all_rooms = {datastore_utils.alphanumeric_to_id(x['key']): x for x in get_rooms_of_hotel(hotel, language=language, include_removed=True)}

	for item in availability_items:

		room_name = ''
		if item.get('roomId', 0) != None:
			room_name = all_rooms.get(int(item.get('roomId', 0)), {}).get('name', '').lower()

		rate_name = ''
		if item.get('rateId', 0) != None:
			rate_name = all_rates.get(int(item.get('rateId', 0)), {}).get('localName', '').lower()

		board_name = ''
		if item.get('boardId', 0) != None:
			board_name = all_boards.get(int(item.get('boardId', 0)), {}).get('name', '').lower()

		# if isinstance(room_name, str):
		# 	room_name = room_name.decode('utf-8')
		#
		# if isinstance(rate_name, str):
		# 	rate_name = rate_name.decode('utf-8')
		#
		# if isinstance(board_name, str):
		# 	board_name = board_name.decode('utf-8')

		item['room_name'] = room_name
		item['rate_name'] = rate_name
		item['board_name'] = board_name