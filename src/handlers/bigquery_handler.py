from datetime import datetime
from google.cloud import bigquery
import logging
from google.api_core.exceptions import NotFound

class BigQueryHandler:
	def __init__(self, integration_name):
		self.proyecto_id = integration_name+"-adapter"
		self.nombre_dataset = 'availability_dataset'
		self.client = bigquery.Client(project=self.proyecto_id)

	def create_table_for_hotel(self, hotel_code):

		table_id = f"{self.proyecto_id}.{self.nombre_dataset}.{hotel_code}"
		schema = [
			bigquery.SchemaField("day", "DATE", mode="NULLABLE"),
			bigquery.SchemaField("roomId", "INT64", mode="NULLABLE"),
			bigquery.Schema<PERSON>ield("boardId", "INT64", mode="NULLABLE"),
			bigquery.SchemaField("rateId", "INT64", mode="NULLABLE"),
			bigquery.SchemaField("capacity", "STRING", mode="NULLABLE"),
			bigquery.SchemaField("price", "FLOAT64", mode="NULLABLE"),
			bigquery.SchemaField("minimumStay", "INT64", mode="NULLABLE"),
			bigquery.SchemaField("maximumStay", "INT64", mode="NULLABLE"),
			bigquery.SchemaField("closedToArrival", "BOOL", mode="NULLABLE"),
			bigquery.SchemaField("closedToDeparture", "BOOL", mode="NULLABLE"),
			bigquery.SchemaField("quantity", "INT64", mode="NULLABLE"),
			bigquery.SchemaField("status", "STRING", mode="NULLABLE"),
			bigquery.SchemaField("request_id", "STRING", mode="NULLABLE"),
			bigquery.SchemaField("operation_id", "INT64", mode="NULLABLE"),
			bigquery.SchemaField("release", "INT64", mode="NULLABLE"),
			bigquery.SchemaField("inserted_at", "TIMESTAMP", mode="NULLABLE")
		]

		table = bigquery.Table(table_id, schema=schema)
		table.time_partitioning = bigquery.TimePartitioning(type_=bigquery.TimePartitioningType.DAY, field="day")
		table.clustering_fields = ["roomId", "rateId", "boardId", "capacity"]

		try:
			self.client.get_table(table_id)
			logging.info(f'Table {table.table_id} already exists.')
		except NotFound:
			table = self.client.create_table(table)
			logging.info(f'Table created successfully: {table.table_id}')

	def insert_data_into_table(self, hotel_code, availability_items):
		logging.info(f'Inserting data in table for project {self.proyecto_id}')
		table_id = f"{self.proyecto_id}.{self.nombre_dataset}.{hotel_code}"

		current_timestamp = datetime.utcnow().isoformat()
		for item in availability_items:
			item["inserted_at"] = current_timestamp

		errors = self.client.insert_rows_json(table_id, availability_items, timeout=20)
		if errors == []:
			logging.info("New rows have been added.")
		else:
			logging.info("Errors while inserting rows: %s", errors)
