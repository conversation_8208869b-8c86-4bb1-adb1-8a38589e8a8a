import cProfile
import json
import logging

from flask import request
from flask.views import MethodView

from handlers.audit.clean_audit import clean_old_audits
from handlers.redo_handler import _redo_operations_from_date
from paraty.utils.hotel_manager_utils import _do_reset_hotel_manager_cache_string_params
from paraty.utils.search_log_utils import add_log_entry


class QueueHandler(MethodView):
	def post(self):

		function_name = request.path.strip("/").split("/")[-1]

		logging.info("starting task: %s", function_name)
		logging.info("payload: %s", request.data)

		payload = request.get_data(as_text=True) or '[]'

		#Here we will have to include all methods to executed in a queue
		FUNCTION_TRANSLATOR = {
			'_redo_operations_from_date': _redo_operations_from_date,
			'_do_reset_hotel_manager_cache': _do_reset_hotel_manager_cache_string_params,
			'clean_old_audits': clean_old_audits,
			'add_log_entry': add_log_entry
		}

		params = json.loads(payload)


		if request.values.get('profile'):
			cProfile.runctx('_redo_operations_from_date(hotel_code, date)', {'_redo_operations_from_date': _redo_operations_from_date, 'hotel_code': params['hotel_code'], 'date': params['date']}, {}, sort='cumulative')
		else:
			FUNCTION_TRANSLATOR.get(function_name)(**params)

		return 'OK'

