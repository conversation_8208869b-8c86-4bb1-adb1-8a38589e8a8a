import json
import logging

from paraty.constants import BUGSEEKER_REDO_OPERATION_FROM_DATE_EXCEPTION, BUGSEEKER_WHILE_REDOING_OPERATION_INTEGRATION_ERROR
from paraty.constants.queue_names import LONG_REQUEST_QUEUE
from paraty.utils.queues.queues_utils import defer
from paraty_commons_3.bugseeker.bugseeker import send_notification_to_bug_seeker

#To make sure the path is correctly set (i.e. problems with request not found)
from datetime import timedelta

from flask import request
from flask.views import MethodView
from google.cloud import ndb

from handlers.bigquery_handler import BigQueryHandler
from paraty.constants.audit import YIELDPLANET
from paraty.utils.hotel_manager_utils import filter_availability_items_fields

from paraty import Config

import time

import interface_to_implement
from model.audit import EndpointCallAuditEvent
from paraty.integration import modify_prices_batch
from paraty.integration.integration_data_cache import IntegrationDataCache

from paraty.utils import date_utils, email_utils
from paraty.utils.email_utils import notifyExceptionByEmail
from paraty.utils.hotel_manager_utils import reset_hotel_manager_cache
from paraty_commons_3 import queue_utils
from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

LARGE_SERVER_MODULE = "largeserver"

class RedoHandler(MethodView):

	def get(self):
		if request.values.get('fromDate'):

			task_name = '%s_%s' % (request.values.get('hotel'), request.values.get('fromDate').replace(" ", "_").replace("-", "_").replace(":", "_"))

			# deferred.defer(_redo_operations_from_date, self.request.get('hotel'), self.request.get('fromDate'), _name=task_name, _queue="audit")

			hotel_code = request.values.get('hotel')
			from_date = request.values.get('fromDate')
			defer(_redo_operations_from_date, hotel_code, from_date, queue_name=LONG_REQUEST_QUEUE, task_name=task_name)

			return 'OK'

		else:
			raise Exception("Operation not supported")


def _update_limits(first_modified_date, last_modified_date, start_date, end_date):

	if not first_modified_date:
		first_modified_date = start_date

	if not last_modified_date:
		last_modified_date = end_date

	if start_date and (start_date < first_modified_date):
		first_modified_date = start_date

	if end_date and (end_date > last_modified_date):
		last_modified_date = end_date

	return first_modified_date, last_modified_date



class LastExecutedTask(ndb.Model):
	start_timestamp = ndb.DateTimeProperty(auto_now=True, indexed=False)
	from_timestamp = ndb.DateTimeProperty(indexed=False)


def _redo_operations_from_date(hotel_code: str, date: str):
	'''
	Reimplements all the operations for the given hotel, starting in the provided date
	'''
	try:

		# Note that we need the integration to be set before using it
		if not interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE:
			import main

		logging.info("Received call for hotel_code: %s, date: %s", hotel_code, date)

		from_date = date_utils.string_to_date(date, "%Y-%m-%d %H:%M:%S")

		operations = __get_operations_by_hotel(hotel_code, from_date)
		operations_array = list(reversed(operations))

		if len(operations_array) == 0:
			logging.info('No operations to redo')
			return True

		total_tasks = len(operations_array)

		logging.info('We are going to redo %s operations.' % total_tasks)

		hotel = get_hotel_by_application_id(hotel_code)

		logging.info("Hotel Name: %s", hotel.get('name'))

		#Let's use an intermediate structure to avoid having to access the datastore continually
		cache = IntegrationDataCache(hotel, operations[0].request_id)

		#Limits
		first_modified_date, last_modified_date = None, None

		#In case we need to break this down into multiple calls
		last_executed_operation_timestamp = None

		start_time = time.time()

		for i, op in enumerate(operations_array):
			# logging.info("Processing operation: %s", op.request)
			logging.info("Processing request number: %s/%s,  id %s ", i, total_tasks, op.request_id)
			try:
				start_date, end_date = _redo_operation_in_memory(op, cache, hotel_code)

				#Update the audit with dates
				requires_update = False
				if start_date and not op.start_date:
					requires_update = True
					op.start_date = date_utils.date_to_string(start_date)

				if end_date and not op.end_date:
					requires_update = True
					op.end_date = date_utils.date_to_string(end_date)

				if requires_update:
					op.put_async()

				first_modified_date, last_modified_date = _update_limits(first_modified_date, last_modified_date, start_date, end_date)

				current_time = time.time()


				if int(current_time - start_time) > Config.MAX_REQUEST_LIMIT:

					last_executed_operation_timestamp = date_utils.date_to_string(op.timestamp, "%Y-%m-%d %H:%M:%S")

					logging.info("Reached max Operations per request, breaking request into another call")

					# If in the same second we need to move forward (or we will create an infinite loop)
					# fmatheis, TODO, if there are more XML to execute in the same second we are going to lose them
					if last_executed_operation_timestamp == date:
						logging.info("New timestamp the same as current, so we will increase one second ")
						new_timestmap = op.timestamp + timedelta(seconds=1)
						last_executed_operation_timestamp = date_utils.date_to_string(new_timestmap, "%Y-%m-%d %H:%M:%S")

					break

			except:

				logging.warning("REQUEST WITH PROBLEM: %s", op.request)

				message = make_traceback()
				logging.exception(message)
				email_utils.notify_error_by_email('Problem while redoing operation in integration', message)
				send_notification_to_bug_seeker(BUGSEEKER_WHILE_REDOING_OPERATION_INTEGRATION_ERROR, message)

		#We use the request of the first one
		cache.save_data_to_datastore()

		#Make Sure that the cache of hotel-manager is cleaned (if something has changed)
		if first_modified_date and last_modified_date:
			reset_hotel_manager_cache(hotel, first_modified_date, last_modified_date)

		#Too much stuff to execute in one single call
		if last_executed_operation_timestamp:
			#Redo starting from the last request
			logging.info("Queueing new task to finish redoing from: %s", last_executed_operation_timestamp)

			task_name = '%s_%s' % (hotel_code, last_executed_operation_timestamp.replace("-", "_").replace(":", "").replace(" ", "_"))

			defer(_redo_operations_from_date, hotel_code, last_executed_operation_timestamp, queue_name=LONG_REQUEST_QUEUE, task_name=task_name)

		logging.info("Finished Redo!")

		return True

	except Exception as e:

		message = make_traceback()
		logging.exception(message)
		logging.exception("Exception in _redo_operations_from_date, %s, %s", hotel_code, date)
		email_utils.notify_error_by_email('Problem while redoing operation in integration', message)
		notifyExceptionByEmail("Exception while redoing", message)
		send_notification_to_bug_seeker(BUGSEEKER_REDO_OPERATION_FROM_DATE_EXCEPTION, message)
		return False



def _redo_operation_in_memory(operation, cache, hotel_code):
	'''
	Function in charge or extracting the required parameters and calling modify_prices_batch
	'''

	# queue_name = webapp2.get_request().headers.get('X-AppEngine-QueueName')

	availability_items = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_availability_items(operation, operation.request_id)

	# perform_bigquery_operations(hotel_code=hotel_code, availability_items=availability_items)

	# be careful with timezone. Let be generous and let process xml about yesterday changes
	yesterday = date_utils.date_to_string(date_utils.get_yesterday_date(), format="%Y-%m-%d")

	#No point on redoing things that are in the past (who cares of the past!)
	if not Config.TESTING:
		availability_items = [x for x in availability_items if x.get('day') >= yesterday]

	if availability_items:
		logging.info("Total availability_items: %s", len(availability_items))
	else:
		logging.info("Ignoring operation that didn't produce any valid availability items")
		return None, None

	filter_availability_items_fields(hotel_code, availability_items)

	start_date, end_date = _get_start_and_end_dates(availability_items)

	logging.info("_redo_operation_in_memory start_date %s end_date %s", start_date, end_date)

	modify_prices_batch.execute_modify_prices_batch(hotel_code, availability_items, start_date, end_date, operation.request_id, cache)

	return start_date, end_date


def _get_start_and_end_dates(availability_items):

	start_date = None
	end_date = None

	for item in availability_items:

		#Initialize this with the first item
		if not start_date:
			start_date = item['day']
			end_date = item['day']
			continue

		if item['day'] < start_date:
			start_date = item['day']

		elif end_date < item['day']:
			end_date = item['day']

	return date_utils.string_to_date(start_date, format=interface_to_implement.AVAILABILITY_ITEM_DATE_FORMAT), date_utils.string_to_date(end_date, format=interface_to_implement.AVAILABILITY_ITEM_DATE_FORMAT)

def perform_bigquery_operations(hotel_code, availability_items):
	"""
	Create a table in BigQuery for the given hotel code and insert availability items into it.

	Parameters:
	- hotel_code: The code of the hotel.
	- availability_items: The data to insert into the table.
	"""
	try:
		logging.info("Perform_bigquery_operations")
		integration_name = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()
		logging.info("Integration Name: %s", integration_name)
		#todo: Only yieldplanet allows data exporting to bigquery in this moment. Refactor this!!
		if integration_name != YIELDPLANET:
			return None
		bq_handler = BigQueryHandler(integration_name=integration_name)
		bq_handler.create_table_for_hotel(hotel_code)
		bq_handler.insert_data_into_table(hotel_code, availability_items)
	except Exception as e:
		logging.info(f"Error handling BigQuery operations: {e}")

def __get_operations_by_hotel(hotel_code: str, from_date: str):
	operations = []

	query = EndpointCallAuditEvent.query(
		EndpointCallAuditEvent.path == interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_modify_url(),
		EndpointCallAuditEvent.hotel_code == hotel_code,
		EndpointCallAuditEvent.timestamp >= from_date).order(-EndpointCallAuditEvent.timestamp)

	data, next_curs, more = query.fetch_page(400)
	logging.info('Querying: 400 operaciones primeras....')

	for op in data:
		operations.append(op)

	while more and next_curs:
		logging.info('Querying: 400 operaciones mas....')
		data, next_curs, more = query.fetch_page(400, start_cursor=next_curs)

		for op in data:
			operations.append(op)

	return operations