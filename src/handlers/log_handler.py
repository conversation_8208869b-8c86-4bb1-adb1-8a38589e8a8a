import datetime
import json
import logging

from paraty_commons_3.decorators.retry import retry
import time

from flask import Response, request, make_response
from flask.views import MethodView
from google.cloud import ndb

from paraty.config import Config
from google.cloud.logging_v2 import Client

import math
import pandas as pd
from paraty_commons_3.concurrency.concurrency_utils import execute_in_parallel

DELETE_LOGS_DAYS = 180


def get_date_from_timestamp(timestamp_local, time_zone='Europe/Madrid'):
	import pytz
	from datetime import datetime

	tz = pytz.timezone(time_zone)

	return datetime.fromtimestamp(timestamp_local, tz).strftime('%Y-%m-%dT%H:%M:%S%z')


def get_logs_in_period(start_time, end_time, log_level, module=None, status=None, path=None, hosts=['']):

	# last_log_time = time.time()
	# if end_time:
	# 	last_log_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').timestamp()
	#
	# start_time = last_log_time - float(hours) * 3600

	start_date = get_date_from_timestamp(start_time)
	end_date = get_date_from_timestamp(end_time)

	if Config.DEV:
		from paraty.base_adapter_constants import LOCAL_SERVICE_ACCOUNT
		scopes = [
			"https://www.googleapis.com/auth/logging.read",
		]
		from google.oauth2 import service_account
		credentials = service_account.Credentials.from_service_account_info(
			LOCAL_SERVICE_ACCOUNT, scopes=scopes)

		client_logging = Client(project=Config.PROJECT, credentials=credentials)
	else:
		client_logging = Client()

	FILTER = f'severity={log_level}' \
	         f' AND (timestamp>="{start_date}" AND timestamp<="{end_date}")'

	if module:
		FILTER += f' AND resource.labels.module_id="{module}"'

	if status:
		FILTER += f' AND httpRequest.status={status}'

	if hosts != ['']:
		FILTER += f" AND (protoPayload.host:{' OR '.join(hosts)})"

	if path:
		FILTER += f' AND protoPayload.resource:"{path}"'

	generator_logs = client_logging.list_entries(filter_=FILTER, page_size=500)
	# logging.info(f'Read log. Run time {(datetime.datetime.now() - start_time).total_seconds()}s')

	start_time = datetime.datetime.now()
	result_valid_log = pd.DataFrame(generator_logs)
	logging.info(f'Total number of records found {len(result_valid_log)} '
	             f'| {(datetime.datetime.now() - start_time).total_seconds()}')

	return result_valid_log


def get_only_valid_logs(module, status, path, hosts, all_logs):
	# Not used
	result = []

	# Note that all_logs at this point is an iterator, not a list (better for memory usage)
	for log in all_logs:
		# MODULE_ID
		try:
			if str(log.resource.labels.get('module_id')).lower() != str(module).lower():
				logging.info("Ignoring logs for a different module_id")
				continue
		except AttributeError:
			logging.info("Ignoring logs for a different MODULE_ID")
			continue

		# STATUS
		try:
			if int(log.httpRequest.status) != int(status):
				logging.info("Ignoring logs for a different status")
				continue
		except AttributeError:
			logging.info("Ignoring logs for a different STATUS")
			continue

		# PATH
		try:
			if path and path not in log.protoPayload.resource:
				logging.info("Ignoring logs for a different path")
				continue
		except AttributeError:
			logging.info("Ignoring logs for a different PATH")
			continue
		# HOST
		try:
			if hosts:
				found = False
				for host in hosts:
					if host in log.protoPayload.host:
						found = True
						break

				if not found:
					logging.info("Ignoring logs for a different host")
					continue

		except AttributeError:
			logging.info("Ignoring logs for a different HOST")
			continue

		result.append(log)

	return result


def build_logs_dictionary(valid_logs):

	def build_column_transform(each_row):
		data_payload = each_row['payload']

		if not isinstance(data_payload, dict):
			result_temp = {
				'start_time': each_row['timestamp'].strftime("%m/%d/%YT%H:%M:%S"),
				'payload': data_payload
			}

		else:
			result_temp = {
				'cost': data_payload.get('cost'),
				'host': data_payload.get('host'),
				'ip': data_payload.get('ip'),
				'latency': data_payload.get('latency'),
				'path': data_payload.get('resource'),
				# 'user_agent': log.user_agent,
				'status': data_payload.get('status'),
				'start_time': data_payload.get('startTime'),
				'payload': data_payload.get('line')
			}

		return result_temp

	if not valid_logs.empty:
		logging.info(f'Total logs found {len(valid_logs)}')
		valid_logs['transform_result'] = valid_logs.apply(lambda row: build_column_transform(row), axis=1)
		result = valid_logs['transform_result'].tolist()
	else:
		result = []

	return result


class LogBackup(ndb.Model):
	date = ndb.StringProperty()
	content = ndb.TextProperty(compressed=True)

	@staticmethod
	def get_log_backup_from_date(date_in):
		query_result = LogBackup.query(LogBackup.date == date_in)
		entities = query_result.fetch()

		return entities


class LogsHandler(MethodView):
	def get(self):
		# Valid hosts (i.e. To separate different hotels in the same application)

		# In case we want to read the logs of a previous day
		if request.args.get("read_date"):
			my_date = request.args.get("read_date")

			my_logs = LogBackup.get_log_backup_from_date(my_date)

			output = ''
			for log in my_logs:
				output += log
			return Response(response=output, status=200, headers={'Content-Type': 'application/json'})

		logging.info("Requested log saving")

		hours = request.args.get('hours', "24")
		end_time = request.args.get("end_time")
		# include_app_logs = request.args.get("details", "False")
		log_level_param = request.args.get("log_level", 'INFO')

		status = request.args.get("status")
		path = request.args.get("path")
		hosts = request.args.get("hosts", '').split(",")
		module = request.args.get("module", 'default')

		last_log_time = time.time()
		if end_time:
			last_log_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').timestamp()
		start_time = last_log_time - float(hours) * 3600

		# MAX_CONCURRENCY = 8
		NUM_CHUNK = 12
		time_sleep = 60

		all_info_chunks = []
		logging.info("Entering get_logs_in_period")
		for chunk in range(0, NUM_CHUNK):
			start_time_chunk = (start_time + (chunk * (float(hours) / NUM_CHUNK) * 3600))
			end_time_chunk = (start_time + (chunk + 1) * (float(hours) / NUM_CHUNK) * 3600)

			message = f'Chunk {chunk + 1}/{NUM_CHUNK} | ' \
			          f'{datetime.datetime.fromtimestamp(start_time_chunk).strftime("%Y-%m-%d %H:%M:%S")} ' \
			          f'- {datetime.datetime.fromtimestamp(end_time_chunk).strftime("%Y-%m-%d %H:%M:%S")}'
			logging.info(message)

			try:
				temp_result = get_logs_in_period(start_time_chunk, end_time_chunk, log_level_param, module, status,
				                                 path, hosts)
				all_info_chunks.append(temp_result)
			except:
				# Sleep 60s for reset quota
				logging.warning(f'Lost -> {message}')
				logging.info(f'Sleeping for {time_sleep} seconds - '
				             f'GOOGLE LIMITATION | QUOTA CLOUD LOGGING API!!')
				time.sleep(time_sleep)

		valid_logs = pd.DataFrame()
		for each_chunk_df in all_info_chunks:
			valid_logs = pd.concat([valid_logs, each_chunk_df], ignore_index=True)

		logs_dictionary = build_logs_dictionary(valid_logs)

		logging.info("Found logs parsed: %s", len(logs_dictionary))
		# logging.info("raw logs_dictionary: %s", logs_dictionary)

		save_logs = "save_logs" in request.path

		if save_logs:
			logging.info("Saving logs!!!")

			log_backup = LogBackup()
			log_backup.date = datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d')
			log_backup.content = json.dumps(logs_dictionary)
			log_backup.put()

			logging.info("Deleting old logs")
			expected_date = datetime.datetime.now() - datetime.timedelta(days=DELETE_LOGS_DAYS)
			iterador = LogBackup.query(LogBackup.date <= expected_date.strftime('%Y-%m-%d')).fetch(keys_only=True)
			ndb.delete_multi(iterador)

		return make_response('', 200)
