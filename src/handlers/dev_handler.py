


'''
Handler in charge of stuff related to development

i.e. Adding a new hotel

'''
import hashlib
import json
import logging
from lxml import etree

from flask import request, Response
from flask.views import MethodView
from google.cloud import ndb

import interface_to_implement
from model.audit import EndpointCallAuditEvent
from model.configuration_model import IntegrationUser
from model.price_model import BOARD_FOR_BASE_PRICES
from paraty.utils import date_utils
from paraty.utils.rendering import render_result
from paraty.utils.security.security_utils import encrypt_xml_sensitive_data
from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.decorators.cache import timebased_cache
from paraty_commons_3.decorators.cache.cache_controller import invalidate_cache
from paraty_commons_3.decorators.cache.timebased_persistent_cache import TimeBasedCacheEntry
from paraty_commons_3.hotel_manager import hotel_manager_utils
import requests


def get_audit_logs(hotel_code, offset, from_date, num_elements, legacy=None):

	#select * from EndpointCallAuditEvent
	#where hotel_code = 'ibersol-alay'
	#and operation = 'HotelAvailRateUpdate'
	#and request_timestamp > DATETIME('2017-05-04T00:00:00.00002-08:00')
	#ORDER BY request_timestamp DESC

	if legacy:
		logging.info("Using legacy query, hotel_code: %s, timestamp: %s", hotel_code, from_date)


		query = EndpointCallAuditEvent.query(EndpointCallAuditEvent.operation == 'HotelAvailRateUpdate',
											 EndpointCallAuditEvent.hotel_code == hotel_code,
											 EndpointCallAuditEvent.request_timestamp >= from_date).order(-EndpointCallAuditEvent.request_timestamp)


	else:
		query = EndpointCallAuditEvent.query(EndpointCallAuditEvent.path == interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_modify_url(),
											 EndpointCallAuditEvent.hotel_code == hotel_code,
											 EndpointCallAuditEvent.timestamp >= from_date).order(-EndpointCallAuditEvent.timestamp)

	result = query.fetch(num_elements, offset=offset)
	return result


def _show_mapping_for_humans():

	user = request.values.get("user")
	hotel_code = request.values.get("hotel_code")
	language = request.values.get("language")
	session = request.values.get("session")
	show_keys = request.values.get("show_keys", "")

	expected_session = hashlib.md5(("%s_@_%s" % (user, hotel_code)).encode('utf-8')).hexdigest()

	if not expected_session == session:
		return Response(response="Acceso no permitido", status=401)

	logging.info("show_mapping_for_humans, hotel_code: %s, user: %s", hotel_code, user)

	# We want fresh Data
	timebased_cache.clear_cache()
	mapping_info = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_mapping_info(hotel_code, language)

	if show_keys.upper() == "TRUE":
		mapping_info['show_keys'] = True

	response_body = render_result(mapping_info, "mapping/human_mapping.html")

	logging.info(response_body)

	return Response(response=response_body, status=200, headers={'Content-Type': 'text/html; charset=utf-8'})


class DevHandler(MethodView):
	
	def get(self):
		action = request.values.get("action", '').lower()
		name = request.values.get("name")
		password = request.values.get("password")
		hotels = request.values.get("hotels")

		if not action:
			options = '''
				Missing action, the options are:
				- newhotel
				- newuser
				- showaudit
				- getinfo
				- clearcache
				- rest
			'''
			return options

		if action == 'showmapping':
			return _show_mapping_for_humans()

		if action == "newhotel":
			my_user = IntegrationUser.query(IntegrationUser.name == name)
			hotels = my_user.hotels
			hotels.extend(hotels.split(","))
			my_user.hotels = hotels
			my_user.put()
			return 'OK'

		if action == "newuser":
			new_user = IntegrationUser()
			new_user.enabled = True
			new_user.name = name
			new_user.language = "SPANISH"
			new_user.password = hashlib.md5(password.encode('utf-8')).hexdigest()
			new_user.hotels = hotels.split(",")
			new_user.put()
			return 'OK'

		if action == 'showaudit':
			request_id = request.values.get("request_id")

			if not request_id or request_id.count('-') < 3:
				# This is used to handle reservations, that request_id is the identifier, and shouldn't be accesible
				return '', 401

			audit = EndpointCallAuditEvent.query(EndpointCallAuditEvent.request_id == request_id).get()

			if not audit:
				audit_id = request_id
				if not type(audit) == str and not "-" in audit_id:
					audit_id = int(audit_id)

				audit = EndpointCallAuditEvent.get_by_id(audit_id)

			my_request = ""

			if audit:
				my_request = audit.request

			try:
				my_request = encrypt_xml_sensitive_data(my_request)
			except:
				logging.error("Error encrypting XML data")
				logging.error(make_traceback())

			try:
				_ = etree.XML(my_request)
				content_type = "application/xml"
			except:
				try:
					_ = json.loads(my_request)
					content_type = "application/json"
				except:
					content_type = "text/plain"

			header_for_response = {'Content-Type': content_type}
			return Response(response=my_request, status=200, headers=header_for_response)


		if action == 'showdata':
			return self.show_data()

		if action == 'test_ip':
			url_to_call = request.values.get("url")
			response = requests.get(url_to_call)
			return Response(response=response.content, status=200, headers={'Content-Type': 'application/json'})

		if action == 'newaudit':
			self.new_audit()
			return 'ok'

		if action == 'clearcache':
			hotel_code = request.values.get('hotel')
			invalidate_cache(hotel_code)
			entries = [x for x in TimeBasedCacheEntry.query()]
			for entry in entries:
				entry.key.delete()

			my_response = "Removed entries: %s" % len(entries)
			my_response += "\n\n\nPlease kill all instances of the application to finish clearing cache"
			return my_response

		if action == 'getinfo':
			hotel_code = request.values.get('hotel')
			hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
			rooms = common_data_provider.get_rooms_of_hotel(hotel, 'SPANISH')
			boards = common_data_provider.get_boards_of_hotel(hotel, 'SPANISH')
			rates = common_data_provider.get_rates_of_hotel(hotel, 'SPANISH')

			my_response = "ROOMS\n"

			for room in rooms:
				room['id'] = alphanumeric_to_id(room['key'])
				my_response += json.dumps(room)
				my_response += "\n"

			my_response += "RATES\n"

			for rate in rates:
				rate['id'] = alphanumeric_to_id(rate['key'])
				my_response += json.dumps(rate)
				my_response += "\n"

			my_response += "BOARDS\n"

			for board in boards:
				board['id'] = alphanumeric_to_id(board['key'])
				my_response += json.dumps(board)
				my_response += "\n"

			return my_response

		if action == 'rest':
			logging.info("Requesting Audit Logs")

			hotel_code = request.values.get('hotel')
			offset = int(request.values.get('offset'))
			startDate = request.values.get('startDate')
			legacy = request.values.get('legacy')

			num_elements = int(request.values.get('numElements', "50"))

			result = get_audit_logs(hotel_code, offset, date_utils.string_to_date(startDate, "%Y-%m-%d"), num_elements, legacy)

			my_requests = [x.to_dict() for x in result]

			for current_request in my_requests:
				if current_request.get('timestamp'):
					current_request['timestamp'] = date_utils.datetime_to_string(current_request['timestamp'])
				if current_request.get('updated_timestamp'):
					current_request['updated_timestamp'] = date_utils.datetime_to_string(current_request['updated_timestamp'])
				if current_request.get('request_timestamp'):
					current_request['request_timestamp'] = date_utils.datetime_to_string(current_request['request_timestamp'])
				if current_request.get('ttl_timestamp'):
					current_request['ttl_timestamp'] = date_utils.datetime_to_string(current_request['ttl_timestamp'])

			logging.info("Found elements: %s" % len(my_requests))

			return Response(response=json.dumps(my_requests), status=200, headers={'Content-Type': 'application/json'})


	def post(self):
		params = json.loads(request.data.decode('utf8'))
		hotel_code = params.get('hotel_code')
		xml_files = params.get('xml_files')

		avail_items = []
		if hotel_code and xml_files:
			from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE
			availability_method = SPECIFIC_INTEGRATION_INTERFACE.get_availability_items
			for xml_name, xml in xml_files:
				try:
					operation = EndpointCallAuditEvent(request=xml, hotel_code=hotel_code, key=ndb.Key('EndpointCallAuditEvent', 2028383))
					avail_items.append((xml_name, availability_method(operation, "audit_assistant")))
				except AttributeError as e:
					logging.error("Error processing %s", xml_name)
					logging.error(make_traceback())

		return Response(response=json.dumps(avail_items), status=200, headers={'Content-Type': 'application/json'})


	def show_data(self):
		room_id = request.values.get("room_id")
		hotel_code = request.values.get('hotel_code')

		hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
		all_rooms = {alphanumeric_to_id(x['key']): x for x in common_data_provider.get_rooms_of_hotel(hotel)}
		all_boards = {x['key']: x for x in common_data_provider.get_boards_of_hotel(hotel)}
		all_rates = {x['key']: x for x in common_data_provider.get_rates_of_hotel(hotel)}

		room_key = all_rooms[int(room_id)]['key']
		date = request.values.get("date")
		rate_id = request.values.get("rate_id")
		board_id = request.values.get("board_id")


		rts = datastore_communicator.get_using_entity_and_params('RoomTypeStatus', search_params=[('date', '=', date), ('roomKey', '=', room_key)], hotel_code=hotel_code)
		# rts = rest_client.get(hotel['url'] + '/rest/', 'RoomTypeStatus', "?feq_date=%s&feq_roomKey=%s" % (date, room_key))
		if rts:
			rts = rts[0]

		fpd = datastore_communicator.get_using_entity_and_params('FinalPriceDay', search_params=[('date', '=', date[0:7]), ('roomKey', '=', room_key)], hotel_code=hotel_code)
		# fpd = rest_client.get(hotel['url'] + '/rest/', 'FinalPriceDay', "?feq_date=%s&feq_roomKey=%s" % (date[0:7], room_key))
		content = json.loads(fpd[0]['content']) if fpd else {}

		filtered_content = {}
		for k, v in content.items():
			rate, capacity, board = k.split("@@")

			if rate_id != 'None' and alphanumeric_to_id(rate) != int(rate_id):
				continue

			if board and board_id != 'None' and board_id != BOARD_FOR_BASE_PRICES and alphanumeric_to_id(board) != int(board_id):
				continue

			filtered_content[k] = v
		filtered_content_str = json.dumps(filtered_content)
		for k, v in all_boards.items():
			filtered_content_str = filtered_content_str.replace(k, v['name'])
			if rts:
				rts['closedRateBoard'] = rts.get('closedRateBoard', "").replace(k, v['name'])
		for k, v in all_rates.items():
			filtered_content_str = filtered_content_str.replace(k, v.get('localName', k))

			if rts:
				rts['closedRateBoard'] = rts.get('closedRateBoard', "").replace(k, v.get('localName', k))

		rts_dict = dict(rts)
		if rts_dict.get("key"):
			rts_dict.pop('key')

		return Response(response=json.dumps([rts_dict, json.loads(filtered_content_str)], indent=4, sort_keys=True, default=lambda o: '<not serializable>'), status=200, headers={'Content-Type': 'application/json'})