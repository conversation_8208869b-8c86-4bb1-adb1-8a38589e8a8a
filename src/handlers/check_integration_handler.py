import logging
import json

from flask.views import MethodView

from handlers.redo_handler import _redo_operations_from_date
from paraty.utils import date_utils
from paraty.utils.email_utils import sendEmail



__author__ = 'nmarin'

'''

============================
fmatheis: 2020-05-31
DEPRECATED: We suppose this no longer used and will remove it in a few months
============================

Same call that SearchByOperationHandler (based on it)

Compare the audits with real values in HOtel DB, and if they aren't the same, we redo the request.
After the redo, we check again, and if not again the same, we send urgents email to everybody

'''


class CheckIntegrationHandler(MethodView):


	def get(self):
		logging.info('REQUEST for CheckIntegration: %s', self.request)
		#Required params!
		date_price = self.request.get('priceDate')
		hotel = self.request.get('hotel')

		if not date_price or not hotel:
			return "date_price o and hotel ARE REQUIRED!!"

		redo_operation = False

		max_items = int(self.request.get('max', 0))
		if not max_items:
			max_items = 200

		get_params = {
			'priceDate': date_price,
			'hotel': hotel,
			'email': self.request.get('email')	,
			'max_items': max_items
		}

		format = self.request.get('format', 'json')
		human = self.request.get('human')

		is_async = self.request.get('async')

		if is_async:
			deferred.defer(check_integration, get_params, format, human, redo_operation, _queue="audit")
			return

		out = check_integration(get_params, format, human, redo_operation)

		if out == -1:
			self.response.status_int = 404
			self.response.out.write('Operations not found.')
			return

		if format == 'json':
			rv = json.dumps(out, indent=4)
			self.response.headers['Content-Type'] = 'application/json'
			self.response.out.write(rv)
			return

		if format == 'text':
			self.response.headers['Content-Type'] = 'text/html'
			self.response.out.write(out)
			return


# def _find_diffs(hotel_code, date_price, max_items):
# 	logging.info("Looking for all operations with hotel %s, date_price %s, max_items %s", hotel_code, date_price, max_items)
#
# 	hotel_object = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
# 	all_availability_items = search_operations_filter_by_limit_dates(date_price, hotel_code, hotel_object)
# 	if not all_availability_items:
# 		return -1, None
#
# 	if not all_availability_items:
# 		logging.info('No operations found!')
# 		return -1, None
#
#
# 	logging.info('Found Availability Items in Audit: %s', len(hotel_object))
#
# 	for ai in all_availability_items:
# 		logging.info('Operations XML Found: %s', ai)
#
# 	operations_sorted = common_utils.from_array_to_grouped_dict(all_availability_items, ['day', 'roomId', 'boardId', 'rateId'])
#
#
# 	min_date = date_utils.string_to_date(min(operations_sorted.keys())).date()
# 	max_date = date_utils.string_to_date(max(operations_sorted.keys())).date()
#
# 	view_prices_service = ViewPricesService(hotel_code, min_date, max_date)
# 	db_availability = view_prices_service.get_availability()
# 	hotel_inventory = view_prices_service.get_hotel_inventory()
# 	logging.info('Boards: %s' % hotel_inventory.get('boards'))
# 	for room_id, room_data in hotel_inventory.get('rooms').items():
# 		logging.info('Room %s: %s', room_id, room_data)
#
# 	logging.info('Rates: %s' % hotel_inventory.get('rates'))
#
# 	logging.info('Found Availability Items in Datastore: %s', len(db_availability))
# 	db_availability_sorted = common_utils.from_array_to_grouped_dict(db_availability, ['day', 'roomId', 'boardId', 'rateId', 'capacity'])
# 	logging.info('Operations DB: %s', db_availability_sorted)
#
# 	logging.info('Calculating quantity param, which applies for room (does not matther board or rate)')
# 	db_sorted_by_room = common_utils.from_array_to_grouped_dict(all_availability_items, ['day', 'roomId'])
# 	quantities_by_room = __calculate_quantities(db_sorted_by_room)
#
# 	diffs_found = __compare_operations_vs_datastore(db_availability_sorted, operations_sorted, hotel_inventory, quantities_by_room)
# 	return diffs_found, hotel_inventory


def check_integration(get_params, format='json', human=False, redo_operation=False):

	hotel_code = get_params["hotel"]
	date_price = get_params["priceDate"]
	max_items = get_params["max_items"]
	email = get_params["email"]

	diffs_found, hotel_inventory = _find_diffs(hotel_code, date_price, max_items)
	if diffs_found == -1:
		return diffs_found

	if redo_operation and diffs_found:
		deferred.defer(_redo_operations_from_date, hotel_code, diffs_found.get('first_error_date'), _queue="audit")

	if format == 'json':
		if email:
			sendEmail(email, 'Check Integration - %s - %s', json.dumps(diffs_found), '')

		return diffs_found
	else:
		text = __build_text_format(hotel_code, hotel_inventory, date_price, diffs_found)

		if email:
			sendEmail(email, 'Check Integration - %s - %s', '', text)

		return text


def __calculate_quantities(operations_by_room):

	quantities_by_room = {}

	for day, operations in list(operations_by_room.items()):
		for room_id, array_of_items in list(operations.items()):
			for a in array_of_items:
				if a.get('quantity'):
					quantities_by_room[room_id] = {'quantity':a.get('quantity'), 'request_id':a.get('request_id')}
					break

	return quantities_by_room


def __build_text_format(hotel_code, hotel_inventory, date_price, diffs_found):

	title = '<h2>Check Integration for hotel "%s" and date "%s"</h2>' % (hotel_code, date_price)
	html = '<html><head></head><body>%s %s</body></html>'

	if not diffs_found:
		body = 'No errors found! Everything seems to be ok!'
		return html % (title, body)


	body = '<ul>'
	for room_id, diffs_by_board in list(diffs_found.get(date_price).items()):
		room_name = hotel_inventory.get('rooms').get(room_id)['name']
		for board_id, diffs_by_rate in list(diffs_by_board.items()):
			board_name = hotel_inventory.get('boards').get(board_id)['name']
			for rate_id, diffs_by_category in list(diffs_by_rate.items()):
				rate_name = hotel_inventory.get('rates').get(rate_id)['name']
				for capacity, diffs in list(diffs_by_category.items()):
					for key, value in list(diffs.items()):
						body += '<li>Room: <i>%s</i> - Board: <i>%s</i> - Rate: <i>%s</i> - Capacity: <i>%s</i>' % (room_name, board_name, rate_name, capacity)
						if key != 'missing':
							body += '====> Invalid value for %s - Datatastore <b>%s</b> - XML request <b>%s</b>' % (key, value.get('datastore'), value.get('operation'))
							body += '     [REQUEST_ID: %s]' % value.get('request_id')
						else:
							body += '====> <b>%s</b>' % value

						body += '</li>'

	body += '</ul>'

	return html % (title, body)





def __merge_availability_items(array_of_items, quantity):
	if len(array_of_items) == 1:
		return array_of_items, None

	# Assertion: All these keys are global, applies for any capacity
	global_values = {
		'status': None,
		'minimumStay': None,
		'closedToArrival': None,
		'closedToDeparture': None
	}

	who_modifies_what = {}

	if quantity:
		global_values['quantity'] = quantity.get('quantity')
		who_modifies_what['quantity'] = quantity.get('request_id')

	for a in array_of_items:
		for global_key in list(global_values.keys()):
			if global_values.get(global_key) is not None:
				# We already have defined a value for this property, this new one is older, so no valid
				continue

			if a.get(global_key) is None:
				# There is nothing about this global property.
				continue

			global_values[global_key] = a.get(global_key)
			who_modifies_what[global_key] = a.get('request_id')

	items_by_capacity = {}
	for a in array_of_items:
		capacity = a.get('capacity')

		if not capacity:
			# If it arrives without capacity is because is defining global values and those has been handle previously.
			continue

		for k, v in list(global_values.items()):
			a[k] = v

		if capacity not in items_by_capacity:
			items_by_capacity[capacity] = a
			who_modifies_what[capacity] = {}
		else:
			# If more than one value for the same capacity arrives, we will fill only empty properties.
			for key, value in list(a.items()):
				if items_by_capacity[capacity].get(key) is None:
					items_by_capacity[capacity][key] = value
					who_modifies_what[capacity][key] = a.get('request_id')

	return list(items_by_capacity.values()), who_modifies_what


def __compare_availability_items(op_item, db_item, who_modifies_what):
	diffs = {}

	keys = ['day', 'roomId', 'boardId', 'rateId', 'capacity']

	logging.info('Who modifies what: %s', who_modifies_what)
	for k in keys:
		val = op_item.get(k)
		if val not in db_item:
			if k == 'capacity':
				request_which_modify_value = who_modifies_what.get(k, list(who_modifies_what.get(op_item.get('capacity')).values())[0])
			else:
				request_which_modify_value = who_modifies_what.get(k, who_modifies_what.get(op_item.get('capacity')).get(k))
			diffs['missing'] = 'Missing value in datastore for %s -> %s [request_id: %s]'  % (k, val, request_which_modify_value)
			logging.warning('Datastore availability item does not have key %s --> %s', k, val)
			return diffs

		db_item = db_item.get(val)

	keys_to_compare = ['price', 'minimumStay', 'maximumStay', 'closedToArrival', 'closedToDeparture', 'quantity', 'status']
	default_values = {
		'status':"open",
		'quantity':99
	}

	for k in keys_to_compare:
		value_db = db_item[0].get(k)
		if not value_db:
			value_db = default_values.get(k)

		value_op = op_item.get(k, default_values.get(k))
		if not value_op:
			value_op = default_values.get(k)

		if k == 'price':
			value_db = float(value_db)
			value_op = float(value_op)

		if value_db != value_op:

			request_which_modify_value = who_modifies_what.get(k, who_modifies_what.get(op_item.get('capacity')).get(k))
			diffs[k] = {
				'request_id': request_which_modify_value,
				'operation': value_op,
				'datastore': value_db
			}

	return diffs

def __is_item_in_inventory(item, type, inventory):
	values = inventory.get(type)
	logging.info('Is %s %s in %s..... ', type, item, list(values.keys()))
	if not item in list(values.keys()):
		return False

	db_item = values.get(item)
	logging.info('Yes! it is! --> %s', db_item)
	return True


def __compare_operations_vs_datastore(db_availability_sorted, operations_sorted, hotel_inventory, quantities_by_room):
	diffs_found = {}
	timestamps_error = None
	for day, values_per_day in list(operations_sorted.items()):
		for room_id, values_per_room in list(values_per_day.items()):
			if not __is_item_in_inventory(room_id, 'rooms', hotel_inventory):
				logging.info('Room key %s is not part of the current hotel inventory', room_id)
				continue

			for board_id, values_per_board in list(values_per_room.items()):
				if not __is_item_in_inventory(board_id, 'boards', hotel_inventory):
					logging.info('Board key %s is not part of the current hotel inventory', board_id)
					continue

				for rate_id, values_per_rate in list(values_per_board.items()):
					if not __is_item_in_inventory(rate_id, 'rates', hotel_inventory):
						logging.info('Rate key %s is not part of the current hotel inventory', rate_id)
						continue

					last_status_items, who_modifies_what = __merge_availability_items(values_per_rate, quantities_by_room.get(room_id))
					for av_item in last_status_items:
						diffs = __compare_availability_items(av_item, db_availability_sorted, who_modifies_what)

						if diffs:
							capacity = av_item['capacity']

							if not timestamps_error or av_item['timestamp'] < timestamps_error:
								timestamps_error = av_item['timestamp']

							diffs_found.setdefault(day, {})\
									   .setdefault(room_id, {})\
									   .setdefault(board_id, {})\
									   .setdefault(rate_id, {})\
									   .setdefault(capacity, diffs)

	if timestamps_error:
		diffs_found['first_error_date'] = date_utils.datetime_to_string(timestamps_error)

	return diffs_found