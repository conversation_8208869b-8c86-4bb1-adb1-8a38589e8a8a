import logging
from datetime import datetime
from cachetools import TTL<PERSON>ache
from flask import request
from handlers.base_handler import BaseIntegrationHandler
from handlers.redo_handler import _redo_operations_from_date
from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE
from paraty.utils.date_utils import datetime_to_string
from paraty.utils.queues.queues_utils import defer, get_tasks_in_queue
from paraty.utils.redis_integrations import set_entry_in_integration, get_entry_from_integration
from paraty.utils.xml_utils import XmlFinder

#The problem here is that if a task takes longer than 60 seconds the work can start to accumulate, so be careful
cache = None

def _set_prices_redis_cache(hotel_id: str, value: str, ttl: int):
	target_key = f'prices_post_{hotel_id}'
	set_entry_in_integration(target_key, value, ttl)


def _get_prices_redis_cache(hotel_id: str) -> str | None:
	target_key = f'prices_post_{hotel_id}'
	return get_entry_from_integration(target_key)


class ModifyPricesHandler(BaseIntegrationHandler):

	def _do_post(self, xml_finder: XmlFinder, request_id, request_body: str):

		global cache
		deferred_count_down = SPECIFIC_INTEGRATION_INTERFACE.get_time_between_tasks()

		# For safety reasons we reduce the cache ttl by 60 seconds
		# There seems to be the risk of issues when the execution coincides exactly with the ttl (i.e. https://app.clickup.com/t/865c8v00w  Checkin lanzarote)
		# We might a receive an xml exactly at the moment when the task is being executed
		memory_cache_ttl = max(10, int(deferred_count_down - 30))

		if not cache:
			cache = TTLCache(maxsize=200, ttl=memory_cache_ttl)

		hotel_id = SPECIFIC_INTEGRATION_INTERFACE.get_hotel_id(request, request_id)

		#Any issue is to be handled using exceptions
		SPECIFIC_INTEGRATION_INTERFACE.validate_update_request(hotel_id, xml_finder, request_body)

		timestamp = datetime.utcnow()

		extra_params = SPECIFIC_INTEGRATION_INTERFACE.get_response_extra_params(request_body)

		if SPECIFIC_INTEGRATION_INTERFACE.is_request_sync(request_body):
			response = self._write_response(extra_params, "ok_response.xml", request_id=request_id,
											request_body=request_body, audited_enabled=True, hotel=hotel_id,
											operation="ModifyPrices")
			_redo_operations_from_date(hotel_id, datetime_to_string(timestamp, "%Y-%m-%d %H:%M:%S"))
			return response

		task_already_created = cache.get(hotel_id) or _get_prices_redis_cache(hotel_id)

		# If a task is already programmed there is no need to create more
		if not task_already_created:

			# Setup a tasks to make sure than in 1 minutes we will process this hotel
			candidates = ['processor0', 'processor2', 'processor3', 'processor4', 'processor5', 'processor6']
			queue_index_to_use = len(hotel_id) % len(candidates)
			queue_to_use = candidates[queue_index_to_use]

			task_name = '%s_%s' % (hotel_id, datetime_to_string(timestamp, "%Y_%m_%d__%H%M%S"))

			#Note, that we don't create the task if the queue already contains a Waiting task for the same hotel

			# Iterate over all results
			related_tasks = 0
			try:
				queue_to_use_cloud_run = queue_to_use + '-cloud-run'
				for current_task in get_tasks_in_queue(queue_to_use_cloud_run):
					if hotel_id in current_task.name:
						related_tasks += 1

						#If it is not running yet for sure, we do not create a new one
						time_to_start_task = (datetime.fromtimestamp(current_task.schedule_time.timestamp()) - datetime.now()).total_seconds()
						if time_to_start_task > 1:
							related_tasks += 1

			except Exception as e:
				#we play it safe, if any problems we suppose there aren't any task in the queue and we add it
				logging.error(e)

			#We allow 2 tasks for the same hotel because one of them can be running
			if related_tasks < 2:
				deferred_count_down = SPECIFIC_INTEGRATION_INTERFACE.get_time_between_tasks()
				if extra_params and extra_params.get("count_down_deferred_prices"):
					deferred_count_down = int(extra_params.get("count_down_deferred_prices"))

				target_date = datetime_to_string(timestamp, "%Y-%m-%d %H:%M:%S")
				defer(_redo_operations_from_date, hotel_id, target_date, queue_name=queue_to_use, countdown=deferred_count_down, task_name=task_name)

				# Prevent calls from creating new tasks while this one hasn't been executed
				cache[hotel_id] = True
				_set_prices_redis_cache(hotel_id, "true", memory_cache_ttl)

			else:
				logging.info(f"Not creating task for {hotel_id} because there are already %s tasks created for the hotel", related_tasks)
		else:
			logging.info(f"Not creating task for {hotel_id} because there is already a task created for the hotel")

		return self._write_response(extra_params, "ok_response.xml", request_id=request_id, request_body=request_body, audited_enabled=True, hotel=hotel_id, operation="ModifyPrices")