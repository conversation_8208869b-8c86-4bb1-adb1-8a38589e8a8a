from model.audit import EndpointCallAuditEvent
from tests_adapter.test_base import DingusAdapterBaseTest
from dingus.dingus_integration import DingusIntegration


class TestAvailabilityItems(DingusAdapterBaseTest):
    def test_check_default_availability_item(self):
        operation_simulation = EndpointCallAuditEvent()
        request_id = 'unit-test-request-id'

        request_data = self.open_test_data_file('test_update_base_prices_by_room_price_request.xml')
        operation_simulation.request = request_data
        operation_simulation.hotel_code = 'dingus1'

        results = DingusIntegration().get_availability_items(operation_simulation, request_id)
        self.assertEqual(320, len(results))


    def test_get_availability_items(self):
        xml_files = []
        xml_content = self.open_test_data_file('test_hotel_availvility_rate_update.xml')
        xml_files.append(('demo.xml', xml_content))

        params = {
            'hotel_code': 'bg-java',
            'xml_files': xml_files
        }

        response = self.testapp.post('/get_availability_items', json=params)
        self.assertEqual(response.status_code, 200)

        json_loaded = response.json
        print(response.text)