from unittest.mock import patch, MagicMock

from tests_adapter.test_base import DingusAdapterBaseTest
from dingus.dingus_constants import UPDATE_AVAIL_AND_RATE_OP, ADAPTER_URL_BASE


class TestUpdateAvailRate(DingusAdapterBaseTest):
	@patch('handlers.prices_handler.cache', new_callable=lambda: None)
	@patch('handlers.prices_handler.defer', return_value=True)
	def test_change_price(self, *args):
		request_data = self.open_test_data_file('test_update_request.xml')
		target_url = ADAPTER_URL_BASE % UPDATE_AVAIL_AND_RATE_OP
		response = self.testapp.post(target_url, data=request_data)

		self.assertEqual(response.status_code, 200)
		self.assertEqual(1, len(args[0].mock_calls))
		self.assertIn('Success', response.data.decode())


	@patch('handlers.prices_handler.cache', new_callable=lambda: None)
	@patch('handlers.prices_handler.defer', return_value=True)
	def test_unmocked_price_integration(self, *args):
		request_data = self.open_test_data_file('test_update_base_prices_by_room_price_request.xml')
		target_url = ADAPTER_URL_BASE % UPDATE_AVAIL_AND_RATE_OP
		response = self.testapp.post(target_url, data=request_data)

		self.assertEqual(response.status_code, 200)
		self.assertEqual(1, len(args[0].mock_calls))
		self.assertIn('Success', response.data.decode())

	@patch('handlers.prices_handler.cache', new_callable=lambda: None)
	@patch('handlers.prices_handler.defer', MagicMock(side_effect=Exception('Boom!')))
	def test_datastore_unavailable_service(self, *args):
		request_data = self.open_test_data_file('test_update_base_prices_by_room_price_request.xml')
		target_url = ADAPTER_URL_BASE % UPDATE_AVAIL_AND_RATE_OP
		response = self.testapp.post(target_url, data=request_data)

		self.assertEqual(response.status_code, 400)
		self.assertIn('Errors', response.data.decode())
