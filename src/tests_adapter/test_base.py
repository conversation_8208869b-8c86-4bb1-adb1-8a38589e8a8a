import logging
import os

from paraty_commons_3.audit_utils import make_traceback

os.environ['UNIT_TEST_EXECUTION'] = "1"

import interface_to_implement
if not interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE:
    from dingus.dingus_integration import DingusIntegration
    interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE = DingusIntegration()
    import routes
    try:
        routes.build_routes()
    except:
        logging.info(make_traceback())

from tests.test_base import BaseTest

class DingusAdapterBaseTest(BaseTest):
    def open_test_data_file(self, filename):
        target_full_file_path = os.path.join(os.path.dirname(__file__), 'test_data', filename)
        with open(target_full_file_path, 'r') as f:
            return f.read()
