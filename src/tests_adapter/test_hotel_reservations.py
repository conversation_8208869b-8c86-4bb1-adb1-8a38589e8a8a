import json
from unittest.mock import patch
from xml.etree import ElementTree

from dingus.dingus_constants import ADAPTER_URL_BASE, BOOKING_RETRIEVAL_OP
from paraty_commons_3.tests.patch_utils.cache_patch import patch_cache
from tests_adapter.test_base import DingusAdapterBaseTest


class TestHotelReservation(DingusAdapterBaseTest):

	@patch('dingus.handlers.reservation_handler.get_reservations')
	@patch_cache
	def test_happy_flow_pull(self, *args):
		args[1].return_value = [self._build_demo_reservation()]

		request_data = self.open_test_data_file('test_booking_retrieval.xml')
		response = self.testapp.post(ADAPTER_URL_BASE % BOOKING_RETRIEVAL_OP, data=request_data)

		self.__validate_xml_ok(response)

		self.assertTrue('<Total AmountAfterTax="270.00"'.upper() in response.text.upper())


	def __validate_xml_ok(self, response):
		# Validate response metadata
		self.assertEqual(response.status_code, 200)
		self.assertEqual(response.content_type, 'application/xml')

		# Validate response xml
		xml = response.text
		root = ElementTree.fromstring(xml)

		self.assertTrue(root.tag == 'BookingRetrievalResponse')

		hotel_reservations = root.findall('.//HotelReservations')
		self.assertIsNotNone(len(hotel_reservations) == 1)

		reservations = hotel_reservations[0]
		self.assertTrue(len(reservations) > 0)
		for r in reservations:
			self.assertTrue(r.attrib['ResStatus'] in ['Reserved', 'Cancelled'])
			self.assertIsNotNone(r.attrib['HotelName'])
			self.assertIsNotNone(r.attrib['BookingDate'])
			self.assertIsNotNone(r.attrib['LastModifyDateTime'])
			self.assertIsNotNone(r.attrib['CheckOutDate'])
			self.assertIsNotNone(r.attrib['CheckinDate'])

			rooms = r.find('Rooms')
			self.assertIsNotNone(rooms)
			for room in rooms:
				guests_root = room.find('Guests')
				self.assertIsNotNone(guests_root)
				guests = guests_root
				self.assertIsNotNone(guests)
				for g in guests:
					self.assertIsNotNone(g.attrib['Title'])
					self.assertIsNotNone(g.attrib['GivenName'])
					self.assertIsNotNone(g.attrib['SurName'])
					self.assertIsNotNone(g.attrib['Age'])

				pricing_root = room.find('Pricing')
				self.assertIsNotNone(pricing_root)

				daily_rates_root = pricing_root.find('DailyRates')
				self.assertIsNotNone(daily_rates_root)
				daily_rates_items = daily_rates_root
				for d in daily_rates_items:
					self.assertEqual(d.tag, 'DailyRate')
					self.assertIsNotNone(d.attrib['EffectiveDate'])
					self.assertTrue(d[0].tag == 'Total')
					self.assertIsNotNone(float(d[0].attrib['AmountAfterTax']))

				total_prices_root = pricing_root.find('RoomTotals')
				self.assertIsNotNone(total_prices_root)
				total_price_items = total_prices_root
				self.assertTrue(len(total_price_items) == 1)
				self.assertEqual(total_price_items[0].tag, 'RoomTotal')
				self.assertIsNotNone(float(total_price_items[0].attrib['AmountAfterTax']))
				self.assertTrue(total_price_items[0].attrib['CurrencyCode'] == 'EUR')
				self.assertTrue(total_price_items[0].attrib['IsNetRate'] == 'false')
				self.assertTrue(total_price_items[0].attrib['IsGrossRate'] == 'true')
				self.assertTrue(total_price_items[0].attrib['Commission'] == '0')

			info = r.find('ReservationInfo')
			self.assertIsNotNone(info)
			total = info.find('Total')
			self.assertIsNotNone(total)
			self.assertIsNotNone(float(total.attrib['AmountAfterTax']))
			self.assertIsNotNone(total.attrib['IsNetRate'])
			self.assertIsNotNone(total.attrib['IsGrossRate'])
			self.assertIsNotNone(total.attrib['Commission'])
			self.assertEqual(total.attrib['CurrencyCode'], 'EUR')
			self.assertIsNotNone(float(total.attrib['AmountAfterTax']))
			res_id_root = info.find('ReservationIDs')
			self.assertIsNotNone(res_id_root)
			reservation_id = res_id_root
			self.assertIsNotNone(reservation_id)
			self.assertTrue(len(reservation_id) == 1)
			self.assertIsNotNone(reservation_id[0].attrib['Value'])
		return


	@staticmethod
	def _build_demo_reservation():
		return {
			'startDate': '2024-12-17',
			'endDate': '2024-12-20',
			'timestamp': '2024-12-20 00:00:00',
			'price': 270.0,
			'numRooms': 1,
			'rate': 'ahhwfmludGVncmF0aW9uLXRlc3QtaG90ZWxyEQsSBFJhdGUYgICAjvvB2gkMogEHZGluZ3VzMQ',
			'regimen': 'ahhwfmludGVncmF0aW9uLXRlc3QtaG90ZWxyFAsSB1JlZ2ltZW4YgICAgLyhggoMogEHZGluZ3VzMQ',
			'roomType1': 'ahhwfmludGVncmF0aW9uLXRlc3QtaG90ZWxyFQsSCFJvb21UeXBlGICAgIDtsYMKDKIBB2Rpbmd1czE',
			'adults1': 2,
			'extraInfo': json.dumps({
				"numfactu": "",
				"cc_brand": "1",
				"cc_type": "C",
				"payed": "637.39",
				"birthday": "",
				"personalId": "NA",
			 	"prices_per_day": {
					 "1: ahhwfmludGVncmF0aW9uLXRlc3QtaG90ZWxyFQsSCFJvb21UeXBlGICAgIDtsYMKDKIBB2Rpbmd1czE": {
						 "17/12/2024": ["134.82", "9.44", "125.38"],
						 "18/12/2024": ["141.24", "9.89", "131.35"],
						 "19/12/2024": ["141.24", "9.89", "131.35"],
						 "20/12/2024": ["109.14", "7.64", "101.50"],
						 "total": 620.94
					 }
				},
				"numflight": "",
			 	"pciTimestamp": "2019-07-08 19:24:28",
			 	"additional_services_keys": "ag5zfmJlc3QtaG90ZWxlc3IXCxIKU3VwcGxlbWVudBiAgICe182eCwyiARJiZXN0LW9hc2lzdHJvcGljYWw - name: Parking - cantidad: 1 - dias: 5 - precio: 50.0;"
			})
		}
