from dingus.dingus_constants import GET_HOTEL_INFO_OP, ADAPTER_URL_BASE
from tests_adapter.test_base import DingusAdapterBaseTest
from xml.etree import ElementTree


class TestHotelInfo(DingusAdapterBaseTest):

	def test_happy_flow(self):
		request_data = self.open_test_data_file('test_get_info_hotel.xml')
		response = self.testapp.post(ADAPTER_URL_BASE % GET_HOTEL_INFO_OP, data=request_data)
		response_xml = ElementTree.fromstring(response.text)

		all_rooms_xml = response_xml.findall('.//Room')
		self.assertGreater(len(all_rooms_xml), 0)

		for room in all_rooms_xml:
			occupation = room.find('./Occupations')
			self.assertIsNotNone(occupation)

			required_occupation_fields = [
				'MaxNumberOfPersons', 'MinNumberOfAdults', 'MinNumberOfPersons',
				'MaxNumberOfAdults', 'MinNumberOfChildren', 'MaxNumberOfChildren'
			]
			for field in required_occupation_fields:
				self.assertIsNotNone(occupation.find(f'./{field}'))

			rate_plans = room.findall('./RatePlans/RatePlan')
			self.assertGreater(len(rate_plans), 0)
			for rate_plan in rate_plans:
				self.assertIsNotNone(rate_plan.get('Code'))
				self.assertIsNotNone(rate_plan.get('CurrencyCode'))
				self.assertIsNotNone(rate_plan.get('Description'))
				self.assertIsNotNone(rate_plan.get('RateType'))

			meal_plans = room.findall('./MealPlans/MealPlan')
			self.assertGreater(len(meal_plans), 0)
			for meal_plan in meal_plans:
				self.assertIsNotNone(meal_plan.get('Code'))
				self.assertIsNotNone(meal_plan.get('Description'))

		self.assertEqual(response.status_code, 200)


	def test_happy_flow_master_user(self):
		request_data = self.open_test_data_file('test_hotel_info_master_user.xml')
		response = self.testapp.post(ADAPTER_URL_BASE % GET_HOTEL_INFO_OP, data=request_data)

		response_xml = ElementTree.fromstring(response.text)

		self.assertEqual(response_xml.tag, 'GetHotelInfoResponse')
		self.assertEqual(response.status_code, 200)
