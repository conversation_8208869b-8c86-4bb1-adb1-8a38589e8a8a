<GetHotelInfoResponse>
  <Hotel Code="dingus1" Name="TEST: Dingus 1">
    <Rooms>
      <Room Code="5644406560391168" Description="Habitaci&#243;n Triple" PriceType="Occupancy">
        <Occupations>
          <MinNumberOfPersons>1</MinNumberOfPersons>
          <MaxNumberOfPersons>3</MaxNumberOfPersons>
          <MinNumberOfAdults>3</MinNumberOfAdults>
          <MaxNumberOfAdults>3</MaxNumberOfAdults>
          <MinNumberOfChildren>0</MinNumberOfChildren>
          <MaxNumberOfChildren>0</MaxNumberOfChildren>
        </Occupations>
        <RatePlans>
          <RatePlan Code="5147289865682944" Description="NR" CurrencyCode="EUR" RateType="Gross"/>
          <RatePlan Code="5664902681198592" Description="PVP" CurrencyCode="EUR" RateType="Gross"/>
        </RatePlans>
        <MealPlans>
          <MealPlan Code="HB" Description="Media Pensi&#243;n"/>
          <MealPlan Code="BB" Description="Alojamiento y Desayuno"/>
          <MealPlan Code="RO" Description="Solo Alojamiento"/>
          <MealPlan Code="AI" Description="Todo Incluido"/>
          <MealPlan Code="FB" Description="Pensi&#243;n Completa"/>
        </MealPlans>
      </Room>
      <Room Code="5654313976201216" Description="Junior Suite" PriceType="Occupancy">
        <Occupations>
          <MinNumberOfPersons>1</MinNumberOfPersons>
          <MaxNumberOfPersons>4</MaxNumberOfPersons>
          <MinNumberOfAdults>2</MinNumberOfAdults>
          <MaxNumberOfAdults>4</MaxNumberOfAdults>
          <MinNumberOfChildren>0</MinNumberOfChildren>
          <MaxNumberOfChildren>2</MaxNumberOfChildren>
        </Occupations>
        <RatePlans>
          <RatePlan Code="5147289865682944" Description="NR" CurrencyCode="EUR" RateType="Gross"/>
          <RatePlan Code="5664902681198592" Description="PVP" CurrencyCode="EUR" RateType="Gross"/>
        </RatePlans>
        <MealPlans>
          <MealPlan Code="HB" Description="Media Pensi&#243;n"/>
          <MealPlan Code="BB" Description="Alojamiento y Desayuno"/>
          <MealPlan Code="RO" Description="Solo Alojamiento"/>
          <MealPlan Code="AI" Description="Todo Incluido"/>
          <MealPlan Code="FB" Description="Pensi&#243;n Completa"/>
        </MealPlans>
      </Room>
      <Room Code="5710239819104256" Description="Habitaci&#243;n Doble" PriceType="Occupancy">
        <Occupations>
          <MinNumberOfPersons>1</MinNumberOfPersons>
          <MaxNumberOfPersons>2</MaxNumberOfPersons>
          <MinNumberOfAdults>1</MinNumberOfAdults>
          <MaxNumberOfAdults>2</MaxNumberOfAdults>
          <MinNumberOfChildren>0</MinNumberOfChildren>
          <MaxNumberOfChildren>1</MaxNumberOfChildren>
        </Occupations>
        <RatePlans>
          <RatePlan Code="5147289865682944" Description="NR" CurrencyCode="EUR" RateType="Gross"/>
          <RatePlan Code="5664902681198592" Description="PVP" CurrencyCode="EUR" RateType="Gross"/>
        </RatePlans>
        <MealPlans>
          <MealPlan Code="HB" Description="Media Pensi&#243;n"/>
          <MealPlan Code="BB" Description="Alojamiento y Desayuno"/>
          <MealPlan Code="RO" Description="Solo Alojamiento"/>
          <MealPlan Code="AI" Description="Todo Incluido"/>
          <MealPlan Code="FB" Description="Pensi&#243;n Completa"/>
        </MealPlans>
      </Room>
    </Rooms>
  </Hotel>
</GetHotelInfoResponse>
