<BookingRetrievalResponse responseDate="2012-12-19T10:52:25.687">

            <HotelReservations>

                    <HotelReservation ResStatus="Reserved" HotelCode="dingus1" HotelName="TEST: Dingus 1" BookingDate="2016-11-03T11:43:11.000" LastModifyDateTime="2016-11-03T11:43:11.000" CheckinDate="2016-12-12" CheckOutDate="2016-12-13">
                        <Rooms>


                                    <Room CheckinDate="2016-12-12" CheckOutDate="2016-12-13" RoomCode="5644406560391168" RoomDescription="Habitación Triple"
                                          MealPlanCode="HB" MealPlanDescription="Media pensión incluida" RatePlanCode="5147289865682944" RatePlanDescription="No Reembolsable"
                                          NumberOfGuests="2" NumberOfAdults="2" NumberOfChildren="0" NumberOfBabies="0">
                                       <Guests>


                                                   <Guest Title="MR" GivenName="Test Paraty" SurName="Test Test" Age="30" />

                                                   <Guest Title="MR" GivenName="Adult2" SurName="Adult2" Age="30" />


                                       </Guests>
                                       <Pricing>
                                          <DailyRates>

                                                 <DailyRate EffectiveDate="2016-12-12">
                                                    <Total AmountAfterTax="220.00" CurrencyCode="EUR" />
                                                 </DailyRate>

                                          </DailyRates>
                                          <RoomTotals>
                                             <RoomTotal AmountAfterTax="220.00" CurrencyCode="EUR" IsNetRate="false" IsGrossRate="true" Commission="0" />
                                          </RoomTotals>
                                       </Pricing>
                                    </Room>


                        </Rooms>
                         <SpecialRequests>
                            <SpecialRequest>
                               <Text>TEST PARATY PUSH RESERVATION</Text>
                            </SpecialRequest>
                         </SpecialRequests>
                         <ReservationInfo>
                            <Total AmountAfterTax="220.00" IsNetRate="false" CurrencyCode="EUR" IsGrossRate="true" Commission="0" />
                            <Customer>
                               <LeadPax GivenName="Test Paraty" SurName="Test Test" />
                            </Customer>
                            <ReservationIDs>
                               <ReservationID Value="4B98888B" />
                            </ReservationIDs>
                         </ReservationInfo>
                        <AdditionalInfoList>
                            <AdditionalInfo Code="Phone number">123123</AdditionalInfo>
                            <AdditionalInfo Code="email"><EMAIL></AdditionalInfo>
                        </AdditionalInfoList>
                    </HotelReservation>

            </HotelReservations>

</BookingRetrievalResponse>