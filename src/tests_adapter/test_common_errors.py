import os
import time
from unittest.mock import patch

from dingus.dingus_constants import ADAPTER_URL_BASE, GET_HOTEL_INFO_OP
from tests_adapter.test_base import DingusAdapterBaseTest


class TestCommonErrors(DingusAdapterBaseTest):

	def _authentication_and_wrongparams_test_code(self, search, replace_by):
		my_test_body = self.open_test_data_file('test_get_info_hotel.xml')
		my_test_body = my_test_body.replace(search, replace_by)

		start = time.time()
		response = self.testapp.post('/dingus/GetHotelInfo', data=my_test_body)
		self.assertEqual(response.status_code, 400)
		self.assertTrue('<Errors>' in response.text)
		end = time.time()

		# Performance is reasonable?
		self.assertTrue((end - start) < 10)


	def test_wrong_login(self):
		self._authentication_and_wrongparams_test_code('<User>demo</User>', '<User>wrongUser</User>')

	def test_wrong_parameters(self):
		self._authentication_and_wrongparams_test_code('<HotelCode>dingus1</HotelCode>', '<HotelCode>dingus**</HotelCode>')

	@patch('dingus.handlers.hotel_info_handler.get_hotel_info', return_value="Example error")
	def test_internal_error(self, *args):
		data_file = self.open_test_data_file('test_get_info_hotel.xml')
		target_url = ADAPTER_URL_BASE % GET_HOTEL_INFO_OP

		start = time.time()
		response = self.testapp.post(target_url, data=data_file)
		self.assertEqual(response.status_code, 400)
		self.assertIn('<Errors>', response.text)

		end = time.time()

		# Performance is reasonable?
		self.assertTrue((end - start) < 10)
