from paraty.utils import date_utils
import logging


class AvailabilityItem(object):

	def __init__(self,
				day=None,
				room_id=None,
				board_id=None,
				rate_id=None,
				price=None,
				capacity=None,
				minimum_stay=None,
				maximum_stay=None,
				closed_to_arrival=None,
				closed_to_departure=None,
				quantity=None,
				quantity_rate_board=None,
				status=None,
				operation_id=None,
				request_id=None,
				release=None):

		self.minimum_stay = minimum_stay
		self.day = day
		self.room_id = room_id
		self.board_id = board_id
		self.rate_id = rate_id
		self.price = price
		self.capacity = capacity
		self.maximum_stay = maximum_stay
		self.closed_to_arrival = closed_to_arrival
		self.closed_to_departure = closed_to_departure
		self.quantity = quantity
		self.quantity_rate_board = quantity_rate_board
		self.status = status
		self.operation_id = operation_id
		self.request_id = request_id
		self.release = release

		self.__validate()

	def to_json(self):
		return {
			'day': self.day,
			'roomId': self.room_id,
			'boardId': self.board_id,
			'rateId': self.rate_id,
			'capacity': self.capacity,
			'price': self.price,
			'minimumStay': self.minimum_stay,
			'maximumStay': self.maximum_stay,
			'closedToArrival': self.closed_to_arrival,
			'closedToDeparture': self.closed_to_departure,
			'quantity': self.quantity,
			'quantity_rate_board': self.quantity_rate_board,
			'status': self.status,
			'request_id': self.request_id,
			'operation_id': self.operation_id,
			'release': self.release,
		}

	def __validate_date(self, date_value):
		'''
		It validates dates with right format: %Y-%m-%d
		'''
		try:
			if date_value is not None:
				date_utils.string_to_date(date_value, '%Y-%m-%d')
		except Exception as e:
			raise Exception('Date format should be Y-m-d: %s. Exception: %s' % (date_value, e))


	def __validate_id(self, id_value):
		'''
		It validates a valid ID for datastore, so a long value
		'''
		try:
			if id_value is not None:
				return int(id_value)
		except Exception as e:
			raise Exception('Error in ID validation (%s): %s' % (id_value, e))

	def __validate_int(self, int_value):
		try:
			if int_value is not None:
				return int(int_value)
		except Exception as e:
			raise Exception('Error in INT validation (%s): %s' % (int_value, e))

	def __validate_int_allowing_empty_str(self, int_value):
		if int_value == "":
			return ""
		try:
			if int_value is not None:
				return int(int_value)
		except Exception as e:
			raise Exception('Error in INT validation (%s): %s' % (int_value, e))


	def __validate_bool(self, bool_value):
		try:
			if bool_value is not None:
				if isinstance(bool_value, bool):
					return bool_value
				elif isinstance(bool_value, str):
					return True
		except Exception as e:
			logging.error('Error in BOOLEAN validation (%s): %s' % (bool_value, e))
			return None
		return False

	def _str_to_bool(self, str_bool):
		if self.__validate_bool(str_bool):
			return str_bool.lower() == 'true'
		#IMPORTANT!!: never convert a None in 'False'
		return None


	def __validate(self):
		# Mandatory fields
		if not self.day:
			raise Exception('Missing mandatory field: day')

		if not self.room_id:
			raise Exception('Missing mandatory field: room_id')

		# Format and validate params
		self.__validate_date(self.day)
		self.room_id = self.__validate_id(self.room_id)
		self.board_id = self.__validate_id(self.board_id)
		self.rate_id = self.__validate_id(self.rate_id)
		self.quantity = self.__validate_int(self.quantity)
		self.minimum_stay = self.__validate_int_allowing_empty_str(self.minimum_stay)
		self.maximum_stay = self.__validate_int_allowing_empty_str(self.maximum_stay)
		self.closed_to_arrival = self._str_to_bool(self.closed_to_arrival)
		self.closed_to_departure = self._str_to_bool(self.closed_to_departure)
		self.release = self.__validate_int_allowing_empty_str(self.release)



def get_start_and_end_dates(availability_items):

	start_date = None
	end_date = None

	for item in availability_items:

		# Initialize this with the first item
		if not start_date:
			start_date = item['day']
			end_date = item['day']
			continue

		if item['day'] < start_date:
			start_date = item['day']

		elif end_date < item['day']:
			end_date = item['day']

	return start_date, end_date
