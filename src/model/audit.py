# -*- coding: utf-8 -*-

from google.cloud import ndb

def generate_ttl_timestamp():
	"""
	Generates a timestamp for the ttl of the audit to be 4 months from now
	:return:
	"""
	import datetime
	return datetime.datetime.now() + datetime.timedelta(days=4*30)

class EndpointCallAuditEvent(ndb.Model):
	timestamp = ndb.DateTimeProperty(auto_now_add=True)
	updated_timestamp = ndb.DateTimeProperty(auto_now=True, indexed=False)
	request_id = ndb.StringProperty()
	chunk_order = ndb.IntegerProperty()
	path = ndb.StringProperty()
	request = ndb.TextProperty(compressed=True)
	response = ndb.TextProperty()
	hotel_code = ndb.StringProperty()
	operation = ndb.StringProperty()
	start_date = ndb.StringProperty()
	end_date = ndb.StringProperty()
	ttl_timestamp = ndb.DateTimeProperty(default=generate_ttl_timestamp())

	#Legacy
	request_timestamp = ndb.DateTimeProperty()

	#Indicates if the audit call has been processed already or not
	processsed = ndb.BooleanProperty()

	@classmethod
	def query_by_request_id(cls, request_id):
		return cls.query(request_id=request_id)



class CommunicationAudit(ndb.Model):
	'''
	Used to audit internal communication and case it is needed
	i.e. This can be useful to audit when we push a reservation to the channel manager
	'''
	timestamp = ndb.DateTimeProperty(auto_now_add=True)
	request_id = ndb.StringProperty()
	path = ndb.StringProperty()
	request = ndb.TextProperty()
	response = ndb.TextProperty()
	hotel_code = ndb.StringProperty()
	operation = ndb.StringProperty()

class AuditResult(ndb.Model):

	creation_timestamp = ndb.DateTimeProperty(auto_now_add=True)
	updated_timestamp = ndb.DateTimeProperty(auto_now=True)

	request = ndb.StringProperty()
	response = ndb.JsonProperty(compressed=True)

	#Limit in current items
	min_date = ndb.DateProperty()
	max_date = ndb.DateProperty()

	hotel_code = ndb.StringProperty()

	#We need to split it as it might not fit in memory
	page = ndb.IntegerProperty()

	request_id = ndb.StringProperty()

	is_last = ndb.BooleanProperty(default=False)

	#For each availability item it indicates if it is correct or not
	validation = ndb.JsonProperty(compressed=True)







