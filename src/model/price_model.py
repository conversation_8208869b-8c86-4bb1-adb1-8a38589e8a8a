import json
import datetime

from paraty_commons_3.datastore import datastore_utils

HOTEL_MANAGER_PARENT_ROOM_CAPACITY = '0-0-0'

CAPACITY_FOR_MAX_STAY = 'MAX'

CAPACITY_FOR_CLOSED_DEPARTURE = 'NO_DEPARTURE'

CAPACITY_FOR_CLOSED_ARRIVAL = 'NO_ARRIVAL'

CAPACITY_FOR_RELEASE = 'RELEASE'

# Only for Vertical Booking
CAPACITY_JUNIOR = 'JUNIOR'
CAPACITY_KID = 'KID'
CAPACITY_BABY = 'BABY'
EXTRA_BED = 'EXTRA_BED'
ADDITIONAL_PRICE = 'ADDITIONAL_PRICE'

#Note that this makes YieldModifications unnecesary
CAPACITY_FOR_BASE_PRICES = 'BASE_PRICE'

#PLEASE KEEP UPDATED
SPECIAL_CAPACITIES_FPD = [ADDITIONAL_PRICE, EXTRA_BED, CAPACITY_BABY, CAPACITY_JUNIOR, CAPACITY_KID, HOTEL_MANAGER_PARENT_ROOM_CAPACITY, CAPACITY_FOR_MAX_STAY, CAPACITY_FOR_CLOSED_DEPARTURE, CAPACITY_FOR_CLOSED_ARRIVAL, CAPACITY_FOR_RELEASE, CAPACITY_FOR_BASE_PRICES]

#From RoomTypeStatus
CAPACITY_FOR_AVAILABILITY ='AVAILABILITY'
CAPACITY_FOR_RESERVATIONS = 'RESERVATIONS'
CAPACITY_FOR_OPEN_ID = 'OPEN'
CAPACITY_FOR_USING_EXTRA_AVAILABILITY = 'USING_EXTRA_AVAILABILITY'
CAPACITY_FOR_CLOSED_REGIMEN = 'CLOSED_REGIMEN'
CAPACITY_FOR_CLOSED_RATE = 'CLOSED_RATE'
CAPACITY_FOR_CLOSED_RATE_BOARD = 'CLOSED_RATE_BOARD' #Note that here we will use numeric ids


#Note that if we are using base prices, it doesn't make any sense to use boards
BOARD_FOR_BASE_PRICES = -1

BASE_PRICES_CONFIG_PROPERTY = 'Precio base en FinalPriceDay'
BASE_PRICES_XML_CONFIG_PROPERTY = 'Precio base en FinalPriceDay'
DISCOUNT_SUPLEMENT_CONFIG = "discount supplements"

BASE_BOARD_RESTRICTIONS_XML_CONFIG_PROPERTY = 'Base board restriction replicate'

DAYS_IN_CONTENT = 31

EMPTY_ITEM = {'item': []}

RATE_BOARD_SEPARATOR = '_@_'

class Entity:
	def to_dict(self):
		result = {}
		for k, v in list(self.__dict__.items()):
			if v:
				if isinstance(v, list):
					result[k] = {'item': v}
				elif isinstance(v, dict):
					result[k] = json.dumps(v)
				else:
					result[k] = v
		return result


# TODO: Base class for to_dict() method
class FinalPrice():
	'''
	Represents the information for a given Room, Rate, Board, Capacity combination
	'''
	def __init__(self, date, rateKey, boardKey, roomKey, capacity, price=None, minStay=None):
		self.date = date
		self.rateKey = rateKey
		self.boardKey = boardKey
		self.roomKey = roomKey
		self.capacity = capacity
		self.price = price
		self.minStay = minStay

	def __repr__(self):
		return str(self.__dict__)


#Note that the class needs to inherit from object in order to __new__ to be called
class FinalPriceDay(object):
	def __init__(self, dict_data={}, date=None, roomKey=None):

		if dict_data:
			self.key = dict_data.key

		self.date = date if date else dict_data.get('date')
		self.roomKey = roomKey if roomKey else dict_data.get('roomKey')
		self.content = json.loads(dict_data.get('content', '{}'))
		self.dirty = False

	def __repr__(self):
		return str(self.__dict__)

	def __eq__(self, other):
		if not isinstance(other, FinalPriceDay):
			return False

		return self.__dict__ == other.__dict__


	def to_datastore_api_entity(self):

		result = {}

		my_properties = {}
		my_properties['content'] = {
			'excludeFromIndexes': True,
			'stringValue': json.dumps(self.content)
		}
		my_properties['date'] = {'stringValue': self.date}
		my_properties['roomKey'] = {'stringValue': self.roomKey}

		result['properties'] = my_properties
		result['key'] = {'path': [{'kind': 'FinalPriceDay'}]}

		if self.key:
			result['key']['path'][0]['id'] = datastore_utils.alphanumeric_to_id(self.key)


		return result



	def to_dict(self):
		result = {}
		for k, v in list(self.__dict__.items()):
			if v:
				if isinstance(v, dict):
					result[k] = json.dumps(v)
				else:
					result[k] = v

		if "dirty" in result:
			result.pop('dirty')

		return result

class RoomTypeStatus():
	def __init__(self, dict_data={}):

		if dict_data:
			self.key = dict_data.key

		self.date = dict_data.get('date')
		self.roomKey = dict_data.get('roomKey')
		self.availability = dict_data.get('availability')
		self.open = dict_data.get('open')

		self.reservations = dict_data.get('reservations')
		self.usingExtraAvailability = dict_data.get('usingExtraAvailability')
		self.closedRegimen = dict_data.get('closedRegimen')
		self.closedRate = dict_data.get('closedRate')
		self.closedRegimen2 = dict_data.get('closedRegimen2')
		self.closedRate2 = dict_data.get('closedRate2')
		self.closedRateBoard = dict_data.get('closedRateBoard', "")
		if not self.closedRateBoard:
			self.closedRateBoard = ''


		self.closedToArrival = dict_data.get('closedToArrival')
		self.closedToDeparture = dict_data.get('closedToDeparture')
		self.availabilityRateBoard = dict_data.get('availabilityRateBoard', "")

		self.dirty = False

	def __repr__(self):
		return str(self.__dict__)

	def __eq__(self, other):
		if not isinstance(other, RoomTypeStatus):
			return False

		return self.__dict__ == other.__dict__

	def to_datastore_api_entity(self):

		result = {}

		my_properties = {}

		my_properties['date'] = {'stringValue': self.date}
		my_properties['roomKey'] = {'stringValue': self.roomKey}

		if self.availability is not None:
			my_properties['availability'] = {'integerValue': self.availability}

		if self.open is not None:
			my_properties['open'] = {'booleanValue': self.open}

		if self.reservations is not None:
			my_reservations = self.reservations
			if 'item' in self.reservations:
				if type(self.reservations.get('item', [])) is list:
					my_reservations = self.reservations['item']
				else:
					my_reservations = [self.reservations['item']]

			my_properties['reservations'] = {
				  "arrayValue": {
					"values": [{"stringValue": x} for x in my_reservations],
					}
				}

		if self.usingExtraAvailability is not None:
			my_properties['usingExtraAvailability'] = {'booleanValue': self.usingExtraAvailability}

		if self.closedToArrival is not None:
			my_properties['closedToArrival'] = {'booleanValue': self.closedToArrival}

		if self.closedToDeparture is not None:
			my_properties['closedToDeparture'] = {'booleanValue': self.closedToDeparture}

		#Note that closedRate and closedRegimen are deprecated (so we ignore them)
		if self.closedRegimen2 is not None:
			my_properties['closedRegimen2'] = {
					"excludeFromIndexes": True,
					"stringValue": self.closedRegimen2
			}

		if self.closedRate2 is not None:
			my_properties['closedRate2'] = {
					"excludeFromIndexes": True,
					"stringValue": self.closedRate2
			}

		if self.closedRateBoard is not None:
			my_properties['closedRateBoard'] = {
					"excludeFromIndexes": True,
					"stringValue": self.closedRateBoard
			}

		if self.availabilityRateBoard is not None:
			my_properties['availabilityRateBoard'] = {
				"excludeFromIndexes": True,
				"stringValue": self.availabilityRateBoard
			}

		result['properties'] = my_properties
		result['key'] = {'path': [{'kind': 'RoomTypeStatus'}]}

		if self.key:
			result['key']['path'][0]['id'] = datastore_utils.alphanumeric_to_id(self.key)


		return result


	def to_dict(self):
		result = {}
		for k, v in list(self.__dict__.items()):
			if v is not None and v != '':
				result[k] = v

		#This is something internal, we don't want it to be sent to the datastore+
		result.pop('dirty')
		return result


class IntegrationConfiguration(Entity):
	def __init__(self, dict_data={}):
		self.key = dict_data.get('key')
		self.name = dict_data.get('name')
		self.downloadBooking = dict_data.get('downloadBooking')
		self.retrievePrices = dict_data.get('retrievePrices')
		self.boardMap = dict_data.get('boardMap')
		self.rateMap = dict_data.get('rateMap')
		self.roomMap = dict_data.get('roomMap')
		self.promotionMap = dict_data.get('promotionMap')
		self.extraMap = dict_data.get('extraMap')
		self.configurations = dict_data.get('configurations', EMPTY_ITEM)['item']
		if not isinstance(self.configurations, list):
			self.configurations = [self.configurations]

def get_year_and_month(date):
	return '%d-%.2d' % (date.year, date.month)


def get_first_day_of_year_and_month(str_year_and_month):
	str_year, str_month = str_year_and_month.split('-')
	return datetime.date(int(str_year), int(str_month), 1)