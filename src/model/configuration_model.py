__author__ = 'fmatheis'

from google.cloud import ndb


class IntegrationUser(ndb.Model):
	'''
	Represents valid users in this integration
	'''
	name = ndb.StringProperty()
	password = ndb.StringProperty()

	#i.e. HotelApplication entity at hotel-manager, applicationId field. i.e. hotel-puentereal,  vita-santacruz
	hotels = ndb.StringProperty(repeated=True)

	enabled = ndb.BooleanProperty(default=False)

	#Remember we are a multinational - multilanguage company now
	language = ndb.StringProperty(default='ENGLISH')