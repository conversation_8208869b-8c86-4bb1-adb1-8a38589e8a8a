<html>
    <head>
        <title>Cargando... </title>
    </head>
    <body>
        <h3>Buscando datos...</h3>
        <div>Numero de intentos: <span id="retries">0</span></div>
        <div id="message">Esperando resultados</div>

        <div style="position:absolute;z-index:1000">
            <img itemprop="logo" src="/static/images/cargando.gif" style="margin:20px" alt="Cargando.." height="70px" title="Cargando..."/>
        </div>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.6.4/jquery.min.js" type="text/javascript"></script>
        <script>
            $(document).ready(function() {
                var retry_number = 0;
                function waiting_for_results() {
                    retry_number++;
                    $("#retries").text(retry_number);
                    $.ajax({
                        url: "{{ url_post_audit }}",
                        type: "GET",
                        dataType: "json",
                        data: JSON.parse('{{ payload|safe }}'),
                        success: function(data) {
                            if(data.error) {
                                console.log(data.error);
                                $("#message").text("Ha ocurrido un error al realizar el audit");
                                return false;
                            }

                            if(data.finished) {
                                console.log("Finished");
                                window.location.reload();
                            } else {
                                if (data.results && data.results !== "") {
                                    $("#message").text("Estamos recibiendo resultados. ¡Ya queda menos!");
                                }
                                console.log("Not Finished");
                                console.log(data);
                                setTimeout(waiting_for_results, 5000);
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            $("#message").text("Ha ocurrido un error al realizar el audit");
                        }
                    });
                }
                setTimeout(waiting_for_results, 5000);
            });
        </script>
    </body>
</html>