<head>

    <title>AUDIT Paso 2: ¿Qué estás buscando?</title>
    <link rel="icon" href="static/images/favicon.png">
    <link rel="stylesheet" type="text/css" href="static/css/common.css">
    <link rel="stylesheet" href="https://www.paratytech.com/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.css">
    <link rel="stylesheet" href="static/js/chosen_v1.7.0/chosen.css">
    <style>
        div {
        margin: 10px;
        }

        form { display: table; }
        div { display: table-row; }
        label { display: table-cell; }
        input { display: table-cell; margin:10px; }
        .ui-datepicker .ui-datepicker-title {display: block;}
    </style>

</head>

<body>
<center>

    <h3>Selecciona el filtro para esta auditoria: </h3>

    <form method="post" style="text-align: left;margin-left:20px">

        <div>
            <label for="priceday">Fecha Específica (o fecha desde, si se elige fecha hasta)</label>
            <!--<input type="date" id="priceday" name="priceday" data-placeholder="Todas"/>-->
            <input style="width:250px" name="priceday" type="text" class="form-control" placeholder="Opcional: Datos para fecha concreta" readonly/>
        </div>

          <div>
            <label for="priceday-to">Fecha Hasta</label>
            <!--<input type="date" id="priceday" name="priceday" data-placeholder="Todas"/>-->
            <input style="width:250px" name="priceday-to" type="text" class="form-control" placeholder="Opcional: Usar siempre con Fecha desde" readonly/>
        </div>

        <div>

            <label for="rates">Tarifas</label>
            <select class="chosen" name="rates" data-placeholder="Tarifas" multiple>
                <option value="all">Todas</option>
                {% for rate in rates %}
                <option value="{{rate.id}}">{{rate.name}}</option>
                {% endfor %}
            </select>
        </div>

        <div>
            <label>Habitación</label>
            <select class="chosen" name="rooms" data-placeholder="Habitaciones" multiple>
                <option value="all">Todas</option>
                {% for room in rooms %}
                <option value="{{room.id}}">{{room.name}}</option>
                {% endfor %}
            </select>

        </div>

        <div>
            <label>Régimen</label>
            <select class="chosen" name="boards" data-placeholder="Regímenes" multiple>
                <option value="all">Todas</option>
                {% for board in boards %}
                <option value="{{board.id}}">{{board.name}}</option>
                {% endfor %}
            </select>

        </div>

        <div>
            <label>Capacidades</label>
            <select class="chosen" name="capacities" data-placeholder="Capacidades" multiple>
                <option id="all">Todas</option>
                {% for capacity in capacities %}
                <option value="{{capacity}}">{{capacity}}</option>
                {% endfor %}
                <option value="BASE_PRICE">BASE_PRICE</option>

            </select>

        </div>

        <div>
            <label>Max Días (Cuántos días en el pasado quieres investigar?)</label>
            <input type="text" name="max_days" value="5"/>
        </div>
        <div>
            <label>Sólo lo último</label>
            <input type="checkbox" name="only_last"/>
        </div>

        <div>
            <label>Propiedades</label>
            &nbsp;&nbsp;&nbsp;Precio<input type="checkbox" id="price_property" name="price_property"  checked="True"/>
            &nbsp;Status<input type="checkbox" id="status_property" name="status_property"  checked="True"/>
            &nbsp;Disponibilidad<input type="checkbox" id="available_property" name="available_property"  checked="True"/>
            &nbsp;Min_stay<input type="checkbox" id="min_stay_property" name="min_stay_property"  checked="True"/>
            &nbsp;Max_stay<input type="checkbox" id="max_stay_property" name="max_stay_property"  checked="True"/>
            &nbsp;Release<input type="checkbox" id="release_property" name="release_property"  checked="True"/>
        </div>


        <p>
            <button class="submit-button" type="submit">Lanzar Audit</button>
        </p>
    </form>
</center>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.6.4/jquery.min.js" type="text/javascript"></script>
<script src="https://code.jquery.com/ui/1.10.4/jquery-ui.min.js"></script>
<script src="https://www.paratytech.com/static_1/js/datepicker/jquery.ui.datepicker-es.js?v=1.1"></script>
<script src="static/js/chosen_v1.7.0/chosen.jquery.js" type="text/javascript"></script>
<script>

    jQuery(window).load(function(){
        $('select').chosen();
        $("input[name=priceday]").datepicker({
            dateFormat: "yy-mm-dd",
        });

        $("input[name=priceday-to]").datepicker({
            dateFormat: "yy-mm-dd",
        });
    });

</script>
</body>