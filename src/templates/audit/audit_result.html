<html>
<body>


{% if pages %}
<h3>
Pages: {% for page in pages %}<a href="/audit3?audit_id={{audit_id}}&page={{page}}&hotel_code={{hotel_code}}">{{loop.index0}}</a> &nbsp;&nbsp;{% endfor %}
</h3>
{% endif %}

{% if not finished %}
    Not Finished, <a href="javascript:window.location.href=window.location.href">Reload page</a>
{% elif finished and not items %}
    Finished without results
{% endif %}

<table id="myTable" style="width:100%">

    <thead>
    <th style="color:white;background:#2261a8;padding:5px">Timestamp</th>
    {% if show_date %}
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px">Date</th>
    {% endif %}
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px">Room</th>
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px">Rate</th>
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px">Board</th>
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px">Ocuppancy</th>
    <th style="color:white;background:#2261a8;padding:5px">Price</th>
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px">Status</th>
    <th style="color:white;background:#2261a8;padding:5px">Disponibles</th>
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px;max-width: 32px;">Min Stay</th>
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px;max-width: 32px;">Max Stay</th>
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px;max-width: 32px;">Release</th>
    <th style="color:white;background:#2261a8;padding:5px">XML</th>
    <th scope="col" filter-type="ddl" style="color:white;background:#2261a8;padding:5px">DB</th>
    </thead>
    <tbody>

    {% for item in items %}
    <tr {% if not loop.index %2 == 0 %}{% else %}style="background-color: #EFEFEF;"{% endif %}>
        <td>
            {{ item.timestamp }}
        </td>
        {% if show_date %}
        <td>
            {{ item.day }}
        </td>
        {% endif %}
        <td>
            {{ item.room_name or ''}}
        </td>
        <td>
            {{ item.rate_name or ''}}
        </td>
        <td>
            {{ item.board_name or ''}}
        </td>
        <td>
            {{ item.capacity or ''}}
        </td>
        <td {% if item.ko is defined %}{% if 'price' in item.ko %} style="background:red" {% endif %}{% endif %}>
            {{ item.price or ''}}
        </td>
        <td {% if item.ko is defined %}{% if 'status' in item.ko %} style="background:red" {% endif %}{% endif %}>
            {{ item.status or ''}}
        </td>
        <td {% if item.ko is defined %}{% if 'quantity' in item.ko %} style="background:red" {% endif %}{% endif %}>
        {% if item.quantity is not none %}
            {{ item.quantity }}
        {% endif %}
        </td>
        <td {% if item.ko is defined %}{% if 'minimumStay' in item.ko %} style="background:red" {% endif %}{% endif %}>
            {{ item.minimumStay or ''}}
        </td>
        <td {% if item.ko is defined %}{% if 'maximumStay' in item.ko %} style="background:red" {% endif %}{% endif %}>
            {{ item.maximumStay or ''}}
        </td>
        <td {% if item.ko is defined %}{% if 'release' in item.ko %} style="background:red" {% endif %}{% endif %}>
            {{ item.release or ''}}
        </td>
        <td>
            <a href="/dev?action=showaudit&request_id={{ item.request_id }}" target="popup" onclick="window.open('/dev?action=showaudit&request_id={{ item.request_id }}','popup','width=600,height=600'); return false;">XML</a>
        </td>

        <td {% if item.ko is defined %}{% if item.ko %} style="background:red" {% endif %}{% endif %}>
            <a href="/dev?action=showdata&rate_id={{ item.rateId }}&board_id={{item.boardId}}&&room_id={{item.roomId}}&&date={{item.day}}&hotel_code={{ hotel_code }}" target="popup" onclick="window.open('/dev?action=showdata&rate_id={{ item.rateId }}&board_id={{item.boardId}}&&room_id={{item.roomId}}&&date={{item.day}}&hotel_code={{ hotel_code }}','popup','width=600,height=600'); return false;">
                {% if item.ko is defined %}
                    {% if item.ko and not item.ko == "" %}
                        KO
                    {% else %}
                        OK
                    {% endif %}
                {% else %}
                    ??
                {% endif %}
            </a>
        </td>

    </tr>
    {% endfor %}
    </tbody>
</table>
</body>
<head>
        <title>{{ title }} </title>

        <script src="https://price-seeker-v2.appspot.com/static/template/assets/plugins/jquery-1.10.2.min.js" type="text/javascript"></script>
        <script src="https://price-seeker-v2.appspot.com/static/lib/tablesorter/picnet.table.filter.min.js"></script>

    </head>

    <script>
        jQuery(document).ready(function () {

            var tableElem = $('#myTable');
            tableElem.tableFilter({enableCookies: false, selectOptionLabel:''});

        });

    </script>

</html>