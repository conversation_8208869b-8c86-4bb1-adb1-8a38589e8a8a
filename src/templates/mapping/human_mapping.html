<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title> {{name}} - Integration Mapping </title>
        <style>

            .category{
              float:left;
              margin:10px;
            }

        </style>
    </head>
    <body>
        <div class="category">
        <h2>Rates</h2>
            <table>
                <tbody>
                    <tr>
                        <th style="color:white;background:#2261a8;padding:5px">Name</th>
                        <th style="color:white;background:#2261a8;padding:5px">Code</th>
                        {% if show_keys %}
                        <th style="color:white;background:#2261a8;padding:5px">Key</th>
                        {% endif %}
                    </tr>
                    {% for rate in rates %}
                        <tr {% if not loop.index %2 == 0 %}{% else %}style="background-color: #EFEFEF;"{% endif %}>
                            <td style="padding:15px">
                                <b>{{rate.localName}}</b>
                            </td>
                            <td style="padding:15px">
                                {{rate.key}}
                            </td>
                            {% if show_keys %}
                            <td style="padding:15px">
                                {{rate.key_safe}}
                            </td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="category">
        <h2>Rooms</h2>
            <table>
                <tbody>
                    <tr>
                        <th style="color:white;background:#2261a8;padding:5px">Name</th>
                        <th style="color:white;background:#2261a8;padding:5px">Code</th>
                        {% if show_keys %}
                        <th style="color:white;background:#2261a8;padding:5px">Key</th>
                        {% endif %}
                    </tr>
                    {% for room in rooms %}
                        <tr {% if not loop.index %2 == 0 %}{% else %}style="background-color: #EFEFEF;"{% endif %}>
                            <td style="padding:15px">
                                <b>{{room.name}}</b>
                            </td>
                            <td style="padding:15px">
                                {{room.key}}
                            </td>
                            {% if show_keys %}
                            <td style="padding:15px">
                                {{room.key_safe}}
                            </td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="category">
        <h2>Boards</h2>
            <table>
                <tbody>
                    <tr>
                        <th style="color:white;background:#2261a8;padding:5px">Name</th>
                        <th style="color:white;background:#2261a8;padding:5px">Code</th>
                        <th style="color:white;background:#2261a8;padding:5px">Code Id</th>
                        {% if show_keys %}
                        <th style="color:white;background:#2261a8;padding:5px">Key</th>
                        {% endif %}
                    </tr>
                    {% for board in boards %}
                        <tr {% if not loop.index %2 == 0 %}{% else %}style="background-color: #EFEFEF;"{% endif %}>
                            <td style="padding:15px">
                                <b>{{board.name}}</b>
                            </td>
                            <td style="padding:15px">
                                {{board.key}}
                            </td>
                            <td style="padding:15px">
                                {{board.key_id}}
                            </td>
                            {% if show_keys %}
                            <td style="padding:15px">
                                {{board.key_safe}}
                            </td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="category">
        <h2>Promotions</h2>
            <table>
                <tbody>
                    <tr>
                        <th style="color:white;background:#2261a8;padding:5px">Name</th>
                        <th style="color:white;background:#2261a8;padding:5px">Code</th>
                        {% if show_keys %}
                        <th style="color:white;background:#2261a8;padding:5px">Key</th>
                        {% endif %}
                    </tr>
                    {% for promotion in promotions %}
                        <tr {% if not loop.index %2 == 0 %}{% else %}style="background-color: #EFEFEF;"{% endif %}>
                            <td style="padding:15px">
                                <b>{{promotion.name}}</b>
                            </td>
                            <td style="padding:15px">
                                {{promotion.key}}
                            </td>
                            {% if show_keys %}
                            <td style="padding:15px">
                                {{promotion.key_safe}}
                            </td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

            {% for remote_hotel_product in remote_product %}


                    <div class="category">
                    <h2>Rooms {{ remote_hotel_product.hotel_name }}</h2>
                        <table>
                            <tbody>
                                <tr>
                                    <th style="color:white;background:#2261a8;padding:5px">Name</th>
                                    <th style="color:white;background:#2261a8;padding:5px">Code</th>

                                </tr>
                                {% for room in remote_hotel_product.rooms %}
                                    <tr {% if not loop.index %2 == 0 %}{% else %}style="background-color: #EFEFEF;"{% endif %}>
                                        <td style="padding:15px">
                                            <b>{{room.name}}</b>
                                        </td>
                                        <td style="padding:15px">
                                            {{room.key}}&nbsp;&nbsp;
                                        </td>
                                        {% if room.key_safe %}
                                        <td style="padding:15px">
                                            {{room.key_safe}}&nbsp;&nbsp;
                                        </td>
                                        {% endif %}
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>



                    <div class="category">
                        <h2>Rates {{ remote_hotel_product.hotel_name }}</h2>
                            <table>
                                <tbody>
                                    <tr>
                                        <th style="color:white;background:#2261a8;padding:5px">Name</th>
                                        <th style="color:white;background:#2261a8;padding:5px">Code</th>
                                    </tr>
                                    {% for rate in remote_hotel_product.rates %}
                                        <tr {% if not loop.index %2 == 0 %}{% else %}style="background-color: #EFEFEF;"{% endif %}>
                                            <td style="padding:15px">
                                                <b>{{rate.localName}}</b>
                                            </td>
                                            <td style="padding:15px">
                                                {{rate.key}}&nbsp;&nbsp;
                                            </td>
                                            {% if rate.key_safe %}
                                            <td style="padding:15px">
                                                {{rate.key_safe}}&nbsp;&nbsp;
                                            </td>
                                            {% endif %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>





                    <div class="category">
                        <h2>Boards {{ remote_hotel_product.hotel_name }}</h2>
                            <table>
                                <tbody>
                                    <tr>
                                        <th style="color:white;background:#2261a8;padding:5px">Name</th>
                                        <th style="color:white;background:#2261a8;padding:5px">Code</th>
                                        <th style="color:white;background:#2261a8;padding:5px">Code Id</th>
                                    </tr>
                                    {% for board in remote_hotel_product.boards %}
                                        <tr {% if not loop.index %2 == 0 %}{% else %}style="background-color: #EFEFEF;"{% endif %}>
                                            <td style="padding:15px">
                                                <b>{{board.name}}</b>
                                            </td>
                                            <td style="padding:15px">
                                                {{board.key}}&nbsp;&nbsp;
                                            </td>
                                            <td style="padding:15px">
                                                {{board.key_id}}&nbsp;&nbsp;
                                            </td>
                                            {% if board.key_safe %}
                                            <td style="padding:15px">
                                                {{board.key_safe}}&nbsp;&nbsp;
                                            </td>
                                            {% endif %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                        </table>
                    </div>



         {% endfor %}
    </body>
</html>