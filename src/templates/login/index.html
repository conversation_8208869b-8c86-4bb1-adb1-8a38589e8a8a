<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>reset_login popup</title>
    <link rel="stylesheet" type="text/css" href="/static/css/login.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/libs/fontawesome5.css"/>

    <script type="text/javascript" src="/static/js/libs/jQuery.min.js"></script>
</head>
<body>

    <div class="modal_login">
        <div class="title">
            <h3>{{ T_RESET_PASS }}</h3>
        </div>
        <div class="subtitle">
            <p>{{ T_RESET_PASS_MSG }}</p>
        </div>
        <form action="/user/change_password" class="form" method="post" id="loginForm">
            <div class="input_wrapper">
                <label>{{ T_ACTUAL_PASS }}</label>
            <div class="input input_password">
                <input type="password" class="loginTextField" title="old" placeholder="{{ T_ACTUAL_PASS }}" name="old_password">
            </div>
            </div>
            <div class="input_wrapper">
                <label>{{ T_NEW_PASS }}</label>
            <div class="input input_password">
                <input type="password" class="loginTextField" title="new_pass" placeholder="{{ T_NEW_PASS }}" name="new_password_1">
            </div>
            </div>
            <div class="input_wrapper">
                <label>{{ T_REPEAT_NEW_PASS }}</label>
            <div class="input input_password">
                <input type="password" class="loginTextField" title="re_new_pass" placeholder="{{ T_REPEAT_NEW_PASS }}" name="new_password_2">
            </div>
            </div>
            <div class="btn_wrapper">
                <button class="form_submit btn btn_disable" type="submit" value="Submit" form="loginForm">{{ T_CHANGE_PASS }}</button>
            </div>
        </form>
        <div class="form_details">
            <p><b>{{ T_PASS_MATCH }}</b></p>
            <ul class="require_form">
                <li class="req_az">{{ T_PASS_MATCH_MAYUS }}</li>
                <li class="req_09">{{ T_PASS_MATCH_NUMBERS }}</li>
                <li class="req_8">{{ T_PASS_MATCH_LENGTH }}</li>
            </ul>
        </div>
    </div>

    <div class="modal_login login_ok" style="display: none;">
        <h1><i class="fas fa-check-circle"></i></h1>
        <div class="title">
            <h3>{{ T_PASS_CHANGED }}</h3>
        </div>
        <div class="subtitle">
            <p class="subtitle">{{ T_PASS_CHANGED_MSG|safe }}</p>
        </div>
    </div>
    
    <script type="text/javascript" src="/static/js/login.js"></script>
</body>

</html>