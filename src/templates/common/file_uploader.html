<!DOCTYPE html>
<html>
<head>
    <title>FilePond from CDN</title>
    <!-- Filepond stylesheet -->
    <link href="https://unpkg.com/filepond/dist/filepond.css" rel="stylesheet">
    <link href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css" rel="stylesheet">
    <link href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css" rel="stylesheet">
    <link href="/static/css/libs/doka.min.css" rel="stylesheet">
    <style>
        .filepond--item {width: calc(50% - .5em);}
        @media (min-width: 30em) {
            .filepond--item {width: calc(50% - .5em);}
        }
        @media (min-width: 50em) {
            .filepond--item {width: calc(33.33% - .5em);}
        }
        html, body, .filepond--root {
            height: 98%;
            margin:0;
        }
    </style>
</head>
<body onload="initFilePond()">
<input type="file" class="filepond" name="filepond"/>
<script src="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.js"></script>
<script src="https://unpkg.com/filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.js"></script>
<!-- include FilePond library -->
<script src="https://unpkg.com/filepond/dist/filepond.min.js"></script>
<script src="https://unpkg.com/filepond-plugin-file-validate-size/dist/filepond-plugin-file-validate-size.js"></script>
<script src="https://unpkg.com/filepond-plugin-image-resize/dist/filepond-plugin-image-resize.js"></script>
<!-- include FilePond plugins -->
<script src="https://unpkg.com/filepond-plugin-image-edit/dist/filepond-plugin-image-edit.js"></script>
<script src="https://unpkg.com/filepond-plugin-image-transform/dist/filepond-plugin-image-transform.js"></script>
<script src="/static/js/libs/doka.min.js"></script>
<script>

    window.onmessage = function(e){

        if (e.data.event_id == 'sign_up' || e.data.event_id == 'modify') {

            var control = document.querySelector('input[name="filepond"]');
            if (control) {
                if (control.value == "") {
                    window.parent.postMessage({event_id: e.data.event_id, data: 'upload_ko' }, '*');
                } else {
                    window.parent.postMessage({event_id: e.data.event_id, data: 'upload_ok' }, '*');
                }
            } else {
                window.parent.postMessage({event_id: e.data.event_id, data: 'upload_ko' }, '*');
            }

        }
    };

  function initFilePond() {
    // First register any plugins
    FilePond.registerPlugin(FilePondPluginImagePreview);
    FilePond.registerPlugin(FilePondPluginImageEdit);
    FilePond.registerPlugin(FilePondPluginFileValidateSize);
    FilePond.registerPlugin(FilePondPluginImageResize);
    FilePond.registerPlugin(FilePondPluginImageTransform);
    FilePond.registerPlugin(FilePondPluginFileValidateType);
    FilePond.setOptions({
      server: '{{post_url}}?{{query_string|safe}}&screen_id={{screen_id}}&hotel_code={{hotel_code}}'
    });
    FilePond.create(document.querySelector('input'), {
        maxFileSize: '{{max_size|safe}}',
        allowImageEdit: true,
        imageEditAllowEdit: {{allow_edit}},
        acceptedFileTypes: [{% for image in acceptedFileTypes %}'{{ image }}',{% endfor %}],
        {% if edit_picture %}
            maxFiles: 1,
            allowMultiple: false,
            instantUpload: true,
             // open editor on image drop
             {% if not remove_editor %}
            imageEditInstantEdit: true,
            {% else %}
            imageEditInstantEdit: false,
            {% endif %}
        {% else %}
            maxFiles: 20,
            allowMultiple: true,
            instantUpload: true,
            imageEditInstantEdit: false,
        {% endif %}
        imageTransformImageFilter: (file) => new Promise(resolve => {
            // no gif mimetype, do transform
            if (!/image\/gif/.test(file.type)) return resolve(true);
            const reader = new FileReader();
            reader.onload = () => {
                var arr = new Uint8Array(reader.result),
                i, len, length = arr.length, frames = 0;
                // make sure it's a gif (GIF8)
                if (arr[0] !== 0x47 || arr[1] !== 0x49 ||
                    arr[2] !== 0x46 || arr[3] !== 0x38) {
                    // it's not a gif, we can safely transform it
                    resolve(true);
                    return;
                }
                for (i=0, len = length - 9; i < len, frames < 2; ++i) {
                    if (arr[i] === 0x00 && arr[i+1] === 0x21 &&
                        arr[i+2] === 0xF9 && arr[i+3] === 0x04 &&
                        arr[i+8] === 0x00 &&
                        (arr[i+9] === 0x2C || arr[i+9] === 0x21)) {
                        frames++;
                    }
                }
                // if frame count > 1, it's animated, don't transform
                if (frames > 1) {
                    return resolve(false);
                }
                // do transform
                resolve(true);
            }
            reader.readAsArrayBuffer(file);

        }),
        {% if not remove_editor %}
        imageEditEditor: Doka.create({
            utils: ['crop', 'filter', 'color', 'markup', 'resize'],
            markupFontFamilyOptions: [
                ['Serif', 'Palatino, \'Times New Roman\', serif'],
                ['Sans Serif', 'Helvetica, Arial, Verdana'],
                ['Monospaced', 'Monaco, \'Lucida Console\', monospaced'],
                ['\'Open Sans\'', 'sans-serif']
             ],
            cropAspectRatioOptions: [
                {
                    label: 'Free',
                    value: null
                },
                {
                    label: 'Portrait',
                    value: 1.25
                },
                {
                    label: 'Square',
                    value: 1
                },
                {
                    label: 'Landscape',
                    value: .75
                }
            ]
        })
        {% endif %}
    });
  };
</script>
</body>
</html>