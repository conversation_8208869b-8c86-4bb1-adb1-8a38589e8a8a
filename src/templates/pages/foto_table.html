<div class="page">
    <div class="input_wrapper">
        <button href="#photo_upload" class="btn open_in_modal">{{ T_ADD_IMAGE }}</button>
        <button href="#delete_confirm" class="btn open_in_modal delete_rows" style="display:none;">{{ T_DELETE }}</button>
    </div>
    <div class="zebra_table_wrapper">
        <table class="zebra_table">
            <tr>
                <th class="sort_all"><i class="fas fa-sort"></i></th>
                <th>{{ T_STATUS }}</th>
                <th>{{ T_IMAGEN }}</th>
                <th class="sm-hide">{{ T_NAME }}</th>
                <th class="sm-hide">{{ T_DESCRIPTION }}</th>
                <th class="md-hide">{{ T_ALT_ATTR }}</th>
                <th class="sm-hide">{{ T_LANGUAGE }}</th>
                <th class="md-hide">{{ T_EXPIRE }}</th>
                <th class="md-hide">{{ T_LINK }}</th>
                <th>{{ T_OPTIONS }}</th>
            </tr>
            <tbody>
            {% for pic in foto_list %}{% if not pic.removed %}
            <tr id="{{ pic.key }}">
                <td class="sort"><i class="fas fa-grip-vertical"></i></td>
                <td><input type="checkbox" class="swicth" {% if pic.enabled %}checked="checked"{% endif %}></td>
                <td><div href='#photo_upload' class='pic open_in_modal'><img src="{{ pic.servingUrl }}=s60"></div></td>
                <td class="sm-hide"><div href='#photo_details' class="crop open_in_modal">{{ pic.title|safe }}</div></td>
                <td class="sm-hide"><div href='#photo_details' class="crop open_in_modal">{{ pic.description|safe }}</div></td>
                <td class="md-hide"><div class="editable"><input type='text' name='altText' value='{{ pic.altText|safe }}'></div></td>
                <td class="sm-hide">
                    {% if pic.language %}
                        <div class="list">
                        {% for lang in pic.language %}
                            <div class="item_list">{{ lang }}</div>
                        {% endfor %}
                        </div>
                    {% else %}
                        {{ T_ALL }}
                    {% endif %}
                </td>
                <td class="md-hide"><div class="crop">{{ pic.expire|safe }}</div></td>
                <td class="md-hide"><div href='#photo_link' class="crop open_in_modal">{{ pic.linkUrl|safe }}</div></td>
                <td>
                    <a href="#photo_configs" class="open_in_modal btn btn_small btn_link"><i class="fad fa-cogs"></i></a>
                    <a href="#photo_details" class="open_in_modal btn btn_small btn_link"><i class="fad fa-edit"></i></a>
                    <a href="#" class="btn btn_small btn_link"><i class="fad fa-trash-alt"></i></a>
                </td>
            </tr>
            {% endif %}{% endfor %}
            </tbody>
        </table>
        <i class="fad fa-recycle zebra_table_toggle" style="display: none"></i>
        <div class="zebra_table_restore">
            <table class="zebra_table restore_table">
                <tbody>
                <tr>
                    <th class="sort_all"><i class="fas fa-sort"></i></th>
                    <th>{{ T_STATUS }}</th>
                    <th>{{ T_IMAGEN }}</th>
                    <th class="sm-hide">{{ T_NAME }}</th>
                    <th class="sm-hide">{{ T_DESCRIPTION }}</th>
                    <th class="md-hide">{{ T_ALT_ATTR }}</th>
                    <th class="sm-hide">{{ T_LANGUAGE }}</th>
                    <th class="md-hide">{{ T_EXPIRE }}</th>
                    <th class="md-hide">{{ T_LINK }}</th>
                    <th>{{ T_OPTIONS }}</th>
                </tr>
                {% for pic in foto_list %}{% if pic.removed %}
                <tr id="{{ pic.key }}">
                    <td class="sort"><i class="fas fa-grip-vertical"></i></td>
                    <td><input type="checkbox" class="swicth" {% if pic.enabled %}checked="checked"{% endif %}></td>
                    <td><div href='#photo_upload' class='pic open_in_modal'><img src="{{ pic.servingUrl }}=s60"></div></td>
                    <td class="sm-hide"><div href='#photo_details' class="crop open_in_modal">{{ pic.title|safe }}</div></td>
                    <td class="sm-hide"><div href='#photo_details' class="crop open_in_modal">{{ pic.description|safe }}</div></td>
                    <td class="md-hide"><div class="editable"><input type='text' name='altText' value='{{ pic.altText|safe }}'></div></td>
                    <td class="sm-hide">
                        {% if pic.language %}
                            <div class="list">
                            {% for lang in pic.language %}
                                <div class="item_list">{{ lang }}</div>
                            {% endfor %}
                            </div>
                        {% else %}
                            {{ T_ALL }}
                        {% endif %}
                    </td>
                    <td class="md-hide"><div class="crop">{{ pic.expire|safe }}</div></td>
                    <td class="md-hide"><div href='#photo_link' class="crop open_in_modal">{{ pic.linkUrl|safe }}</div></td>
                    <td>
                        <a href="#" class="restore_data btn btn_small btn_link"><i class="fad fa-upload"></i></a>
                        <a href="#delete_modal" class="open_modal btn btn_small btn_link"><i class="fad fa-trash-alt"></i></a>
                    </td>
                </tr>
                {% endif %}{% endfor %}
                </tbody></table></div>
    </div>
</div>
<div style="display: none">
    <div id="photo_configs" data-title="{{ T_ADV_CONFIGS }}">
        <br>
        <div class="zebra_table_wrapper">
            <table class="zebra_table">
                <tr>
                    <th>{{ T_NAME }}</th>
                    <th>{{ T_VALUE }}</th>
                    <th><!-- Options --></th>
                </tr>
                <tr>
                    <td><div class="editable"><input type='text' name='altText' value='test_propertie'></div></td>
                    <td><div class="crop">Test Value</div></td>
                    <td>
                        <a href="#delete_modal" class="open_modal btn btn_small btn_link"><i class="fad fa-trash-alt"></i></a>
                    </td>
                </tr>
            </table>
            <br><br>
            <div class="input_wrapper input_inline">
                <button class="btn"> {{ T_ADD }} </button>
            </div>
        </div>
    </div>
    
    <div id="photo_link" data-title="{{ T_ADV_CONFIGS }}">
        <div class="input_wrapper">
            <div class="input select">
                <select>
                    <option value="None"></option>
                    <option value="#">section_name_1</option>
                    <option value="#">section_name_2</option>
                    <option value="#">section_name_3</option>
                    <option value="#">section_name_4</option>
                    <option value="#">section_name_5</option>
                    <option value="#">section_name_6</option>
                    <option value="#">section_name_7</option>
                </select>
            </div>
        </div>
        <div class="input_wrapper">
            <div class="input">
                <input type="text" placeholder="http...">
            </div>
        </div>
        <div class="input_wrapper input_inline">
            <button class="btn"> {{ T_SAVE }} </button>
        </div>
    </div>
    <div id="photo_upload" data-title="{{ T_UPLOAD_IMAGE }}">
        <iframe src="https://hotel-manager-2-dot-admin-hotel.appspot.com/utils/upload_pictures?sessionKey=396a0a06-d9a4-4690-9736-b9b9518a652d&amp;language=default&amp;mainKey=ag9zfnNlY3VyZS1vaHRlbHNyFwsSCldlYlNlY3Rpb24YgIDA9t3kyggMogEOb2h0ZWxzLW1hemFnb24&amp;pictureKey=ag9zfnNlY3VyZS1vaHRlbHNyFAsSB1BpY3R1cmUYgIDAtqS60gkMogEOb2h0ZWxzLW1hemFnb24" style="height: 70vh; width: 70vw; border: none;"></iframe>
    </div>
    <div id="photo_details" data-title="{{ T_EDIT }}">
        {{ text_editor|safe }}
    </div>
</div>