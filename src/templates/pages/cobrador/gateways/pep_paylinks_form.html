<div id="paymentFakeForm" class="paymentFakeForm" >

    <div class="wrapper_pep_paylinks_element msg_pep_links">
       {{ T_MSG_PAYMENT_LINKS }}
    </div>

    {% if percent_is_disable %}
        <input type="hidden" name="pep_paylinks_percent_fake" id="pep_paylinks_percent_fake" class="pep_fake_elemen" value="100">
    {% else %}
        <div class="wrapper_pep_paylinks_element">
            <label for="pep_paylinks_percent_fake">{{ T_PAYMENT_PERCENTAGE_AMOUNT }}:</label>
            <label for="pep_paylinks_nights_fake" hidden>{{ T_PAYMENT_NIGHTS_AMOUNT }}:</label>
            <label for="pep_paylinks_fixed_amount_fake" hidden>{{ fixed_amount_info }}:</label>
            <select name="pep_paylinks_percent_fake" id="pep_paylinks_percent_fake" class="pep_fake_elemen pep_paylinks_percent">
                 {% for percent, percent_info in percents.items() %}
                     {%  set percent_label = percent_info.label %}
                     {%  set percent_discount = percent_info.discount %}
                     {%  set selected = option_selected == percent or loop.first %}
                     <option name="paylinks_percent_fake" value="{{percent}}" class="percentage-card" {% if selected %}selected{% endif %}  {% if percent_discount %}data-discount="{{percent_discount}}"{% endif %}>{{percent_label}}</option>
                 {% endfor %}
                {% for nights,amount in nights.items() %}
                    <option name="paylinks_percent_fake" value="{{nights + "-n"}}" data-amount="{{ amount }}" class="night-card">{{nights + " " + T_payment_days_b_t}}</option>
                {% endfor %}
                {% if fixed_amount %}
                    <option name="paylinks_percent_fake" value="fixed_amount" data-amount="{{ fixed_amount }}" class="fixed-amount-card">{{ fixed_amount + " " + currency }}</option>
                {% endif %}
            </select>
        </div>
    {% endif %}
    <div class="wrapper_pep_paylinks_element">
        <input type="checkbox" name="unique_usage_fake" id="unique_usage_fake" class="pep_fake_elemen" checked>
        <label for="unique_usage_fake">{{ T_single_use_link }}</label>
    </div>
    <div class="wrapper_pep_paylinks_element">
        <textarea class="pep_fake_elemen pep_comments" name="pep_paylinks_comments_fake" rows="8" cols="40" placeholder="{{ T_PAYMENT_COMMENT_CUSTOMER_EMAIL }}"></textarea>
    </div>
</div>
<div class="pep_links_spinner_wrapper">
    <div id="pep_links_spinner" ></div>
</div>
<style>
        /* Estilos de la página */

        .wrapper_pep_paylinks_element {
            margin: 5px;
        }
        /* Estilos para el recuadro de comentarios */
        .pep_comments {
            width: 95%;
            padding: 10px;
            margin-bottom: 20px;
            border: 2px solid #c5c5c5;
            resize: vertical;
        }
        /* Estilos para el selector de porcentaje */
        .pep_paylinks_percent {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin: 10px 0;
            border: 2px solid #c5c5c5;
        }

        .percentage-card, .night-card {
            width: calc(33.33% - 20px);
            margin: 10px;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 5px;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .percentage-card:hover {
            background-color: #f0f0f0;
        }

        .pep_links_spinner_wrapper {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .pep_links_spinner_wrapper.peplinks_active_spinner::before {
            content:'';
            position: fixed;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                background: white;
                opacity: .5;
        }


        .peplinks_spinner{
              height: 60px;
              width: 60px;
              border: 5px solid rgba(0, 0, 0, .1);
              border-top-color: #FF5252;
              border-radius: 50px;
              animation: spin 1s ease infinite;
            }
            @keyframes spin{
              to{ transform: rotate(360deg); }
            }

         .msg_pep_links{
            color: #8E6800;
            font-weight: 600;
         }

    </style>


{% set items = percents.items()|list %}
{% set first_item = items|first %}
{% set first_percent = first_item[0] %}

<form id="paymentForm" action="{{ post_url }}" method="POST">
{#<form id="paymentForm" action="http://localhost:8080/forms/cobrador/send_new_paymentlink_external" method="POST">#}
    <input type="hidden" name="pep_paylinks_percent" id="pep_paylinks_percent" value="{% if first_percent %}{{ first_percent }}{% endif %}">
    <input type="hidden" name="pep_paylinks_comments" id="pep_paylinks_comments"></textarea>

    <input type="hidden" name="payment_order_id" value="{{ payment_order_id }}">
    <input type="hidden" name="amount" value="">
    <input type="hidden" name="sid" value="{{ sid }}">
    <input type="hidden" name="hotel_code" value="{{ hotel_code }}">
    <input type="hidden" name="unique_usage" id="unique_usage" value="on">
    <input type="hidden" name="user_name" id="user_name" value="">
</form>

<script>


    function apply_early_discount_pep_links(option_value){

         const url = new URL(window.location.href);
         url.searchParams.set('forceGateway', '{{ tpv_name }}');
         url.searchParams.set('forceGateway-with-discount', 'True');
         url.searchParams.set('option_selected', option_value);

         $(".pep_links_spinner_wrapper").css("display", "flex").addClass("peplinks_active_spinner");
         $("#pep_links_spinner").addClass("peplinks_spinner");

         window.location.href = url.toString();

    }

    function not_apply_early_discounts_pep_links(option_value) {
        const url = new URL(window.location.href);
        url.searchParams.delete('forceGateway-with-discount');
        url.searchParams.set('option_selected', option_value);

        $(".pep_links_spinner_wrapper").css("display", "flex").addClass("peplinks_active_spinner");
        $("#pep_links_spinner").addClass("peplinks_spinner");

        window.location.href = url.toString();
    }

    const select_fake_percents = document.querySelector('#pep_paylinks_percent_fake');
    const hiddenInput_fake_percents = document.querySelector('#pep_paylinks_percent');

    if (select_fake_percents && hiddenInput_fake_percents) {
        const selectedOption = select_fake_percents.options[select_fake_percents.selectedIndex];
        if (selectedOption && selectedOption.getAttribute('name') === 'paylinks_percent_fake') {
            hiddenInput_fake_percents.value = selectedOption.value;
        }
    }

    $("#destination_cobrador_form").append($("#paymentFakeForm"));

    $(".pep_fake_elemen").each(function () {
        $(this).on("change", function () {
            var id_hidden = $(this).attr("name").replace("_fake", "");
            var option_selected = $(this).find(":selected");
            if($(this).attr("type") === "checkbox"){
                if ($(this).is(":checked")){
                    $("#" + id_hidden).val("on");
                }else{
                    $("#" + id_hidden).val("");
                }
            }else{
                $("#" + id_hidden).val($(this).val());
                if(option_selected.hasClass("night-card") || option_selected.hasClass("fixed-amount-card")){
                    $("#paymentForm input[name='amount']").val(option_selected.attr("data-amount"));
                } else{
                    $("#paymentForm input[name='amount']").val('');
                }
            }

            if (option_selected.className === "night-card"){
                $(this).removeClass("pep_paylinks_percent");
                $(this).addClass("pep_paylinks_nights_fake");
                $(this).attr("id", "pep_paylinks_nights_fake");
                $(this).attr("name", "pep_paylinks_nights_fake");
            } else{
                $(this).addClass("pep_paylinks_percent");
                $(this).removeClass("pep_paylinks_nights_fake");
                $(this).attr("id", "pep_paylinks_percent_fake");
                $(this).attr("name", "pep_paylinks_percent_fake");
            }


        });

        if($(this).closest('select').length === 1 && $(this).closest('select').find('option').length === 1) {
            const selectElement = $(this).closest('select');
            const firstOptionValue = selectElement.find('option:first').val();
            selectElement.val(firstOptionValue).trigger("change");
        }
    });

    $("select.pep_fake_elemen").change(function (){
        var selectedOption = $('select[id=pep_paylinks_percent_fake] option:selected')[0];
        if (selectedOption.className === "night-card"){
            $("label[for=pep_paylinks_nights_fake]").show();
            $("label[for=pep_paylinks_percent_fake]").hide();
            $("label[for=pep_paylinks_fixed_amount_fake]").hide();
        }
        if (selectedOption.className === "percentage-card"){
            $("label[for=pep_paylinks_percent_fake]").show();
            $("label[for=pep_paylinks_nights_fake]").hide();
            $("label[for=pep_paylinks_fixed_amount_fake]").hide();
        }
        if (selectedOption.className === "fixed-amount-card"){
            $("label[for=pep_paylinks_fixed_amount_fake]").show();
            $("label[for=pep_paylinks_nights_fake]").hide();
            $("label[for=pep_paylinks_percent_fake]").hide();
        }

    });

     {% if early_discount_activated %}
          $("#pep_paylinks_percent_fake").change(function (){
            var option_selected = $(this).find(":selected");
            if (option_selected.data('discount') !== undefined) {
                const discount = option_selected.data('discount');
                apply_early_discount_pep_links($(this).val());
            } else {
                not_apply_early_discounts_pep_links($(this).val());
            }
         });
     {% endif %}



 tpv_cobrador_controller = function() {
      return {
        execute_controller: function() {

            $(".pep_links_spinner_wrapper").css("display", "flex").addClass("peplinks_active_spinner");
            $("#pep_links_spinner").addClass("peplinks_spinner");
            $("#user_name").val($("input[name=agentId]").length?$("input[name=agentId]").val():"")
           document.querySelector("#paymentForm").submit();
        }
      }
    }();
</script>