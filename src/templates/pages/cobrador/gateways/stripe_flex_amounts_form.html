{% if dev %}
<script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>
{% endif %}
<style>
    .worldline_payment_form_container{
      padding: 5px;
      max-width: 400px;
      margin: 0 auto 20px;
    }
    .worldline_payment_form_container .payment_option.selected-option{
    background-color:  #e8f2fa;
    border-color: #2196F3;
    z-index: 1;
    }
    .worldline_payment_form_container .payment_option .payment_radiobtn{
        appearance: none;
        position: relative;
    }
    .worldline_payment_form_container .payment_option .payment_radiobtn:before{
        content: '';
        width: 10px;
        height: 10px;
        border: 1px solid #333333;
        position: absolute;
        top: -11px;
        left: -8px;
        border-radius: 50%;
    }
    .worldline_payment_form_container .payment_option.selected-option .payment_radiobtn:after{
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: #333333;
        border-radius: 50%;
        top: -9px;
        left: -6px;
    }
    .worldline_payment_form_container .payment_option label{
        font-size: 14px;
        margin-left: 5px;
    }
    .worldline_payment_form_container .payment_option{
    padding: 10px 12px;
    position: relative;
    border: 1px solid gray;
    transition: background .3s;
    }
    .worldline_payment_form_container .payment_option:only-child{
    border-radius: 5px;
    }
    .worldline_payment_form_container .payment_option:first-child:not(:only-child){
    border-radius: 5px 5px 0 0;
    }
    .worldline_payment_form_container .payment_option:last-child{
    border-radius: 0 0 5px 5px;
    }
    .worldline_payment_form_container .payment_option:not(:first-child){
        margin-top: -1px;
    }
    .worldline_payment_form_container .payment_option .cards_images{
        display: inline-block;
        width: 90px;
        height: auto;
        position: absolute;
        right: 5px;
        top: 20px;
        text-align: right;
        transform: translateY(-50%);
    }
    .worldline_payment_form_container .payment_option .cards_images img{
        width: calc((100% / 3) - 14px);
        height: auto;
        object-fit: cover;
        margin-right: 5px;
    }
    .worldline_payment_form_container .payment_option#msi-payment:after{
        content: '\f133';
        position: absolute;
        right: 10px;
        font-family: 'Font Awesome 5 Pro';
        font-size: 18px;
        top: 10px;
        font-weight: 100;
    }
    .worldline_payment_form_container .payment_option .payment_option_hide_info{
    max-height: 0;
    overflow: hidden;
    transition: max-height .5s;
    }
    .worldline_payment_form_container .payment_option.selected-option .payment_option_hide_info{
    max-height: 300px;
    }
    .worldline_payment_form_container .payment_option .payment_option_description{
        font-weight: lighter;
        border-top: 1px solid rgba(0,0,0,0.2);
        padding-top: 10px;
        font-size: 12px;
        line-height: 22px;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper{
    position: relative;
    margin-top: 10px;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper .installments_quantity_label{
    position: absolute;
    top: -6px;
    left: 10px;
    font-size: 11px;
    padding: 0 5px;
    background: white;
    transition: background .3s;
    }
    .worldline_payment_form_container .payment_option.selected-option .msi_payment_select_wrapper .installments_quantity_label{
    background: #e8f2fa;
    }
    .worldline_payment_form_container .payment_option .installments_quantity{
    padding: 12px 10px 8px ;
    border-radius: 5px;
    background: transparent;
    font-size: 14px;
    appearance: none;
    width: 100%;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper:after{
    content: '\f107';
    position: absolute;
    right: 10px;
    font-family: "Font Awesome 5 Pro";
    font-size: 24px;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 100;
    }
    .worldline_payment_form_container .payment_option .installments_quantity div{
    margin: 5px;
    }
    .worldline_payment_form_container .payment_option .installments_quantity div input[type="radio"]{
    width: inherit;
    margin-right: 5px;
    height: inherit;
    }
</style>
{% if form_style == "v2"  %}
<link rel="stylesheet" href="{{ '/static/css/pages/cobrador/stripe_form_v2.css?v=1'|fixurl }}">
{% endif %}
<div class="worldline_payment_form_container">
    <div id="worldline_payment_form" >
        {% for amount in amounts %}
      <div class="payment_option {% if loop.index == 1 %}selected-option{% endif %}" id="card-payment">
          <div class="cards_images">
              <img src="{{'/static/images/credit-cards_mastercard.png'|fixurl}}">
              <img src="{{'/static/images/credit-cards_visa.png'|fixurl}}">
              <img src="{{'/static/images/credit-cards_amex.png'|fixurl}}">
          </div>
          <input class="payment_radiobtn" type="radio" id="radio{{loop.index}}" value="{{amount}}" {% if loop.index == 1 %}checked{% endif %}><label for="radio{{loop.index}}">{{ T_hide_msi_100|safe if T_hide_msi_100 and amount == 100 else T_payment_100_t + " " + amount|money_format + "%" }}</label>
          {% if T_payment_100 %}<div class="payment_option_hide_info"><p class="payment_option_description">{{ T_payment_100 }}</p></div>{% endif %}
        </div>
        {% endfor %}
        {% if add_google_pay %}
        <div class="payment_option" id="google-pay">
            <div class="cards_images">
                <img src="{{'/static/images/google-pay.png'|fixurl}}">
            </div>
            <input class="payment_radiobtn" type="radio" id="radiogoogle" value="100"> <label for="radiogoogle">{{ T_payment_google_t }}</label>
        </div>
        {% endif %}
        {% if add_apple_pay %}
        <div class="payment_option" id="apple-pay">
            <div class="cards_images">
                <img src="{{'/static/images/apple-pay.png'|fixurl}}">
            </div>
            <input class="payment_radiobtn" type="radio" id="radioapple" value="100"> <label for="radioapple">{{ T_payment_apple_t }}</label>
        </div>
        {% endif %}
        {% if add_amazon_pay %}
        <div class="payment_option" id="amazon-pay">
            <div class="cards_images">
                <img src="{{'/static/images/amazon-pay.png'|fixurl}}">
            </div>
            <input class="payment_radiobtn" type="radio" id="radioamazon" value="100"> <label for="radioamazon">{{ T_payment_amazon_t }}</label>
        </div>
        {% endif %}
         {% if msi_payment %}
            <div class="payment_option {% if not amounts %}selected-option{% endif %}" id="msi-payment">
              <div class="cards_images">
                  <img src="{{'/static/images/credit-cards_mastercard.png'|fixurl}}">
                  <img src="{{'/static/images/credit-cards_visa.png'|fixurl}}">
                  <img src="{{'/static/images/credit-cards_amex.png'|fixurl}}">
              </div>
              <input class="payment_radiobtn" type="radio" id="radiomsi" value="100"><label for="radiomsi">{% if custom_msi_option_description %}{{ custom_msi_option_description }} {% else %}{{ T_payment_msi_t }}{% endif %}</label>
              {% if T_payment_msi %}
              <div class="payment_option_hide_info">
                    <p class="payment_option_description">{{ T_payment_msi }}</p>
                    {% if installments %}
                        <div class="msi_payment_select_wrapper">
                            <select id="installments_quantity" name="installments_quantity" class="installments_quantity">
                                {% for installment in installments %}
                                <option name="installments" value="{{installment.amount}}@@{{installment.flex}}" {% if loop.first %}selected{% endif %}>{{T_payment_msi_option_a}}{{ installment.flex }} x {{ installment.amount }}{{ T_payment_msi_option_b }}</option>
                                {% endfor %}
                            </select>
                            <label for="installments_quantity" class="installments_quantity_label">{{ T_seleccione_una_opcion }}</label>
                        </div>
                    {% endif %}
                </div>
              {% endif %}
            </div>
             {% if tw_bw_installments %}
                 {% for installment in tw_bw_installments %}
                     <div class="payment_option {% if not amounts %}selected-option{% endif %}" id="msi-payment">
                        <div class="cards_images">
                            <img src="{{'/static/images/credit-cards_mastercard.png'|fixurl}}">
                            <img src="{{'/static/images/credit-cards_visa.png'|fixurl}}">
                            <img src="{{'/static/images/credit-cards_amex.png'|fixurl}}">
                        </div>
                        <input class="payment_radiobtn" type="radio" id="radiomsi" value="100">
                        <label for="radiomsi">
                            {{ installment.description }}
                        </label>
                        {% if T_payment_msi %}<div class="payment_option_hide_info"><p class="payment_option_description">{{ T_payment_msi }}</p></div>{% endif %}
                    </div>
                 {% endfor %}
             {% endif %}
         {% endif %}
    </div>
</div>
<form action="{{'/stripe/redirect'|fixurl}}" id="worldline_payment_form_hidden" method="post" style="display: none" autocomplete="off">
    <input type="hidden" name="payload" value="{{ payload }}">
    <input type="hidden" name="payment-amount" value="{{ amounts[0] }}">
    <input type="hidden" name="payment-method" value="card-payment">
</form>
<script>
  tpv_cobrador_controller = function() {
      return {
        execute_controller: function() {
            var payment_method = $("#worldline_payment_form .selected-option").attr("id");
            //if (payment_method == "msi-payment") {
            //    $('#worldline_payment_form_hidden input:hidden[name=payment-amount]').val("forcemsi@@" + $(".installments_quantity").val());
            //}
            $('#worldline_payment_form_hidden input:hidden[name=payment-method]').val($("#worldline_payment_form .selected-option").attr("id"));
          document.querySelector("#worldline_payment_form_hidden").submit();
        }
      }
    }();
  $(".payment_option").on("click", (e)=>{
        if(!$(e.currentTarget).hasClass("selected-option")){
            $(".payment_option.selected-option").find(".installments_quantity").fadeOut();
            $(".payment_option.selected-option").find('.payment_radiobtn').prop('checked', false);
            $(".payment_option.selected-option").removeClass("selected-option");
            $(e.currentTarget).addClass("selected-option");
            $('#worldline_payment_form_hidden input:hidden[name=payment-method]').val(e.currentTarget.id);
            $(e.currentTarget).addClass("selected-option").find('.payment_radiobtn').prop('checked', true);
            $(e.currentTarget).find(".installments_quantity").fadeIn();
            $('#worldline_payment_form_hidden input:hidden[name=payment-amount]').val($(e.currentTarget).find("input").val());
        }
  });
</script>
<script>
    $("#destination_cobrador_form").append($(".worldline_payment_form_container"));
</script>