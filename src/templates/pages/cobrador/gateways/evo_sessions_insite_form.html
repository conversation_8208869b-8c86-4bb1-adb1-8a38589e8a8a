{#<script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>#}
<script src="https://evopaymentsmexico.gateway.mastercard.com/form/version/81/merchant/{{ merchant }}/session.js?debug=true"></script>
<script src="{{'/static/js/libs/paraty_logger.js'|fixurl}}"></script>
<script id="payment-options-ctx" type="application/json">
    {{payment_methods|tojson}}
</script>
<link rel="stylesheet" href="{{'/static/css/libs/payment_widget.css'|fixurl}}">
{#{% include "pages/cobrador/gateways/paypal_pr/booking3_mock.html" %}#}
<style>
    .evo-form{
        margin: 5px;
    }

    .evo-form label {
        display: block;
        margin-bottom: 8px;
        color: #081d4f;
    }

    .evo-form input {
        width: calc(100% - 16px);
        padding: 8px;
        margin-bottom: 16px;
        box-sizing: border-box;
        border: 1px solid #081d4f;
        border-radius: 4px;
    }

    .evo-form input.error{
        border: 1px solid red;
    }

    .evo-form .payment-double-cols{
        margin-top: 4px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }

    .evo-form .payment-double-cols .payment-form-col{
        width: 50%;
    }

    .evo-form .error{
        color: red;
    }
    
    .evo-message{
        width: calc(100% - 56px);
        display: none;
        font-size: 1.2em;
        border-radius: 10px;
        padding: 8px 20px;
        margin-bottom: 20px;
        line-height: 1.1;
        background-color: #081d4f;
        color:#efefef;
    }
    .evo-message.error-message{
        background-color: #ff9e9e;
        color:#ff3636;
    }

    .pep_links_spinner_wrapper {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .pep_links_spinner_wrapper.peplinks_active_spinner::before {
        content:'';
        position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: white;
            opacity: .5;
    }


    .peplinks_spinner{
          height: 60px;
          width: 60px;
          border: 5px solid rgba(0, 0, 0, .1);
          border-top-color: #081d4f;
          border-radius: 50px;
          animation: spin 1s ease infinite;
        }
        @keyframes spin{
          to{ transform: rotate(360deg); }
        }
</style>
<div id="3DSUI"></div>
<div class="pep_links_spinner_wrapper">
    <div id="pep_links_spinner" ></div>
</div>
<div class="evo-form-container">
    <div class="payment-method-selector" id="payment-options">

    </div>
    <div id="evo-payment-form" class="evo-form">
        <div class="evo-message">
            Lorem ipsum dolor sit amet, consectetur adipisicing elit. Amet animi aperiam autem, eius facere ipsum maiores minima nam nesciunt numquam officiis optio, perspiciatis placeat praesentium quae reiciendis repellendus sequi veritatis.
        </div>
        <label for="cardholder-name">{{ T_holders_name }}</label>
        <input type="text" id="cardholder-name" required readonly>

        <label for="card-number">{{ T_numero_tarjeta }}</label>
        <input type="text" id="card-number" required readonly>

        <div class="payment-double-cols">
            <div class="payment-form-col">
                <label for="expiry-month">{{ T_MONTH }}</label>
                <input type="text" id="expiry-month" placeholder="MM" required readonly>
            </div>
            <div class="payment-form-col">
                <label for="expiry-year">{{ T_YEAR }}</label>
                <input type="text" id="expiry-year" placeholder="YY" required readonly>
            </div>
            <div class="payment-form-col">
                <label for="security-code">{{ T_cvv }}</label>
                <input type="text" id="security-code" placeholder="123" required readonly>
            </div>
        </div>
    </div>
</div>
<script>
    var paraty_logger = new ParatyLogger("{{'/log'|fixurl}}", "{{logging_session}}", namespace="[JS][EVO][{{hotel_code}}][{{payment_order_id}}]");
	paraty_logger.info("ping");
	var payment_method_id = "{{payment_methods[0].id}}";

    window.addEventListener("load", function (){
		import("{{'/external/js/libs/payment_widget.js'|fixurl}}").then(function(payment_widget){
			var options = JSON.parse(document.getElementById("payment-options-ctx").innerHTML);

			if(options.length > 1) {
				payment_widget.init_payment_options(
					"payment-options",
					options,
					function (selected_option) {
						payment_method_id = selected_option.id;
					},
					false
				);
			}
        });

		PaymentSession.configure({
                    session: "{{ sessionid }}",
                    fields: {
                        // ATTACH HOSTED FIELDS TO YOUR PAYMENT PAGE FOR A CREDIT CARD
                        card: {
                            number: "#evo-payment-form #card-number",
                            securityCode: "#evo-payment-form #security-code",
                            expiryMonth: "#evo-payment-form #expiry-month",
                            expiryYear: "#evo-payment-form #expiry-year",
                            nameOnCard: "#evo-payment-form #cardholder-name"
                        }
                    },
                    //SPECIFY YOUR MITIGATION OPTION HERE
                    frameEmbeddingMitigation: ["javascript"],
                    callbacks: {
                        initialized: function(response) {
							paraty_logger.info(`payment gateway init response: ${JSON.stringify(response)}`);
							disable_spinner();
                            // HANDLE INITIALIZATION RESPONSE
                        },
                        formSessionUpdate: function(response) {
                            // HANDLE RESPONSE FOR UPDATE SESSION
                            paraty_logger.info("Form response: "+ JSON.stringify(response));

                            if (response.status) {
                                if ("ok" == response.status) {
                                    paraty_logger.info("Session updated with data: " + response.session.id);


                                    let date = new Date();
	                                let dateOffset = date.getTimezoneOffset();

									var browserData = {
                                        colorDepth: screen.colorDepth,
                                        javaEnabled: navigator.javaEnabled(),
                                        javaScriptEnabled: true,
                                        language: navigator.language,
                                        screenHeight: screen.height,
                                        screenWidth: screen.width,
                                        timeZone: dateOffset
                                    }

                                    var dataToPost = {
										browserData: browserData,
                                        sessionId: "{{ sessionid }}",
                                        payload: "{{ payload }}" ,
                                        paymentMethodId: payment_method_id
                                    }

									fetch("{{'/evo/order/create/'|fixurl}}", {
										method: 'POST',
                                        mode: 'cors',
                                        headers: {
                                            "Content-Type": "application/json"
                                        },
                                        body: JSON.stringify(dataToPost)
                                    }).then(function(response){
										return response.json();
                                    }).then(function(data){
										if(data.status == "OK"){
											window.location.href = data.redirect_url;
                                        }else{
											show_message(data.error_message, level="ERROR");
											disable_spinner();
                                        }
                                    });


                                } else if ("fields_in_error" == response.status)  {
									disable_spinner();
                                    paraty_logger.info("Session update failed with field errors.");
									paraty_logger.warn(`Invalid field: ${JSON.stringify(response)}`)
                                    if (response.errors.cardNumber) {
										show_message("Card number invalid or missing.", level="ERROR");
                                    }
                                    if (response.errors.expiryYear) {
										show_message("Expiry year invalid or missing.", level="ERROR");
                                    }
                                    if (response.errors.expiryMonth) {
										show_message("Expiry month invalid or missing.", level="ERROR");
                                    }
                                    if (response.errors.securityCode) {
										show_message("Security code invalid.", level="ERROR");
                                    }
                                } else if ("request_timeout" == response.status)  {
									disable_spinner();
                                    paraty_logger.warn("Session update failed with request timeout: " + response.errors.message);

                                } else if ("system_error" == response.status)  {
									disable_spinner();
                                    paraty_logger.warn("Session update failed with system error: " + response.errors.message);
                                }
                            } else {
								disable_spinner();
                                paraty_logger.warn("Session update failed: " + response);
                            }
                        }
                    },
                    interaction: {
                        displayControl: {
                            formatCard: "EMBOSSED",
                            invalidFieldCharacters: "REJECT"
                        }
                    }
                 });
    });



    tpv_cobrador_controller = function() {
        return {
            execute_controller: function() {
				remove_message();
				enable_spinner();
				var result = PaymentSession.updateSessionFromForm('card');
				paraty_logger.info("response of tpv_cobrador_gateway");
				paraty_logger.info(result);
            }
        }
    }();

	function show_message(message, level="INFO"){
		remove_message();
        if(level=="INFO"){
            $(".evo-message").removeClass("error-message");
            $(".evo-message").html(message);
            $(".evo-message").css("display", "block");
        }else{
            $(".evo-message").addClass("error-message");
            $(".evo-message").html(message);
            $(".evo-message").css("display", "block");
        }
    }

    function remove_message(){
        $(".evo-message").css("display", "none");
    }

	function enable_spinner(){
        $(".pep_links_spinner_wrapper").css("display", "flex").addClass("peplinks_active_spinner");
        $("#pep_links_spinner").addClass("peplinks_spinner");
    }

    function disable_spinner(){
        disable_booking3_popup()
        enable_submitButton()
        $(".pep_links_spinner_wrapper").css("display", "none").removeClass("peplinks_active_spinner");
        $("#pep_links_spinner").removeClass("peplinks_spinner");
    }

    function disable_booking3_popup(){
        let processing_booking_popup = $("#processing_your_booking");
             if(processing_booking_popup.length){
                processing_booking_popup.removeClass("active");
             }
    }
    function enable_submitButton(){
        let submitButton = $("#submitButton");
        if(submitButton.length){
            submitButton.prop("disabled", false);
         }
    }

	enable_spinner();
</script>
<script>
    $("#destination_cobrador_form").append($(".evo-form-container"));
{#	document.querySelector("body").appendChild(document.querySelector(".evo-form-container"));#}
</script>