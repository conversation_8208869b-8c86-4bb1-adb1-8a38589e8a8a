<link rel="stylesheet" href="{{ base_url }}static/css/pages/cobrador/sermepa_insight_styles.css">
<div class="card_form_insight_popup" style="display: none">
    <div id="card-form-insight"></div>
</div>

<input type="hidden" id="token_insite"/>
<input type="hidden" id="error_code_insite"/>
<script src="{{ sermepa_js_path }}"></script>

<script>
    window.addEventListener("message", function receiveMessage(event) {
        storeIdOper(event, "token_insite", "error_code_insite");
        var token_insite = document.getElementById("token_insite").value;
        if(document.getElementById("error_code_insite").value || (token_insite && token_insite == -1)) {
            token_insite = "ERROR"
        }

        if(token_insite) {
            tpv_cobrador_controller.config.url += "&token_insite=" + token_insite;
            console.log(tpv_cobrador_controller.config.url)
            setTimeout(function() {
                window.location.href = tpv_cobrador_controller.config.url;
            }, 100)
        }
    });

    getInSiteForm('card-form-insight', '', '', '', '', '{{ T_DO_PAY }}', '{{ merchant_code }}', '{{ merchant_terminal }}', '{{ merchant_order_id }}', 'ES');

    var tpv_cobrador_controller = function () {
        return {
            config: {
                url: "{{ merchant_url }}",
            },

            execute_controller: function () {
                $(".card_form_insight_popup").fadeIn();
            }
        }
    }();
</script>