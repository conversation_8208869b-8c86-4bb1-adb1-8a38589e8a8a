<ns1:doWebPaymentRequest xmlns:ns2="http://obj.ws.payline.experian.com" xmlns:ns1="http://impl.ws.payline.experian.com" xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
 <ns1:version/>
 <ns1:payment>
     <ns2:amount>{{ Ds_Merchant_Amount }}</ns2:amount>
     <ns2:currency>{{ Ds_Merchant_Currency }}</ns2:currency>
     <ns2:action>{{ Ds_Merchant_TransactionType }}</ns2:action>
     <ns2:mode>{{ Ds_Merchant_Mode }}</ns2:mode>
     <ns2:contractNumber>{{ Ds_Merchant_ContractNumber }}</ns2:contractNumber>
     <ns2:differedActionDate xsi:nil="true"/>
     <ns2:method xsi:nil="true"/>
     <ns2:softDescriptor xsi:nil="true"/>
     <ns2:cardBrand xsi:nil="true"/>
 </ns1:payment>
 <ns1:returnURL>{{ Ds_Merchant_UrlOK }}</ns1:returnURL>
 <ns1:cancelURL>{{ Ds_Merchant_UrlKO }}</ns1:cancelURL>
 <ns1:order>
     <ns2:ref>{{ Ds_Merchant_Order }}</ns2:ref>
     <ns2:origin xsi:nil="true"/>
     <ns2:country>{{ Ds_Merchant_Country }}</ns2:country>
     <ns2:taxes>{{ Ds_Merchant_Taxes }}</ns2:taxes>
     <ns2:amount>{{ Ds_Merchant_Amount }}</ns2:amount>
     <ns2:currency>{{ Ds_Merchant_Currency }}</ns2:currency>
     <ns2:date>{{ Ds_Merchant_Date }}</ns2:date>
     <ns2:deliveryTime xsi:nil="true"/>
     <ns2:deliveryMode xsi:nil="true"/>
 </ns1:order>
 <ns1:notificationURL>{{ Ds_Merchant_MerchantURL }}</ns1:notificationURL>
 <ns1:selectedContractList>
     <ns2:selectedContract>{{ Ds_Merchant_ContractNumber }}</ns2:selectedContract>
 </ns1:selectedContractList>
 <ns1:secondSelectedContractList xsi:nil="true"/>
 <ns1:privateDataList>
     <ns2:privateData>
     <ns2:key/>
     <ns2:value/>
     </ns2:privateData>
 </ns1:privateDataList>
 <ns1:languageCode>{{ Ds_Merchant_ConsumerLanguage }}</ns1:languageCode>
 <ns1:customPaymentPageCode xsi:nil="true"/>
 <ns1:buyer>
     <ns2:title xsi:nil="true"/>
     <ns2:lastName>{{ Ds_Merchant_Buyer_LastName }}</ns2:lastName>
     <ns2:firstName>{{ Ds_Merchant_Buyer_Name }}</ns2:firstName>
     <ns2:email>{{ Ds_Merchant_Buyer_Email }}</ns2:email>
 </ns1:buyer>

 <ns1:securityMode>{{ Ds_Merchant_SecurityMode }}</ns1:securityMode>
 <ns1:recurring xsi:nil="true"/>
 <ns1:customPaymentTemplateURL xsi:nil="true"/>
 <ns1:contractNumberWalletList xsi:nil="true"/>
 <ns1:merchantName>{{ Ds_Merchant_MerchantName }}</ns1:merchantName>
</ns1:doWebPaymentRequest>