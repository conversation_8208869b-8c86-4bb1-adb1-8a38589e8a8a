<form class="payment_payu" action="{{ url_checkout }}" method="POST" id="paymentForm">
    <input name="merchantId"      type="hidden"  value="{{ merchant_id }}">
    <input name="accountId"       type="hidden"  value="{{ account_id }}">
    <input name="description"     type="hidden"  value="{{ description }}">
    <input name="referenceCode"   type="hidden"  value="{{ reference_code }}">
    <input name="amount"          type="hidden"  value="{{ amount }}">
    <input name="tax"             type="hidden"  value="{{ tax }}">
    <input name="taxReturnBase"   type="hidden"  value="{{ tax_return_base }}">
    <input name="currency"        type="hidden"  value="{{ currency }}">
    <input name="signature"       type="hidden"  value="{{ signature }}">
    <input name="test"            type="hidden"  value="{{ test }}">
    <input name="extra1"          type="hidden"  value="{{ extra1 }}">
    <input name="buyerFullName"   type="hidden"  value="">
    <input name="buyerEmail"      type="hidden"  value="">
    <input name="responseUrl"     type="hidden"  value="{{ responseUrl }}">
    <input name="confirmationUrl" type="hidden"  value="{{ confirmationUrl }}">
</form>
<script>
    tpv_cobrador_controller = function() {
        return {
            execute_controller: function () {
                var personal_details_form = $("#personal-details-form");
                if (personal_details_form.length == 0) {
                    personal_details_form = $("#personalDetailsForm");
                }
                var email = personal_details_form.find("[name='email']").val();
                var name = personal_details_form.find("[name='firstName']").val();
                var lastname = personal_details_form.find("[name='lastName1']").val();
                $("[name='buyerEmail']").val(email);
                $("[name='buyerFullName']").val(name + " " + lastname);

                var startDate = $('[name="startDate"]').val();
                var endDate = $('[name="endDate"]').val();
                if (calendar_v2_search_params != undefined && calendar_v2_search_params != ''){
                    startDate = JSON.parse(calendar_v2_search_params).startDate;
                    endDate = JSON.parse(calendar_v2_search_params).endDate;
                }

                var dates = startDate + ' - ' + endDate;
                var rate = $('#RS_selected_rate').val();
                var room = $('#RS_selected_room').val();
                var old_description = "{{ description }}"
                var identifier = 'Código de reserva '+"{{ reference_code }}"
                var list_ = [rate, room, dates, identifier]
                var description = old_description
                for (var key in list_){
                    description += ' * ' + list_[key];
                }
                $('.payment_payu').find('[name="description"]').val(description);
                var paymentForm = document.getElementById("paymentForm");
				paymentForm.submit();
			}
        }
    }();
</script>