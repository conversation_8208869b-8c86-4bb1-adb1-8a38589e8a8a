<form method="POST" action="https://payments.securetrading.net/process/payments/choice" id="paymentForm" class="trust_form">
    <input type="hidden" name="sitereference" value="{{ sitereference }}">
    <input type="hidden" name="stprofile" value="default">
    <input type="hidden" name="orderreference" value="{{ payment_order_id }}">
    <input type="hidden" name="currencyiso3a" value="{{ currency }}">
    <input type="hidden" name="mainamount" value="{{ amount }}">
    <input type="hidden" name="version" value="2">
    <input type=hidden name="ruleidentifier" value="STR-6">
    <input type=hidden name="ruleidentifier" value="STR-13">
    <input type="hidden" name="errorurlredirect" value="{{ url_ko }}">
    <input type=hidden name="ruleidentifier" value="STR-7">
    <input type="hidden" name="declinedurlredirect" value="{{ url_ko }}">
    <input type="hidden" name="sitesecurity" value="{{ security_hash }}">
    <input type="hidden" name="sitesecuritytimestamp" value="{{ timestamp }}">
    <input type=hidden name="ruleidentifier" value="STR-8">
    <input type="hidden" name="successfulurlnotification" value="{{ merchant_url }}">
    <input type="hidden" name="ruleidentifier" value="STR-2">
    <input type="hidden" name="sid" value="{{ sid }}">
    <input type="hidden" name="stextraurlnotifyfields" value="mainamount">
    <input type="hidden" name="stextraurlnotifyfields" value="currencyiso3a">
    <input type="hidden" name="stextraurlnotifyfields" value="sid">
    <input type="hidden" name="ruleidentifier" value="STR-11">
    <input type="hidden" name="credentialsonfile" value="1">
    {% if tokenize %}
        <input type="hidden" name="requesttypedescriptions" value="ACCOUNTCHECK">
        <input type="hidden" name="requesttypedescriptions" value="THREEDQUERY">
        <input type="hidden" name="successfulurlredirect" value="{{ merchant_url }}">
    {% else %}
        <input type="hidden" name="successfulurlredirect" value="{{ url_ok }}">
    {% endif %}
</form>
{#           todo en esta url, se activará la reserva, es donde se recibe la notificacion del pago #}

<script>
    var tpv_cobrador_controller = function () {
        return {
            execute_controller: function() {
                append_inputs_to_form();
                var paymentForm = $("#paymentForm");
                paymentForm.submit();
            }
        }
    }();
    function append_inputs_to_form(){
        var inputs = {
            firstName: 'billingfirstname',
            lastName1: 'billinglastname',
        };
        var payment_form = $('#paymentForm');
        for (var key in inputs) {
            if (inputs.hasOwnProperty(key)) {
                var elemento = $('input[name="' + key + '"]').val();
                if (elemento !== undefined){
                    payment_form.append(get_input_to_append(inputs[key], elemento))
                }
            }
        }
        payment_form.append(get_input_to_append('billingcountryiso2a', $('#internalCountryCode').val()))
    }
    function get_input_to_append(input_name, value){
        return '<input type="hidden" name="'+input_name+'" value="'+value+'">'
    }
</script>