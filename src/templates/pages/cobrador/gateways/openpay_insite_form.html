<script type="text/javascript" src="https://openpay.s3.amazonaws.com/openpay.v1.min.js"></script>
<script type='text/javascript' src="https://openpay.s3.amazonaws.com/openpay-data.v1.min.js"></script>

<style>

    ::-webkit-input-placeholder {
       font-style: italic;
    }
    :-moz-placeholder {
       font-style: italic;
    }
    ::-moz-placeholder {
       font-style: italic;
    }
    :-ms-input-placeholder {
       font-style: italic;
    }


    strong {
        font-weight: 700;
    }
    a {
        cursor: pointer;
        display: block;
        text-decoration: none;
    }
    a.button {
        border-radius: 5px 5px 5px 5px;
        -webkit-border-radius: 5px 5px 5px 5px;
        -moz-border-radius: 5px 5px 5px 5px;
        text-align: center;
        font-size: 21px;
        font-weight: 400;
        padding: 12px 0;
        display: table;
        background: #E51F04;
        background: -moz-linear-gradient(top,  #E51F04 0%, #A60000 100%);
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#E51F04), color-stop(100%,#A60000));
        background: -webkit-linear-gradient(top,  #E51F04 0%,#A60000 100%);
        background: -o-linear-gradient(top,  #E51F04 0%,#A60000 100%);
        background: -ms-linear-gradient(top,  #E51F04 0%,#A60000 100%);
        background: linear-gradient(top,  #E51F04 0%,#A60000 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#E51F04', endColorstr='#A60000',GradientType=0 );
    }
    a.button i {
        margin-right: 10px;
    }
    a.button.disabled {
        background: none repeat scroll 0 0 #ccc;
        cursor: default;
    }
    .bkng-tb-cntnt {
        float: left;
    }
    .bkng-tb-cntnt a.button {
        color: #fff;
        float: right;
        font-size: 18px;
        padding: 5px 20px;
    }
    .bkng-tb-cntnt a.button.o {
        background: none repeat scroll 0 0 rgba(0, 0, 0, 0);
        color: #e51f04;
        border: 1px solid #e51f04;
    }
    .bkng-tb-cntnt a.button i {
        color: #fff;
    }
    .bkng-tb-cntnt a.button.o i {
        color: #e51f04;
    }
    .bkng-tb-cntnt a.button.right i {
        float: right;
        margin: 2px 0 0 10px;
    }
    .bkng-tb-cntnt a.button.left {
        float: left;
    }
    .bkng-tb-cntnt a.button.disabled.o {
        border-color: #ccc;
        color: #ccc;
    }
    .bkng-tb-cntnt a.button.disabled.o i {
        color: #ccc;
    }
    .pymnts {
        float: left;
        flex-direction: column;
    }


    .sctn-row {
        margin-bottom: 15px;
        padding-left: 10px;
        padding-right: 10px;
    }

    .sctn-col input {
        border: 1px solid #ccc;
        font-size: 18px;
        line-height: 24px;
        max-width: 100%;
        border-radius: 5px;
    }

    .sctn-col{
        display: flex;
        flex-direction: column;
    }
    .sctn-col.l {
        display: flex;
        flex-direction: column;
    }

    .sctn-col label {
        font-size: initial;
        line-height: inherit;
        margin: 5px 0;
        width: 100%;
    }
    .sctn-col.x3 {
        width: 100%;
    }

    .sctn-col.x3 a {
        float: right;
    }

    .pymnt-itm {
        margin: 0 0 3px;
        width: 100%;
    }
    .pymnt-itm h2 {
        background-color: #e9e9e9;
        font-size: 24px;
        line-height: 24px;
        margin: 0;
        padding: 28px 0 28px 20px;
    }
    .pymnt-itm.active h2 {
        cursor: default;
    }
    .pymnt-itm div.pymnt-cntnt {
        display: none;
    }
    .pymnt-itm.active div.pymnt-cntnt {
        background-color: #f7f7f7;
        display: flex;
        flex-direction: column;
        padding: 0 0 30px;
        width: 100%;
    }

    .pymnt-cntnt div.sctn-row {
        display: flex;
        flex-direction: column;
        margin: 5px 10px;
    }


    .pymnt-cntnt div.sctn-row div.sctn-col.cvv {
        background-image: url("{{'/static/images/openpay/cvv.png'|fixurl}}");
        background-position: 156px center;
        background-repeat: no-repeat;
        padding-bottom: 30px;
    }

    .pymnt-cntnt div.sctn-row div.sctn-col input{
        height: 40px;
    }


    .openpay {
        display: flex;
        flex-direction: column;
        margin: 5px 10px;
    }
    .openpay div.logo {
        background-image: url("{{'/static/images/openpay/openpay.png'|fixurl}}");
        background-position: left bottom;
        background-repeat: no-repeat;
        font-size: 12px;
        font-weight: 400;
        height: 45px;
    }
    .openpay div.shield {
        background-image: url("{{'/static/images/openpay/security.png'|fixurl}}");
        background-position: left bottom;
        background-repeat: no-repeat;
        font-size: 12px;
        font-weight: 400;
        margin-left: 5px;
        padding: 20px 0 0 40px;
    }
    .card-expl {
        float: left;
        width: 100%;
    }
    .card-expl div {
        background-position: left 45px;
        background-repeat: no-repeat;
        height: 70px;
        padding-top: 10px;
    }
    .card-expl div.debit {
        background-image: url("{{'/static/images/openpay/cards2.png'|fixurl}}");
        margin-left: 5px;
        background-size: contain;
    }
    .card-expl div.credit {
        background-image: url("{{'/static/images/openpay/cards1.png'|fixurl}}");
        margin-left: 5px;
        background-size: initial;

    }
    .card-expl h4 {
        font-weight: 400;
        margin: 0;
        margin-left: 10px;
        font-size: initial;
    }

    .sctn-second-wrapper input{
        width: 100%;
    }
    .sctn-second-wrapper .sctn-col{
        margin: 5px 0;
    }

    .sctn-second-wrapper .sctn-col .sctn-col{
        width: 40%;
    }

    .expiration-data{
        display: inline-flex;
        width:100%;
        justify-content: space-between;
    }
    .expiration-data

    .cvv-data{
        display: inline-flex;
        width:100%;
    }

    #paymentForm ::placeholder {
      color: rgba(0, 0, 0, 0.4);
      font-size: 14px;
    }

    .error-box {
        background-color: #ff9e9e;
        border: 2px solid #ff3636;
        padding: 2px 4px;
        margin: 2px;
        border-radius: 5px;
        text-align: center;
        display: none;
    }

    .error-text {
        font-size: 18px;
        color: #ff3636;
    }
    .warning-box {
        background-color: #ffffcc;
        border: 2px solid #ffcc00;
        padding: 2px 4px;
        margin: 2px;
        border-radius: 5px;
        text-align: center;
        display: none;
    }

</style>
    <div class="bkng-tb-cntnt openpay_payment_form_container">
        <div class="pymnts">
            <div id="paymentForm">
                <div class="warning-box">
                    <p class="warning-text">{{ T_WARNING_TITLE }}</p>
                    <p>{{ T_WARNING_MESSAGE }}</p>
                </div>
                <input type="hidden" name="token_id" id="token_id">
                <input type="hidden" name="amount" id="amount" value="{{ amount }}">
                <input type="hidden" name="charge_description" id="charge_description" value="{{ charge_description }}">
                <input type="hidden" name="sid" id="sid" value="{{ sid }}">
                <input type="hidden" name="currency" id="currency" value="{{ currency }}">
                <div class="pymnt-itm card active">
                    <div class="pymnt-cntnt">
                        <div class="card-expl">
                            <div class="credit"><h4>{{ T_credit_cards }}</h4></div>
                            <div class="debit"><h4>{{ T_debit_cards }}</h4></div>
                        </div>
                        <div class="sctn-row sctn-first-wrapper">
                            <div class="sctn-col l">
                                <label>{{ T_holders_name }}</label><input type="text" placeholder="Como aparece en la tarjeta" autocomplete="off" data-openpay-card="holder_name">
                            </div>
                            <div class="sctn-col">
                                <label>{{ T_numero_tarjeta }}</label><input type="text" autocomplete="off" data-openpay-card="card_number"></div>
                            </div>
                            <div class="sctn-row sctn-second-wrapper">
                                <div class="sctn-col l">
                                    <label>{{ T_fecha_expiracion }}</label>
                                    <div class="expiration-data">
                                        <div class="sctn-col half l"><input type="text" placeholder="Mes" data-openpay-card="expiration_month"></div>
                                        <div class="sctn-col half l"><input type="text" placeholder="Año" data-openpay-card="expiration_year"></div>
                                    </div>

                                </div>
                                <div class="sctn-col l">
                                    <label>{{ T_security_code }}</label>
                                    <div  class="cvv-data">
                                        <div class="sctn-col half l"><input type="text" placeholder="3 dígitos" autocomplete="off" data-openpay-card="cvv2"></div>
                                    </div>

                                </div>
                            </div>
                            <div class="openpay sctn-third-wrapper"><div class="logo">{{ T_transactions_carried_out_through }}</div>
                            <div class="shield">{{ T_payments_encryption }}</div>
                        </div>
                    </div>
                </div>
                <div id="openpay-error" class="error-box">
                    <p class="error-text"></p>
                </div>
            </div>
        </div>
    </div>

<script type="text/javascript">
    $(document).ready(function() {
        OpenPay.setId('{{ merchant_id }}');
        OpenPay.setApiKey('{{ public_key }}');
        OpenPay.setSandboxMode(true);
        //Se genera el id de dispositivo
        var deviceSessionId = OpenPay.deviceData.setup("paymentForm", "device_id");

        $("#destination_cobrador_form").append($(".openpay_payment_form_container"));
    });

    var sucess_callbak = function(response) {
            var token_id = response.data.id;
            $('#token_id').val(token_id);
            debugger;
            window.location.href = "{{merchant_url|safe}}&token_id=" + token_id + "&device_session_id=" + $("#device_id").val() + "&amount={{ amount }}" + "&currency={{ currency }}";
        };

    var error_callbak = function(response) {
        var desc = response.data.description != undefined ? response.data.description : response.message;
        alert("{{ T_error_payment_TPV }}");
        debugger;
        window.location.href = "{{url_ko | safe}}";

    };

    tpv_cobrador_controller = function() {
            return {
                execute_controller: function () {
                    $(".warning-box").slideDown();
                    $("#openpay-error-error").slideUp();
                    setTimeout(function (){
                        if(localStorage.getItem("beacon_sended_{{ sid }}") == null){
                          navigator.sendBeacon("{{merchant_url|safe}}");
                          localStorage.setItem("beacon_sended_{{ sid }}", "true")
                        }
                    }, 15000);
                    window.addEventListener("unload", function(e){
                      if(localStorage.getItem("beacon_sended_{{ sid }}") == null){
                          navigator.sendBeacon("{{merchant_url|safe}}");
                          localStorage.setItem("beacon_sended_{{ sid }}", "true")
                      }
                    });
                    OpenPay.token.extractFormAndCreate('paymentForm', sucess_callbak, error_callbak);
                }
            }
        }();

</script>

