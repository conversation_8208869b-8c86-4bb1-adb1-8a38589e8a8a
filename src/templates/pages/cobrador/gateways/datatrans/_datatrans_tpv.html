<link rel="stylesheet" href="{{ base_url }}static/css/pages/cobrador/datatrans_tpv_styles.css">

<div class="datatrans_payment_selector booking-box booking-box--form v2">
    <div class="booking-box__content payment_gateway_info_box">
        <div id="payment_card" class="card_payment_method payment_option_wrapper active">
            <div class="method_title">
                <div class="title">{{ title_card|safe if title_card else T_Pago_Tarjeta_SIBS }}</div>
                <div class="logo_card">
                    <img src="https://cdn2.paraty.es/test-backend2/images/0a2d6398e183112=s30">
                    <img src="https://cdn2.paraty.es/test-backend2/images/e4dafdba0beb2b8=s30">
                </div>
            </div>
            <div class="payment_card_clone_wrapper">
                <div class="input_card">
                    <div id="card_number"></div>
                </div>
                <div class="input_card">
                    <div id="card_cvv"></div>
                </div>
                <div class="input_card expiry_dates">
                    <input type="text" name="card_expiry_month" placeholder="MM">
                    <input type="text" name="card_expiry_year" placeholder="YY">
                </div>

                {% if sibs_messages and sibs_messages.message_card %}
                    <div class="card_message">{{ sibs_messages.message_card|safe }}</div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<form action="{{ url_redirect }}" class="send_to_cobrador" method="POST">
    <input type="hidden" name="payment_order_id" value="{{ payment_order_id }}">
    <input type="hidden" name="uppTransactionId" value="{{ transaction_id }}">
</form>

<form action="" class="form_to_redirect" method="GET"></form>
{% if in_production %}
    <script src="https://pay.datatrans.com/upp/payment/js/secure-fields-2.0.0.min.js"></script>
{% else %}
    <script src="https://pay.sandbox.datatrans.com/upp/payment/js/secure-fields-2.0.0.min.js"></script>
{% endif %}
<script>
    $("#destination_cobrador_form").append($(".datatrans_payment_selector"));
    $(".payment_option_wrapper.to_reload.sibs").remove();

    var secureFields = new SecureFields();
    secureFields.init("{{ transaction_id }}", {
        cardNumber: {
            placeholder: "{{ T_numero_tarjeta }}",
            placeholderElementId: "card_number"
        },
        cvv: {
            placeholder: "CVV",
            placeholderElementId: "card_cvv"
        }
    });

    secureFields.on("success", function (data) {
        if (data.redirect) {
            $("form.form_to_redirect").attr("action", data.redirect);
            $("form.form_to_redirect").submit();
        } else {
            $("form.send_to_cobrador").submit();
        }
    });

    secureFields.on("validate", function (event) {
        // Add a red border if a field is invalid
        console.log(event);
        secureFields.setStyle("cardNumber.invalid", "border: 1px solid #f00");
        secureFields.setStyle("cvv.invalid", "border: 1px solid #f00");

        if(event.hasErrors) {
            form_controller.enable_payment_button();
        }
    });

    secureFields.on("error", function (data) {
        console.log("error");
        console.log(data);

        if(data.error === "declined" || data.error === "Internal error") {
            setTimeout(function() {
                window.location.href = window.location.href
            }, 100);
        }

        form_controller.enable_payment_button();
    });

    var tpv_cobrador_controller = function () {
        return {
            execute_controller: function () {
                var month_input = $("input[name=card_expiry_month]"),
                    year_input = $("input[name=card_expiry_year]"),
                    invalid_send = false,
                    actual_month_year = $.datepicker.formatDate("mmy", new Date())

                if (!month_input.val() || isNaN(month_input.val()) || month_input.val().length < 2) {
                    month_input.addClass("invalid");
                    invalid_send = true;
                } else {
                    month_input.removeClass("invalid");
                }

                if (!year_input.val() || isNaN(year_input.val()) | year_input.val().length < 2) {
                    year_input.addClass("invalid");
                    invalid_send = true;
                } else {
                    year_input.removeClass("invalid");
                }

                if (month_input.val() + year_input.val() <= actual_month_year) {
                    year_input.addClass("invalid");
                    month_input.addClass("invalid");
                    invalid_send = true;
                }

                if (invalid_send) {
                    form_controller.enable_payment_button();
                    return false;
                }
                secureFields.submit({
                    expm: month_input.val(),
                    expy: year_input.val()
                });
            }
        }
    }();
</script>