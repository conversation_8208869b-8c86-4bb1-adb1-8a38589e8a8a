<script src="https://payment.preprod.direct.worldline-solutions.com/hostedtokenization/js/client/tokenizer.min.js"></script>
<style>
    #wordline_eu_error_message{
        color: #d20000!important;
        font-size: 12px!important;
        text-align: center!important;
    }
</style>

<div id="worldline_eu_payment">
    <div id="wordline_eu_error_message" style="display: none">
        Ha ocurrido un error con la pasarela de pago. Pruebe de nuevo o póngase en contacto con nosotros.
    </div>

</div>

<script>
    var tokenizer = null;
    var tpv_cobrador_controller = null;

    window.onload = function(){
        tokenizer =  new Tokenizer("{{ token_hosted_url }}", 'worldline_eu_payment', {hideCardholderName: false });
        tokenizer.initialize().then(() => {
        // Do work after initialization, if any
        })
    }
    tpv_cobrador_controller = function() {
    return {

        execute_controller: function () {
                tokenizer.submitTokenization().then((result) => {
                    console.log(result);
                    if (result.success) {
                        var token_id = result.hostedTokenizationId;

						var personal_details_form = document.querySelector("#personal-details-form");
                        if (!personal_details_form) {
                            personal_details_form = document.querySelector("#personalDetailsForm");
                        }

						var client_metadata = {
                            timeoffset: (new Date()).getTimezoneOffset(),
                            locale: navigator.language,
                            userAgent: navigator.userAgent,
                            javaEnabled: navigator.javaEnabled(),
                            colorDepth: screen.colorDepth,
                            height: screen.height,
                            width: screen.width,
                            email: personal_details_form?personal_details_form.querySelector("[name='email']").value:"",
                            phone: personal_details_form?personal_details_form.querySelector("[name='telephone']").value:""
                        }

						var data = {
							"token_id": token_id,
                            "payload": "{{payload}}",
                            "client_metadata": client_metadata
                        }

                        fetch("{{'/worldline_eu/authorize_payment'|fixurl }}", {
                            method: 'POST',
                            mode: 'cors',
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify(data)
                        }).then(function(res) {
                            return res.json();
                        }).then(function(orderData) {
                            if(orderData.error){
                                $("#wordline_eu_error_message").text(orderData.message)
                                $("#wordline_eu_error_message").show();
                                debugger
                                setTimeout(function() {
                                    window.location.reload();
                                }, 7000);
                            }
							if(orderData.redirect_to){
								window.location.href = orderData.redirect_to;
                            }
                        });
                    } else {
                        console.error(result)
                    }
                });
            }
        }
    }();
</script>
<script>
    $("#destination_cobrador_form").append($("#worldline_eu_payment"));
</script>