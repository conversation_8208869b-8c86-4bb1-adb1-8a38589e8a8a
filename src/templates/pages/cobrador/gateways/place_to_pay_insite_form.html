<form id="paymentForm" action="https://payment-seeker.appspot.com/placetopay/redirect?sid={{ sid }}" method="POST">
	<input type='hidden' name='Ds_Merchant_Currency' value="{{ form.Ds_Merchant_Currency }}">
	<input type='hidden' name='Ds_Merchant_Amount' value="{{ form.Ds_Merchant_Amount }}">
	<input type='hidden' name='Ds_Merchant_MerchantCode' value="{{ form.Ds_Merchant_MerchantCode  }}">
	<input type='hidden' name='Ds_Merchant_MerchantName' value="{{ form.Ds_Merchant_MerchantName }}">
	<input type='hidden' name='Ds_Merchant_UrlOK' value="{{ form.Ds_Merchant_UrlOK }}">
	<input type='hidden' name='Ds_Merchant_UrlKO' value="{{ form.Ds_Merchant_UrlKO }}">
    <input type='hidden' name='DS_Merchant_Real_UrlKo' value="{{ form.DS_Merchant_Real_UrlKo }}">
	<input type='hidden' name='Ds_Merchant_MerchantURL' value="{{ form.Ds_Merchant_MerchantURL }}">
	<input type='hidden' name='Ds_Merchant_Expiration' value="{{ form.Ds_Merchant_Expiration }}">
	<input type='hidden' name='Ds_Merchant_Logo' value="{{ form.Ds_Merchant_Logo }}">

	<input type='hidden' name='Ds_Merchant_ipAddress' value="{{ form.Ds_Merchant_ipAddress }}">
	<input type='hidden' name='Ds_Merchant_UserAgent' value="{{ form.Ds_Merchant_Expiration }}">
	<input type='hidden' name='Ds_Merchant_ConsumerLanguage' value="{{ form.Ds_Merchant_ConsumerLanguage  }}">
	<input type='hidden' name='Ds_Merchant_Order' value="{{ form.Ds_Merchant_Order  }}">

	<input type='hidden' name='DS_Login' value="{{ form.DS_Login  }}">

	<input type='hidden' id="Ds_Merchant_Buyer_Email" name='Ds_Merchant_Buyer_Email' value="">
	<input type='hidden' id="Ds_Merchant_Buyer_Name" name='Ds_Merchant_Buyer_Name' value="">
	<input type="hidden" id="card_type" name='card_type' value="">
	<input type="hidden" id="card_number" name="card_number" value="">
	<input type="hidden" id="card_cvv" name="card_cvv" value="">
	<input type="hidden" id="card_expiration_month" name="card_expiration_month" value="">
	<input type="hidden" id="card_expiration_year" name="card_expiration_year" value="">
	<input type="hidden" id="payment_url" name="payment_url" value="{{ form.Ds_Merchant_Payment_Url|safe}}">
	<input type="hidden" id="sid" name="sid" value="{{ sid }}">
	<input type="hidden" id="hotel_code" name="hotel_code" value="{{ hotel_code }}">
	<input type="hidden" id="gateway_type" name="gateway_type" value="{{ gateway_type }}">
</form>

<script>
  tpv_cobrador_controller = function() {
      return {
          execute_controller: function () {
              document.querySelector("form#paymentForm").submit();
          }
      }
  }();
</script>