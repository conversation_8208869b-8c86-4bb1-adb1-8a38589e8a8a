<script
        type="text/javascript"
        src="https://checkout.wompi.co/widget.js"
></script>

<script>
    var checkout = new WidgetCheckout({
        currency: '{{ currency }}',
        amountInCents: {{ amount }},
        reference: '{{ id }}',
        publicKey: '{{ public_key }}',
        signature: {integrity: '{{ signature }}'},
        redirectUrl: '{{ merchant_url|safe }}',
        {% if tokenize %}widgetOperation: 'tokenize'{% endif %}
    })

    var tpv_cobrador_controller = function () {
        return {
            execute_controller: function () {
                checkout.open(function (result) {
                    {% if tokenize %}
                        var payment_source = result.payment_source,
                        email = $("input[name=email]").val()
                        var return_url =  `{{ merchant_url|safe }}&token=${payment_source.token}&type=${payment_source.type}&email=${email}`
                    {% else %}
                        var transaction = result.transaction;
                        console.log("Transaction object: ", result);
                        var return_url =  `{{ merchant_url|safe }}&id=${transaction.id}`
                    {% endif %}
                    window.location.href = return_url
                });
            }
        }
    }();
</script>