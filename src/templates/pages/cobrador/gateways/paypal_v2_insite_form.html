{% if dev %}
<script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>
{% endif %}
<script src="{{'/static/js/libs/paraty_logger.js'|fixurl}}"></script>
<script data-client-metadata-id="{{ fraudnet_token }}" src="https://www.paypal.com/sdk/js?client-id={{ client_id }}&components=buttons,funding-eligibility&currency={{ currency }}&commit=false{% if locale %}&locale={{ locale }}{% endif %}"></script>
{% if amounts %}
<style>
    .worldline_payment_form_container{
      padding: 5px;
      max-width: 400px;
      margin: 0 auto 20px;
    }
    .worldline_payment_form_container .paypal-container{
        margin-top: 8px;
    }
    .worldline_payment_form_container .payment_option.selected-option{
    background-color:  #e8f2fa;
    border-color: #2196F3;
    z-index: 1;
    }
    .worldline_payment_form_container .payment_option .payment_radiobtn{
        appearance: none;
        position: absolute;
        top: 22px;
        left: 12px;
    }
    .worldline_payment_form_container .payment_option .payment_radiobtn:before{
        content: '';
        width: 10px;
        height: 10px;
        border: 1px solid #333333;
        position: absolute;
        top: -11px;
        left: -8px;
        border-radius: 50%;
    }
    .worldline_payment_form_container .payment_option.selected-option .payment_radiobtn:after{
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: #333333;
        border-radius: 50%;
        top: -9px;
        left: -6px;
    }
    .worldline_payment_form_container .payment_option label{
        font-size: 14px;
        margin-left: 5px;
        display: block;
        padding: 0px 56px 0px 0px;
    }
    .worldline_payment_form_container .payment_option{
    padding: 10px 50px 10px 30px;
    position: relative;
    border: 1px solid gray;
    transition: background .3s;
    }
    .worldline_payment_form_container .payment_option:only-child{
    border-radius: 5px;
    }
    .worldline_payment_form_container .payment_option:first-child:not(:only-child){
    border-radius: 5px 5px 0 0;
    }
    .worldline_payment_form_container .payment_option:last-child{
    border-radius: 0 0 5px 5px;
    }
    .worldline_payment_form_container .payment_option:not(:first-child){
        margin-top: -1px;
    }
    .worldline_payment_form_container .payment_option .cards_images{
        display: inline-block;
        width: 90px;
        height: auto;
        position: absolute;
        right: 5px;
        top: 20px;
        text-align: right;
        transform: translateY(-50%);
    }
    .worldline_payment_form_container .payment_option .cards_images img{
        width: calc((100% / 3) - 14px);
        height: auto;
        object-fit: cover;
        margin-right: 5px;
    }
    .worldline_payment_form_container .payment_option#msi-payment:after{
        content: '\f133';
        position: absolute;
        right: 10px;
        font-family: 'Font Awesome 5 Pro';
        font-size: 18px;
        top: 10px;
        font-weight: 100;
    }
    .worldline_payment_form_container .payment_option .payment_option_hide_info{
    max-height: 0;
    overflow: hidden;
    transition: max-height .5s;
    }
    .worldline_payment_form_container .payment_option.selected-option .payment_option_hide_info{
    max-height: 500px;
    }
    .worldline_payment_form_container .payment_option .payment_option_description{
        font-weight: lighter;
        border-top: 1px solid rgba(0,0,0,0.2);
        padding-top: 10px;
        font-size: 12px;
        line-height: 22px;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper{
    position: relative;
    margin-top: 10px;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper .installments_quantity_label{
    position: absolute;
    top: -6px;
    left: 10px;
    font-size: 11px;
    padding: 0 5px;
    background: white;
    transition: background .3s;
    }
    .worldline_payment_form_container .payment_option.selected-option .msi_payment_select_wrapper .installments_quantity_label{
    background: #e8f2fa;
    }
    .worldline_payment_form_container .payment_option .installments_quantity{
    padding: 12px 10px 8px ;
    border-radius: 5px;
    background: transparent;
    font-size: 14px;
    appearance: none;
    width: 100%;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper:after{
    content: '\f107';
    position: absolute;
    right: 10px;
    font-family: "Font Awesome 5 Pro";
    font-size: 24px;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 100;
    }
    .worldline_payment_form_container .payment_option .installments_quantity div{
    margin: 5px;
    }
    .worldline_payment_form_container .payment_option .installments_quantity div input[type="radio"]{
    width: inherit;
    margin-right: 5px;
    height: inherit;
    }
</style>

    <div class="worldline_payment_form_container">
        {% if custom_text %}
            <div style="display: none" class="b3_custom_text">
                {% for option, text in custom_text.items() %}
                    <div id="{{ option }}" amount="{{ custom_amounts[option] }}">
                        {{ text|safe }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        <div id="worldline_payment_form" >
            {% if rate_condition == "flex" %}
                {% for amount in amounts %}
                    <div class="payment_option" id="card-payment">
                    <div class="cards_images">
                      <img src="{{'/static/images/credit-cards_mastercard.png'|fixurl}}">
                      <img src="{{'/static/images/credit-cards_visa.png'|fixurl}}">
                      <img src="{{'/static/images/credit-cards_amex.png'|fixurl}}">
                    </div>
                    <input class="payment_radiobtn" type="radio" id="radio{{loop.index}}" value="{{amount}}" {% if loop.index == 1 %}checked{% endif %}>
                        <label for="radio{{loop.index}}">
                            {% if "-day" in amount %}
                                {% if custom_text.select_option_button_n_day %}
                                    {{ custom_text.select_option_button_n_day }}
                                {% else %}
                                    {{ T_payment_days_a_t }} {{ amount|replace("-day", "") }} {{ T_payment_days_b_t }}{{ T_payment_days_c_t }}
                                {% endif %}
                            {% else %}
                                {% if custom_text.select_option_button_n_day_100 %}
                                    {{ custom_text.select_option_button_n_day_100 }}
                                {% else %}
                                    {{ T_payment_100_t }} {{amount}}%
                                {% endif %}
                            {% endif %}</label>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
        <div class="paypal-container">
            <div id="paypal-button-container"></div>
            <br>
            <div id="paypal-button-container-cc"></div>
        </div>
    </div>
{% else %}
    <div class="paypal-container">
    <div id="paypal-button-container"></div>
    <br>
    <div id="paypal-button-container-cc"></div>
</div>
{% endif %}


<script>
    var paraty_logger = new ParatyLogger("{{'/log'|fixurl}}", "{{logging_session}}", namespace="[JS][PAYPAL][{{hotel_code}}][{{payment_order_id}}]");
	paraty_logger.info("ping");
    var paypal_order_id = "";
    var paypal_payer_id = "";
    window.addEventListener("load", function (){
	    var booking3_url = new URL(window.location.href);
        if(booking3_url.searchParams.get("launch_popup_tpv_error") == "true"){
            $(document).trigger("paypal_payment_attempt_failed", []);
        }
    });

    {% if amounts %}

        if(document.querySelector(".tpv_message_informative .currencyValue") != null){
            var amount_to_pay = parseFloat(document.querySelector(".tpv_message_informative .currencyValue").innerHTML);
        }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
            var amount_to_pay = parseFloat(document.querySelector(".tpv_text_wrapper .currencyValue").innerHTML);
        }

        var current_currency = $("#currencySelect :selected").data("shortname");
        if(current_currency == undefined){
            current_currency = $(".currency_selector .item_selected").text().trim()
        }

        var percent_to_pay = $("#worldline_payment_form input[checked]").val();

    {% endif %}

    $("#submitButton, #btn-finish-booking").hide();
    $("#submitButton, #btn-finish-booking").attr("style","display:none !important");
    {% if not only_show_one_payment_method or show_card  %}
    var FUNDING_SOURCES = [
        paypal.FUNDING.CARD
    ];
    try{
	    FUNDING_SOURCES.forEach(function(fundingSource) {
            var button = paypal.Buttons({
                fundingSource: fundingSource,
                createOrder: function(data, actions){
                    let personal_details_form_is_valid = $("#personal-details-form, #personalDetailsForm").length && $("#personal-details-form, #personalDetailsForm").valid();
                    {% if dev %}
                        personal_details_form_is_valid = true;
                    {% endif %}

                    if (personal_details_form_is_valid) {
						var phone_prefix = ($("#personal-details-form, #personalDetailsForm").find("select[name=phone_prefix]").val() || "").replace("+", "");
                        var telephone = $("#personal-details-form, #personalDetailsForm").find("input[name=telephone]").val() || ""

                        var full_phone = phone_prefix + telephone;
                        var data = {
							selected_payment_option: fundingSource,
                            payment_order_id: "{{ payment_order_id }}",
                            concept_name: generate_name(),
                            email:  $("#personal-details-form, #personalDetailsForm").find("input[name=email]").val() || "",
                            name: $("#personal-details-form, #personalDetailsForm").find("input[name=firstName]").val() || "",
                            surname: $("#personal-details-form, #personalDetailsForm").find("input[name=lastName1]").val() || "",
                            country: $("#personal-details-form, #personalDetailsForm").find("select[name=country]").val() || "",
                            city: $("#personal-details-form, #personalDetailsForm").find("input[name=city]").val() || "",
                            postal_code: $("#personal-details-form, #personalDetailsForm").find("input[name=postal_code]").val() || "",
                            phone: full_phone,
                            sid: "{{ sid }}",
                            hotel_name: "{{ hotel_name }}",
                            {% if amounts %}
                              percent_to_pay: percent_to_pay,
                            {% endif %}
                            payload: "{{ payload }}",
                            hotel_code: "{{ hotel_code }}",
                            msi: {% if valid_installments %}true{% else %}false{% endif %}
                        }
						paraty_logger.info(`sending payload for create payment ${JSON.stringify(data)}`);

                        return fetch('{% if dev %}http://127.0.0.1:8090{% else %}https://payment-seeker.appspot.com{% endif %}/paypal/order/create/', {
                            method: 'POST',
                            mode: 'cors',
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify(data)
                        }).then(function(res) {
                            return res.json();
                        }).then(function(orderData) {
                            paraty_logger.info(`init payment corectly ${JSON.stringify(orderData)}`);
                            paypal_order_id = orderData.id;
                            return orderData.id;
                        });
                    } else {
                        paraty_logger.info(`[JS][PAYPAL][{{hotel_code}}][{{payment_order_id}}] personal details form is not valid`);
                    }
                },
                onApprove: function(data, actions) {
					paraty_logger.info(`payment approved ${JSON.stringify(data)}`);
                    if(data.orderID != "") {
                        paypal_order_id = data.orderID;
                    }
					paypal_payer_id = data.PayerID;
					if(window.show_booking_search_popup != undefined){
                        show_booking_search_popup();
                    }
                    $("#submitButton, #btn-finish-booking").click();
                },
                onError: function(err){
                    paraty_logger.info(`Error sended by paypal button ${err}`);
                }
            });

            paraty_logger.info(`init button corectly ${JSON.stringify(button)}`);

            if (button.isEligible()) {
                // Render the standalone button for that funding source
                button.render('#paypal-button-container-cc');
            }

        });
    }catch (e) {
        paraty_logger.info(`Error creating paypal card button ${e}`);
	}

    {% endif %}
    {% if not only_show_one_payment_method or not show_card  %}

    var FUNDING_SOURCES = [
        paypal.FUNDING.PAYPAL
    ];

	try{
		FUNDING_SOURCES.forEach(function(fundingSource) {
            var button = paypal.Buttons({
                fundingSource: fundingSource,
                createOrder: function(data, actions){
                    let personal_details_form_is_valid = $("#personal-details-form, #personalDetailsForm").length && $("#personal-details-form, #personalDetailsForm").valid();
                    {% if dev %}
                        personal_details_form_is_valid = true;
                    {% endif %}

                    if(personal_details_form_is_valid) {
						var phone_prefix = ($("#personal-details-form, #personalDetailsForm").find("select[name=phone_prefix]").val() || "").replace("+", "");
						var telephone = $("#personal-details-form, #personalDetailsForm").find("input[name=telephone]").val() || ""

						var full_phone = phone_prefix + telephone;
                        var data = {
							selected_payment_option: fundingSource,
                            payment_order_id: "{{ payment_order_id }}",
                            concept_name: generate_name(),
                            email:  $("#personal-details-form, #personalDetailsForm").find("input[name=email]").val() || "",
                            name: $("#personal-details-form, #personalDetailsForm").find("input[name=firstName]").val() || "",
                            surname: $("#personal-details-form, #personalDetailsForm").find("input[name=lastName1]").val() || "",
                            country: $("#personal-details-form, #personalDetailsForm").find("select[name=country]").val() || "",
                            city: $("#personal-details-form, #personalDetailsForm").find("input[name=city]").val() || "",
                            postal_code: $("#personal-details-form, #personalDetailsForm").find("input[name=postal_code]").val() || "",
                            phone: full_phone,
                            sid: "{{ sid }}",
                            hotel_name: "{{ hotel_name }}",
                            {% if amounts %}
                              percent_to_pay: percent_to_pay,
                            {% endif %}
                            payload: "{{ payload }}",
                            prices_per_day:"{{ prices_per_day }}",
                            hotel_code: "{{ hotel_code }}",
                            msi: {% if valid_installments %}true{% else %}false{% endif %}
                        }
						paraty_logger.info(`sending payload for create payment ${JSON.stringify(data)}`);
                        return fetch('{% if dev %}http://127.0.0.1:8090{% else %}https://payment-seeker.appspot.com{% endif %}/paypal/order/create/', {
                            method: 'POST',
                            mode: 'cors',
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify(data)
                        }).then(function(res) {
                            return res.json();
                        }).then(function(orderData) {
                            paypal_order_id = orderData.id;
                            paraty_logger.info(`[JS][PAYPAL][{{hotel_code}}][{{payment_order_id}}] init gateway corectly ${JSON.stringify(orderData)}`);
                            return orderData.id;
                        });
                    } else {
                        paraty_logger.info(`[JS][PAYPAL][{{hotel_code}}][{{payment_order_id}}] personal details form is not valid`);
                    }
                },
                onApprove: function(data, actions) {
					paraty_logger.info(`payment approved ${JSON.stringify(data)}`);
                    if(data.orderID != "") {
                        paypal_order_id = data.orderID;
                    }
                    paypal_payer_id = data.PayerID;
					if(window.show_booking_search_popup != undefined){
                        show_booking_search_popup();
                    }
                    $("#submitButton, #btn-finish-booking").click();
                },
                onError: function(err){
                    paraty_logger.info(`Error sended by paypal button ${err}`);

                }
            });

            paraty_logger.info(`init button corectly ${JSON.stringify(button)}`);

            if (button.isEligible()) {
                // Render the standalone button for that funding source
                button.render('#paypal-button-container');
            }
        });

    }catch (e) {
        paraty_logger.info(`Error creating paypal button ${e}`);
	}

    {% endif %}

    tpv_cobrador_controller = function() {
        return {
            execute_controller: function () {
				var url_to_redirect = "{{ merchant_url|safe }}&PayerID=" + paypal_payer_id + "&orderID=" + paypal_order_id;
                paraty_logger.info(`redirecting to ${url_to_redirect}`);
                window.location.href = url_to_redirect;
            }
        }
    }();

    function sumFirstNDays(list, n) {
      let sum = 0;
      for (let i = 0; i < n && i < list.length; i++) {
        const num = parseFloat(list[i]); // Attempt to convert to float
        if (!isNaN(num)) { // Check if it's a valid number
          sum += num;
        } else {
          console.error("Element at index", i, "is not a valid number:", list[i]);
        }
      }
      return sum;
    }

    if($(".worldline_payment_form_container").length == 1 ){
        $("#destination_cobrador_form").append($(".worldline_payment_form_container"));
    }else {
        $("#destination_cobrador_form").append($(".paypal-container"));
    }

    function generate_name(){
        if(Boolean($("#personal-details-form").length)){
            var start_day = $(".booking_details_prices_wrapper .entry_date .day_number").text().trim();
            var start_month = $(".booking_details_prices_wrapper .entry_date .month_name").text().trim();
            var start_year = $(".booking_details_prices_wrapper .entry_date .year_number").text().trim();
            var end_day = $(".booking_details_prices_wrapper .departure_date .day_number").text().trim();
            var end_month = $(".booking_details_prices_wrapper .departure_date .month_name").text().trim();
            var end_year = $(".booking_details_prices_wrapper .entry_date .year_number").text().trim();

            var name = `${start_day} ${start_month} ${start_year} - ${end_day} ${end_month} ${end_year}`;
        }else{
            var name = $(".reservation_summary div.dates p").text().trim();
        }
        return name;
    }
    {% if amounts %}
        function calculate_amount_to_show(percent_to_pay) {
            if (document.querySelector(".tpv_message_informative .currencyValue") != null || document.querySelector(".tpv_text_wrapper .currencyValue") != null) {
                 if(document.querySelector(".tpv_message_informative .currencyValue") != null){
                    amount_to_pay = parseFloat(document.querySelector(".tpv_message_informative .currencyValue").getAttribute("latest_value"));
                    if(isNaN(amount_to_pay)){
                        amount_to_pay = parseFloat(document.querySelector(".tpv_message_informative .currencyValue").text());
                    }
                }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
                    amount_to_pay = parseFloat(document.querySelector(".tpv_text_wrapper .currencyValue").getAttribute("latest_value"));
                    if(isNaN(amount_to_pay)){
                        amount_to_pay = parseFloat(document.querySelector(".tpv_text_wrapper .currencyValue").text());
                    }
                }

                /*if (percent_to_pay.search("-day") == 1) {
                    num_days_to_pay = parseInt(percent_to_pay.replace("-day", ""));
                    value_to_show = (amount_to_pay / {{ num_nights }})*num_days_to_pay;
                } else {
                    value_to_show = (amount_to_pay / 100) * parseInt(percent_to_pay);
                }*/
                 if (percent_to_pay.search("-day") == 1) {
                    num_days_to_pay = parseInt(percent_to_pay.replace("-day", ""));
                    //by default, an avarage!
                    value_to_show = (amount_to_pay / {{ num_nights }}) * num_days_to_pay;
                    var prices_per_day = {};
                    {%  if prices_per_day %}
                         prices_per_day = {{ prices_per_day | tojson }};
                    {%  endif %}
                    if (!Object.keys(prices_per_day).length == 0) {
                        value_to_show = 0;
                        for (let room in prices_per_day) {
                            if (prices_per_day.hasOwnProperty(room)) {
                                let prices = prices_per_day[room];
                                value_to_show += sumFirstNDays(prices, num_days_to_pay);
                            }
                        }
                        value_to_show = Math.min(value_to_show, parseFloat(amount_to_pay));
                    }
                } else if(isNaN(percent_to_pay)){
                    value_to_show = amount_to_pay;
                } else {
                    value_to_show = (amount_to_pay / 100) * parseInt(percent_to_pay);
                }
                value_to_show = Math.round(value_to_show * 100) / 100

                if(document.querySelector(".tpv_message_informative .currencyValue") != null){
                    document.querySelector(".tpv_message_informative .currencyValue").innerHTML = value_to_show;
                }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
                    document.querySelector(".tpv_text_wrapper .currencyValue").innerHTML = value_to_show;
                }
                return value_to_show;
            }
        }

        $(".payment_option").on("click", (e)=>{
            if(!$(e.currentTarget).hasClass("selected-option")){
                $(".payment_option.selected-option").find(".installments_quantity").fadeOut();
                $(".payment_option.selected-option").find('.payment_radiobtn').prop('checked', false);
                $(".payment_option.selected-option").removeClass("selected-option");
                $(e.currentTarget).addClass("selected-option");
                $(e.currentTarget).addClass("selected-option").find('.payment_radiobtn').prop('checked', true);
                $(e.currentTarget).find(".installments_quantity").fadeIn();
                percent_to_pay = $(e.currentTarget).find("input").val();
                calculate_amount_to_show(percent_to_pay);
                if($(".b3_custom_text").html()){
                    let current_currency = $(".monedaConv").first().text();
                    let value_to_show = calculate_amount_to_show(percent_to_pay);
                    let selected_option = $(".payment_option.selected-option").find('.payment_radiobtn').prop('checked', false).val();
                    $(".b3_custom_text").find("#"+ selected_option).find("span").text(value_to_show);
                    let text_to_change = $(".b3_custom_text").find("#"+ selected_option).html();
                    if(document.querySelector(".tpv_message_informative") != null){
                        $(".tpv_message_informative").html(text_to_change);
                        $(".tpv_message_informative").find(".monedaConv.first_currency").text(current_currency);
                    } else{
                        $(".tpv_text_wrapper").find("p").html(text_to_change);
                        $(".tpv_text_wrapper").find(".monedaConv.first_currency").text(current_currency);
                    }

                };
            }
        });
        latest_value = $(".b3_custom_text").find("span[latest_value]:first").text();
        latest_currency = $(".b3_custom_text").find(".monedaConv.first_currency:first").text();
        if(document.querySelector(".tpv_message_informative") != null) {
            $(".tpv_message_informative").find(".currencyValue").attr("latest_value", latest_value);
        }else{
            $(".tpv_text_wrapper.has_pad").find(".currencyValue").attr("latest_value", latest_value);
            $(".tpv_text_wrapper.has_pad").find(".monedaConv.first_currency").text(latest_currency);
        }
        $(".payment_option:first").click();

        var priceObserver = new MutationObserver(mutations => {
            var selected_currency = $("#currencySelect :selected").data("shortname");
            if(selected_currency == undefined){
                selected_currency = $(".currency_selector .item_selected").text().trim()
            }
            for(let mutation of mutations){
                if( current_currency != selected_currency){
                    amount_to_pay = mutation.target.innerHTML;
                    current_currency = selected_currency;
                    calculate_amount_to_show(percent_to_pay);
                }
            }
        });

        if(document.querySelector(".tpv_message_informative .currencyValue") != null){
            var element_to_observe = document.querySelector(".tpv_message_informative .currencyValue");
        }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
            var element_to_observe = document.querySelector(".tpv_text_wrapper .currencyValue");
        }

        priceObserver.observe(
            element_to_observe,
            {
                childList: true,
                subtree: true
            }
        )
    {% endif %}
</script>
{% if merchant_name and fraudnet_token %}
<script type="application/json" fncls="fnparams-dede7cc5-15fd-4c75-a9f4-36c430ee3a99">
    {
        "f":"{{ fraudnet_token }}",
        "s":"{{ merchant_name }}"{% if test %},
        "sandbox":true{% else %},
        "sandbox":false{% endif %}
    }
</script>

<script type="text/javascript" src="https://c.paypal.com/da/r/fb.js"></script>
{% endif %}