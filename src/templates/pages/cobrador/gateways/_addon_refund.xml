<?xml version="1.0" encoding="UTF-8"?>
<request type="rebate" timestamp="{{ timestamp }}">
  <merchantid>{{ merchant_id }}</merchantid>
  <account>{{ account }}</account>
  <orderid>{{ order_id }}</orderid>
  <amount currency="{{ currency }}">{{ amount }}</amount>
  <pasref>{{ pasref }}</pasref>
  <authcode>{{ authcode }}</authcode>
  <refundhash>{{ refundhash }}</refundhash>
  <sha1hash>{{ sha1hash }}</sha1hash>
    {%  if dcc_info %}
        <dccinfo>
            <ccp>{{ dcc_info.ccp }}</ccp>
            <type>{{ dcc_info.type }}</type>
            <rate>{{ dcc_info.rate }}</rate>
            <ratetype>{{ dcc_info.rate_type }}</ratetype>
            <amount currency="{{ dcc_info.currency }}">{{ dcc_info.amount }}</amount>
        </dccinfo>
    {% endif %}
</request>