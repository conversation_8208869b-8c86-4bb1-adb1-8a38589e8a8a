{% if add_doc_headers %}
<!DOCTYPE html><html><head>
    {% for css_url in css_list %}
        <link rel="stylesheet" type="text/css" href="{{ css_url }}"/>
    {% endfor %}
    {% for js_url in jsLib_list %}
        <script type="text/javascript" src="{{ js_url }}"></script>
    {% endfor %}

</head><body>
{% endif %}
<form id="cobradorPaymentForm" action="{{ post_url }}" method="POST">
    <div id="card_details_wrapper">
        <div class="input_wrapper input_inline" style="display: flex;flex-wrap: wrap">
            <div class="input" style="width: 100%">
                <label for="expiryMonth" style="width: 100%">{{ T_numero_tarjeta }}</label>
                <input type="text" id="cardNumber" name="cardNumber" placeholder="1234 5678 ..." class="booking-form-control required">
            </div>
            <label for="expiryMonth" style="width: 60%;padding-left: 0">{{ T_fecha_caducidad }}</label>
            <label for="expiryMonth" style="width: 40%">{{ T_cvv }}</label>
            <div class="input select" style="width: 30%">
                <select name="expiryMonth" class="booking-form-control-inline" id="expiryMonth">
                   {% for number_element in range(1, 13) %}
                        <option value="{{ number_element }}">{{ number_element }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="input select" style="width: 30%">
                <select name="expiryYear" class="booking-form-control-inline" id="expiryYear">
                    {% for i in credit_cards_years %}
                        <option value="{{ i }}">{{ i }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="input" style="width: 40%;{% if hide_cvv %}display:none;{% endif %}">
                <input type="text" name="cvv" class="booking-form-control required" placeholder="***"/>
                <div class="extra_buttons toolkit top-left">
                    <i class="fad fa-info-square"></i>
                    <div class="tooltip">{{ T_que_es_cvv }}</div>
                </div>
            </div>
            <div class="input" style="width: 100%">
                <label for="ccOwnerName">{{ T_cc_owner_name }}</label>
                <input type="text" id="ccOwnerName" name="ccOwnerName" placeholder="{{ T_NAME }}" class="booking-form-control required"/>
            </div>
             <div class="input" style="width: 100%">
                <label for="billingAddress">{{ T_full_address }}</label>
                <input type="text" id="billingAddress" name="billingAddress" placeholder="{{ T_ADDRESS }}" class="booking-form-control required"/>
            </div>
        {% if not hide_billingZipCode %}
             <div class="input" style="width: 100%">
                <label for="billingZipCode">{{ T_codigo_postal }}</label>
                <input type="text" id="billingZipCode" name="billingZipCode" placeholder="****" class="booking-form-control required"/>
            </div>
        {% endif %}
        {% if send_email_to_tpv %}
            <div class="input" style="display: none">
                <input type="text" id="customerEmail" name="customerEmail" class="booking-form-control"/>
            </div>
        {% endif %}
        {% if currency %}
            <div class="input" style="display: none">
                <input type="text" id="currency" name="currency" class="booking-form-control" value="{{ currency }}"/>
            </div>
        {% endif %}
        </div>
    </div>
    <div class="cvv_wrapper form_message_wrapper" style="display: none">
        <div id="cvv-help">
        <div class="close"><i class="fal fa-times"></i></div>
            <p>{{ T_cvv_visa_mastercard }}</p>
            {% if american_express %}
                <h3>{{ T_american_express }}</h3>
                <p>{{ T_cvv_american_express }}</p>
            {% endif %}
        </div>
    </div>
    <div class="wait-for-response_wrapper form_message_wrapper" style="display: none">
        <div id="wait-for-response">
            <h3>{{ T_PROCESANDO }}</h3>
            <h3>{{ T_ESPERE }}</h3>
            <img src="/static/images/spinner.gif">

        </div>
    </div>
    <div class="error_message_wrapper form_message_wrapper" style="display: none">
        <div id="error_message">
            <div class="close"><i class="fal fa-times"></i></div>
            <h3>{{ T_TITLE_EMAIL_PAYMENT_ERROR }}</h3>
            {{ T_error_payment_TPV }}
            <h3>{{ T_ERROR_FROM_GATEWAY }}:</h3>
            <p id="codigo_error_pasarela"></p>
        </div>
    </div>
    {% if add_button %}
        <button type="submit" class=" booking-button booking-button--confirm-booking" id="submitButton">
            <span>{{ T_DO_PAY }}</span></button>
    {% endif %}
    <input type="hidden" id="hotel_code" name="hotel_code" value="{{ hotel_code }}">
    <input type="hidden" id="amount" name="amount" value="{{ amount }}">
    <input type="hidden" id="sid" name="sid" value="{{ sid }}">
    <input type="hidden" id="payment_order_id" name="payment_order_id" value="{{ payment_order_id }}">
     {% if date_card_validator_year and date_card_validator_month %}
            <input type="hidden" id="date_card_validator_year" value="{{ date_card_validator_year }}"/>
            <input type="hidden" id="date_card_validator_month" value="{{ date_card_validator_month }}"/>
        {% endif %}
    <input type="hidden" id="security_str" name="security_str" value="{{ security_str }}">
</form>
 <script>
    var $paymentForm = $("#cobradorPaymentForm"),
    $cvv_wrapper_toggle = $paymentForm.find(".extra_buttons"),
    $cvv_wrapper = $paymentForm.find(".cvv_wrapper"),
    $cvv_wrapper_close = $cvv_wrapper.find(".close");

    $cvv_wrapper_toggle.click(function () {
        $cvv_wrapper.fadeIn();
    })
    $cvv_wrapper_close.click(function () {
         $cvv_wrapper.fadeOut();
    })

    $wait_wrapper = $paymentForm.find(".wait-for-response_wrapper"),
    $wait_wrapper_close = $wait_wrapper.find(".close");

    $error_wrapper = $paymentForm.find(".error_message_wrapper"),
    $error_wrapper_close = $error_wrapper.find(".close");
    $error_wrapper_close.click(function () {
         $error_wrapper.fadeOut();
    })

    $paymentForm.submit(function(e) {
        e.preventDefault();
            add_email_to_form();
            var form = $(this);
            var url = form.attr('action');
            if (validate_form()){
                $wait_wrapper.fadeIn();
                $.ajax({
                       type: "POST",
                       url: url,
                       data: form.serialize(),
                       success: function(data)
                       {
                           if (data && "OK" == data){
                                window.parent.postMessage({ event_id: 'cobrador_redirect_ok'}, '*');
                                //document.location.href = "{{ ok_url }}";
                           }
                           else{
                               process_error_tpv(data)
                           }
                       },
                       error:function(jqXHR, textStatus, errorThrown){
                            process_error_tpv("{{ T_GENERIC_ERROR }}")
                       }
                });
            }
            else{
                window.parent.postMessage({ event_id: 'cobrador_redirect_ko'}, '*');
            }
    });
function process_error_tpv(error_txt){
   $("#codigo_error_pasarela").html(error_txt);
   $error_wrapper.fadeIn();
   $wait_wrapper.fadeOut();
   setTimeout(auto_close_error, 10000);
   window.parent.postMessage({ event_id: 'cobrador_redirect_ko'}, '*');
}
function auto_close_error(){
    $error_wrapper.fadeOut();
}
window.onmessage = function(e) {
    if (e.data.event_id == 'cobrador_submit') {
        $("#cobradorPaymentForm").submit();
    }
}
function validate_form(){
    var all_is_ok = true;
    $("#cobradorPaymentForm .required").each(function(){
        if (!$(this).val()){
            $(this).parent().addClass("input_ko");
            all_is_ok = false;
        }
        else{
            $(this).parent().removeClass("input_ko");
        }

    });
    var currentDate = new Date();
    var currentMonth = document.getElementById("date_card_validator_month").value;
    var currentYear = document.getElementById("date_card_validator_year").value;

    if(!currentMonth || !currentYear){
        currentMonth = currentDate.getMonth(currentDate)+1;
        currentYear = currentDate.getFullYear(currentDate)-2000;
    }

    currentMonth = parseInt(currentMonth);
    currentYear = parseInt(currentYear);

    var selectedMonth = document.getElementById("expiryMonth").value;
    selectedMonth = parseInt(selectedMonth);
    var selectedYear = document.getElementById("expiryYear").value;
    selectedYear = parseInt(selectedYear);

    if ((selectedMonth < currentMonth && (selectedYear <= currentYear )) || (selectedYear < currentYear )){
        all_is_ok = false;
        $("#expiryMonth").css("border", "1px solid red");
        $("#expiryYear").css("border", "1px solid red");
    }
    else{
        $("#expiryMonth").css("border", "1px solid #ededed");
        $("#expiryYear").css("border", "1px solid #ededed");
    }
   return all_is_ok;
}
function add_email_to_form(){
    var customer_email_input = $('#customerEmail')
    if (customer_email_input){
        customer_email_input.val($('#id_email').val())
    }
}

$(document).ready(function() {
     window.parent.postMessage({ event_id: 'cobrador_change_iframe_height', new_heigth: $("#cobradorPaymentForm").height()}, '*');
});
</script>
{% if add_doc_headers %}
</body></html>
{% endif %}