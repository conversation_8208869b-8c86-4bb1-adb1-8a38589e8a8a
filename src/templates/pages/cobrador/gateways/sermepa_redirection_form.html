<form id="paymentForm" action="{{ post_url }}" method="POST">
    <input type="hidden" name="Ds_SignatureVersion" value="{{ Ds_Merchant_Version_SHA256|safe}}"/>
    <input type="hidden" name="Ds_MerchantParameters" value="{{ Ds_Merchant_JsonDatasSHA256|safe }}"/>
    <input type="hidden" name="Ds_Signature" value="{{ Ds_Merchant_MerchantSignature|safe }}"/>
    {% if add_button %}
    <div class="cobrador_gateway_button"><button type="submit" value="Submit">Submit</button></div>
    {% endif %}
</form>
{% if custom_text_by_key %}
    <div class="sermepa_redirection_payment_form_container">
            <div style="display: none" class="b3_custom_text">
                {% for option, text in custom_text_by_key.items() %}
                    <div id="custom_text_key" amount="{{ custom_amounts_by_key[option] }}">
                        {{ text|safe }}
                    </div>
                {% endfor %}
            </div>
    </div>
{% endif %}



<script>
    if(document.querySelector(".tpv_message_informative .currencyValue") != null){
        var amount_to_pay = parseFloat(document.querySelector(".tpv_message_informative .currencyValue").innerHTML);
    }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
        var amount_to_pay = parseFloat(document.querySelector(".tpv_text_wrapper .currencyValue").innerHTML);
    }
</script>

<script>
    if($("#custom_text_key").html()){
        let current_currency = $(".monedaConv").first().text();
        let id_text_to_show = $(".b3_custom_text").find("div")[0].id;
        let text_to_change = $(".b3_custom_text").find("#"+ id_text_to_show).html();
        if(document.querySelector(".tpv_message_informative") != null){
            $(".tpv_message_informative").html(text_to_change);
            $(".tpv_message_informative").find(".monedaConv.first_currency").text(current_currency);
        } else{
            $(".tpv_text_wrapper").find("p").html(text_to_change);
            $(".tpv_text_wrapper").find(".monedaConv.first_currency").text(current_currency);
        }

    };
</script>

