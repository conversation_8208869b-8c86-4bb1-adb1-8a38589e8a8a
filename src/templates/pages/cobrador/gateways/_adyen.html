<script src="https://checkoutshopper-test.adyen.com/checkoutshopper/sdk/5.52.0/adyen.js"></script>
<link rel="stylesheet" href="https://checkoutshopper-test.adyen.com/checkoutshopper/sdk/5.52.0/adyen.css" />

<div id="dropin-container"></div>

<script>
    	const configuration = {
            clientKey: "{{ client_key }}",
            environment: "test",
            showPayButton: false, // Disable adyen's button
            analytics: {
                enabled: false  // Disable analytics to prevent CORS errors
            },
            amount: {
                value: {{ amount }},
                currency: '{{ currency }}'
            },
            paymentMethodsConfiguration: {
                card: {
                    hasHolderName: true,
                    holderNameRequired: true,
                    challengeWindowSize: '01'

                }
            },
            locale: '{{ locale }}',
            countryCode: '{{ country }}',
            // The full /paymentMethods response object from your server. Contains the payment methods configured in your account.
            paymentMethodsResponse: {{payment_methods | tojson }},
            onSubmit: async (state, component, actions) => {
                try {
                    // Make a POST /payments request from your server.
                    const result = await makePaymentsCall(state.data, '{{ country }}', '{{ locale }}', '{{encrypted_data}}' , '{{cobrador_signature}}', '{{hotel_code}}', '{{gateway_type}}', '{{ sid }}');

                    // If the /payments request from your server fails, or if an unexpected error occurs.
                    if (!result.resultCode) {
                        console.error('Payment failed.');
                        redirectWithError();
                        return;
                    }
                  if (result.action) {
                    component.handleAction(result.action); // Process action required payment method
                  } else {
                    redirectToMerchant(result.cobrador_response) // We have the payment response from adyen, we need to send it to cobrador
                  }
                    // If the /payments request request form your server is successful, you must call this to resolve whichever of the listed objects are available.
                    // You must call this, even if the result of the payment is unsuccessful.
                } catch (error) {
                    console.log(error)
                    console.error("onSubmit", error);
                    redirectWithError();
                }
            },
            onAdditionalDetails: async (state, component, actions) => {
                try {
                    {# 3Ds #}
                    // Make a POST /payments/details request from your server.
                    const result = await makePaymentsDetailsCall(state.data, '{{ country }}', '{{ locale }}', '{{encrypted_data}}' , '{{cobrador_signature}}', '{{hotel_code}}', '{{gateway_type}}', '{{ sid }}');
                    if (!result.resultCode) {
                        console.error('Payment failed.');
                        redirectWithError();
                        return;
                    }
                    if (result.action) {
                        component.handleAction(result.action); // Process action required payment method
                    } else {
                        redirectToMerchant(result.cobrador_response) // We have the payment response from adyen, we need to send it to cobrador
                    }

                    // If the /payments/details request from your server fails, or if an unexpected error occurs.
                    if (!result.resultCode) {
                        console.error('Payment failed.');
                        redirectWithError();
                    }}
                    catch (error) {
                    console.log(error)
                    console.error("onAdditionalDetails", error);
                    redirectWithError();
                }
            },
            onError: (error, component) => {
                console.error(error.name, error.message, error.stack, component);
                redirectWithError();
            }
        };
let dropin;

async function initCheckout() {
    const checkout = await AdyenCheckout(configuration);
    dropin = checkout.create('dropin').mount('#dropin-container');
}

initCheckout();

var tpv_cobrador_controller = function () {
    return {
        execute_controller: function () {
            if (dropin) {
                dropin.submit();
            } else {
                console.error("Dropin no está inicializado aún.");
                redirectWithError();
            }
        }
    };
}();




$("#destination_cobrador_form").append($("#dropin-container"));

// Helper function to redirect with error code when payment fails
function redirectWithError() {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('errorCode', 'PAYMENT');
    console.log('redirecting to '+ currentUrl.toString())
    window.location.href = currentUrl.toString();
}

function makePaymentsCall(state_data, country_code, locale, encrypted_data, signature, hotel_code, gateway_type, sid) {
    return new Promise((resolve, reject) => {
        // Prepare the request data
        const requestData = {
            encrypted_data: encrypted_data, // amount, currency, payment_order_id
            signature: signature,
            hotel_code: hotel_code,
            gateway_type: gateway_type,
            sid: sid,
            payment_data: state_data,
            country: country_code,
            locale: locale,
            origin: window.location.origin
        };
        
        // Make the AJAX call to the backend endpoint
        $.ajax({
            url: '{{ payment_seeker_url }}' + '/adyen/payments?sid={{ sid }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function(response) {
                console.log('Payment response:', response);
                resolve(response);
            },
            error: function(error) {
                console.error('Payment error:', error);
                redirectWithError();
                reject(error);
            }
        });
    });
}function makePaymentsDetailsCall(state_data, country_code, locale, encrypted_data, signature, hotel_code, gateway_type, sid) {
    return new Promise((resolve, reject) => {
        // Prepare the request data
        const requestData = {
            encrypted_data: encrypted_data, // amount, currency, payment_order_id
            signature: signature,
            hotel_code: hotel_code,
            gateway_type: gateway_type,
            sid: sid,
            payment_data: state_data,
            country: country_code,
            locale: locale,
        };

        // Make the AJAX call to the backend endpoint
        $.ajax({
            url: '{{ payment_seeker_url }}' + '/adyen/payments_details?sid={{ sid }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function(response) {
                console.log('Payment response:', response);
                resolve(response);
            },
            error: function(error) {
                console.error('Payment error:', error);
                redirectWithError();
                reject(error);
            }
        });
    });
}


function redirectToMerchant(cobradorResponse) {
    if (!cobradorResponse.cobrador_url || !cobradorResponse.cobrador_parameters || !cobradorResponse.cobrador_signature) {
        console.error("Faltan parámetros requeridos para la redirección.");
        redirectWithError();
        return;
    }

    const $form = $('<form>', {
        method: 'POST',
        action: cobradorResponse.cobrador_url
    });

    $('<input>', {
        type: 'hidden',
        name: 'cobrador_parameters',
        value: cobradorResponse.cobrador_parameters
    }).appendTo($form);

    $('<input>', {
        type: 'hidden',
        name: 'cobrador_signature',
        value: cobradorResponse.cobrador_signature
    }).appendTo($form);

    $form.appendTo('body').submit();
}
</script>