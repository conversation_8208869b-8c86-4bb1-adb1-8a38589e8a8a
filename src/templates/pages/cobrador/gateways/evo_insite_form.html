{#<script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>#}
<script src="https://evopaymentsmexico.gateway.mastercard.com/static/checkout/checkout.min.js" data-error="errorCallback" data-cancel="cancelCallback"></script>

<style>
    .payment_form_container{
      padding: 5px;
      max-width: 400px;
      margin: 0 auto 20px;
    }
    .payment_form_container .paypal-container{
        margin-top: 8px;
    }
    .payment_form_container .payment_option.selected-option{
    background-color:  #e8f2fa;
    border-color: #2196F3;
    z-index: 1;
    }
    .payment_form_container .payment_option .payment_radiobtn{
        appearance: none;
        position: absolute;
        top: 22px;
        left: 12px;
    }
    .payment_form_container .payment_option .payment_radiobtn:before{
        content: '';
        width: 10px;
        height: 10px;
        border: 1px solid #333333;
        position: absolute;
        top: -11px;
        left: -8px;
        border-radius: 50%;
    }
    .payment_form_container .payment_option.selected-option .payment_radiobtn:after{
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: #333333;
        border-radius: 50%;
        top: -9px;
        left: -6px;
    }
    .payment_form_container .payment_option label{
        font-size: 14px;
        margin-left: 5px;
        display: block;
        padding: 0px 56px 0px 0px;
    }
    .payment_form_container .payment_option{
    padding: 10px 50px 10px 30px;
    position: relative;
    border: 1px solid gray;
    transition: background .3s;
    }
    .payment_form_container .payment_option:only-child{
    border-radius: 5px;
    }
    .payment_form_container .payment_option:first-child:not(:only-child){
    border-radius: 5px 5px 0 0;
    }
    .payment_form_container .payment_option:last-child{
    border-radius: 0 0 5px 5px;
    }
    .payment_form_container .payment_option:not(:first-child){
        margin-top: -1px;
    }
    .payment_form_container .payment_option .cards_images{
        display: inline-block;
        width: 90px;
        height: auto;
        position: absolute;
        right: 5px;
        top: 20px;
        text-align: right;
        transform: translateY(-50%);
    }
    .payment_form_container .payment_option .cards_images img{
        width: calc((100% / 3) - 14px);
        height: auto;
        object-fit: cover;
        margin-right: 5px;
    }
    .payment_form_container .payment_option#msi-payment:after{
        content: '\f133';
        position: absolute;
        right: 10px;
        font-family: 'Font Awesome 5 Pro';
        font-size: 18px;
        top: 10px;
        font-weight: 100;
    }
    .payment_form_container .payment_option .payment_option_hide_info{
    max-height: 0;
    overflow: hidden;
    transition: max-height .5s;
    }
    .payment_form_container .payment_option.selected-option .payment_option_hide_info{
    max-height: 500px;
    }
    .payment_form_container .payment_option .payment_option_description{
        font-weight: lighter;
        border-top: 1px solid rgba(0,0,0,0.2);
        padding-top: 10px;
        font-size: 12px;
        line-height: 22px;
    }
    .payment_form_container .payment_option .msi_payment_select_wrapper{
    position: relative;
    margin-top: 10px;
    }
    .payment_form_container .payment_option .msi_payment_select_wrapper .installments_quantity_label{
    position: absolute;
    top: -6px;
    left: 10px;
    font-size: 11px;
    padding: 0 5px;
    background: white;
    transition: background .3s;
    }
    .payment_form_container .payment_option.selected-option .msi_payment_select_wrapper .installments_quantity_label{
    background: #e8f2fa;
    }
    .payment_form_container .payment_option .installments_quantity{
    padding: 12px 10px 8px ;
    border-radius: 5px;
    background: transparent;
    font-size: 14px;
    appearance: none;
    width: 100%;
    }
    .payment_form_container .payment_option .msi_payment_select_wrapper:after{
    content: '\f107';
    position: absolute;
    right: 10px;
    font-family: "Font Awesome 5 Pro";
    font-size: 24px;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 100;
    }
    .payment_form_container .payment_option .installments_quantity div{
    margin: 5px;
    }
    .payment_form_container .payment_option .installments_quantity div input[type="radio"]{
    width: inherit;
    margin-right: 5px;
    height: inherit;
    }
</style>
<div id="embed-target"> </div>
<script>

    Checkout.configure({
            session: {
              id: '{{ sessionid }}'
            }
        });

    function errorCallback(error) {
        console.log(JSON.stringify(error));
    }
    function cancelCallback() {
		console.log('Payment cancelled');
    }

    tpv_cobrador_controller = function() {
        return {
            execute_controller: function() {
				{% if from_callcenter %}
                    var open_tab_cheker = window.open("data:text/html;base64,PCFET0NUWVBFIGh0bWw+PGh0bWwgbGFuZz0iZW4iPjxoZWFkPjxtZXRhIGNoYXJzZXQ9IlVURi04Ij48bWV0YSBuYW1lPSJ2aWV3cG9ydCIgY29udGVudD0id2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCI+PHRpdGxlPkJsYW5rPC90aXRsZT48L2hlYWQ+PGJvZHk+PHNjcmlwdD53aW5kb3cuY2xvc2UoKTs8L3NjcmlwdD48L2JvZHk+PC9odG1sPiA=", "_blank");
                    if(open_tab_cheker != null){
						var gateway_tab = window.open("https://evopaymentsmexico.gateway.mastercard.com/checkout/pay/{{ sessionid|safe }}", "_blank");
                        if(gateway_tab != null){
                            window.location.href = "{{ confirm_url|safe }}";
                        }
					}else{
						alert("Necesitamos permiso para abrir una pestaña nueva");
					}

				{% else %}
                    {#Checkout.showPaymentPage();#}
                    window.location.href = "https://evopaymentsmexico.gateway.mastercard.com/checkout/pay/{{ sessionid|safe }}";
				{% endif %}
            }
        }
    }();
</script>
