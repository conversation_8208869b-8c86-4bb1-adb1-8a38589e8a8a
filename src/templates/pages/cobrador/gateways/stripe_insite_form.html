{#<script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>#}
<style>
    .error-box {
        background-color: #ff9e9e;
        border: 2px solid #ff3636;
        padding: 2px 4px;
        margin: 2px;
        border-radius: 5px;
        text-align: center;
        display: none;
    }
    .error-text {
        font-size: 18px;
        color: #ff3636;
    }
    .pep_links_spinner_wrapper {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

    .pep_links_spinner_wrapper.peplinks_active_spinner::before {
        content:'';
        position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: white;
            opacity: .5;
    }

    .stripe_message_text{
        background-color: #6c8eef;
        color:#efefef;
        display: none;
        font-size: 2em;
        border-radius: 10px;
        padding: 8px 20px;
        margin-bottom: 20px;
        max-width: 50vw;
        line-height: 1.1;
        justify-content: stretch;
        z-index: 1500;
    }

    .stripe_message_text.error{
        background-color: #ff9e9e;
        border: 2px solid #ff3636;
        color:#ff3636;
    }

    #warning-message, #error-message{
        display: none;
    }

    .stripe_message_text h3{
        margin: 0.8vh 0px;
    }


    .peplinks_spinner{
          height: 60px;
          width: 60px;
          border: 5px solid rgba(0, 0, 0, .1);
          border-top-color: #6c8eef;
          border-radius: 50px;
          animation: spin 1s ease infinite;
        }
        @keyframes spin{
          to{ transform: rotate(360deg); }
        }
    #stripeCustomFields{
        margin-bottom: 0.75rem;
    }

    #stripeCustomFields .Input::placeholder, #stripeCustomFields .p-Input--placeholder {
        color: #757680;
    }

    #stripeCustomFields input::placeholder {
        color: darkgray;
        opacity: 1;
    }

    #stripeCustomFields .Input {
        padding: 0.75rem;
        width: calc(100% - 24px);
        background-color: #fff;
        border-radius: 5px;
        transition: background 0.15s ease, border 0.15s ease, box-shadow 0.15s ease, color 0.15s ease;
        border: 1px solid #e6e6e6;
        box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.03), 0px 3px 6px rgba(0, 0, 0, 0.02);
    }

    #stripeCustomFields input {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border-radius: 0;
        border-style: none;
        box-shadow: none;
        color: inherit;
        -webkit-filter: none;
        filter: none;
        font: inherit;
        letter-spacing: inherit;
        outline-offset: 0;
        outline-width: 2px;
    }

    #stripeCustomFields .Label {
        margin-bottom: 0.25rem;
        font-size: 0.93rem;
        transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), opacity 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    }

    #stripeCustomFields .p-FieldLabel {
        display: block;
    }
</style>
<script src="https://js.stripe.com/v3/"></script>
<div class="stripe_payment booking-box booking-box--form v2">
  <form id="paymentForm">
    <div class="pep_links_spinner_wrapper peplinks_active_spinner">
        <div class="stripe_message_text">
            <div id="warning-message">
                <h3>{{ T_WARNING_TITLE }}</h3>
                {{ T_WARNING_MESSAGE }}
            </div>
            <div id="error-message">
            </div>
        </div>
        <div id="pep_links_spinner" ></div>
    </div>
      <div id="stripe-error" class="error-box">
        <p class="error-text"></p>
    </div>
      {% if ask_billing_info %}
      <div id="stripeCustomFields">
            <div data-field="name" class="p-Field">
                <label class="p-FieldLabel Label Label--empty" for="stripe_billing_name">{{ billing_name_label }}</label>
                <div>
                    <input type="text" autocomplete="billing name" placeholder="{{ billing_name_placeholder }}" aria-invalid="false" aria-required="true" class="p-Input-input Input Input--empty stripe_billing_name" value="">
                </div>
            </div>
        </div>
    {% endif %}
    <div id="payment-element">
    <!-- Elements will create form elements here -->
    </div>
  </form>
</div>
<script>
  function utf8_to_b64(str) {
        console.log("response: " + str);
        console.log(unescape(encodeURIComponent(str)));
        return window.btoa(unescape(encodeURIComponent(str)));
    }

    window.mobileCheck = function() {
      let check = false;
      (function(a){if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0,4))) check = true;})(navigator.userAgent||navigator.vendor||window.opera);
      return check;
    };
    window.onload = ()=>{
        const stripe = Stripe("{{public_key}}");
        const form = document.getElementById('paymentForm');

		{% if ask_billing_info %}
		$("#personal-details-form, #personalDetailsForm, .personal_details_form").validate().settings.ignore += " , .stripe_billing_name";
		{% endif %}
        const options = {
            clientSecret: '{{client_secret}}',
            // Fully customizable with appearance API.
            appearance: {},
            locale: '{{lang}}'
        };
        // Set up Stripe.js and Elements to use in checkout form, passing the client secret obtained in step 2
        const elements = stripe.elements(options);

		var payment_element_options = {};


		{% if ask_billing_info %}
            payment_element_options["fields"] = {
				billingDetails:{
					name: "never"
                }
            }

        {% endif %}
        // Create and mount the Payment Element
        const paymentElement = elements.create('payment', payment_element_options);


        paymentElement.on('ready', function(event) {
            if(window.enable_payment_button != undefined ) {
                enable_payment_button($("#personal-details-form, #personalDetailsForm"));
            }
            $(".pep_links_spinner_wrapper").css("display", "none").removeClass("peplinks_active_spinner");
            $(".pep_links_spinner_wrapper .stripe_message_text").css("display", "none");
            $("#pep_links_spinner").removeClass("peplinks_spinner");
        });

        paymentElement.mount('#payment-element');

      $(form).on("submit", (event) =>{
            event.preventDefault();
            event.stopPropagation();

			var payment_data = {};

		    {% if ask_billing_info %}
			payment_data["billing_details"] = {
                name: document.querySelector("input.stripe_billing_name").value || ""
            };
		    {% endif %}

          {% if normal_payment %}
            const error = stripe.confirmPayment({
                elements,
                confirmParams: {
                    return_url: '{{url_merchant | safe}}',
                    payment_method_data: payment_data
                }
            });
          {% else %}
              const error = stripe.confirmSetup({
                elements,
                confirmParams: {
                    return_url: '{{url_merchant | safe}}',
                    payment_method_data: payment_data
                }
            });
          {% endif %}

            error.then((err)=>{
                var messageContainer = document.querySelector('.pep_links_spinner_wrapper .stripe_message_text #error-message');
                messageContainer.innerHTML = err.error.message;
                if(window.form_controller != undefined ) {
                    form_controller.enable_payment_button();
                }
                var new_error_code = "PAYMENT";

                var new_error_force = utf8_to_b64(err.error.message);
                {#var base64Encoded = btoa(err.error.message); // Base64 encode#}
                {#var new_error_force = encodeURIComponent(base64Encoded);#}
                
                var url =  location.href;

                var regex = /([&?]errorCode=)[^&]*/;
                var regex_1 = /([&?]errorForcedMessage=)[^&]*/;

                if (regex.test(url)) {

                    url = url.replace(regex, "$1" + new_error_code);
                } else {

                    url += (url.indexOf('?') !== -1 ? '&' : '?') + "errorCode=" + new_error_code;
                }



                if (regex_1.test(url)) {

                    url = url.replace(regex_1, "$1" + new_error_force);
                } else {

                    url += (url.indexOf('?') !== -1 ? '&' : '?') + "errorForcedMessage=" + new_error_force;
                }




                window.location.href = url;


            });
            return false;
        });
        if(window.disable_payment_button != undefined ) {
            disable_payment_button($("#personal-details-form, #personalDetailsForm"));
        }
        $(".pep_links_spinner_wrapper").css("display", "flex").addClass("peplinks_active_spinner");
        $("#pep_links_spinner").addClass("peplinks_spinner");

      tpv_cobrador_controller = function() {
        return {
          execute_controller: function() {
              if($("#personal-details-form, #personalDetailsForm, .personal_details_form").length && $("#personal-details-form, #personalDetailsForm, .personal_details_form").valid()){

				  var all_ok = true;


				  {% if ask_billing_info %}

				  if(document.querySelector("input.stripe_billing_name").value == ""){
					  all_ok = false;
				  }

				  {% endif %}

				  if(all_ok){
					  $(".pep_links_spinner_wrapper").css("display", "flex").addClass("peplinks_active_spinner");
                      $(".pep_links_spinner_wrapper .stripe_message_text").css("display", "block");
                      $(".pep_links_spinner_wrapper .stripe_message_text #warning-message").css("display", "block");
                      $("#pep_links_spinner").addClass("peplinks_spinner");
                      $("html, body").animate({scrollTop: 0}, "slow");
                      {#$("#stripe-error").slideUp();#}
                      $(form).submit();
                  } else {
                      if (window.form_controller != undefined) {
                          form_controller.enable_payment_button();
                      }
                  }
              }
          }
        }
      }();
    }
</script>
<script>
    $("#destination_cobrador_form").append($(".stripe_payment"));
    {% if is_spa %}
    setTimeout(function(){
        window.onload();
    }, 400)
    {% endif %}
</script>