<script type="text/javascript" src="https://checkout.epayco.co/checkout.js"></script>
<script>
    var handler = ePayco.checkout.configure({
        key: '{{ key }}',
        test: {{ "true" if test else "false" }}
    });

    var data = {
        //Parámetros compra (obligatorio)
        name: "{{ name }}",
        description: "{{ description }}",
        invoice: "{{ order_id }}",
        currency: "{{ currency }}",
        amount: "{{ amount }}",
        country: "co",
        lang: "{{ language }}",
        external: "true",

        response: "{{ merchant_url|safe }}",
        rejected: "{{ rejected_url|safe }}",
        confirmation: "{{ confirmation_url|safe }}",

    };

    var tpv_cobrador_controller = function () {
        return {
            execute_controller: function () {
                var form = $("#personal-details-form, #personalDetailsForm")
                data.name_billing = form.find("input[name=firstName]").val() + " " + form.find("input[name=lastName1]").val();
                data.email_billing = form.find("input[name=email]").val();

                if(form.find("input[name=address]").length) {
                    data.address_billing = form.find("input[name=address]").val()
                }

                data.mobilephone_billing = form.find("input[name=telephone]").val()

                if(form.find("input[name=personalID]").length) {
                    data.number_doc_billing = form.find("input[name=personalID]").val()
                }

                console.log(data);
                handler.open(data);
            }
        }
    }();
</script>