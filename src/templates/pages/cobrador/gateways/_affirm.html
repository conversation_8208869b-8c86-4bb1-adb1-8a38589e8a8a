{# jinja2 #}
<script src="https://cdn1-sandbox.affirm.com/js/v2/affirm.js"></script>
<script>
var _affirm_config = {
    public_api_key: "{{public_api_key}}",  /* <PERSON>emplazar con tu clave pública */
    script: "https://cdn1-sandbox.affirm.com/js/v2/affirm.js"
};

(function(l,g,m,e,a,f,b){
    var d,c=l[m]||{},h=document.createElement(f),n=document.getElementsByTagName(f)[0],
    k=function(a,b,c){return function(){a[b]._.push([c,arguments])}};
    c[e]=k(c,e,"set");d=c[e];c[a]={};c[a]._=[];d._=[];c[a][b]=k(c,a,b);a=0;
    for(b="set add save post open empty reset on off trigger ready setProduct".split(" ");a<b.length;a++) d[b[a]]=k(c,e,b[a]);
    a=0;
    for(b=["get","token","url","items"];a<b.length;a++) d[b[a]]=function(){};
    h.async=!0;h.src=g[f];n.parentNode.insertBefore(h,n);delete g[f];d(g);l[m]=c
})(window,_affirm_config,"affirm","checkout","ui","script","ready");


var tpv_cobrador_controller = function () {
    return {
        execute_controller: function() {
            var paymentForm = $("#paymentForm");
            var checkout_response = initiate_checkout();
        }
    }
}();

function initiate_checkout(){
    var full_name = $('[name=firstName]').val() + ' ' + $('[name=lastName1]').val();
    affirm.checkout({
       "merchant": {
          "user_cancel_url": "{{url_ko}}",
          "user_confirmation_url": "{{do_payment_by_post}}&hotel_code={{ hotel_code }}",
          "user_confirmation_url_action": "POST",
          "use_vcn": "true",
          "name": "Your Customer-Facing Merchant Name"
       },
       "metadata": {
          "mode": "modal"
       },
       "shipping": {
          "name": {
             {#"full": "{{full_name}}"#}
             "full": "123 213"
          },
          "address": {
             "line1": "225 Bush Street",
             "city": "San Francisco",
             "state": "CA",
             "zipcode": "94104"
          }
       },
       "items": [{
          "display_name": "Purchase",
          "sku": "purchase",
          "unit_price": {{ amount }},
          "qty": 1
       }],
       "currency": "{{currency}}",
       "shipping_amount": {{ amount }},
       "tax_amount": 0,
       "total": {{ amount }}
    });

    affirm.checkout.open({
        onFail: function(error){
            console.log("Error: ", error);
        }
    });

    $("#submitButton").prop("disabled", false);
}
</script>
