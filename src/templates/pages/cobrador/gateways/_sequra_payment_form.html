<div class="sequra-promotion-widget" data-type="text" data-alignment="left" data-amount="{{price}}" data-product="pp3" data-size="M" data-branding="green" style="margin-bottom:25px;"></div>

<div id="popup_sequra_payment_wrapper" class="fs" style="display: none;">
</div>
<input type="hidden" name="payment_order_id" value="{{ payment_order_id }}">

<script>
    function getFormSequra() {

		var personal_details = $("#personal-details-form, #personalDetailsForm");

        var hotel = "{{ hotel_name }}";
        var checkin = $("#internalStartDate").val();
        var checkout = $("#internalEndDate").val();
        var name = personal_details.find('input[name=firstName]').val();
        var surname = personal_details.find('input[name=lastName1]').val();
        var email =  personal_details.find('input[name=email]').val();
        var nin =  personal_details.find('input[name=personalID]').val();
        var sid = "{{ sid }}";
        var price = {{ price }};
        var currency = "{{ currency }}";
        var namespace = $('input[name=namespace]').val();
        var payment_order_id = $('input[name=payment_order_id]').val();

        var form_params = {
            'hotel': hotel,
            'checkin': checkin,
            'checkout': checkout,
            'email': email,
            'name': name,
            'surname': surname,
            'price': price,
            'currency': currency,
            'nin': nin,
            'payment_order_id': payment_order_id
        }

        $.post('https://payment-seeker.appspot.com/sequra/form?sid=' + sid + '&namespace=' + namespace, form_params, function (response) {
            $('#popup_sequra_payment_wrapper').html(response);
            if (response) {

                window.SequraFormElement = 'sq-identification-pp3';

                var src = response.match("<script src='(.*)'><\/script>");

                console.log("loading script: " + src[1]);
                $.getScript(src[1], function(){
                   var sequraCallbackFunction = function() {
                        done = false;
                        $("#submitButton").prop("disabled", false);
                        $("#popup_sequra_payment_wrapper").fadeOut();
                    }
                    window.SequraFormInstance.setCloseCallback(sequraCallbackFunction);
                    window.SequraFormInstance.show();
                });

            }

        })

    }
    var tpv_cobrador_controller = function () {
        return {
            execute_controller: function() {
                getFormSequra()
                $("#popup_sequra_payment_wrapper").fadeIn();
            }
        }
    }();

    var sequraConfigParams = {
        merchant: "{{ merchant }}",            // Your merchant reference given by SeQura.
        assetKey: "{{ asset_key }}",           // Your asset key given by SeQura.
        products: ["pp3"],                     // List of SeQura products that you want to include components.
        scriptUri: "{{ url_asset }}",          // SeQura Javascript library uri for production or sandbox.
        decimalSeparator: ",",                 // Decimal separator used in currencies formatting. Optional, default `,`.
        thousandSeparator: ".",                // Thousand separator used in currencies formatting. Optional, default `.`.
        rebranding: true
    };

    (function(i, s, o, g, r, a, m) {
        i["SequraConfiguration"] = g;
        i["SequraOnLoad"] = [];
        i[r] = {};
        i[r][a] = function(callback) {
            i["SequraOnLoad"].push(callback);
        };
        (a = s.createElement(o)), (m = s.getElementsByTagName(o)[0]);
        a.async = 1;
        a.src = g.scriptUri;
        m.parentNode.insertBefore(a, m);
    })(window, document, "script", sequraConfigParams, "Sequra", "onLoad");



</script>
<script>
    $("#destination_cobrador_form").append($(".sequra-promotion-widget"));
    {% if is_spa %}
    setTimeout(function(){
        window.onload();
    }, 400)
    {% endif %}
</script>