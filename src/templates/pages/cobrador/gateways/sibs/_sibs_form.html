<div class="sibs_payment_selector booking-box booking-box--form v2">
    <div class="booking-box__content payment_gateway_info_box">
        <input type="hidden" class="sibs_option_selected" name="sibs_option_selected">
        <div id="payment_card" class="card_payment_method sibs_payment_wrapper payment_option_wrapper"
             onclick="sibs_controller.changePayment(sibs_controller.PAYMENT_CARD, this);">
            <div class="method_title">
                <div class="title">{{ title_card|safe if title_card else T_Pago_Tarjeta_SIBS }}</div>
                <div class="logo_card">
                    <img src="https://cdn2.paraty.es/test-backend2/images/0a2d6398e183112=s30">
                    <img src="https://cdn2.paraty.es/test-backend2/images/e4dafdba0beb2b8=s30">
                </div>
            </div>
            <div class="payment_card_clone_wrapper" style="display: none">
                <div class="input_card">
                    <input type="text" data-to_clone="card.holder" name="card.holder" class="wpwl-control"
                           placeholder="{{ T_cc_owner_name }}" autocomplete="off">
                </div>
                <div class="input_card">
                    <input type="text" data-to_clone="card.number" name="card.number" class="wpwl-control"
                           placeholder="{{ T_numero_tarjeta }}" autocomplete="off">
                </div>
                <div class="input_card">
                    <select name="paymentBrand" data-to_clone="paymentBrand" class="wpwl-control">
                        {% for card_type, card_name in cards_types.items() %}
                            <option value="{{ card_type }}">{{ card_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="input_card">
                    <input type="text" data-to_clone="card.expiry" name="card.expiry" autocomplete="off"
                           class="wpwl-control wpwl-control-expiry" placeholder="{{ T_expiry_date_mm_yy }}">
                </div>
                <div class="input_card">
                    <input type="text" data-to_clone="card.cvv" name="card.cvv" autocomplete="off"
                           class="wpwl-control" placeholder="{{ T_cvv }}">
                </div>

                {% if sibs_messages and sibs_messages.message_card %}
                    <div class="card_message">{{ sibs_messages.message_card|safe }}</div>
                {% endif %}
            </div>
        </div>
        {% if mbway_payment %}
        <div id="payment_mbway" class="mbway_payment_method sibs_payment_wrapper payment_option_wrapper"
             onclick="sibs_controller.changePayment(sibs_controller.PAYMENT_MBWAY, this);">
            <div class="method_title">
                <div class="title">{{ title_mbway|safe if title_mbway else "MB Way" }}</div>
                <div class="logo_card">
                    <img src="https://cdn2.paraty.es/test-backend2/images/346d64300f3f2c6=s50">
                </div>
            </div>

            <div class="payment_card_clone_wrapper" style="display: none">
                <div class="input_card">
                    <input type="text" name="virtualAccount.accountId" placeholder="{{ T_telefono }}"
                        data-to_clone="virtualAccount.accountId" autocomplete="off">
                </div>

                {% if sibs_messages and sibs_messages.message_mbway %}
                    <div class="card_message">{{ sibs_messages.message_mbway|safe }}</div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% if sibs_multibanco_payment %}
        <div id="payment_multibanco" class="sibs_multibanco_method sibs_payment_wrapper payment_option_wrapper"
             onclick="sibs_controller.changePayment(sibs_controller.PAYMENT_MULTIBANCO, this);">
            <div class="method_title">
                <div class="title">{{ title_multibanco|safe if title_multibanco else "Multibanco" }}</div>
                <div class="logo_card">
                    <img src="https://cdn2.paraty.es/test-backend2/images/fdd673d8c3294cc=s30">
                </div>
            </div>
            <div class="payment_card_clone_wrapper" style="display: none">
                {% if sibs_messages and sibs_messages.message_multibanco %}
                    <div class="card_message">{{ sibs_messages.message_multibanco|safe }}</div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>

</div>

<div class="hidden_sibs_form" style="display: none">
    <form action="{{ shop_result_url }}" class="paymentWidgets" data-brands="VISA MASTER AMEX" novalidate></form>
    {% if mbway_payment %}
    <form action="{{ shop_result_url }}" class="paymentWidgets" data-brands="MBWAY" novalidate></form>
    {% endif %}
    {% if sibs_multibanco_payment %}
    <form action="{{ shop_result_url }}" class="paymentWidgets" data-brands="SIBS_MULTIBANCO" novalidate></form>
    {% endif %}
</div>

<script>
    $("#destination_cobrador_form").append($(".sibs_payment_selector"));
    $(".payment_option_wrapper.to_reload.sibs").remove();
</script>


<script>
    $(window).on("load", function () {
        $("body").append('<script src="{{ url }}">');
        $("body").append($(".hidden_sibs_form"));
    });
    var sibs_controller = function () {
        return {
            PAYMENT_CARD: "SIBS_PAYMENT_CARD",
            PAYMENT_MULTIBANCO: "SIBS_PAYMENT_MULTIBANCO",
            PAYMENT_MBWAY: "SIBS_PAYMENT_MBWAY",
            payment: NaN,
            amount: 0,
            currency: "",
            changePayment: function (payment, option_selected) {

                $("#errorPaymentSibs").hide();
                var option_wrapper = $(option_selected);
                if(option_wrapper.hasClass("active")) {
                    return false;
                }
                $(".sibs_payment_wrapper").removeClass("active");
                option_wrapper.addClass("active");
                sibs_controller.payment = payment

                var booking_button = $("#submitButton");
                if (booking_button.length === 0) {
                    booking_button = $(".personal_details_form_wrapper #btn-finish-booking");
                }

                booking_button.show()
                $(".payment_card_clone_wrapper").slideUp();
                option_wrapper.find(".payment_card_clone_wrapper").slideDown();
            },
        }
    }();

    function validateHolder(e) {
        var holder = $('.wpwl-control-cardHolder').val();
        if (holder.trim().length < 2) {
            $('.wpwl-control-cardHolder').addClass('wpwl-has-error').after('<div class="wpwl-hint wpwl-hint-cardHolderError">{{ T_campo_obligatorio }}</div>');
            return false;
        }
        return true;
    }

    var wpwlOptions = {
        paymentTarget: "_top",
        disableSubmitOnEnter: true,
        style: "plain",
        locale: "{{ language }}",
        onReady: function () {
            var card_number = "<input name=\"card.number\" type=\"hidden\" value=\"\" autocomplete=\"off\">",
                cvv_number = "<input name=\"card.cvv\" type=\"hidden\" value=\"\" autocomplete=\"off\">";

            $('form.wpwl-form-card').append(card_number);
            $('form.wpwl-form-card').append(cvv_number);

            $('.wpwl-form-card').find('.wpwl-button-pay').on('click', function (e) {
                validateHolder(e);
            });

            $(".payment_card_clone_wrapper input").each(function () {
                var data_to_clone = $(this).data("to_clone");
                $(this).on("keyup", function () {
                    if (data_to_clone === "card.expiry") {
                        var date_split = $(this).val().split("/");
                        if (date_split.length === 2) {
                            $(".wpwl-form-card input[name='card.expiryMonth']").val(date_split[0].trim());
                            $(".wpwl-form-card input[name='card.expiryYear']").val("20" + date_split[1].trim());
                        }
                    } else if (data_to_clone === "card.number") {
                        $(".wpwl-form-card input[name='card.number']").val($(this).val().replaceAll(" ", "").trim());
                    } else {
                        $(".hidden_sibs_form input[name='" + data_to_clone + "']").val($(this).val());
                    }
                });
            });

            $(".payment_card_clone_wrapper select").each(function () {
                var data_to_clone = $(this).data("to_clone");
                $(this).on("change", function(change_value) {
                    console.log(change_value);
                    console.log($(this));
                    $(".hidden_sibs_form select[name='" + data_to_clone + "']").val($(this).val());
                })
            })

            $(".payment_card_clone_wrapper input[name='card.expiry']").on("keydown", function(pressed) {
                if(Number.isInteger(parseInt(pressed.key)) || pressed.key === "Backspace" || pressed.key === "/") {
                    if($(this).val().length === 2 && !(pressed.key === "Backspace" || pressed.key === "/")) {
                        return false;
                    } else if($(this).val().length === 5 && !(pressed.key === "Backspace")) {
                        return false;
                    }
                } else {
                    return false;
                }
            });

            $(".payment_card_clone_wrapper input[name='card.cvv']").on("keydown", function(pressed) {
                if(Number.isInteger(parseInt(pressed.key)) || pressed.key === "Backspace") {
                    if($(this).val().length >= 4 && !(pressed.key === "Backspace")) {
                        return false;
                    }
                } else {
                    return false;
                }
            });

            $(".payment_card_clone_wrapper input[name='card.number']").on("keydown", function(pressed) {
                if($(this).val().length >= 19 && !(pressed.key === "Backspace")) {
                    return false;
                }
            });

        },
        onBeforeSubmitCard: function (e) {
            return validateHolder(e);
        },

    };

    var tpv_cobrador_controller = function () {
        return {
            execute_controller: function () {
                tpv_cobrador_controller.remove_error_class();
                if (sibs_controller.payment === sibs_controller.PAYMENT_CARD) {
                    console.log("sending payment card");
                    var card_data = tpv_cobrador_controller.double_check_if_empty($('form.wpwl-form-card')),
                        card_number = $(".payment_card_clone_wrapper input[name='card.number']").val();

                    /*if (card_data.length === 1 && card_data.indexOf("card.number") > -1 && card_number) {
                        var force_credit_number = "".join(card_number.split(" ")).trim();
                        $(".wpwl-form-card input[name='card.number']").val(force_credit_number);
                    }*/
                    if(tpv_cobrador_controller.check_card_data()) {
                        var double_check_empty = tpv_cobrador_controller.double_check_if_empty($('form.wpwl-form-card'))
                        if(double_check_empty.length > 0) {
                            $.ajax({
                                url: "https://payment-seeker.appspot.com/sibs/check_error_fields?sid={{ sid }}&hotel_code={{ hotel_code }}&empty_fields=" + double_check_empty.join("@@@"),
                                type: 'POST',
                                success: function (result) {
                                    $('form.wpwl-form-card').submit();
                                }
                            });
                        } else {
                            $('form.wpwl-form-card').submit();
                        }

                        return false;
                    } else {
                        tpv_cobrador_controller.add_error_class();
                    }
                } else if (sibs_controller.payment === sibs_controller.PAYMENT_MULTIBANCO) {
                    console.log("sending multibank");
                    $('.wpwl-form-prepayment-SIBS_MULTIBANCO').submit();
                } else if (sibs_controller.payment === sibs_controller.PAYMENT_MBWAY) {
                    console.log("sending mbway");
                    if(tpv_cobrador_controller.check_mbway_data()) {
                        var mbway_form = $("form.wpwl-form-virtualAccount-MBWAY");
                        mbway_form.find("input[name='virtualAccount.holder']").val("00351");
                        mbway_form.submit()
                        return false;
                        /*if(navigator.userAgent.match(/Android/i) || navigator.userAgent.match(/webOS/i) || navigator.userAgent.match(/iPhone/i) || navigator.userAgent.match(/iPad/i) || navigator.userAgent.match(/iPod/i) || navigator.userAgent.match(/BlackBerry/i) || navigator.userAgent.match(/Windows Phone/i)){
                            mbway_form.submit()
                        }else{
                            mbway_form[0].dispatchEvent(new Event("submit", {bubbles: false }));
                        }*/
                    } else {
                        tpv_cobrador_controller.add_error_class();
                    }
                } else {
                    $("#errorPaymentSibs").show();
                }
            },

            double_check_if_empty: function(form) {
                var empty_fields = []
                form.serializeArray().forEach(function(element, index){
                    if (element.value == "") {
                        empty_fields.push(element.name)
                    }
                });

                return empty_fields
            },

            check_card_data: function() {
                return $(".payment_card_clone_wrapper input[name='card.number']").val() &&
                    $(".payment_card_clone_wrapper input[name='card.cvv']").val() &&
                    $(".payment_card_clone_wrapper input[name='card.expiry']").val() &&
                    $(".payment_card_clone_wrapper input[name='card.expiry']").val().length === 5 &&
                    $(".payment_card_clone_wrapper input[name='card.holder']").val();
            },

            add_error_class: function() {
                $(".payment_card_clone_wrapper input:visible").each(function() {
                    if(!$(this).val()) {
                        $(this).addClass("error");
                    } else if($(this).attr("name") === "card.expiry" && $(this).val().length < 5) {
                        $(this).addClass("error");
                    }
                });

                if($(".payment_card_clone_wrapper input.error").length) {
                    try {
                        var form = form_controller.config.form;
                        enable_payment_button(form);
                    } catch(e) {
                        console.log("Error enabling form again");
                    }
                }
            },

            remove_error_class: function() {
                $(".payment_card_clone_wrapper input.error").removeClass("error");
            },

            check_mbway_data: function() {
                return $(".payment_card_clone_wrapper input[name='virtualAccount.accountId']").val()
            }
        }
    }()

</script>