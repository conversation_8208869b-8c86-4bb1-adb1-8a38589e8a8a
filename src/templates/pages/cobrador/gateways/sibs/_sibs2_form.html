

{% if no_form %}
    <script>
        var tpv_cobrador_controller = function () {
            return {
                execute_controller: function () {
                    $.ajax({
                        method: "POST",
                        url: "{{ url|safe }}",
                        data: {
                            'hotel_code': '{{ hotel_code }}',
                            'gateway_type': '{{ gateway_type }}',
                            'amount': '{{ amount }}',
                            'sid': '{{ sid }}',
                            'add_button': '{{ add_button }}',
                            'get_form': true,
                            'payment_order_id': '{{ payment_order_id }}',
                            'extra_data': '{{ extra_data|safe }}',
                            'personal_data': JSON.stringify({
                                'email': $("input[name=email]").val(),
                                'name': $("input[name=firstName]").val() + " " + $("input[name=lastName1]").val(),
                                'country': $("select[name=country]").val(),
                                'postal_code': $("input[name=postalCode]").val(),
                                'full_address': $("input[name=address]").val(),
                                'city': $("input[name=city]").val()
                            })
                        },
                        success: function (response) {
                            $("body").append(response);
                        }
                    }).fail(function () {
                        console.log("error")
                    });
                },
            }
        }()


        $("button#submitButton, button#btn-finish-booking").on("click", function() {

            if ($("#personal-details-form, #personalDetailsForm").valid()) {
                $(this).html('<span><i class="fas fa-spinner-third fa-spin" aria-hidden="true" style="font-size: 30px;position: relative;z-index: 100"></i></span>');
            }
        })

    </script>
{% else %}
    <link rel="stylesheet" href="https://payment-seeker.appspot.com/static/css/pages/cobrador/sibs2_styles_tpv.css?v=1">
    <div class="hidden_sibs_form">
        <form class="paymentSPG" spg-context="{{ form_context }}" spg-config="{{ form_config }}" spg-style="{{ form_style }}" target="_self"></form>
    </div>

    <script src="{{ "https://spg.qly.site1.sibs.pt" if test else endpoint }}/assets/js/widget.js?id={{ transactionID }}"></script>
{% endif %}