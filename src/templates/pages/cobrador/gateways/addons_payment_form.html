{% if dev %}
    <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
{% endif %}
<script src="{{ "/static/js/libs/rxp-js.min.js"|fixurl }}"></script>
<style>
    .addons-form{
        margin: 5px;
    }

    .addons-form label {
        display: block;
        margin-bottom: 8px;
        color: #007aff;
    }

    .addons-form input {
        width: calc(100% - 16px);
        padding: 8px;
        margin-bottom: 16px;
        box-sizing: border-box;
        border: 1px solid #007aff;
        border-radius: 4px;
    }

    .addons-form input.error{
        border: 1px solid red;
    }

    .addons-form .payment-double-cols{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }

    .addons-form .payment-double-cols .payment-form-col{
        width: 50%;
    }

    .addons-form .error{
        color: red;
    }

    .pep_links_spinner_wrapper {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .pep_links_spinner_wrapper.peplinks_active_spinner::before {
        content:'';
        position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: white;
            opacity: .5;
    }


    .peplinks_spinner{
          height: 60px;
          width: 60px;
          border: 5px solid rgba(0, 0, 0, .1);
          border-top-color: #007aff;
          border-radius: 50px;
          animation: spin 1s ease infinite;
        }
        @keyframes spin{
          to{ transform: rotate(360deg); }
        }

    .addons-message{
        width: calc(100% - 56px);
        display: none;
        font-size: 2em;
        border-radius: 10px;
        padding: 8px 20px;
        margin-bottom: 20px;
        line-height: 1.1;
        background-color: #007aff;
        color:#efefef;
    }
    .addons-message.error-message{
        background-color: #ff9e9e;
        color:#ff3636;
    }
</style>
{% if dev %}
    <form id="personal-details-form">
        <input type="text" name="email" value="<EMAIL>">
        <input type="text" name="telephone" value="000000000">
        <input type="text" name="address" value="inventada">
        <input type="text" name="city" value="Torremolinos">
        <input type="text" name="postalCode" value="29620">
        <input type="text" name="country_code" value="724">
        <input type="text" name="prefixes_phone" value="34">
    </form>
{% endif %}
<div class="pep_links_spinner_wrapper">
    <div id="pep_links_spinner" ></div>
</div>
<div class="addons-form-container">
    <div class="addons-form">
        <div class="addons-message">
            Lorem ipsum dolor sit amet, consectetur adipisicing elit. Amet animi aperiam autem, eius facere ipsum maiores minima nam nesciunt numquam officiis optio, perspiciatis placeat praesentium quae reiciendis repellendus sequi veritatis.
        </div>
        <label for="cardOwner">Nombre del Propietario</label>
        <input type="text" id="cardOwner" name="pas_ccname" required>

        <label for="cardNumber">Número de Tarjeta</label>
        <input type="text" id="cardNumber" name="pas_card" required>

        <div class="payment-double-cols">
            <div class="payment-form-col">
                <label for="expirationDate">Fecha de expiración</label>
                <input type="text" id="expirationDate" name="cc_date" placeholder="MM/YY" required>
            </div>
            <div class="payment-form-col">
                <label for="cvv">CVV</label>
                <input type="text" id="cvv" name="pas_cccvc" placeholder="123" required>
            </div>
        </div>
    </div>
</div>
<div style="opacity: 0 !important;">
    <iframe id="targetIframe" ></iframe>
</div>

<script>
    const js_context = {{ js_context|safe }};
    var check_booking_finished = true;
    var first_attempt = true;

    function remove_prefix(prefix, number) {
        if(number !== undefined){
            var phone_number = number.replace(/\D/g, "");
            const regex = new RegExp(`^\\${prefix}`);
            const phone_number_formatted = phone_number.replace(regex, '');
            return phone_number_formatted;
        }
        return number
    }

    function build_personal_details(){
        var result = {}
        var personal_details_form = $("#personal-details-form");
        if (personal_details_form.length == 0) {
            personal_details_form = $("#personalDetailsForm");
        }

        var email = personal_details_form.find("[name='email']").val();
        var phone_number = personal_details_form.find("[name='telephone']").val();
        var billing_street = personal_details_form.find("[name='address']").val();
        billing_street = billing_street.replace("(", "");
        billing_street = billing_street.replace(")", "");
        billing_street = billing_street.replace("\"", "");
        billing_street = billing_street.replace("\'", "");
        var billing_city = personal_details_form.find("[name='city']").val();
        billing_city = billing_city.replace("(", "");
        billing_city = billing_city.replace(")", "");
        billing_city = billing_city.replace("\"", "");
        billing_city = billing_city.replace("\'", "");
        var billing_postalcode = personal_details_form.find("[name='postalCode']").val();
        var billing_country = "";
        var prefix_phone = personal_details_form.find("[name='prefixes_phone']").val();
        var phone_number_without_prefix = remove_prefix(prefix_phone, phone_number);

        if (prefix_phone==undefined || prefix_phone=="") { prefix_phone = "34"; }
        if (billing_country==undefined || billing_country=="") { billing_country = "724";}

        {% if psd2_enabled %}
            result["HPP_CUSTOMER_EMAIL"] = email;
            result["HPP_CUSTOMER_PHONENUMBER_MOBILE"] = prefix_phone + "|" + phone_number_without_prefix;
            result["HPP_BILLING_STREET1"] = billing_street.replace(';', ',').substr(0,50);
            result["HPP_BILLING_STREET2"] = "";
            result["HPP_BILLING_STREET3"] = "";
            result["HPP_BILLING_CITY"] = billing_city;
            result["HPP_BILLING_POSTALCODE"] = billing_postalcode;
            result["HPP_BILLING_COUNTRY"] = billing_country;
        {% endif %}

        return result
    }

    window.onload = function(){

        document.querySelector("#targetIframe").addEventListener("message", (event) => {
          console.log(`Received message: ${event.data}`);
        });
    }

    function add_error_message(target, message){
        var el = $(target);
        el.after($(`<label class="error">${message}</label>`));
    }

    function remove_messages(){
        $(".addons-form label.error").remove();
    }

    $(".addons-form input").on("keyup", function(event){

        remove_messages();

        switch(event.target.name){
            case "pas_ccname":
                break;
            case "pas_card":
                if(!RealexRemote.validateCardNumber(event.target.value)){
                    add_error_message(event.target, "tarjeta invalida");
                }
				break;
            case "cc_date":
                var regex = new RegExp(/^(0[0-9])|(1[0-2])\/[0-9][0-9]$/);
                if(!regex.test(event.target.value)){
                    add_error_message(event.target, "Formato de fecha incorrecto");
                }
                break;
            case "pas_cccvc":
                if(!RealexRemote.validateCvn(event.target.value)){
                    add_error_message(event.target, "cvv invalido");
                }
                break;
        }
    });

    function form_validation(){
        var invalid_form = false;
        remove_messages();

        $(".addons-form input").each(function(){
            if(this.value.trim() == ""){
                add_error_message(this, "campo obligatorio");
            }
        });

        if($(".addons-form .error").length > 0){
            return false;
        }
        return true;
    }

    tpv_cobrador_controller = function(){
        return {
            execute_controller: function() {
                if($("#personal-details-form, #personalDetailsForm, .personal_details_form").length && $("#personal-details-form, #personalDetailsForm, .personal_details_form").valid()) {
                    if (form_validation()) {
                        if (first_attempt == true) {
                            var userdata = {
                                ...build_personal_details(),
                                ...js_context
                            };

                            RealexHpp.setHppUrl('{{ payment_url|safe }}');

                            RealexHpp.embedded.init(
                                "autoload",
                                "targetIframe",
                                function (answer, close) {
                                    console.log(answer);
                                    if (answer.action == "hpp-listener-loaded") {
                                        send_card_number();
                                    } else if (answer.action == "form-field-errors") {
                                        check_booking_finished = false;
                                        disable_spinner();
                                    }
                                },
                                userdata
                            );

                            first_attempt = false;
                        } else {
                            send_card_number();
                        }
                        enable_spinner();
                    }
                }
            }
        }
    }();

    function send_card_number(){
        var cardData = {
            action: "populate-form-fields",
            payload: {
              pas_ccnum: document.querySelector("input[name=pas_card]").value,
              pas_ccmonth: document.querySelector("input[name=cc_date]").value.split("/")[0],
              pas_ccyear: document.querySelector("input[name=cc_date]").value.split("/")[1],
              pas_cccvc: document.querySelector("input[name=pas_cccvc]").value,
              pas_ccname: document.querySelector("input[name=pas_ccname]").value
            }
          }
          // convert to JSON
          cardData = JSON.stringify(cardData);
          // send the JSON string to the hidden HPP
            console.log(cardData);
          document.querySelector("#targetIframe").contentWindow.postMessage(cardData, "{{ payment_url|safe }}");
          reservation_finished();
    }

    function reservation_finished(retry=1){
        if(check_booking_finished != true){
            check_booking_finished = true;
            return null;
        }

        fetch("{{ url_validation|fixurl }}")
        .then(function(res){
            return res.text();
        }).then(function(data){
            if(data){
                console.log(`Response from cobrador ${data}`);
                if(data == "true"){
                    window.location.href = "{{ url_ok|safe }}";
                }else{
                    if(retry < 20){
                       setTimeout(reservation_finished, 4000, retry+1);
                    }else{
                        disable_spinner();
                    }
                }
            }
        });
    }

    function enable_spinner(){
        $(".pep_links_spinner_wrapper").css("display", "flex").addClass("peplinks_active_spinner");
        $("#pep_links_spinner").addClass("peplinks_spinner");
    }

    function disable_spinner(){
        $(".pep_links_spinner_wrapper").css("display", "none").removeClass("peplinks_active_spinner");
        $("#pep_links_spinner").removeClass("peplinks_spinner");
    }

    function show_message(message, level="INFO"){
        if(level=="INFO"){
            $(".addons-message").removeClass("error-message");
            $(".addons-message").html(message);
            $(".addons-message").css("display", "block");
        }else{
            $(".addons-message").addClass("error-message");
            $(".addons-message").html(message);
            $(".addons-message").css("display", "block");
        }
    }

    function remove_message(){
        $(".addons-message").css("display", "none");
    }




</script>
<script>
    $("#destination_cobrador_form").append($(".addons-form-container"));

</script>