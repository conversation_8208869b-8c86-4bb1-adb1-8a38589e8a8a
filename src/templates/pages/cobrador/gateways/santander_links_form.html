<style>
        .santander_paylinks_form{
            padding: 15px;
            border-radius: 8px;
            width: fit-content;
            max-width: 400px;
        }
        .wrapper_santander_paylinks_element {
            margin: 10px;
            width: 100%;
        }
        .pep_links_spinner_wrapper {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .pep_links_spinner_wrapper.peplinks_active_spinner::before {
            content:'';
            position: fixed;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                background: white;
                opacity: .5;
        }


        .peplinks_spinner{
              height: 60px;
              width: 60px;
              border: 5px solid rgba(0, 0, 0, .1);
              border-top-color: #FF5252;
              border-radius: 50px;
              animation: spin 1s ease infinite;
            }
            @keyframes spin{
              to{ transform: rotate(360deg); }
            }

         .msg_pep_links{
             color: #8E6800;
             font-weight: 600;
             margin: 0px 0px 20px 0;
             text-align: center;
         }
         h3{
             margin: 0;
             padding: 0;
         }

         .pep_fake_elemen{
             border: 1px solid rgba(0,0,0,0.1);
             border-radius: 5px;
             padding: 5px;
             width: 100%;
         }
         .santander_paylinks_element_container{
             display: inline-flex;
             width: 97%;
             justify-content: center;
             align-items: center;

         }

</style>
{% if DEV %}
    <script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>
{% endif %}
<div class="santander_paylinks_form ">
    <div action="{{ payment_url }}" class="form pep_fake_elemen" method="POST">
{#    <form action="http://localhost:8080/forms/cobrador/send_new_paymentlink_external" class="form" method="POST" id="paymentForm">#}
        <div class="wrapper_santander_paylinks_element msg_pep_links">
           <h3>
              {{ T_customer_data_link }}
           </h3>
        </div>
        <div class="santander_paylinks_element_container">
            <div class="wrapper_santander_paylinks_element">
                <input type="text" name="paylink_customer_email_fake" class="pep_fake_elemen" placeholder="{{ T_EMAIL }}" required>
            </div>


        </div>

        <div class="santander_paylinks_element_container">
            <div class="wrapper_santander_paylinks_element">
                <input type="text" name="paylink_customer_billingAddress_fake" maxlength="60" class="pep_fake_elemen" placeholder="{{ T_ADDRESS }}" required/>
            </div>
        </div>

        <div class="santander_paylinks_element_container">
            <div class="wrapper_santander_paylinks_element">
                <input type="text" name="paylink_customer_city_fake" class="pep_fake_elemen" placeholder="{{ T_CITY }}" required/>
            </div>

            <div class="wrapper_santander_paylinks_element">
                <input type="text" name="paylink_customer_customer_postal_code_fake" class="pep_fake_elemen" placeholder="{{ T_codigo_postal }}" required/>
            </div>
        </div>
    </div>
    <div class="pep_links_spinner_wrapper">
        <div id="pep_links_spinner" ></div>
    </div>
</div>

<form action="{{ payment_url }}" name="santander_hidden_form" method="POST">
    <input type="hidden" id="paylink_customer_email" name="paylink_customer_email" class="pep_fake_elemen" placeholder="{{ T_EMAIL }}" required>
    <input type="hidden" id="paylink_customer_phone_number" name="paylink_customer_phone_number" class="pep_fake_elemen" placeholder="{{ T_telefono }}" required>
    <input type="hidden" id="paylink_customer_billingAddress" name="paylink_customer_billingAddress" class="pep_fake_elemen" placeholder="{{ T_ADDRESS }}" required/>
    <input type="hidden" id="paylink_customer_city" name="paylink_customer_city" class="pep_fake_elemen" placeholder="{{ T_CITY }}" required/>
    <input type="hidden" id="paylink_customer_customer_postal_code" name="paylink_customer_customer_postal_code" class="pep_fake_elemen" placeholder="{{ T_codigo_postal }}" required/>

    <input type="hidden" name="hotel_code" value="{{ hotel_code }}">
    <input type="hidden" name="sid" value="{{ sid }}">
    <input type="hidden" name="payment_order_id" value="{{ payment_order_id }}">
    <input type="hidden" name="special_tpv_link" value="{{ special_tpv_link }}">
    <input type="hidden" name="amount" value="{{ amount }}">
    <input type="hidden" name="currency" value="{{ currency }}">
    {% if original_identifier %}
        <input type="hidden" name="original_identifier" value="{{ original_identifier }}">
    {% endif %}
</form>
<script>
    $(".pep_fake_elemen input").each(function () {
        $(this).on("change", function () {
            var id_hidden = $(this).attr("name").replace("_fake", "")
            if($(this).attr("type") === "checkbox"){
                if ($(this).is(":checked")){
                    $("#" + id_hidden).val("on");
                }else{
                    $("#" + id_hidden).val("");
                }
            }else{
                $("#" + id_hidden).val($(this).val());
            }

        });
    });
    var destination_cobrador_form = $("#destination_cobrador_form")
    destination_cobrador_form.append($(".santander_paylinks_form"));

    if (destination_cobrador_form.length < 1 ){
        $('.santander_paylinks_form').hide();
    }

    tpv_cobrador_controller = function() {
        return {
            execute_controller: function () {
                $(".pep_links_spinner_wrapper").css("display", "flex").addClass("peplinks_active_spinner");
                $("#pep_links_spinner").addClass("peplinks_spinner");
                $("form[name=santander_hidden_form]")[0].submit();
            }
        }
    }();
</script>