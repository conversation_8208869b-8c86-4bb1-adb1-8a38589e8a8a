{#<script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>#}
<link rel="stylesheet" href="{{'/static/css/libs/payment_widget.css'|fixurl}}">
{#{% include "pages/cobrador/gateways/paypal_pr/booking3_mock.html" %}#}
<script id="payment-options-ctx" type="application/json">{{payment_options|tojson}}</script>

<div class="scalapay-payment">
    <div id="payment-options">
    </div>
    <div id="payment-option-label">
        {% for payment_option in payment_options %}
            <div class="scalapay-label" id="{{ payment_option.product_id }}" {% if not loop.first %}style="display: none" {% endif %}>
                <scalapay-widget
                    frequency-number='30'
                    number-of-installments='{{ payment_option.amount }}'
                    show-title="{% if payment_option.product_id == 'pay-now-checkout' %}false{% else %}true{% endif %}"
                    hide-price='true'
                    max='1500'
                    amount="{{ amount }} {{ currency }}"
                    currency-display='code'
                    logo-size='100'
                    locale='{{ locale }}'
                    channel='travel'
                ></scalapay-widget>
                    </div>
        {% endfor %}
    </div>
</div>

<script>

    function get_personal_details(){

		var phone_prefix = ($("#personal-details-form, #personalDetailsForm").find("select[name=phone_prefix]").val() || "").replace("+", "");
        var telephone = $("#personal-details-form, #personalDetailsForm").find("input[name=telephone]").val() || ""

        var full_phone = phone_prefix + telephone;

		var personal_details = {
            email:  $("#personal-details-form, #personalDetailsForm").find("input[name=email]").val() || "",
            name: $("#personal-details-form, #personalDetailsForm").find("input[name=firstName]").val() || "",
            surname: $("#personal-details-form, #personalDetailsForm").find("input[name=lastName1]").val() || "",
            country: $("#personal-details-form, #personalDetailsForm").find("select[name=country]").val() || "",
            city: $("#personal-details-form, #personalDetailsForm").find("input[name=city]").val() || "",
            postal_code: $("#personal-details-form, #personalDetailsForm").find("input[name=postal_code]").val() || "",
            phone: full_phone
        }

		return personal_details;
    }

	var scalapay_option = "{{ payment_options[0].id }}";

    window.onload = function () {
		import("https://cdn.scalapay.com/widget/v3/js/scalapay-widget.esm.js").then(function (mod) {
		})

        {% if multiple_options %}
        import("{{'/external/js/libs/payment_widget.js'|fixurl}}").then(function(payment_widget){
			payment_widget.init_payment_options(
                "payment-options",
                JSON.parse(document.getElementById("payment-options-ctx").innerHTML),
                function(selected_option){
					$(".scalapay-label").hide();
					$(`#${selected_option.product_id}`).show();
					scalapay_option = selected_option.id;

					console.log(selected_option);
                },
                false
            )
		}).catch(function(err){
			console.log(err);
		});
		{% endif %}
	};

    tpv_cobrador_controller = function() {
		return {
			execute_controller: function () {
				var request_data = {
					personal_details: get_personal_details(),
                    payload: "{{ payload }}",
                    "scalapay_option": scalapay_option
				}

				fetch("{{'/scalapay/checkout'|fixurl}}", {
                        method: "POST",
                        mode: "cors",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify(request_data)
                    }
                ).then(function(res){
					return res.json();
                }).then(function(data){
					if(data.error != undefined){
						return;
                    }
					document.location.href = data.checkout_url;
                })
			}
		}
	}();

	$("#destination_cobrador_form").append($(".scalapay-payment"));
</script>