{% if dev %}
<script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>
{% endif %}
<style>
    .worldline_payment_form_container{
      padding: 5px;
      max-width: 400px;
      margin: 0 auto 20px;
    }
    .worldline_payment_form_container .payment_option.selected-option{
    background-color:  #e8f2fa;
    border-color: #2196F3;
    z-index: 1;
    }
    .worldline_payment_form_container .payment_option .payment_radiobtn{
        appearance: none;
        position: absolute;
        top: 22px;
        left: 12px;
    }
    .worldline_payment_form_container .payment_option .payment_radiobtn:before{
        content: '';
        width: 10px;
        height: 10px;
        border: 1px solid #333333;
        position: absolute;
        top: -11px;
        left: -8px;
        border-radius: 50%;
    }
    .worldline_payment_form_container .payment_option.selected-option .payment_radiobtn:after{
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: #333333;
        border-radius: 50%;
        top: -9px;
        left: -6px;
    }
    .worldline_payment_form_container .payment_option label{
        font-size: 14px;
        margin-left: 5px;
        display: block;
        padding: 0px 56px 0px 0px;
    }
    .worldline_payment_form_container .payment_option{
    padding: 10px 50px 10px 30px;
    position: relative;
    border: 1px solid gray;
    transition: background .3s;
    }
    .worldline_payment_form_container .payment_option:only-child{
    border-radius: 5px;
    }
    .worldline_payment_form_container .payment_option:first-child:not(:only-child){
    border-radius: 5px 5px 0 0;
    }
    .worldline_payment_form_container .payment_option:last-child{
    border-radius: 0 0 5px 5px;
    }
    .worldline_payment_form_container .payment_option:not(:first-child){
        margin-top: -1px;
    }
    .worldline_payment_form_container .payment_option .cards_images{
        display: inline-block;
        width: 90px;
        height: auto;
        position: absolute;
        right: 5px;
        top: 20px;
        text-align: right;
        transform: translateY(-50%);
    }
    .worldline_payment_form_container .payment_option .cards_images img{
        width: calc((100% / 3) - 14px);
        height: auto;
        object-fit: cover;
        margin-right: 5px;
    }
    .worldline_payment_form_container .payment_option#msi-payment:after{
        content: '\f133';
        position: absolute;
        right: 10px;
        font-family: 'Font Awesome 5 Pro';
        font-size: 18px;
        top: 10px;
        font-weight: 100;
    }
    .worldline_payment_form_container .payment_option .payment_option_hide_info{
    max-height: 0;
    overflow: hidden;
    transition: max-height .5s;
    }
    .worldline_payment_form_container .payment_option.selected-option .payment_option_hide_info{
        max-height: 500px;
    }
    .worldline_payment_form_container .payment_option .payment_option_description{
        font-weight: lighter;
        border-top: 1px solid rgba(0,0,0,0.2);
        padding-top: 10px;
        font-size: 12px;
        line-height: 22px;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper{
    position: relative;
    margin-top: 10px;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper .installments_quantity_label{
    position: absolute;
    top: -6px;
    left: 10px;
    font-size: 11px;
    padding: 0 5px;
    background: white;
    transition: background .3s;
    }
    .worldline_payment_form_container .payment_option.selected-option .msi_payment_select_wrapper .installments_quantity_label{
    background: #e8f2fa;
    }
    .worldline_payment_form_container .payment_option .installments_quantity{
    padding: 12px 10px 8px ;
    border-radius: 5px;
    background: transparent;
    font-size: 14px;
    appearance: none;
    width: 100%;
    }
    .worldline_payment_form_container .payment_option .msi_payment_select_wrapper:after{
    content: '\f107';
    position: absolute;
    right: 10px;
    font-family: "Font Awesome 5 Pro";
    font-size: 24px;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 100;
    }
    .worldline_payment_form_container .payment_option .installments_quantity div{
    margin: 5px;
    }
    .worldline_payment_form_container .payment_option .installments_quantity div input[type="radio"]{
    width: inherit;
    margin-right: 5px;
    height: inherit;
    }
</style>
<div class="worldline_payment_form_container">
    {% if custom_text %}
        <div style="display: none" class="b3_custom_text">
            {% for option, text in custom_text.items() %}
                <div id="{{ option }}" amount="{{ custom_amounts[option] }}">
                    {{ text|safe }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
    <div id="worldline_payment_form" >
        {% for amount in amounts %}
            <div class="payment_option" id="card-payment">
              <div class="cards_images">
                  <img src="{{'/static/images/credit-cards_mastercard.png'|fixurl}}">
                  <img src="{{'/static/images/credit-cards_visa.png'|fixurl}}">
                  <img src="{{'/static/images/credit-cards_amex.png'|fixurl}}">
              </div>
            <input class="payment_radiobtn" type="radio" id="radio{{loop.index}}" value="{{amount}}" {% if loop.index == 1 %}checked{% endif %}><label for="radio{{loop.index}}">{% if "-day" in amount %}{{ T_payment_days_a_t }} {{ amount|replace("-day", "") }} {{ T_payment_days_b_t }} {{ T_payment_days_c_t }}{% else %}{{ T_payment_100_t }} {{amount}}%{% endif %}</label>
            {% if T_payment_100 %}<div class="payment_option_hide_info"><p class="payment_option_description">{{ T_payment_100 }}</p></div>{% endif %}
            </div>
        {% endfor %}
        {% if add_fake_payment_methods %}
        <div class="payment_option" id="apple-pay">
            <div class="cards_images">
                <img src="{{'/static/images/apple-pay.png'|fixurl}}">
            </div>
             <input class="payment_radiobtn" type="radio" id="radioapple"> <label for="radioapple">{{ T_payment_apple_t }}</label>
        </div>
        <div class="payment_option" id="amazon-pay">
            <div class="cards_images">
                <img src="{{'/static/images/amazon-pay.png'|fixurl}}">
            </div>
             <input class="payment_radiobtn" type="radio" id="radioamazon"> <label for="radioamazon">{{ T_payment_amazon_t }}</label>
        </div>
        <div class="payment_option" id="bizum-pay">
            <div class="cards_images">
                <img src="{{'/static/images/bizum-logo-DCFC870E8B-seeklogo.com.png'|fixurl}}">
            </div>
             <input class="payment_radiobtn" type="radio" id="radiobizum"> <label for="radiobizum">{{ T_payment_bizum_t }}</label>
        </div>
        {% endif %}
        {% if msi_payments %}
        <div class="payment_option" id="msi-payment">
            <input class="payment_radiobtn" type="radio" id="radiomsi"  value="radiomsi"> <label for="radiomsi">{{ T_payment_msi_t }}</label>
            <div class="payment_option_hide_info">
                {% if T_payment_msi %}<p class="payment_option_description">{{ T_payment_msi }}</p>{% endif %}
                <div class="msi_payment_select_wrapper">
                  <select id="installments_quantity" name="installments_quantity" class="installments_quantity">
                      {% for installment in installments %}
                        <option name="installments" value="{{installment.installment}}" {% if loop.first %}selected{% endif %}>{{T_payment_msi_option_a}}{{ installment.installment }} x {{ installment.price|money_format }}{{ T_payment_msi_option_b }}</option>
                      {% endfor %}
                  </select>
                  <label for="installments_quantity" class="installments_quantity_label">{{ T_seleccione_una_opcion }}</label>
                </div>
          </div>
        </div>
        {% endif %}
    </div>
</div>
<form action="{% if dev %}http://127.0.0.1:8080{% else %}https://payment-seeker.appspot.com{% endif %}/worldline/redirect" id="worldline_payment_form_hidden" method="post" style="display: none" autocomplete="off">
    <input type="hidden" name="payload" value="{{ payload }}">
    <input type="hidden" name="installments">
    <input type="hidden" name="payment-amount" value="100">
    <input type="hidden" name="payment-method" value="card-payment">
    <input type="hidden" name="metadata">
</form>
<script>
    if(document.querySelector(".tpv_message_informative .currencyValue") != null){
        var amount_to_pay = parseFloat(document.querySelector(".tpv_message_informative .currencyValue").innerHTML);
    }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
        var amount_to_pay = parseFloat(document.querySelector(".tpv_text_wrapper .currencyValue").innerHTML);
    }

    var current_currency = $("#currencySelect :selected").data("shortname");
    if(current_currency == undefined){
        current_currency = $(".currency_selector .item_selected").text().trim()
    }

    var percent_to_pay = $("#worldline_payment_form input[checked]").val();


    function get_client_metadata(){
        var client_metadata = {
            timeoffset: (new Date()).getTimezoneOffset(),
            locale: navigator.language,
            userAgent: navigator.userAgent,
            javaEnabled: navigator.javaEnabled(),
            colorDepth: screen.colorDepth,
            height: screen.height,
            width: screen.width
        }
        $("input[name=metadata]").val(JSON.stringify(client_metadata))
    }
  tpv_cobrador_controller = function() {
      return {
        execute_controller: function() {
            get_client_metadata();
            $('#worldline_payment_form_hidden input:hidden[name=installments]').val($("#worldline_payment_form select.installments_quantity").val());
          document.querySelector("#worldline_payment_form_hidden").submit();
        }
      }
    }();


    function sumFirstNDays(list, n) {
      let sum = 0;
      for (let i = 0; i < n && i < list.length; i++) {
        const num = parseFloat(list[i]); // Attempt to convert to float
        if (!isNaN(num)) { // Check if it's a valid number
          sum += num;
        } else {
          console.error("Element at index", i, "is not a valid number:", list[i]);
        }
      }
      return sum;
    }

    function calculate_amount_to_show(percent_to_pay) {
            if (document.querySelector(".tpv_message_informative .currencyValue") != null || document.querySelector(".tpv_text_wrapper .currencyValue") != null) {
                if(document.querySelector(".tpv_message_informative .currencyValue") != null){
                    amount_to_pay = parseFloat(document.querySelector(".tpv_message_informative .currencyValue").getAttribute("latest_value"));
                }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
                    amount_to_pay = parseFloat(document.querySelector(".tpv_text_wrapper .currencyValue").getAttribute("latest_value"));
                }

                /*if (percent_to_pay.search("-day") == 1) {
                    num_days_to_pay = parseInt(percent_to_pay.replace("-day", ""));
                    value_to_show = (amount_to_pay / {{ num_nights }}) * num_days_to_pay;
                } else if(isNaN(percent_to_pay)){
                    value_to_show = amount_to_pay;
                } else {
                    value_to_show = (amount_to_pay / 100) * parseInt(percent_to_pay);
                }*/


                if (percent_to_pay.search("-day") == 1) {
                    num_days_to_pay = parseInt(percent_to_pay.replace("-day", ""));
                    //by default, an avarage!
                    value_to_show = (amount_to_pay / {{ num_nights }}) * num_days_to_pay;

                    var prices_per_day = {};
                    {%  if prices_per_day %}
                         prices_per_day = {{ prices_per_day | tojson }};
                    {%  endif %}
                    if (!Object.keys(prices_per_day).length == 0) {
                        value_to_show = 0;
                        for (let room in prices_per_day) {
                            if (prices_per_day.hasOwnProperty(room)) {
                                let prices = prices_per_day[room];
                                value_to_show += sumFirstNDays(prices, num_days_to_pay);
                            }
                        }
                        value_to_show = Math.min(value_to_show, parseFloat(amount_to_pay));
                    }
                } else if(isNaN(percent_to_pay)){
                    value_to_show = amount_to_pay;
                } else {
                    value_to_show = (amount_to_pay / 100) * parseInt(percent_to_pay);
                }


                value_to_show = Math.round(value_to_show * 100) / 100
                if(document.querySelector(".tpv_message_informative .currencyValue") != null){
                    document.querySelector(".tpv_message_informative .currencyValue").innerHTML = value_to_show;
                }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
                    document.querySelector(".tpv_text_wrapper .currencyValue").innerHTML = value_to_show;
                }
                return value_to_show;
            }
        }
    $(".payment_option").on("click", (e)=>{
        if(!$(e.currentTarget).hasClass("selected-option")){
            $(".payment_option.selected-option").find(".installments_quantity").fadeOut();
            $(".payment_option.selected-option").find('.payment_radiobtn').prop('checked', false);
            $(".payment_option.selected-option").removeClass("selected-option");
            $(e.currentTarget).addClass("selected-option");
            $(e.currentTarget).addClass("selected-option").find('.payment_radiobtn').prop('checked', true);
            $(e.currentTarget).find(".installments_quantity").fadeIn();
            $('#worldline_payment_form_hidden input:hidden[name=payment-method]').val(e.currentTarget.id);
            $('#worldline_payment_form_hidden input:hidden[name=payment-amount]').val($(e.currentTarget).find("input").val());
            percent_to_pay = $(e.currentTarget).find("input").val();
            calculate_amount_to_show(percent_to_pay);
            if($(".b3_custom_text").html()){
                let current_currency = $(".monedaConv").first().text();
                let value_to_show = calculate_amount_to_show(percent_to_pay);
                let selected_option = $(".payment_option.selected-option").find('.payment_radiobtn').prop('checked', false).val();
                $(".b3_custom_text").find("#"+ selected_option).find("span").text(value_to_show);
                let text_to_change = $(".b3_custom_text").find("#"+ selected_option).html();
                if(document.querySelector(".tpv_message_informative") != null){
                    $(".tpv_message_informative").html(text_to_change);
                    $(".tpv_message_informative").find(".monedaConv.first_currency").text(current_currency);
                } else{
                    $(".tpv_text_wrapper").find("p").html(text_to_change);
                    $(".tpv_text_wrapper").find(".monedaConv.first_currency").text(current_currency);
                }

            };
        }
    });
    var priceObserver = new MutationObserver(mutations => {
        var selected_currency = $("#currencySelect :selected").data("shortname");
        if(selected_currency == undefined){
            selected_currency = $(".currency_selector .item_selected").text().trim()
        }
        for(let mutation of mutations){
            if( current_currency != selected_currency){
                amount_to_pay = mutation.target.innerHTML;
                current_currency = selected_currency;
                calculate_amount_to_show(percent_to_pay);
            }
        }
    });

    if(document.querySelector(".tpv_message_informative .currencyValue") != null){
        var element_to_observe = document.querySelector(".tpv_message_informative .currencyValue");
    }else if(document.querySelector(".tpv_text_wrapper .currencyValue") != null){
        var element_to_observe = document.querySelector(".tpv_text_wrapper .currencyValue");
    }

    priceObserver.observe(
        element_to_observe,
        {
            childList: true,
            subtree: true
        }
    )
</script>
<script>
    $("#destination_cobrador_form").append($(".worldline_payment_form_container"));
    latest_value = $(".b3_custom_text").find("span[latest_value]:first").text();
    latest_currency = $(".b3_custom_text").find(".monedaConv.first_currency:first").text();
    if(document.querySelector(".tpv_message_informative") != null) {
        $(".tpv_message_informative").find(".currencyValue").attr("latest_value", latest_value);
    }else{
        $(".tpv_text_wrapper.has_pad").find(".currencyValue").attr("latest_value", latest_value);
        $(".tpv_text_wrapper.has_pad").find(".monedaConv.first_currency").text(latest_currency);
    }
    $(".payment_option:first").click();
</script>