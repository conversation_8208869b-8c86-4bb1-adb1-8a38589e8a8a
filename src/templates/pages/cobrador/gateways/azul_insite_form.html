<form action="{{ url_post }}" class="payment_azul" id="paymentForm" method="POST">
    {% if only_tokenizer %}
        <input type="hidden" id="MerchantID" name="MerchantID" value="{{ merchant_id }}">
        <input type="hidden" id="MerchantName" name="MerchantName" value="{{ merchant_name }}">
        <input type="hidden" id="MerchantType" name="MerchantType" value="{{ merchant_type }}">
        <input type="hidden" id="ApprovedUrl" name="ApprovedUrl" value="{{ approved_url }}">
        <input type="hidden" id="DeclinedUrl" name="DeclinedUrl" value="{{ declined_url }}">
        <input type="hidden" id="CancelUrl" name="CancelUrl" value="{{ cancel_url }}">
        <input type="hidden" id="TrxType" name="TrxType" value="CREATE">
        <input type="hidden" id="DataVaultToken" name="DataVaultToken" value="{{ datavaulttoken }}">
        <input type="hidden" id="UseCustomField1" name="UseCustomField1" value="{{ use_custom1 }}">
        <input type="hidden" id="CustomField1Label" name="CustomField1Label" value="{{ custom1_label }}">
        <input type="hidden" id="CustomField1Value" name="CustomField1Value" value="{{ custom1_value }}">
        <input type="hidden" id="UseCustomField2" name="UseCustomField2" value="{{ use_custom2 }}">
        <input type="hidden" id="CustomField2Label" name="CustomField2Label" value="{{ custom2_label }}">
        <input type="hidden" id="CustomField2Value" name="CustomField2Value" value="{{ custom2_value }}">
        <input type="hidden" id="SaveToDataVault" name="SaveToDataVault" value="{{ datavaulttoken }}">
        <input type="hidden" id="AuthHash" name="AuthHash" value="{{ auth_hash }}">
         {%  if language_locale %}
            <input type="hidden" id="Locale" name="Locale" value="{{ language_locale }}">
        {% endif %}
    {% else %}
        <input type="hidden" id="MerchantID" name="MerchantID" value="{{ merchant_id }}">
        <input type="hidden" id="MerchantName" name="MerchantName" value="{{ merchant_name }}">
        <input type="hidden" id="MerchantType" name="MerchantType" value="{{ merchant_type }}">
        <input type="hidden" id="CurrencyCode" name="CurrencyCode" value="{{ currency_code }}">
        <input type="hidden" id="OrderNumber" name="OrderNumber" value="{{ order_number }}">
        <input type="hidden" id="Amount" name="Amount" value="{{ amount }}">
        <input type="hidden" id="Itbis" name="Itbis" value="{{ itbis }}">
        <input type="hidden" id="ApprovedUrl" name="ApprovedUrl" value="{{ approved_url }}">
        <input type="hidden" id="DeclinedUrl" name="DeclinedUrl" value="{{ declined_url }}">
        <input type="hidden" id="CancelUrl" name="CancelUrl" value="{{ cancel_url }}">
        <input type="hidden" id="UseCustomField1" name="UseCustomField1" value="{{ use_custom1 }}">
        <input type="hidden" id="CustomField1Label" name="CustomField1Label" value="{{ custom1_label }}">
        <input type="hidden" id="CustomField1Value" name="CustomField1Value" value="{{ custom1_value }}">
        <input type="hidden" id="UseCustomField2" name="UseCustomField2" value="{{ use_custom2 }}">
        <input type="hidden" id="CustomField2Label" name="CustomField2Label" value="{{ custom2_label }}">
        <input type="hidden" id="CustomField2Value" name="CustomField2Value" value="{{ custom2_value }}">
        <input type="hidden" id="AuthHash" name="AuthHash" value="{{ auth_hash }}">
        <input type="hidden" id="SaveToDataVault" name="SaveToDataVault" value="{{ datavaulttoken }}">
        {%  if language_locale %}
            <input type="hidden" id="Locale" name="Locale" value="{{ language_locale }}">
        {% endif %}
    {% endif %}
</form>

<script>
    tpv_cobrador_controller = function() {
        return {
            execute_controller: function () {
				var paymentForm = document.getElementById("paymentForm");
				paymentForm.submit();
			}
        }
    }();
</script>