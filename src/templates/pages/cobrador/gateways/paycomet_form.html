<form action="{{ url_payment }}" class="payment_paycomet" method="GET" id="paymentForm">
    <input name="sid" type=hidden value="{{ sid }}">
    <input name="gateway_type" type=hidden value="{{ gateway_type }}">
    <input name="price_paycomet" type=hidden value="{{ price_paycomet }}">
    <input name="currency_paycomet" type=hidden value="{{ currency_paycomet }}">
    <input name="bookingid_paycomet" type=hidden value="{{ bookingid_paycomet }}">
    <input name="namespace" type="hidden" value="{{ hotel_code }}">
</form>

{% if custom_text_by_key %}
    <div class="paycomet_payment_form_container">
            <div style="display: none" class="b3_custom_text">
                {% for option, text in custom_text_by_key.items() %}
                    <div id="custom_text_key" amount="{{ custom_amounts_by_key[option] }}">
                        {{ text|safe }}
                    </div>
                {% endfor %}
            </div>
    </div>
{% endif %}
<script>
    if($("#custom_text_key").html()){
        let current_currency = $(".monedaConv").first().text();
        let id_text_to_show = $(".b3_custom_text").find("div")[0].id;
        let text_to_change = $(".b3_custom_text").find("#"+ id_text_to_show).html();
        if(document.querySelector(".tpv_message_informative") != null){
            $(".tpv_message_informative").html(text_to_change);
            $(".tpv_message_informative").find(".monedaConv.first_currency").text(current_currency);
        } else{
            $(".tpv_text_wrapper").find("p").html(text_to_change);
            $(".tpv_text_wrapper").find(".monedaConv.first_currency").text(current_currency);
        }

    };
    var tpv_cobrador_controller = function () {
        return {
            execute_controller: function() {

                var paymentForm = $("#paymentForm");
                paymentForm.submit();

            }
        }
    }();
</script>
