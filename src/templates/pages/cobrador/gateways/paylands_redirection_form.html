{#way 1#}
{#<form id="paymentForm" method="GET">#}
{#    <!-- Campos del formulario de pago -->#}
{#</form>#}
{#<button id="payButton">Pagar</button>#}
{##}
{#<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>#}
{#<script>#}
{#    $(document).ready(function() {#}
{#        $('#payButton').click(function() {#}
{#            var url;#}
{#            {% if url_payment %}#}
{#                url = "{{ url_payment|safe }}";#}
{#            {% elif url_token %}#}
{#                url = "{{ url_token|safe }}";#}
{#            {% else %}#}
{#                url = ""; // O cualquier valor por defecto que desees usar#}
{#            {% endif %}#}
{#            // Realizar la solicitud AJAX#}
{#            $.ajax({#}
{#                url: url,#}
{#                type: "GET",#}
{#                async: false#}
{#            }).done(function(url_merchant) {#}
{#                // Procesar la respuesta y enviar el formulario#}
{#                var paymentForm = $("#paymentForm");#}
{#                paymentForm.attr('action', url_merchant);#}
{#                paymentForm.submit();#}
{#            }).fail(function(message) {#}
{#                // Manejar el error#}
{#                console.error("Error:", message);#}
{#            });#}
{#        });#}
{#    });#}
{#</script>#}


{# way2 #}
{#<iframe id="paylands"#}
{#    title="Paylands"#}
{#    width="600"#}
{#    height="800"#}
{#    src="{{ url_payment }}">#}
{#</iframe>#}

{# way 3 #}

<form id="paymentForm" action="{% if url_payment %}{{ url_payment|safe }}{% else %}{{ url_token|safe }}{% endif %}" method="GET">
</form>