{#<script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>#}
<form action="{{ "/paybyrd/redirect"|fixurl }}" class="paybyrd_form_gateway" method="POST">
    <input type="hidden" name="payload" value="{{ payload|safe }}"/>
    <input type="hidden" name="shopper_first_name" value=""/>
    <input type="hidden" name="shopper_last_name" value=""/>
    <input type="hidden" name="shopper_country" value=""/>
    <input type="hidden" name="shopper_email" value=""/>
    <input type="hidden" name="shopper_telephone" value=""/>
</form>

<script>

    var tpv_cobrador_controller = function() {


        return {
            execute_controller: function() {
				var personal_details_form = $("#personal-details-form");

                if (personal_details_form.length == 0) {
                    personal_details_form = $("#personalDetailsForm");
                }

				$(".paybyrd_form_gateway input[name=shopper_first_name]").val(personal_details_form.find("[name=firstName]").val());
				$(".paybyrd_form_gateway input[name=shopper_last_name]").val(personal_details_form.find("[name=lastName1]").val());
				$(".paybyrd_form_gateway input[name=shopper_country]").val(personal_details_form.find("[name=country]").val());
				$(".paybyrd_form_gateway input[name=shopper_email]").val(personal_details_form.find("[name=email]").val());
				$(".paybyrd_form_gateway input[name=shopper_telephone]").val(personal_details_form.find("[name=telephone]").val());


                $(".paybyrd_form_gateway")[0].submit();
            }
        };
    }()
</script>