<form id="paymentForm" action="{{ url_payment }}" method="POST">

	<input type='hidden' name='Ds_Merchant_Amount' value="{{ form.Ds_Merchant_Amount }}">
	<input type='hidden' name='Ds_Merchant_ConsumerLanguage' value="{{ form.Ds_Merchant_ConsumerLanguage  }}">
	<input type='hidden' name='Ds_Merchant_ConsumerCountry' value="{{ form.Ds_Merchant_ConsumerCountry  }}">
	<input type='hidden' name='Ds_Merchant_Order' value="{{ form.Ds_Merchant_Order  }}">
	<input type='hidden' id="Ds_Merchant_Buyer_Email" name='Ds_Merchant_Buyer_Email' value="">
	<input type='hidden' id="Ds_Merchant_Buyer_Name" name='Ds_Merchant_Buyer_Name' value="">
	<input type='hidden' id="Ds_Merchant_Buyer_LastName" name='Ds_Merchant_Buyer_LastName' value="">
	<input type="hidden" id="sid" name="sid" value="{{ sid }}">
	<input type="hidden" id="integration_type" name="integration_type" value="{{ integration_type }}">
	<input type="hidden" id="hotel_code" name="hotel_code" value="{{ hotel_code }}">
	<input type="hidden" id="identifier" name="identifier" value="{{ identifier }}">
	<input type="hidden" id="price" name="price" value="{{ price }}">
    {% if debug %}
        <table>
            <tr><td>
            Order: <font color="blue">{{ form.Ds_Merchant_Order }}</font>
            </td></tr>
            <tr><td>
            Importe: <font color="blue">{{ form.Ds_Merchant_Amount }}</font>
            </td></tr>
            <tr><td>
            integration_type: <font color="blue">{{ integration_type }}</font>
            </td></tr>
            <tr><td>
            Ds_Merchant_ConsumerLanguage: <font color="blue">{{ form.Ds_Merchant_ConsumerLanguage|safe}}</font>
            </td></tr>
        </table>
    {% endif %}
</form>

{% if gateway_logos %}
    <div id="logos-gateway-wrapper" style="PADDING-LEFT: 25px;">
    {% for my_logo in gateway_logos %}
        <div id="logo-gateway" style="margin-right: 10px;float:left">

        {% if my_logo.advise_popup and 'http' in my_logo.advise_popup %}
            <a href="javascript:window.open('{{ my_logo.advise_popup }}','mywindow','status=1,toolbar=1');" rel="nofollow">
            <img src="{{ my_logo.src }}" style="float:left"/>
            </a>
        {% else %}
            {% if my_logo.advise_popup %}
                <a class="myFancyPopupIframe fancybox.iframe"
                href="/{{ language }}/?sectionContent={{ my_logo.advise_popup }}"
                rel="nofollow">
                <img src="{{ my_logo.src }}" style="float:left"/>
                </a>
            {% else %}
                <img src="{{ my_logo.src }}" style="float:left"/>
            {% endif %}
        {% endif %}


        </div>
    {% endfor %}
    </div>

    <div id="destination_cobrador_form"></div>

    {% if billing_data %}
        <div class="billing_data_wrapper">
        <div class="billing_title">{{ T_datos_facturacion }}</div>
        <input type="text" name="billing_name" id="billing_name" placeholder="{{ T_nombre_y_apellidos }}"/>
        <input type="text" name="billing_cif" id="billing_cif" placeholder="{{ T_nif }}"/>
        <input type="text" name="billing_address" id="billing_address" placeholder="{{ T_direccion }}"/>
        </div>
    {% endif %}
{% endif %}
<script>
    $("#destination_cobrador_form").after($("#logos-gateway-wrapper"));
    var tpv_cobrador_controller = function () {
        return {
            execute_controller: function() {

                var paymentForm = $("#paymentForm");
                paymentForm.submit();

            }
        }
    }();
</script>