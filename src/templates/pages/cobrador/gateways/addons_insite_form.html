{% if dev %}
    <script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>
{% endif %}
<form action="{{ payment_url }}" method="POST" id="paymentForm" class="addon_form">
    <input type="hidden" name="TIMESTAMP" value="{{ my_timestamp }}">
    <input type="hidden" name="MERCHANT_ID" value="{{ merchant_id }}">
    <input type="hidden" name="ACCOUNT" value="{{ account }}">
    <input type="hidden" name="ORDER_ID" value="{{ order_id }}">
    <input type="hidden" name="AMOUNT" value="{{ amount }}">
    <input type="hidden" name="CURRENCY" value="{{ currency }}">
    <input type="hidden" name="SHA1HASH" value="{{ sha1hash }}">
    <input type="hidden" name="AUTO_SETTLE_FLAG" value="1">
    <input type="hidden" name="HPP_LANG" value="{{ language_code }}">
    <input type="hidden" name="HPP_VERSION" value="2">
    <input type="hidden" name="MERCHANT_RESPONSE_URL" value="{{ merchant_response_url }}">
    <input type="hidden" name="CARD_STORAGE_ENABLE" value="1">
    <input type="hidden" name="OFFER_SAVE_CARD" value="0">
    <input type="hidden" name="PAYER_EXIST" value="0">
    <input type="hidden" name="PAYER_REF" value="{{ payer_ref }}">
    <input type="hidden" name="PMT_REF" value="{{ pmt_ref }}">

    {% if tokenizator %}
        <input type="hidden" name="VALIDATE_CARD_ONLY" value="1">
        <input type="hidden" name="CARD_PAYMENT_BUTTON" value="{{ T_message_button_token }}">
    {% endif %}

    {% if psd2_enabled %}
        <input type="hidden" name="HPP_CUSTOMER_EMAIL" value="<EMAIL>">
        <input type="hidden" name="HPP_CUSTOMER_PHONENUMBER_MOBILE" value="44|789456123">
        <input type="hidden" name="HPP_BILLING_STREET1" value="test">
        <input type="hidden" name="HPP_BILLING_STREET2" value="">
        <input type="hidden" name="HPP_BILLING_STREET3" value="">
        <input type="hidden" name="HPP_BILLING_CITY" value="Torremolinos">
        <input type="hidden" name="HPP_BILLING_POSTALCODE" value="29620">
        <input type="hidden" name="HPP_BILLING_COUNTRY" value="724">

        <input type="hidden" name="HPP_ADDRESS_MATCH_INDICATOR" value="false">
        <input type="hidden" name="HPP_CHALLENGE_REQUEST_INDICATOR" value="CHALLENGE_MANDATED">
    {% endif %}


    {% if force_info_hotel %}
        <input type="hidden" name="HPP_CUSTOMER_EMAIL" value="<EMAIL>">
        <input type="hidden" name="HPP_CUSTOMER_PHONENUMBER_MOBILE" value="44|789456123">
        <input type="hidden" name="HPP_BILLING_STREET1" value="{{ billing_street  }}">
        <input type="hidden" name="HPP_BILLING_CITY" value="{{ billing_city }}">
        <input type="hidden" name="HPP_BILLING_POSTALCODE" value="{{ billing_postalcode }}">
        <input type="hidden" name="HPP_BILLING_COUNTRY" value="724">
    {% endif %}

</form>

{% if force_phone %}
    <input type="hidden" name="HPP_CUSTOMER_PHONENUMBER_MOBILE_FORCE" value="44|789456123">
{% endif %}

{# This is for mock data #}
{% if dev %}
    <form id="personal-details-form">
        <input type="text" name="email" value="<EMAIL>">
        <input type="text" name="telephone" value="000000000">
        <input type="text" name="address" value="inventada">
        <input type="text" name="city" value="Torremolinos">
        <input type="text" name="postalCode" value="29620">
        <input type="text" name="country_code" value="724">
        <input type="text" name="prefixes_phone" value="34">
    </form>
{% endif %}

<script>

    function remove_prefix(prefix, number) {
        if(number !== undefined && prefix !== "" ){
            var phone_number = number.replace(/\D/g, "");
            const regex = new RegExp(`^\\${prefix}`);
            const phone_number_formatted = phone_number.replace(regex, '');
            return phone_number_formatted;
        }
        return number
    }

    var addon_controller = function () {

        return {

            open_popup: function() {
                var personal_details_form = $("#personal-details-form");
                if (personal_details_form.length == 0) {
                    personal_details_form = $("#personalDetailsForm");
                }
                var email = personal_details_form.find("[name='email']").val() || "";
                var phone_number = personal_details_form.find("[name='telephone']").val() || "";
                var billing_street = personal_details_form.find("[name='address']").val() || "";
                if (billing_street!==undefined && billing_street!=="") {
                    billing_street = billing_street.replace("(", "");
                    billing_street = billing_street.replace(")", "");
                    billing_street = billing_street.replace("\"", "");
                    billing_street = billing_street.replace("\'", "");
                }
                var billing_city = personal_details_form.find("[name='city']").val() || "";
                if (billing_city!==undefined && billing_city!=="") {
                    billing_city = billing_city.replace("(", "");
                    billing_city = billing_city.replace(")", "");
                    billing_city = billing_city.replace("\"", "");
                    billing_city = billing_city.replace("\'", "");
                }
                var billing_postalcode = personal_details_form.find("[name='postalCode']").val() || "";
				if (billing_postalcode == ""){
					billing_postalcode = personal_details_form.find("[name='postal_code']").val() || "";
                }
                var billing_country = "";
                var prefix_phone = personal_details_form.find("[name='prefixes_phone']").val() || "";
                var phone_number_without_prefix = remove_prefix(prefix_phone, phone_number);

                if (prefix_phone==undefined || prefix_phone=="") { prefix_phone = "34"; }
                if (billing_country==undefined || billing_country=="") { billing_country = "724";}

                {% if psd2_enabled %}
                    $("[name='HPP_CUSTOMER_EMAIL']").val(email);
                    $("[name='HPP_CUSTOMER_PHONENUMBER_MOBILE']").val(prefix_phone + "|" + phone_number_without_prefix);
                    $("[name='HPP_BILLING_STREET1']").val(billing_street.replace(';', ',').substr(0,50));
                    $("[name='HPP_BILLING_STREET2']").val("");
                    $("[name='HPP_BILLING_STREET3']").val("");
                    $("[name='HPP_BILLING_CITY']").val(billing_city);
                    $("[name='HPP_BILLING_POSTALCODE']").val(billing_postalcode);
                    $("[name='HPP_BILLING_COUNTRY']").val(billing_country);
                {% endif %}
                {% if force_info_hotel %}
                    $("[name='HPP_CUSTOMER_EMAIL']").val(email);
                    $("[name='HPP_CUSTOMER_PHONENUMBER_MOBILE']").val(prefix_phone + "|" + phone_number_without_prefix);
                {% endif %}
                if($("[name='HPP_CUSTOMER_PHONENUMBER_MOBILE_FORCE']").length > 0) {
                    $("[name='HPP_CUSTOMER_PHONENUMBER_MOBILE']").val($("[name='HPP_CUSTOMER_PHONENUMBER_MOBILE_FORCE']").val());
                }

                $("#paymentForm")[0].submit();
            }
        }

    }();

</script>