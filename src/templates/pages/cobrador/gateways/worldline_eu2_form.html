{#<script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>#}
<script src="{{'/static/js/libs/paraty_logger.js'|fixurl}}"></script>
<script src="https://payment.preprod.direct.worldline-solutions.com/hostedtokenization/js/client/tokenizer.min.js"></script>
<script async src="https://pay.google.com/gp/p/js/pay.js">
</script>
<script id="payment-options-ctx" type="application/json">{{payment_methods|tojson}}</script>
<link rel="stylesheet" href="{{'/static/css/libs/payment_widget.css'|fixurl}}">
{#{% include "pages/cobrador/gateways/paypal_pr/booking3_mock.html" %}#}
<style>
    #wordline_eu_error_message{
        color: #d20000!important;
        font-size: 12px!important;
        text-align: center!important;
    }

    #worldline_eu_tokenizer{
        padding: 5px;
        max-width: 400px;
        margin: 0px auto 20px;
    }

    #worldline_eu_tokenizer>div{
        display: flex !important;
        justify-content: center !important;
    }
</style>

<div id="worldline_eu_payment">
    <div id="wordline_eu_error_message" style="display: none">
        Ha ocurrido un error con la pasarela de pago. Pruebe de nuevo o póngase en contacto con nosotros.
    </div>
    <div class="payment-method-selector" id="payment-options">

    </div>
    <div id="worldline_eu_tokenizer">

    </div>

</div>

<script>
    const paraty_logger = new ParatyLogger("{{'/log'|fixurl}}", "{{logging_session}}", namespace="[JS][WORLDLINE_EU2][{{hotel_code}}][{{payment_order_id}}]");
    var tokenizer = null;
    var tpv_cobrador_controller = null;
	var payment_method_type = "insite";
    var redirect_url = "";
	var enable_google_pay = false;
	var enable_apple_pay = false;

	function check_google_pay(){
		const client = new google.payments.api.PaymentsClient({
            environment: 'PRODUCTION',
        });

		var paymentOptionsParams = {
			apiVersion: 2,
            apiVersionMinor: 0,
            allowedPaymentMethods: [
				{
					type: 'CARD',
                    parameters: {
						allowedAuthMethods: ['PAN_ONLY', 'CRYPTOGRAM_3DS'],
                        allowedCardNetworks: ['MASTERCARD', 'VISA'],
                    },
                    tokenizationSpecification: {
						type: 'PAYMENT_GATEWAY'
                    }
                }
            ]
        }

		client.isReadyToPay(paymentOptionsParams).then(function(result){
			if(result.result){
				paraty_logger.info(`Google Pay available`);
				enable_google_pay = true;
            }
			check_apple_pay();
        }).catch(function(e){
            console.log(e);
			paraty_logger.warn(`Error checking google pay ${e}`);
            check_apple_pay();
        })
    }

	function check_apple_pay(){
		if(window.ApplePaySession) {
			try{
                if(ApplePaySession.canMakePayments()){
					paraty_logger.info(`Apple Pay available`);
                    enable_apple_pay = true;
                }
                enable_payment_options_widget();
			}catch(e){
				console.log(e);
				paraty_logger.warn(`Error checking apple pay ${e}`);
				enable_payment_options_widget();
            }
		}else{
			enable_payment_options_widget();
        }
    }

	function enable_payment_options_widget(){
		import("{{'/external/js/libs/payment_widget.js'|fixurl}}").then(function(payment_widget){
			var options = JSON.parse(document.getElementById("payment-options-ctx").innerHTML);
			if(!enable_apple_pay || !enable_google_pay){
				var final_options = []
                for(let option of options){
					var valid_option = true;

					if(option.payment_method == 302 && !enable_apple_pay){
						valid_option = false
                    }

					if(option.payment_method == 320 && !enable_google_pay){
						valid_option = false
                    }

					if(valid_option){
						final_options.push(option)
                    }
                }
				options = final_options;
            }

            payment_widget.init_payment_options(
                "payment-options",
                options,
                function(selected_option){
                    if(selected_option.type == "insite"){
                        $("#worldline_eu_tokenizer").show();
                        payment_method_type = "insite"
                    }else{
                        $("#worldline_eu_tokenizer").hide();
                        payment_method_type = "checkout"
                        redirect_url = selected_option.redirect_url
                    }
                },
                false
            )
            paraty_logger.info(`Paraty payment widget enabled`);
        })
    }


    window.onload = function(){
		check_google_pay();
        tokenizer =  new Tokenizer("{{ token_hosted_url }}", 'worldline_eu_tokenizer', {hideCardholderName: false });
        tokenizer.initialize().then(() => {
			paraty_logger.info(`Tokenizer initialized`);
        // Do work after initialization, if any
        })
    }

    tpv_cobrador_controller = function() {
    return {

        execute_controller: function () {
			if(payment_method_type == "insite"){
                tokenizer.submitTokenization().then((result) => {
                    console.log(result);
                    if (result.success) {
						try{
							var token_id = result.hostedTokenizationId;

							paraty_logger.info(`Token id ${token_id}`);

                            var personal_details_form = document.querySelector("#personal-details-form");
                            if (!personal_details_form) {
                                personal_details_form = document.querySelector("#personalDetailsForm");
                            }

                            var client_metadata = {
                                timeoffset: (new Date()).getTimezoneOffset(),
                                locale: navigator.language,
                                userAgent: navigator.userAgent,
                                javaEnabled: navigator.javaEnabled(),
                                colorDepth: screen.colorDepth,
                                height: screen.height,
                                width: screen.width,
                                email: personal_details_form?personal_details_form.querySelector("[name='email']").value:"",
                                phone: personal_details_form?personal_details_form.querySelector("[name='telephone']").value:""
                            }

							paraty_logger.info(`Client metadata ${JSON.stringify(client_metadata)}`);

                            var data = {
                                "token_id": token_id,
                                "payload": "{{payload}}",
                                "client_metadata": client_metadata
                            }

                            fetch("{{'/worldline_eu2/authorize_payment'|fixurl }}", {
                                method: 'POST',
                                mode: 'cors',
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify(data)
                            }).then(function(res) {
                                return res.json();
                            }).then(function(orderData) {
                                if(orderData.error){
									paraty_logger.warn(`Error from pep: ${orderData.error}`)
                                    $("#wordline_eu_error_message").text(orderData.message)
                                    $("#wordline_eu_error_message").show();
                                    debugger
                                    setTimeout(function() {
                                        window.location.reload();
                                    }, 7000);
                                }
                                if(orderData.redirect_to){
									paraty_logger.info(`Redirecting to ${orderData.redirect_to}`)
                                    window.location.href = orderData.redirect_to;
                                }
                            });
                        } catch (e) {
							paraty_logger.error(`Error creating initial payment ${e}`)
                        }
                    } else {
                        console.error(result)
                    }
                });
            }else{
				paraty_logger.info(`Redirecting to ${redirect_url}`)
				window.location.href = redirect_url;
            }
		}
        }
    }();
</script>
<script>
    $("#destination_cobrador_form").append($("#worldline_eu_payment"));
</script>