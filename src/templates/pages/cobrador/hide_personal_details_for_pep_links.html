<script>
    //SCRIPT ADDED BY COBRADOR FROM PEP_LINKS!!!
document.addEventListener("DOMContentLoaded", function () {

    if (window.location.href.indexOf("booking3_tpv") !== -1) {



        let personalDetailsGrid;
        const bodyHasMobileClass = document.body.className.includes("mobile");

        // 1. Añadir estilos al div con clase "personal_details_grid"
        if (!bodyHasMobileClass) {
            personalDetailsGrid = document.querySelector(".personal_details_grid");
            if (personalDetailsGrid) {
                personalDetailsGrid.style.visibility = "hidden";
                personalDetailsGrid.style.width = "0";
                personalDetailsGrid.style.height = "0";
            }
        }
        else{
            // lo mismo versión movil:
            personalDetailsGrid = document.querySelector(".inputs_wrapper");
            if (personalDetailsGrid) {
                personalDetailsGrid.style.visibility = "hidden";
                personalDetailsGrid.style.height = "0";
            }
        }


        // 2. Añadir estilo width 100% al div con clase "payment_type_grid tpv_payment"
        const paymentTypeGrid = document.querySelector(".payment_type_grid.tpv_payment");
        if (paymentTypeGrid) {
            paymentTypeGrid.style.width = "100%";
        }

        // 3. Mover el div con clase "newsletter_subscribe_query_top" al inicio de "personal_details_payment_wrapper"
         if (!bodyHasMobileClass) {
             const newsletterDiv = document.querySelector(".newsletter_subscribe_query_top");
             const paymentWrapper = document.querySelector(".personal_details_payment_wrapper");
             if (newsletterDiv && paymentWrapper) {
                 paymentWrapper.insertBefore(newsletterDiv, paymentWrapper.firstChild);
             }
         }
         else{
            const newsletterDiv = document.querySelector(".newsletter_subscribe_query_top");
            const formDetailsMobile = document.querySelector(".personal_details_form");

            if (newsletterDiv && formDetailsMobile) {
                // Crear nuevo div contenedor
                const wrapper = document.createElement("div");
                wrapper.className = "inputs_wrapper";
                // Mover newsletterDiv dentro del nuevo contenedor
                wrapper.appendChild(newsletterDiv);
                // Insertar el contenedor en el primer lugar de formDetailsMobile
                formDetailsMobile.insertBefore(wrapper, formDetailsMobile.firstChild);
            }

        }


        const label = document.querySelector("#step-3 .newsletter_subscribe_query_top .check_wrapper label");
        if (label) {
            label.style.width = "60%";
        }

        // 4. Recorrer inputs de tipo text en "personal_details_grid"
        if (personalDetailsGrid) {
            const textInputs = personalDetailsGrid.querySelectorAll('input[type="text"], input[type="tel"], input[type="email"], input[type="number"], input[type="search"], input[type="url"]');
            textInputs.forEach(function (input) {
                const id = input.id;
                const name = input.name;
                if (!input.value) {
                    if (id === "emailConfirmation") {
                        const emailInput = document.getElementById("id_email");
                        if (emailInput) {
                            input.value = emailInput.value;
                        } else {
                            input.value = "<EMAIL>";
                        }
                    } else {
                        if (id === "id_email" || name === "email") {
                            input.value = "<EMAIL>";
                        } else {
                            if (name === "telephone") {
                                input.value = "123456789";
                            } else {
                                input.value = "from_pep_link";
                            }

                        }

                    }
                }

            });
        }
    }
});
</script>
