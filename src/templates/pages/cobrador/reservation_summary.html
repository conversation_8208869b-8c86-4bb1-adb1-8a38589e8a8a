    <!-- <div class="cols">
        <h4 class="col1 title">
           {{ T_PAYMENTS_RESERVATION_INFO }}
        </h4>
    </div> -->

    <table class="zebra_table">
      <tr>
        <th>{{ T_BOOKING_IDENTIFIER }}</th>
       <th>{{ T_CUSTOMER }}</th>
       <th>{{ T_EMAIL }}</th>
        <th>{{ T_PRICE }}</th>
        <th>{{ T_TOTAL_PAYED }}</th>
        <th>{{ T_PENDING_PAYED }}</th>

      </tr>

          <tr>
            <td>{{ identifier }}</td>
              <td>{{ customer }}</td>
              <td>{{ email }}</td>
            <td>{{ total_price }}</td>
            <td>
                <p id="total_payed">
                    {{ total_payed }}
                </p>
                {% if total_extra_payed != 0.0 %}
                    <p style="font-size: 10px" class="extra_payed">(Extra: + <b style="font-size: 10px" id="total_extra_payed">{{ total_extra_payed }}</b>)</p>
                {% endif %}
            </td>
            <td>{{ total_pending }}</td>
          </tr>

        </table>
    {% if back_button %}
    <div class="">
            <div class="">
                <div class="input_wrapper">
                    <button class="btn btn_small" onclick="window.location.href='{{ back_button.href}}';">{{ back_button.label }}</button>
                </div>
            </div>
            <div class="input_wrapper" style="text-align: right">
                {% if back_button %}
                    <a class="btn btn_link close_modal_info" id="back_button">{{ back_button.label }}</a>
                {% endif %}
            </div>
    {% endif %}

<div id="reservation_amounts_info" hidden="">
    <p id="price_amount_info">{{ total_price }}</p>
    <p id="payed_amount_info">{{ total_payed }}</p>
    <p id="pending_amount_info">{{ total_pending }}</p>
</div>



