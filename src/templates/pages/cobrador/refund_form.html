<script>
    var CURRENCY = "{% if paid_currency %} {{ paid_currency }} {% else %} {{ price_currency }} {% endif %}";
</script>
<input type="hidden" name="locale_language" id="locale_language" value="{{locale_language}}" />
<div class="page">


    {% if show_transactions_to_refund %}
        <div class="cols">
            <h4 class="col1 title" id="new_payment_title">
               {{ T_GENERATE_REFUND }}
            </h4>
        </div>
    {% endif %}

    <div class="zebra_table_wrapper">
      {{ reservation_summary_html|safe }}
    </div>
    <div class="input_wrapper">
        <div class="separator"></div>
    </div>


    {% if not hide_form %}
        <div class="cols">
            <h4 class="col1 title" id="new_payment_title">
               {{ T_GENERATE_REFUND }}
            </h4>
        </div>


         {% if transaction_sumary %}
            <div class="zebra_table_wrapper">
              {{ transaction_sumary|safe }}
            </div>

         {% endif %}


        <form action="/pages/cobrador/do_refund" class="form" method="post" id="new_refund_form">
             <span type="hidden" id="t_are_you_sure" style="display:none">{{T_ARE_YOU_SURE}} {{ T_REFUND_AMOUNT_CONFIRMATION}}</span>
            <span type="hidden" id="t_error_max_amount" style="display:none">{{ T_ERROR_MAX_NOT_PERMITED_REFUND }}</span>
            <input type="hidden" name="hotel_code" value="{{hotel_code}}" />
            <input type="hidden" name="sessionKey" value="{{session_key}}" />
            <input type="hidden" name="identifier" value="{{identifier}}" />
            <input type="hidden" name="order" value="{{order}}" />
             <input type="hidden" id="max_permited_amount" value="{{max_permited_amount}}" />

            <div class="cols">

                 <div class="input_wrapper">
                     <label>{{ T_REFUND_AMOUNT }}</label>
                    <div class="input">
                         <input type="text" value="{{default_value_to_refund}}"
                                id="amount" name="amount" placeholder="" required
                                 {%  if full_refund_mandatory %} readonly onClick="(function(){alert('{{ T_ERROR_FULL_REFUND_MANDATORY }}');
    return false;})();return false;"{% endif %}>
                    </div>

                </div>


                 <div class="input_wrapper">
                     <label>{{ T_DESCRIPTION }} </label>
                    <div class="input">
                         <textarea value="" id="comments" name="comments" placeholder="" ></textarea>
                    </div>
                </div>

               <div class="col1 input_wrapper">
                <label>{{ T_EXCLUDE_RULES }}</label>
                     <div class="input select">
                         <select name="rules" id="rules" class="rule-selection" multiple>
                              {% if (rules)%}
                                        <option value="all">{{ T_ALL }}</option>
                              {% endif %}
                             {% for rule in rules %}
                                 <option value="{{ rule.id }}" {{ "selected" if rule.selected == "true" }}> {{ rule.description }}</option>
                             {% endfor %}

                         </select>
                     </div>
                </div>

                {% if payment_type != "manual" %}
                    <div class="col1 input_wrapper input_inline checkbox">
                        <input type="checkbox" title="i" name="extra_payment" id="extra_payment" {{ "checked" if is_extra_payment }}> <label>{{T_REFUND_EXTRA_IN_RECEPTION}}</label>
                    </div>
                {% endif %}

                <div class="col1 input_wrapper input_inline checkbox">
                    <input type="checkbox" title="i" name="customer_email" id="customer_email" checked="checked"> <label>{{T_SEND_EMAIL_CUSTOMER}}</label>
                </div>
                <div class="col1 input_wrapper input_inline checkbox">
                    <input type="checkbox" title="i" name="hotel_email" id="hotel_email" checked="checked"> <label>{{T_SEND_EMAIL_HOTEL}}</label>
                </div>

                <div class="col1 input_wrapper input_inline checkbox">
                    <input type="checkbox" title="i" name="send_customer_error_email" id="send_customer_error_email" checked="checked"> <label>{{T_CUSTOMER_NOTIFY_IF_ERROR}}</label>
                </div>

                 <div class="input_wrapper" id="comment_email_wrapper" >
                     <label>{{ T_PAYMENT_COMMENT_CUSTOMER_EMAIL }} </label>
                    <div class="input">
                         <textarea value="" id="comment_email" name="comment_email" placeholder="" ></textarea>
                    </div>
                </div>


                <div class="col1 input_wrapper" style="text-align: right">
                     {% if back_button %}
                        <a class="btn btn_link close_modal_info" id="back_button">{{ back_button.label }}</a>
{#                         <a class="btn btn_small btn_link" onclick="window.location.href='{{ back_button.href}}';" id="back_button">{{ back_button.label }}</a>#}
                    {% endif %}
                    <button class="form_submit btn" type="submit" value="Submit" id="btn_submit_new_payment"  form="new_refund_form">{{T_GENERATE_REFUND}}</button>

                </div>


            </div>

        </form>


        <div class="input_wrapper">
            <div class="separator"></div>
        </div>

    {% endif %}




    {% if payment_id %}
         <div id="confirmations_text" class="confirmations_text">
            {{T_REFUND_DONE_OK}}
                <div class="input_wrapper">
                    <div class="separator"></div>
                </div>
            </div>
    {% endif %}

     {% if error %}
         <div id="confirmations_text" class="confirmations_text pay_status_color_ko">

          {% if error == "true" %}
                {{T_GENERIC_ERROR}}
             {% endif %}
            {% if error == "payment" %}
                     {{T_PAYMENT_ERROR}}
            {% endif %}
            {% if error == "payment_time" %}
                    {{T_PAYMENT_ERROR_TIME}}

            {% endif %}
                <div class="input_wrapper">
                    <div class="separator"></div>
                </div>
            </div>



    {% endif %}


     {% if table_list_html %}
     <div class="cols">
            <h4 class="col1 title">
               {{ T_PAYMENTS_TABLE_TITLE }}
            </h4>
     </div>

     <div class="zebra_table_wrapper">
      {{ table_list_html|safe }}
    </div>
    {% endif %}

</div>

<script>
    $(window).ready(function() {
        $("select.rule-selection").chosen();
    })
</script>