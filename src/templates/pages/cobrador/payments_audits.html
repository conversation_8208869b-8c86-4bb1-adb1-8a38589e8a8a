<link rel="stylesheet" href="/static/css/libs/jquery.json-viewer.css">
<link rel="stylesheet" href="/static/css/libs/simpleXML.css">
<div class="page">
    <div class="cols form_search">
        <div class="cols">
            <h4 class="col1 title">Filtros</h4>
        </div>
        <div class="input_wrapper col3">
            <label for="sid">SID</label>
            <div class="input">
                <input type="text" name="web_sid" id="sid">
            </div>
        </div>
        <div class="input_wrapper col3">
            <label for="payment_order_id">ID de pago</label>
            <div class="input">
                <input type="text" name="payment_order_id" id="payment_order_id">
            </div>
        </div>
        <div class="input_wrapper col3">
            <label for="reservation_id">ID de reserva</label>
            <div class="input">
                <input type="text" name="reservation_id" id="reservation_id">
            </div>
        </div>
        <div class="input_wrapper col3">
            <label for="response">Respuesta</label>
            <div class="input">
                <input type="text" name="response" id="response">
            </div>
        </div>
        <div class="input_wrapper col3">
            <label for="response">Payload</label>
            <div class="input">
                <input type="text" name="payload" id="payload">
            </div>
        </div>
        <div class="input_wrapper col3">
            <label for="response">Tipo</label>
            <div class="input">
                <select name="type" id="type">
                    <option value="" disabled selected>Selecciona un tipo</option>
                    <option value="Process payment">Process payment</option>
                    <option value="Form Payment">Form Payment</option>
                    <option value="">Todos</option>
                </select>
            </div>
        </div>
        <div class="col3 input_wrapper">
            <a class="btn btn_small"  id="payment_audit_search_button">{{T_SEARCH}}</a>
        </div>
    </div>


        {% include "pages/cobrador/payments_audits_list.html" %}

</div>

<div class="popup_wrapper" style="display: none">
    <div class="background"></div>
    <div class="popup_body">
        <div class="popup_title">Info del payment</div>
        <div class="popup_content"></div>
    </div>
</div>

<script src="/static/js/libs/jquery.json-viewer.js"></script>
<script src="/static/js/libs/simpleXML.js"></script>

<script>

    //Show/hide payments audits response and exchange img (plus/minus)
    $(document).on("click", ".button_response", function () {
        let button = $(this),
            button_parent = button.closest(".payments_audits_info"),
            popup_data = button_parent.find(".popup_data"),
            popup_wrapper = $(".popup_wrapper");

        try {
            popup_wrapper.find(".popup_content").jsonViewer(JSON.parse(popup_data.text()));
        } catch (e) {
            try {
                popup_wrapper.find(".popup_content").simpleXML({
                    xmlString: popup_data.html()
                });
            } catch (f) {
                popup_wrapper.find(".popup_content").html(popup_data.html());
            }
        }
        popup_wrapper.fadeIn();
    });

    $(".popup_wrapper .background").on("click", function () {
        $(".popup_wrapper").fadeOut();
    });

    //Search function
    let payment_audit_search_button = $('#payment_audit_search_button');
    payment_audit_search_button.on("click", function (){

        let search_inputs = $(".form_search input, .form_search select");
        let data = {}
        search_inputs.each(function (){
            if ($(this).val()) {
                data[$(this).attr("name")] = $(this).val();
            }
        })

        $.ajax({
            method: 'POST',
            url: "/pages/show_filter_payments_audits",
            data: JSON.stringify(data),
            contentType: "application/json",
            success: function(data)
            {
                $("table").html(data);
            },
            error:function(jqXHR, textStatus, errorThrown){
                alert("{{ T_GENERIC_ERROR }}")
            }
        });
    })
</script>