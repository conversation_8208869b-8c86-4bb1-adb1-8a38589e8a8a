<div class="page">
    <input type="hidden" name="multiple_tokenization" id="multiple_tokenization" value="{{multiple_tokenization}}" />
    <input type="hidden" name="allow_tokenize_with_payments" id="allow_tokenize_with_payments" value="{{allow_tokenize_with_payments}}" />
    <div class="zebra_table_wrapper">
      {{ reservation_summary_html|safe }}
    </div>

    <div class="input_wrapper">
        <div class="separator"></div>
    </div>
     <div class="cols">
        <h4 class="col1 title" id="new_link_title">
           {{ T_NEW_LINK_TITLE }}
        </h4>
    </div>

    <form action="/pages/cobrador/send_new_link?hotel_code={{ hotel_code }}" class="form" method="post" id="new_link_form">
        <span type="hidden" id="t_are_you_sure" style="display:none">{{T_ARE_YOU_SURE}}</span>
        <input type="hidden" name="hotel_code"  id="hotel_code" value="{{hotel_code}}" />
        <input type="hidden" name="sessionKey" value="{{session_key}}" />
        <input type="hidden" name="identifier" id="identifier" value="{{identifier}}" />
        <input type="hidden" name="locale_language" id="locale_language" value="{{locale_language}}" />



         <div class="input_wrapper">
            <label>{{T_NEW_LINK_TYPE}}</label>
            <div class="input select">
                <select name="type_link" id="type_link">
                    <option value="pending">{{T_NEW_LINK_TYPE_OPTION_PENDING}}</option>
                    {#   <option value="full">{{T_NEW_LINK_TYPE_OPTION_FULL}}</option> #}
                    {% if gateway_has_token %}
                    <option value="token">{{T_NEW_LINK_TYPE_OPTION_TOKEN}}</option>
                    {% endif %}
                    <option value="price">{{T_NEW_LINK_TYPE_OPTION_PRICE}}</option>
                    <option value="datatrans">{{T_SAVE_CREDIT_CARD_PCI}}</option>
                </select>
            </div>
        </div>


         <div class="input_wrapper" id="new_link_amount" style="display:none;">
            <label>{{T_PAYMENT_AMOUNT}}</label>
            <div class="input">
                 <input type="text" value="" id="amount" name="amount" placeholder="123.12">
            </div>

         </div>


         <div class="input_wrapper" id="comment_email_wrapper" >
             <label>{{ T_COMMENTS_FOR_CLIENT }} </label>
            <div class="input">
                 <textarea value="" id="comment_email" name="comment_email" placeholder="" ></textarea>
            </div>
        </div>

         <div class="input_wrapper">
             <div class="input">
                <a id="generate_payment_link" href="#" style="text-decoration:underline">{{T_GENERATE_LINK_AJAX}}</a>
             </div>
             <div class="input input_wrapper" id="wrapper_generated_link" style="display:none">
                 <input type="text" value="" id="generated_link" name="generated_link" readonly><br>
                 <div class="input_wrapper">
                 <a id="check_link" href="#" style="text-decoration:underline" target="_blank"><i class="fad fa-link"></i>{{T_CHECK_LINK}}</a>&nbsp;&nbsp;&nbsp;<a id="copy_link" href="#" style="text-decoration:underline"><i class="fad fa-copy"></i>{{T_COPY_LINK}}</a>
                </div>
            </div>
         </div>
        <div class="check_links_restrictions_wrappers">

            <input type="checkbox" name="unique_usage" id="unique_usage" checked>
            <label for="unique_usage">{{ T_single_use_link }}</label>

            <input type="checkbox" name="allow_sending" id="allow_sending" checked>
            <label for="unique_usage">{{ T_allow_sending }}</label>

        </div>


        <div class="input_wrapper" style="text-align: right">
        {% if back_button %}
            <a class="btn btn_link close_modal_info" id="back_button">{{ back_button.label }}</a>
{#            <a class="btn btn_link" onclick="window.location.href='{{ back_button.href}}'" id="back_button">{{ back_button.label }}</a>#}
        {% endif %}
        <button class="form_submit btn" type="submit" value="Submit"  form="new_link_form" id="submit_button">{{T_SEND_EMAIL_CUSTOMER}}</button>
        </div>


    </form>


</div>

<div class="error_limit_amount_link" style="display:none">
    <p style="margin: 10px 0">{{ error_limit_amount_link }}</p>
</div>
<span type="hidden" id="t_error_0_amount_link" style="display:none">{{T_ERROR_0_AMOUNT_LINK}}</span>
<span type="hidden" id="t_error_token_link_if_payed" style="display:none">{{T_ERROR_TOKEN_LINK_IF_PAYED}}</span>
