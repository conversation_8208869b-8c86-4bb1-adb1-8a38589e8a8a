<table class="zebra_table">
    <tr>
        <th>{{ T_PAYMENT_DATE }}</th>
        <th>{{ T_PAYMENT_TYPE }}</th>
        <th>{{ T_PAYMENT_ORDER }}</th>
        <th>{{ T_ERROR }}</th>
        <th>{{ T_AMOUNT }}</th>
        <th>{{ T_USER }}</th>
        <th>{{ T_BOOKING_IDENTIFIER }}</th>
        <th>{{ T_COMMENTS }}</th>
          {% if show_transactions_to_refund %}
           <th></th>
          {% endif %}
    </tr>
    {% for payment in payments_list %}
    <tr class="{{ payment.color_class_transaction }} {% if payment.deleted %}logical_removed{% endif %}">
        <td>{{ payment.timestamp }}</td>
        <td>{{ payment.type_txt }}
        {% if payment.rules_descripcion %}
            <div class="popup_info_p_{{ payment.index }}" style="display:none">
                <p style="margin: 10px 0">{{ payment.rules_descripcion }}</p>
            </div>
            <a class="btn btn_small btn_link" onclick="info({{ payment.index}})"><i class="toolkit fad fa-info info" ></i></a>
        {% endif %}
        </td>
        <td>{{ payment.order }}</td>
        <td>
            {% if payment.error %}
                {{ payment.error_code}}
                    {% if payment.error_info %}
                        <div class="popup_error_info" style="display:none">
                            <p style="margin: 10px 0">ID de pago: {{ payment.order }}</p>
                            <p style="margin: 10px 0">Error recibido de la entidad bancaria: {{payment.error_code}} </p>
                            <p style="margin: 10px 0">{{ payment.error_info }}</p>
                        </div>
                        <a class="btn btn_small btn_link" onclick="error_info()"><i class="toolkit fad fa-info error_info" ></i></a>
                    {% endif %}
            {% endif %}
        </td>
        <td>{% if payment.deleted_amount  %} {{ payment.deleted_amount }} {% else %} {{ payment.amount }} {% endif %} {% if paid_currency %} {{ paid_currency }} {% else %} {{ price_currency }} {% endif %}</td>
        <td>{{ payment.user }}</td>
        <td>{{ payment.reservation_identifier }}</td>
        <td id="comment_{{ payment.index }}"  style="max-width: 200px; max-height: 100px; overflow: auto; white-space: nowrap;">{{ payment.comments|safe }}</td>
        {% if show_transactions_to_refund and payment.amount > 0 and not payment.type == "manual_extra" and not payment.type == "Envío de link al cliente" and not payment.error%}
        <td>
           <a url="/pages/cobrador/refund_form?identifier={{payment.reservation_identifier}}&sessionKey={{session_key}}&order={{ payment.order }}{{ "&is_extra=true" if payment.type and payment.type|lower == "extra" }}&hotel_code={{ hotel_code }}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-hand-holding-usd"><span class="tooltip">{{ T_GENERATE_REFUND }}</span></i></a>
        </td>
        {% endif %}
        {% if payment.datatrans %}
        <td>
           <a href="{{payment.datatrans}}" target="_blank">
               <i class="fas fa-credit-card"></i>
           </a>
        </td>
        {% endif %}
        {% if payment.amount or payment.deleted %}
        <td>
            <a class="btn btn_small btn_link">
                <i class="fas fa-edit edit_comment"
                 style="display: inline-block"
                 data-field_comment="{{ payment.comments}}"
                 data-id_comment="comment_{{ payment.index }}"
                 data-field_key="{{ payment }}"
                 data-identifier="{{ payment.order }}"
                 data-field_hotel_code="{{ payment.hotel_code }}"
                 data-user="{{ user }}"
                >
                </i>
            </a>
            <a class="btn btn_small btn_link" {% if payment.payed_by_tpv_link_index %} payed_by_tpv_link_index="{{ payment.payed_by_tpv_link_index }}"{% endif %} {% if payment.id %}payment_internal_id="{{ payment.id }}" {% else %}payment_internal_id="payed_by_tpv_link_{{ payment.reservation_identifier }}"{% endif %}>
                <i class="fas fa-trash delete_historic_payment" type="delete" {% if not payment.deleted %}style="display: inline-block" {% else %} style="display: none" {% endif %}></i>
                <i class="fas fa-recycle recover_historic_payment" type="recover" {% if payment.deleted %}style="display: inline-block" {% else %} style="display: none" {% endif %}></i>
            </a>
        </td>
        {% endif %}
    </tr>
    {% endfor %}
    <tr>
        <td colspan="7"></td>
        {% if show_transactions_to_refund %}
        <td></td>
        {% endif %}
    </tr>
    <tr>
        <th >{{ T_TOTAL_PAYMENTS }}: {{ summary.total_payments }}</th>
        <th></th>
        <th>{{ T_TOTAL_PAYED }}: {{ summary.total_payed }} {% if paid_currency %} {{ paid_currency }} {% else %} {{ price_currency }} {% endif %}</th>
        <th colspan="4"></th>
        {% if show_transactions_to_refund %}
            <th></th>
        {% endif %}
    </tr>
</table>

{% if back_button %}
    <div class="">
{#            <div class="">#}
{#                <div class="input_wrapper">#}
{#                    <button class="btn btn_small close_popup_info">{{ back_button.label }}</button>#}
{#                    <a class="btn btn_link close_popup_info" id="back_button">{{ back_button.label }}</a>#}
{#                </div>#}
{#            </div>#}
            <div class="input_wrapper" style="text-align: right">
                {% if back_button %}
                    <a class="btn btn_link close_modal_info" id="back_button">{{ back_button.label }}</a>
                {% endif %}
            </div>
{#        <div class="">#}
{#                <div class="input_wrapper">#}
{#                    <button class="btn btn_small" onclick="window.location.href='{{ back_button.href}}';">{{ back_button.label }}</button>#}
{#                </div>#}
{#        </div>#}
    </div>
{% endif %}

<span type="hidden" id="t_delete" style="display:none">{{T_DELETE}}</span>
<span type="hidden" id="t_edit" style="display:none">{{T_EDIT_COMMENT}}</span>
<span type="hidden" id="t_delete_historic_payment" style="display:none">{{T_REMOVE_HISTORIC_PAYMENT}}</span>
<span type="hidden" id="t_recover" style="display:none">{{T_RECOVER}}</span>
<span type="hidden" id="t_recover_historic_payment" style="display:none">{{T_RECOVER_HISTORIC_PAYMENT}}</span>
