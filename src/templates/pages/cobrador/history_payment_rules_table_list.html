<script src="/static/js/libs/jquery.json-viewer.js"></script>
<script>
    function renderJSON(id) {
    // Function to be able to see the prettiest json
    // Requires to operate: jquery.json-viewer.css & jquery.json-viewer.js (obviously jquery.js too)
    try {
      object_name_search = '#textJson-' + id;
      input_search =  $(object_name_search).text().replace(/'/g, '"').trim() ;
      input_search = input_search.replace(/None/g, 'null');
      input_search = input_search.replace(/False/g, 'null');
      input_search=JSON.parse(input_search);

      var render_search = '#preJson-' + id; // This is the id of the <pre> item in html; where we put the <pre> the new JSON will appear

    }
    catch (error) {
      return console.log("ID:" + id + " - Cannot eval JSON: ", input_search);
    }
    var options = {
      collapsed: true,
      rootCollapsable: true,
      withQuotes: $('#with-quotes').is(':checked'),
      withLinks: $('#with-links').is(':checked')
    };

    $(render_search).jsonViewer(input_search, options);
  }
</script>
<table class="zebra_table">
      <thead>
            <tr>
                <th>Fecha de Acción</th>
                <th>Usuario</th>
                <th>ID de Regla de Pago</th>
                <th>Regla de Pago</th>
                <th>Detalles de la Acción</th>
                <th>Tipo de Acción</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in all_configurations %}
                <tr id="row_{{loop.index}}" style="color:{% if payment.action_type == 'delete'%} red {% else %} black {% endif %};">
                    <td>{{ payment.action_date }}</td>
                    <td>{{ payment.user }}</td>
                    <td>{{ payment.payment_rule_id }}</td>
                    <td>{{ payment.payment_rule_description }}</td>
                    <td>
                        <pre id="preJson-{{loop.index}}" style="text-align: left"></pre>
                        <span>
                            <textarea id="textJson-{{loop.index}}" autocomplete="off" style="width:100%;display:none">{{ payment.action_details }}</textarea>
                        </span>
                    </td>
                    <td>{{ payment.action_type }}</td>
                </tr>

                <script>renderJSON("{{loop.index}}")</script>

            {% endfor %}
        </tbody>
</table>
