<script>
    var CURRENCY = "{{ currency }}";
</script>
<div class="page">


    <div id="data1" class="zebra_table_wrapper">
      {{ reservation_summary_html|safe }}
    </div>
    <div class="input_wrapper">
        <div class="separator"></div>
    </div>
    {% if not hide_form %}
        <div class="cols">
            <h4 class="col1 title" id="new_payment_title">
               {{ T_NEW_PAYMENT }}
            </h4>
        </div>

        <form action="/pages/cobrador/do_payment" class="form" method="post" id="new_payment_form">
             <span type="hidden" id="t_are_you_sure" style="display:none">{{T_ARE_YOU_SURE}} {{ T_PAYMENT_AMOUNT_CONFIRMATION}}</span>
             <span type="hidden" id="t_error_max_amount" style="display:none">{{ T_ERROR_MAX_NOT_PERMITED }}</span>
            <input type="hidden" name="hotel_code" value="{{hotel_code}}" />
            <input type="hidden" name="sessionKey" value="{{session_key}}" />
            <input type="hidden" name="identifier" value="{{identifier}}" />
            <input type="hidden" id="max_permited_amount" value="{{max_permited_amount}}" />



            <div class="cols">

                 <div class="input_wrapper">
                     <label>{{ T_PAYMENT_AMOUNT }}</label>
                    <div class="input">
                         <input type="text" value="" id="amount" name="amount" placeholder="" required>
                    </div>

                </div>


                 <div class="input_wrapper">
                     <label>{{ T_DESCRIPTION }} </label>
                    <div class="input">
                         <textarea value="" id="comments" name="comments" placeholder=""></textarea>
                    </div>
                </div>

                <div class="col1 input_wrapper input_inline checkbox">
                    <input type="checkbox" title="i" name="extra_payment" id="extra_payment"> <label>{{T_PAYMENT_EXTRA_IN_RECEPTION}}</label>
                </div>

                <div class="col input_wrapper">
                    <label>{{ T_EXCLUDE_RULES }}</label>
                        <div class="input select">
                            <select  class="chosen" name="rules" data-placeholder="rules" multiple>
                                {% if (rules)%}
                                    <option value="all">{{ T_ALL }}</option>
                                {% endif %}
                                {% for rule in rules %}
                                    <option value="{{rule.id}}">{{rule.description}}</option>
                                {% endfor %}
                            </select>
                        </div>
                </div>
                <div class="col1 input_wrapper input_inline checkbox">
                    <input type="checkbox" title="i" name="send_modification_email" id="send_modification_email">
                    <label>{{T_SEND_NOTIFICATION_EMAIL_MODIFICATION_TO_CUSTOMER}}</label>
                </div>

                <div class="col1 input_wrapper input_inline checkbox">
                    <input type="checkbox" title="i" name="customer_email" id="customer_email" checked="checked"> <label>{{T_SEND_EMAIL_CUSTOMER}}</label>
                </div>
                <div class="col1 input_wrapper input_inline checkbox">
                    <input type="checkbox" title="i" name="hotel_email" id="hotel_email" checked="checked"> <label>{{T_SEND_EMAIL_HOTEL}}</label>
                </div>

                <div class="col1 input_wrapper input_inline checkbox">
                    <input type="checkbox" title="i" name="send_customer_error_email" id="send_customer_error_email" checked="checked"> <label>{{T_CUSTOMER_NOTIFY_IF_ERROR}}</label>
                </div>

                 <div class="input_wrapper" id="comment_email_wrapper" >
                     <label>{{ T_PAYMENT_COMMENT_CUSTOMER_EMAIL }} </label>
                    <div class="input">
                         <textarea value="" id="comment_email" name="comment_email" placeholder="" ></textarea>
                    </div>
                </div>

                <div class="col1 input_wrapper" style="text-align: right">
                    {% if back_button %}
                        <a class="btn btn_link close_modal_info" id="back_button">{{ back_button.label }}</a>
{#                        <a class="btn btn_small btn_link" onclick="window.location.href='{{ back_button.href}}';" id="back_button">{{ back_button.label }}</a>#}
                    {% endif %}
                    <button class="form_submit btn" type="submit" value="Submit" id="btn_submit_new_payment"  form="new_payment_form">{{T_DO_PAY}}</button>

                </div>


            </div>

        </form>
        <div class="input_wrapper">
            <div class="separator"></div>
        </div>

    {% endif %}


     <div class="cols">
            <h4 class="col1 title">
               {{ T_PAYMENTS_TABLE_TITLE }}
            </h4>
     </div>

    {% if payment_id %}
         <div id="confirmations_text" class="confirmations_text">
            {{T_PAYMENT_DONE_OK}}
                <div class="input_wrapper">
                    <div class="separator"></div>
                </div>
            </div>
    {% endif %}

     {% if error %}
         <div id="confirmations_text" class="confirmations_text pay_status_color_ko">

             {% if error == "true" %}
                {{T_GENERIC_ERROR}}
             {% endif %}
            {% if error == "payment" %}
                     {{T_PAYMENT_ERROR}}
            {% endif %}
            {% if error == "payment_time" %}
                    {{T_PAYMENT_ERROR_TIME}}

            {% endif %}
                <div class="input_wrapper">
                    <div class="separator"></div>
                </div>
            </div>



    {% endif %}

    <div id="data2" class="zebra_table_wrapper">
      {{ table_list_html|safe }}
    </div>
    <div style="width: 100%; text-align: right;">
        <form id="pdf-build-form-details" action="/pages/cobrador/build_pdf_list" method="POST" target="_blank">
            <input type ="hidden" id="content-pdf-to-build-details" name="content-pdf-to-build-details">
            <input type="hidden" id="pdf_type" name="pdf_type" value="details">
            <input type="hidden" name="sessionKey" value="{{session_key}}" />
            <a class="btn" id="button_gen_pdf_details">{{ T_GENERATE_PDF }}</a>
        </form>
    </div>
</div>

<script>
    window.onload = ()=>{
        $(".chosen").chosen();    }
</script>