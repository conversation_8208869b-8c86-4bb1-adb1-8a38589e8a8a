
<div id="search_filter_text" class="search_filter_text">
      {% if not default_search %}<a href="/pages/cobrador/reservations?sessionKey={{session_key}}&language={{ locale_language }}" class="btn btn_small btn_link"><i class="fad fa-backspace"></i></a>{% endif %} {{ filter_text|safe }}
</div>
<div class="input_wrapper">
            <div class="separator"></div>
        </div>

<table class="zebra_table">
      <tr>
        <th class="short_td">{{ T_STATUS }}</th>
        <th class="short_td">{{ T_LOCALIZADOR }}</th>
        <th class="short_td">{{ T_BOOK_DATE }}</th>
        <th class="short_td">{{ T_CUSTOMER }}</th>
        <th>{{ T_ENTRY }} - {{ T_DEPARTURE }}</th>
        <th  class="short_td">{{ T_PRICE }}</th>
        <th  class="short_td">{{ T_AMOUNT_PAYED }}</th>
        {% if has_remote_hotels %}<th class="short_td">{{ T_HOTEL }}</th>{% endif %}
       </tr>
       {% for reservation in reservations %}
          <tr id="{{ reservation.identifier }}">
            <td><span class="circle {{ reservation.status }}"></span></td>
            <td>{{ reservation.identifier }}</td>
            <td>{{ reservation.timestamp }}</td>
            <td>{{ reservation.name }} {{ reservation.lastName }}</td>
            <td>{{ reservation.startDate }}<br>{{ reservation.endDate }}</td>
            <td><strong>{{ reservation.total }} {% if reservation.price_currency %}{{ reservation.price_currency }}{% else %}{{ reservation.currency }}{% endif %}</strong></td>
            <td>
                <strong class="pay_status_color_{{ reservation.pay_status }}">
                    <p id="{{ 'total_payed_'+reservation.identifier }}">{{ reservation.total_payed }} {{ reservation.currency }}</p>
                </strong>
                {% if reservation.total_extra_payed != 0.0 %}
                    <p style="font-size: 10px" class="extra_payed">(Extra: + <b style="font-size: 10px" id="{{ 'total_extra_payed_'+reservation.identifier }}">{{ reservation.total_extra_payed }}</b> {{ reservation.currency }})</p>
                {% endif %}
            </td>
            {% if has_remote_hotels %}<td>{{ reservation.hotel }}</td>{% endif %}
          <td class="visual_icons">
              <a url="/pages/cobrador/form_send_link?identifier={{reservation.identifier}}&sessionKey={{session_key}}&language={{ locale_language }}&hotel_code={{ reservation.hotel_code }}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-link"><span class="tooltip">{{ T_NEW_LINK_EMAIL_TITLE }}</span></i></a>
{#            <a href="/pages/cobrador/form_send_link?identifier={{reservation.identifier}}&sessionKey={{session_key}}" class="btn btn_small btn_link"><i class="toolkit fad fa-link"><span class="tooltip">{{ T_NEW_LINK_EMAIL_TITLE }}</span></i></a>#}
             {% if reservation.has_token %}
              <a url="/pages/cobrador/payment_form?identifier={{reservation.identifier}}&sessionKey={{session_key}}&language={{ locale_language }}&hotel_code={{ reservation.hotel_code }}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-credit-card"><span class="tooltip">{{ T_NEW_PAYMENT }}</span></i></a>
{#                 <a href="/pages/cobrador/payment_form?identifier={{reservation.identifier}}&sessionKey={{session_key}}" class="btn btn_small btn_link"><i class="toolkit fad fa-credit-card"><span class="tooltip">{{ T_NEW_PAYMENT }}</span></i></a>#}
              {% endif %}

              {% if not hidden_refund and reservation.has_refund %}
                  <a url="/pages/cobrador/refund_list?identifier={{reservation.identifier}}&sessionKey={{session_key}}&language={{ locale_language }}&hotel_code={{ reservation.hotel_code }}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-hand-holding-usd"><span class="tooltip">{{ T_GENERATE_REFUND }}</span></i></a>
{#                  <a href="/pages/cobrador/refund_list?identifier={{reservation.identifier}}&sessionKey={{session_key}}" class="btn btn_small btn_link"><i class="toolkit fad fa-hand-holding-usd"><span class="tooltip">{{ T_GENERATE_REFUND }}</span></i></a>#}
              {% endif %}

              {% if reservation.has_historic %}
              <a url="/pages/cobrador/payments_historic?identifier={{reservation.identifier}}&sessionKey={{session_key}}&language={{ locale_language }}&hotel_code={{ reservation.hotel_code }}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-file-invoice-dollar"><span class="tooltip">{{ T_SEE_PAYMENTS }}</span></i></a>
{#                  <a href="/pages/cobrador/payments_historic?identifier={{reservation.identifier}}&sessionKey={{session_key}}" class="btn btn_small btn_link"><i class="toolkit fad fa-file-invoice-dollar"><span class="tooltip">{{ T_SEE_PAYMENTS }}</span></i></a>#}
              {% endif %}

               <a url="/pages/cobrador/manual_payment?identifier={{reservation.identifier}}&sessionKey={{session_key}}&language={{ locale_language }}&hotel_code={{ reservation.hotel_code }}" class="btn btn_small btn_link open_modal show_modal_info">  <i class="toolkit fad fa-download"><span class="tooltip">{{ T_PAYMENT_MANUAL_EXTRA }}</span></i>
{#            <a href="/pages/cobrador/manual_payment?identifier={{reservation.identifier}}&sessionKey={{session_key}}" class="btn btn_small btn_link open_modal">  <i class="toolkit fad fa-edit"><span class="tooltip">{{ T_EDIT }}</span></i>#}

                    </a>


           </td>
          </tr>
       {% endfor %}
          <tr id="new-reservations-placeholder" style="display:none;">
                <td colspan="8"></td>
          </tr>
          <tr>
            <td colspan="8"></td>
            {% if has_remote_hotels %}<td></td>{% endif %}
          </tr>
          <tr>
            <th id="total_reservations">{{ summary.total_reservations }} {{ T_RESERVATIONS }}</th>
            <th colspan="3"></th>
            <th id="total_price">{{ T_TOTAL_AMOUNT }}: {{ summary.total_price }}</th>
             <th id="total_payed">{{ T_TOTAL_PAYED }}: {{ summary.total_payed }}</th>
            {% if has_remote_hotels %}<th></th>{% endif %}
            <th></th>
            <th>
                <button class="btn btn_small" id="more_reservations">{{ T_LOAD_MORE }}</button>
            </th>
          </tr>

    </table>


    {% if back_button %}
    <div class="">
            <div class="">
                <div class="input_wrapper">
                    <button class="btn btn_small" onclick="window.location.href='{{ back_button.href}}';">{{ back_button.label }}</button>
                </div>
            </div>

        </div>
    {% endif %}


<script>

    $(document).ready(function() {
    var page = 1;
    var totalReservations = parseInt({{ total_reservations }});
    var totalPriceDict = {{ total_price_dict | tojson | safe }};
    var totalPayedDict = {{ total_payed_dict | tojson | safe }};
    console.log(totalPriceDict);
    $('#more_reservations').click(function() {
        $("#more_reservations").addClass("btn_disable").prop("disabled", true);
        var newReservationsPlaceholder = $('#new-reservations-placeholder');
        $.get('/pages/cobrador/next_reservations?sessionKey='+"{{session_key}}"+"&page="+page, function(data) {
            console.log(data)
            page++;

            data.next_reservations.forEach(function(reservation){
                var new_row = `<tr id="${reservation.identifier}">
                                <td><span class="circle ${ reservation.status }"></span></td>
                                <td>${ reservation.identifier }</td>
                                <td>${ reservation.timestamp }</td>
                                <td>${ reservation.name } ${ reservation.lastName }</td>
                                <td>${ reservation.startDate }<br>${ reservation.endDate }</td>
                                <td><strong>${ reservation.total } ${ reservation.currency }</strong></td>
                                <td>
                                    <strong class="pay_status_color_${ reservation.pay_status }">
                                        <p id="${ 'total_payed_'+reservation.identifier }">${ reservation.total_payed } ${ reservation.currency }</p>
                                    </strong>

                                </td>`;

                {% if has_remote_hotels %}new_row += `<td>${ reservation.hotel }</td>`{% endif %}

                new_row +=   `<td><a url="/pages/cobrador/form_send_link?identifier=${reservation.identifier}&sessionKey={{session_key}}&language={{ locale_language }}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-link"><span class="tooltip">{{ T_NEW_LINK_EMAIL_TITLE }}</span></i></a>
{#            <a href="/pages/cobrador/form_send_link?identifier=${reservation.identifier}&sessionKey={{session_key}}" class="btn btn_small btn_link"><i class="toolkit fad fa-link"><span class="tooltip">{{ T_NEW_LINK_EMAIL_TITLE }}</span></i></a>#}`;

                if(reservation.has_token){
                    new_row +=  `<a url="/pages/cobrador/payment_form?identifier=${reservation.identifier}&sessionKey={{session_key}}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-credit-card"><span class="tooltip">{{ T_NEW_PAYMENT }}</span></i></a>
    {#                 <a href="/pages/cobrador/payment_form?identifier=${reservation.identifier}&sessionKey={{session_key}}" class="btn btn_small btn_link"><i class="toolkit fad fa-credit-card"><span class="tooltip">{{ T_NEW_PAYMENT }}</span></i></a>#}`;
                }

                if (reservation.has_refund){
                  new_row += `<a url="/pages/cobrador/refund_list?identifier=${reservation.identifier}&sessionKey={{session_key}}&language={{ locale_language }}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-hand-holding-usd"><span class="tooltip">{{ T_GENERATE_REFUND }}</span></i></a>
{#                  <a href="/pages/cobrador/refund_list?identifier=${reservation.identifier}&sessionKey={{session_key}}" class="btn btn_small btn_link"><i class="toolkit fad fa-hand-holding-usd"><span class="tooltip">{{ T_GENERATE_REFUND }}</span></i></a>#}`;
                }

              if (reservation.has_historic){
              new_row += `<a url="/pages/cobrador/payments_historic?identifier=${reservation.identifier}&sessionKey={{session_key}}&language={{ locale_language }}" class="btn btn_small btn_link show_modal_info"><i class="toolkit fad fa-file-invoice-dollar"><span class="tooltip">{{ T_SEE_PAYMENTS }}</span></i></a>
{#                  <a href="/pages/cobrador/payments_historic?identifier=${reservation.identifier}&sessionKey={{session_key}}" class="btn btn_small btn_link"><i class="toolkit fad fa-file-invoice-dollar"><span class="tooltip">{{ T_SEE_PAYMENTS }}</span></i></a>#}`;
              }

              new_row +=    `<a url="/pages/cobrador/manual_payment?identifier=${reservation.identifier}&sessionKey={{session_key}}&language={{ locale_language }}" class="btn btn_small btn_link open_modal show_modal_info">  <i class="toolkit fad fa-download"><span class="tooltip">{{ T_PAYMENT_MANUAL_EXTRA }}</span></i>
{#            <a href="/pages/cobrador/manual_payment?identifier=${reservation.identifier}&sessionKey={{session_key}}" class="btn btn_small btn_link open_modal">  <i class="toolkit fad fa-edit"><span class="tooltip">{{ T_EDIT }}</span></i>#}</td>`;


                new_row += `</tr>`;
                newReservationsPlaceholder.before(new_row);

                var currency = reservation.currency;
                if (totalPriceDict[currency] !== undefined) {
                    totalPriceDict[currency] += reservation.total;
                } else {
                    totalPriceDict[currency] = reservation.total;
                }

                if (totalPayedDict[currency] !== undefined) {
                    totalPayedDict[currency] += reservation.total_payed;
                } else {
                    totalPayedDict[currency] = reservation.total_payed;
                }

            });
            totalReservations += parseInt(data.next_reservations.length);
            $('#total_reservations').text(totalReservations + ' {{ T_RESERVATIONS }}');
            updateTotalPayed(totalPayedDict);
            updateTotalPrice(totalPriceDict);
            $("#more_reservations").removeClass("btn_disable").prop("disabled", false);
        });
    });
});

    function updateTotalPrice(totalPriceDict){
        var result = [];

        for (var currency in totalPriceDict) {
            if (totalPriceDict.hasOwnProperty(currency)) {
                result.push(totalPriceDict[currency].toFixed(2) + ' ' + currency);
            }
        }

        var finalResult = result.join(' + ');
        $('#total_price').text('{{ T_TOTAL_AMOUNT }} ' + finalResult);
    }

    function updateTotalPayed(totalPayedDict){
        var result = [];

        for (var currency in totalPayedDict) {
            if (totalPayedDict.hasOwnProperty(currency)) {
                result.push(totalPayedDict[currency].toFixed(2) + ' ' + currency);
            }
        }

        var finalResult = result.join(' + ');
        $('#total_payed').text('{{ T_TOTAL_PAYED }} ' + finalResult);
    }

</script>
