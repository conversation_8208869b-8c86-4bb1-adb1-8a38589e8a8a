{% from "pages/cobrador/_country_selector.html" import build_country_selector %}
{% from "pages/cobrador/_language_selector.html" import build_language_selector %}
<div class="page">

    <div class="cols">
        <h4 class="col1 title">
           {{ T_TITLE_COBRADOR_NEW_CONFIG }}
        </h4>
    </div>

    <form action="/pages/cobrador/save_configuration" class="form cols" method="post" id="new_config_form">
        <span type="hidden" id="t_error_fechas" style="display:none">{{T_ERROR_FECHAS}}</span>
        <span type="hidden" id="t_error_fechas_limit" style="display:none">{{T_ERROR_FECHAS_LIMIT}}</span>

        <input type="hidden" name="hotel_code" value="{{hotel_code}}" />
        <input type="hidden" name="sessionKey" value="{{session_key}}" />
        <input type="hidden" name="configuration_id" value="{{configuration_id}}" />
        <input type="hidden" name="calendar_language" id="calendar_language" value="{{ calendar_language }}">
        <input type="hidden" name="locale_language" id="locale_language" value="{{ locale_language }}">


        <div class="col4 input_wrapper">
            <label>{{T_STATUS}}</label>
            <div class="input switch">
                <input id="status" type="checkbox" class="swicth" name="status"  {% if configuration.status == "on" %}checked="checked"{% endif %}>
            </div>
        </div>

        <div class="col3 input_wrapper">
            <label>{{T_PAYMENT_RULE}}</label>
            <div class="input select">
                <select name="type_rule" id="type_rule">
                    <option value="in_web" {% if configuration.type_rule == "in_web" %}selected{% endif %}>{{T_PAYMENT_IN_WEB}}</option>
                    <option value="programmatically" {% if configuration.type_rule == "programmatically" %}selected{% endif %}>{{T_PAYMENT_PROGRAMMATICALLY}}</option>
                    <option value="early_payment" {% if configuration.type_rule == "early_payment" %}selected{% endif %}>{{T_PAYMENT_EARLY}}</option>
                    <option value="create_token" {% if configuration.type_rule == "create_token" %}selected{% endif %}>{{T_CREATE_TOKEN}}</option>
                    <option value="flight_hotel" {% if configuration.type_rule == "flight_hotel" %}selected{% endif %}>Vuelo + Hotel</option>
                    {# <option value="credit_card_conditional" {% if configuration.type_rule == "credit_card_conditional" %}selected{% endif %}>{{T_PAYMENT_CREDIT_CART_CONDITIONAL}}</option> #}
                </select>
            </div>
        </div>

    {% if custom_booking_3_text %}

        <div class="tab-container">
            <label class="section-title">Texto de la pasarela en booking 3</label>
            <div class="content">
                <ul class="tab-list">
                    {% for language, value in all_languages.items() %}
                        <li {% if loop.first %}class="active"{% endif %}><a href="#{{ language }}">{{ language }}</a></li>
                    {% endfor %}
                </ul>
                <div class="input-container">
                    {% for language, value in all_languages.items() %}
                        <input type="text" id="{{ language }}_booking_3_gateway_text" name="{{ language }}_booking_3_gateway_text" placeholder="Escribe aquí..." value="{{ value }}" {% if not loop.first %}style="display:none;"{% endif %}>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

         <div class="col2 input_wrapper">
            <label>{{T_DESCRIPTION}}</label>
            <div class="input">
                 <input type="text" value="{{configuration.description}}" id="description" name="description" placeholder="">
            </div>
        </div>

        <hr>


        <div class="col2 input_wrapper input_inline checkbox optional_token" {% if not configuration.type_rule == "create_token" %}style="display: none" {% endif %}>
            <input type="checkbox" name="optional_tokenizador" id="optional_tokenizador" {% if configuration.type_rule == "create_token" and configuration.optional_payment %}checked{% endif %}>
            <label>{{T_OPTIONAL_TOKEN}}</label>
        </div>

        <div class="col1 input_wrapper early_payment_input_wrapper" style="display: none">
            <label>{{ T_EARLY_PAYMENT_DISCOUNT }}
                <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                    <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                        {{ T_EARLY_PAYMENT_DISCOUNT_DESCRIPTION }}
                    </span>
                </i>
            </label>
            <div class="input">
                <input type="text" placeholder="%" name="early_payment_percent" id="early_payment_percent" value="{{ configuration.early_payment_percent }}">
            </div>
        </div>
        <div class="col1 input_wrapper select_gateway" >
            <label>{{T_PAYMENT_GATEWAY}}</label>
            <div class="input select">
                <select class="gateways" name="use_gateway" id="use_gateway" multiple="multiple">
                    <option value="@@all@@">Todas las pasarelas</option>
                    {% for gateway_option in all_gateways %}
                        <option value="{{ gateway_option }}" {% if gateway_option in gateways %}selected{% endif %}>{{ gateway_option }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="col1 input_wrapper input_inline checkbox use_extra_gateway_cc_div">
            <input type="checkbox" title="i" name="use_extra_gateway_cc" id="use_extra_gateway_cc" {% if gateways_cc %}checked{% endif %}>
            <label>Pasarelas en caso de Callcenter</label>
        </div>
        <div class="col1 input_wrapper select_gateway_cc" style="display: none">
            <div class="input select">
                <select class="gateways" name="use_gateway_cc" id="use_gateway_cc" multiple="multiple">
                    <option value="@@all@@">Todas las pasarelas</option>
                    {% for gateway_option in all_gateways %}
                        <option value="{{ gateway_option }}" {% if gateway_option in gateways_cc %}selected{% endif %}>{{ gateway_option }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div class="col2 input_wrapper country_restriction">
            <label>{{ T_COUNTRY }}</label>
            <div class="input select country_selector" id="country_selector">
                {{ build_country_selector("country", country, multiple=True) }}
            </div>
        </div>
        <div class="col2 input_wrapper country_restriction">
            <label>{{ T_ALL_EXCEPT }}</label>
            <div class="input checkbox">
                <input type="checkbox" name="negative_country" id="negative_country" {% if configuration.negative_country %}checked{% endif %}>
            </div>
        </div>
        <div class="col2 input_wrapper language_restriction">
            <label>{{T_LANGUAGE}}</label>
            <div class="input select language_selector" id="language_selector">
                {{ build_language_selector("language", language, multiple=True) }}
            </div>
        </div>
        <div class="col2 input_wrapper language_restriction">
            <label>{{ T_ALL_EXCEPT }}</label>
            <div class="input checkbox">
                <input type="checkbox" name="negative_language" id="negative_language" {% if configuration.negative_language %}checked{% endif %}>
            </div>
        </div>
         <div class="col1 input_wrapper">
            <label>{{T_PAYMENT_RATE_SELECTION}}</label>
            <div class="input select">
                <select name="type_rate_filter" id="type_rate_filter">
                    <option value="all">{{T_ALL}}</option>
                    <option value="by_policies" {% if configuration.rate_policies %}selected{% endif %}>{{T_PAYMENT_BY_POLICIES}}</option>
                    <option value="by_words" {% if configuration.rate_words %}selected{% endif %}>{{T_PAYMENT_BY_WORDS}}</option>
                    <option value="by_rates" {% if configuration.rates  %}selected{% endif %}>{{T_PAYMENT_BY_RATES}}</option>
                </select>
            </div>
        </div>

        <div class="col1 input_wrapper" id="wraper_payment_by_rates" {% if not configuration.rates %}style="display:none;"{% endif %}>
            <label>{{T_PAYMENT_BY_RATES}}</label>
            <div class="input select">
                <select name="rates" id="rates" class="rate-selection apply_priority" multiple="multiple" increase-priority="1">
                     <option value="@@all@@" {% if all_rate_selected %}selected{% endif %}> {{ T_ALL }} </option>
                    {% for rate in rates %}
                        <option value="{{ rate.id }}"  {% if rate.selected == "true" %}selected{% endif %}> {{ rate.localName }} - {{ rate.name }}</option>
                    {% endfor  %}
                </select>
            </div>
        </div>

         <div class="col1 input_wrapper" id="wraper_payment_by_words" {% if not configuration.rate_words %}style="display:none;"{% endif %}>
            <label>{{T_PAYMENT_BY_WORDS}}</label>
             <div class="input">
                 <input type="text" value="{{configuration.rate_words}}" id="rate_words" name="rate_words" placeholder="" class="apply_priority" increase-priority="1">
            </div>
        </div>

        <div class="col1 input_wrapper" id="wraper_payment_by_policies" {% if not configuration.rate_policies %}style="display:none;"{% endif %}>
            <label>{{T_PAYMENT_BY_POLICIES}}</label>
             <div class="input select">
                <select name="rate_policies" id="rate_policies" class="apply_priority" increase-priority="1">
                    <option value="nr"  {% if configuration.rate_policies == "nr" %}selected{% endif %}> {{T_PAYMENT_NR_POLICY}}</option>
                    <option value="pvp"  {% if configuration.rate_policies == "pvp" %}selected{% endif %}> {{T_PAYMENT_FLEX_POLICY}}</option>
                </select>
            </div>
        </div>

     <div class="col1 input_wrapper">
         <label>{{ T_PAYMENT_ROOM_SELECTION }}</label>
         <div class="input select">
             <select name="rooms" id="rooms" class="room-selection apply_priority" multiple="multiple" increase-priority="1">
                 <option value="@@all@@" {% if all_rooms_selected %}selected{% endif %}> {{ T_ALL }} </option>
                 {% for room in rooms %}
                     <option value="{{ room.id }}" {{ "selected" if room.selected == "true" }}> {{ room.name }}</option>
                 {% endfor %}

             </select>
         </div>
     </div>
       <div class="col2 input_wrapper">
         <label>{{ T_PAYMENT_STAY_RESERVATION }}
             <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                 <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                    {{ T_PAYMENT_STAY_RESERVATION_DESCRIPTION }}
                </span>
             </i>
         </label>
           <div class="input">
                <input type="text" value="{{ configuration.number_days_stay_reservation }}" id="number_days_stay_reservation" name="number_days_stay_reservation" id="number_days_stay_reservation" placeholder="">
            </div>
     </div>
      <div class="col2 input_wrapper">
         <label>{{ T_PAYMENT_STAY_MAX_RESERVATION }}
             <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                 <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                    {{ T_PAYMENT_STAY_MAX_RESERVATION_DESCRIPTION }}
                </span>
             </i>
         </label>
           <div class="input">
                <input type="text" value="{{ configuration.number_days_stay_max_reservation }}" id="number_days_stay_max_reservation" name="number_days_stay_max_reservation" placeholder="">
            </div>
     </div>
        <div class="col2 input_wrapper">
         <label>{{ T_PAYMENT_AMOUNT_RESERVATION }}
             <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                 <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                    {{ T_PAYMENT_AMOUNT_RESERVATION_DESCRIPTION }}
                </span>
             </i>
         </label>
           <div class="input">
                <input type="text" value="{{ configuration.minimum_reservation_amount }}" id="minimum_reservation_amount" name="minimum_reservation_amount" placeholder="">
            </div>
     </div>

    <hr>

    <div class="col3 input_wrapper class_amount">
        <label>{{ T_PAYMENT_TYPE }}</label>
        <div class="input select">
            <select name="type_amount" id="type_amount">
                <option value="num_days"
                        {% if configuration.type_amount == "num_days" %}selected{% endif %}>{{ T_NUM_DAYS }}</option>
                <option value="percentage"
                        {% if configuration.type_amount == "percentage" %}selected{% endif %}>{{ T_PERCENTAGE }}</option>
                <option value="supplement"
                        {% if configuration.type_amount == "supplement" %}selected{% endif %}>{{ T_PAY_ONLY_SUPPLEMENTS }}</option>
                <option value="fixed_amount"
                        {% if configuration.type_amount == "fixed_amount" %}selected{% endif %}>{{ T_PAY_FIXED_AMOUNT }}</option>
            </select>
        </div>
    </div>

    <div class="col3 input_wrapper class_percentage">
        <label id="label_num_days">{{ T_NUM_DAYS }}</label>
        <label id="label_percentage" style="display:none">{{ T_PAYMENT_PERCENTAGE_AMOUNT }}
            <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                 <span class="tooltip" style="width: max-content; white-space: normal; line-height: 1.4; padding: 8px;">
                    {{ T_PAYMENT_PERCENTAGE_AMOUNT_DESCRIPTION }}
                </span>
            </i>
        </label>
        <label id="label_fixed_amount" style="display:none">{{ T_PAYMENT_AMOUNT }}</label>
        <div class="input">
            <input type="text" value="{{ configuration.amount }}" id="amount" name="amount" placeholder="" required>
        </div>

        <label for="average_price_day" style="display: none">
            <input type="checkbox" name="average_price_day" id="average_price_day" {{ "checked" if configuration.average_price_day }}> Hacer media diaria
        </label>
        <label for="include_supplements" style="display: none">
            <input type="checkbox" name="include_supplements" id="include_supplements" {{ "checked" if configuration.include_supplements }}> Incluir suplementos
        </label>
        <label for="fake_tokenizator" style="display: none">
            <input type="checkbox" name="fake_tokenizator" id="fake_tokenizator" {{ "checked" if configuration.fake_tokenizator }}> Devolución automática (simula tokenizador)
        </label>
        {% if force_token %}
            <label for="force_tokenizator" style="display: none">
                <input type="checkbox" name="force_tokenizator" id="force_tokenizator" {{ "checked" if configuration.force_tokenizator }}> Forzar tokenizacion
            </label>
        {% endif %}
    </div>

    <div class="col3 input_wrapper" id="fixed_amount_wrapper">
        <label style="display:none">{{ T_TYPE_FIXED_AMOUNT }}</label>
        <div class="input select">
            <select name="type_fixed_amount" id="type_fixed_amount">
                <option value="full_reservation"
                        {% if configuration.type_fixed_amount == "full_reservation" %}selected{% endif %}>{{ T_POR_ESTANCIA }}</option>
                <option value="per_room"
                        {% if configuration.type_fixed_amount == "per_room" %}selected{% endif %}>{{ T_PER_ROOM }}</option>
                <option value="per_night"
                        {% if configuration.type_fixed_amount == "per_night" %}selected{% endif %}>{{ T_PER_NIGHT }}</option>
            </select>
        </div>
    </div>

    <hr>
    <div class="col1 input_wrapper input_inline checkbox">
            <input type="checkbox" title="create_promocode" name="add_dates_range" id="add_dates_range"  {% if configuration.add_dates_range%}checked="checked"{% endif %} increase-priority="1">
            <label>{{T_DATE_RANGES_START_DATE}}</label>
    </div>
        <div id ="class_dates" class="class_dates">
             <div class="rows_container dates">
             <div class="row row_to_copy col1" style="display:none;">
                 <div id="dates_range_wrapper" class="input_wrapper input_inline row2">
                     <label>{{T_DATE_START}}</label>
                     <div class="input input_inline input_wrapper">
                        <input type="text" id="start_date" autocomplete="off" name="start_date" placeholder="Ej. 14/07/2023" class="datepicker">

                     </div>
                     <label>{{T_DATE_END}}</label>
                     <div class="input  input_inline input_wrapper">
                            <input autocomplete="off" type="text"  id="end_date" name="end_date" placeholder="Ej. 14/07/2023" class="datepicker">
                     </div>
                     <div class=" delete_row">
                         <i class="fas fa-trash"></i>
                     </div>
                     <div class="input checkbox  input_inline input_wrapper">
                        <input type="checkbox" name="1dayinrange" id="1dayinrange" {% if configuration.get("1day_inrange") %} checked {% endif %}>
                        <label>{{T_1DAY_IN_RANGE}}</label>
                     </div>
                     <div class="input checkbox  input_inline input_wrapper">
                        <input type="checkbox" name="payed_day_range" id="payed_day_range" {% if configuration.get("payed_day_range") %} checked {% endif %}>
                        <label>{{PAY_DAY_IN_RANGE}}</label>
                     </div>
                 </div>

             </div>
               <div class="show_rows" id="show_rows">

                       {% for conditions in config_conditions_days %}

                          <div class="row  col1">
                             <div id="dates_range_wrapper" class="input_wrapper input_inline row2">
                                    <label>{{T_DATE_START}}</label>
                                   <div class="input hasDatepicker input_inline input_wrapper">
                                        <input type="text" id="{{conditions.name_date_start}}" value="{{conditions.start_date}}"  autocomplete="off" name="{{conditions.name_date_start}}" placeholder=""
                                               class="datepicker">
                                        <i class="fad fa-calendar-alt"></i>
                                   </div>
                                    <label>{{T_DATE_END}}</label>
                                    <div class="input hasDatepicker  input_inline input_wrapper">
                                          <i class="fad fa-calendar-alt"></i>
                                        <input autocomplete="off" type="text"  id="{{conditions.name_date_end}}" name="{{conditions.name_date_end}}"  value="{{conditions.end_date}}" placeholder="" class="datepicker">
                                    </div>
                                    <div class=" delete_row"><i class="fas fa-trash"></i>
                                       </div>
                                    <div class="input checkbox  input_inline input_wrapper">
                                            <input type="checkbox" name="{{conditions.name_1dayinrange}}" id="{{conditions.name_1dayinrange}}" {% if conditions.get("1day_inrange")=="on" %} checked {% endif %}>
                                            <label>{{T_1DAY_IN_RANGE}}</label>
                                     </div>
                                    <div class="input checkbox  input_inline input_wrapper">
                                         <input type="checkbox" name="{{conditions.name_payed_day_range}}" id="{{conditions.name_payed_day_range}}" {% if conditions.get("payed_day_range")=="on" %} checked {% endif %}>
                                        <label>{{PAY_DAY_IN_RANGE}}</label>
                                    </div>

                            </div>

                          </div>
                       {% endfor %}
               </div>
             </div>

             <div class="input_wrapper">
                <input type="button" id ="add_row_dates" class="btn add_row_dates" value="{{ T_ADD_DATE_RANGE }}"/>
             </div>
        </div>
         <div class="col3 input_wrapper class_days">
{#             {% if configuration.get("select_day_run") %}#}
            <label>{{T_NUM_DAYS_BEFORE}}
                <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                     <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                        {{ T_NUM_DAYS_BEFORE_DESCRIPTION }}
                    </span>
                </i>
            </label>
            <div class="input">
                   <select name="filter_conditions_days" id="filter_conditions_days">
                    <option value="0" {% if configuration.conditions_days== "100000" %}selected{% endif %}>{{T_ALWAYS}}</option>
                    <option value="1" {% if configuration.conditions_days!="100000" %}selected{% endif %}>{{T_NUM_DAYS}}</option>
                   </select>
                     <input type="text" value="{{configuration.conditions_days}}" id="conditions_days" name="conditions_days" placeholder="" {% if configuration.conditions_days=="100000" %} style="display:none;" {% endif %}>

            </div>
{#             {% endif %}#}
        </div>
          <div class="col3 input_wrapper class_room">
            <label>{{ T_PAYMENT_RELEASE }}</label>
            <div class="input">
                <select name="number_day" id="number_day">
                    <option value="0">Hoy</option>
                    <option value="1" >Hoy y Mañana</option>
                    <option value="2">Siempre</option>

                </select>

            </div>
        </div>

        <div class="col1 input_wrapper  input_inline checkbox class_conditions_dates">
                <input type="checkbox" name="limitdates1day" id="limitdates1day" {% if configuration.get("limit_dates")=="on" %} checked {% endif %}>
                <label>{{T_LIMIT_1_DAY}}
                    <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                        <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                            {{ T_NUM_DAYS_BEFORE_LIMIT_DESCRIPTION }}
                        </span>
                    </i>
                </label>
            </div>

        <div id ="class_days_limit" class="col3 input_wrapper class_days_limit" {% if configuration.get("limit_dates")=="on" %} style="display:block;" {% else %} style="display:none;" {% endif %}>
            <label>{{T_NUM_DAYS_BEFORE_LIMIT}}</label>
            <div class="input">
                 <input type="text" value="{{configuration.num_limit_dates}}" id="conditions_days_limit" name="conditions_days_limit" class="apply_priority" placeholder="" increase-priority="1">
            </div>
        </div>

         <div class="col1 input_wrapper  input_inline checkbox execute_rule_expire_date">
            <input type="checkbox" name="cc_expire_date_payment" id="cc_expire_date_payment" {% if configuration.get("cc_expire_date_payment") == "on" %} checked {% endif %}>
            <label>{{ T_EXECUTE_PAYMENT_CC_EXPIRE_DATE }}</label>
            <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                    {{ T_EXECUTE_PAYMENT_CC_EXPIRE_DATE_DESCRIPTION }}
                </span>
            </i>
        </div>

        <div class ="col1 class_acumutative">
          <div class="col1 input_wrapper input_inline checkbox">
                <input type="checkbox" title="create_promocode" name="cumulative" id="cumulative"  {% if configuration.acumulate_rules %}checked{% endif %}>
                <label>{{T_CUMULATIVE_RULES}}</label>
          </div>


          <div id="acumulate_wrapper" class="input select col1 input_wrapper">

            <select  class="chosen" name="acumulate_rules" data-placeholder="acumulate_rules" multiple>
                <option value="all">{{ T_ALL }}</option>
                {% for rule in rules %}
                       <option value="{{ rule.id }}"  {% if rule.selected == "true" %}selected{% endif %}> {{ rule.description }}</option>
                {% endfor %}
            </select>


{#                <select name="acumulate_rules" id="acumulate_rules" class="select2" multiple="multiple">#}
{#                    <option value="@@all@@" {% if alall_acumulate_rulesl_rooms_selected %}selected{% endif %}> {{ T_ALL }} </option>#}
{#                    {% for rule in rules %}#}
{#                        <option value="{{ rule.id }}"  {% if rule.selected == "true" %}selected{% endif %}> {{ rule.description }}</option>#}
{#                    {% endfor  %}#}
{##}
{#                </select>#}
            </div>
        </div>
       <div class="input_wrapper" style="display: flex;">
        <div class="col1 input_wrapper input_inline checkbox">
            <input type="checkbox" title="i" name="send_customer_email" id="send_customer_email" {% if configuration.send_customer_email == "on" or not configuration %}checked{% endif %}>
            <label>{{T_SEND_NOTIFICATION_EMAIL_TO_CUSTOMER}}</label>
        </div>
        <div class="col1 input_wrapper input_inline checkbox">
            <input type="checkbox" title="i" name="send_modification_email" id="send_modification_email" {% if configuration.send_modification_email == "on" or not configuration %}checked{% endif %}>
            <label>{{T_SEND_NOTIFICATION_EMAIL_MODIFICATION_TO_CUSTOMER}}</label>
        </div>
        </div>

        <div class="col1 input_wrapper input_inline checkbox">
            <input type="checkbox" title="i" name="send_customer_error_email" id="send_customer_error_email" {% if configuration.send_customer_error_email == "on" or not configuration %}checked{% endif %}>
            <label>{{T_CUSTOMER_NOTIFY_IF_ERROR}}</label>
        </div>



        <div class="col1 input_wrapper input_inline checkbox">
             <input type="checkbox" title="i" name="send_payment_link_if_error" id="send_payment_link_if_error" {% if configuration.send_payment_link_if_error == "on" or not configuration %}checked{% endif %}>
             <label>{{ T_SEND_PAYMENT_LINK_IF_ERROR }}</label>
        </div>

        <div class="col1 input_wrapper input_inline checkbox"
                {% if configuration.send_payment_link_if_error == "on" or not configuration %}
                    style="display: flex;
                    flexDirection: row;
                    justifyContent: space-around;
                    alignItems: center;
                    width: 70%; "
                {% else %}
                    style="display: none"
                {% endif %} id="options_payment_link_if_error">
            <div>
                <label>Email</label>
                <input type="email" id="email_if_error" name ="email_if_error"
                        {% if (configuration.send_payment_link_if_error == "on" or not configuration) and  configuration.email_if_fail%} value="{{ configuration.email_if_fail }}" {% endif %}>
            </div>
            <div>
                <label>Telefono</label>
                <input type="number" id="phone_payment_link_if_error" name ="phone_if_error"
                        {% if (configuration.send_payment_link_if_error == "on" or not configuration) and  configuration.phone_if_fail%} value="{{ configuration.phone_if_fail }}" {% endif %}>

            </div>
            <div>
                <label>Horas</label>
                <input type="number" id="hours_payment_link_if_error" name ="hours_if_error"
                        {% if (configuration.send_payment_link_if_error == "on" or not configuration) and  configuration.hours_if_fail%} value="{{ configuration.hours_if_fail }}" {% endif %}>

            </div>
        </div>

        <div id="payment_reminder_container" class="col1 input_wrapper input_inline checkbox">
             <input type="checkbox" title="i" name="payment_reminder" id="payment_reminder" {% if configuration.payment_reminder == "on" or not configuration %}checked{% endif %}>
             <label>{{ T_SEND_PAYMENT_REMINDER }}</label>
            <br>
        </div>

        <div id="days_before_payment_reminder_container" class="col3 input_wrapper days_before_payment_reminder_container" {% if configuration.get("payment_reminder")=="on" %} style="display:block;" {% else %} style="display:none;" {% endif %}>
            <label>{{ T_DAYS_TO_SEND_PAYMENT_REMINDER }}
                <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                    <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                        {{ T_SEND_PAYMENT_REMINDER_DESCRIPTION.replace("@@num_days@@", configuration.get("days_before_payment_reminder", "1")) }}
                    </span>
                </i>
            </label>
            <div class="input">
                 <input type="number" name="days_before_payment_reminder" id="days_before_payment_reminder" min="1" max="100" value="{% if configuration.get("days_before_payment_reminder") %}{{ configuration.get("days_before_payment_reminder") }}{% else %}1{% endif %}">
            </div>
        </div>
        <div id ="class_days_reminder" class="class_days_reminder">
             <div class="rows_container days_reminder">
                 <div class="row row_to_copy col3" style="display:none;">
                     <div id="days_reminder_wrapper" class="col3 input_wrapper input_inline days_before_payment_reminder_container row2">
                         <label>{{T_DAYS_TO_SEND_PAYMENT_REMINDER}}</label>
                         <div class="input">
                            <input type="number" name="additional_days_reminder" id="additional_days_reminder" min="1">
                         </div>
                     </div>
                     <div class="delete_row">
                         <i class="fas fa-trash"></i>
                     </div>
                 </div>
                 <div class="show_rows_days_reminder" id="show_rows_days_reminder">
                     {% if configuration.get("additional_days_before_payment_reminder") %}
                         {% for days in configuration.additional_days_before_payment_reminder %}
                             <div class="row col3" style="display:block;">
                                 <div id="days_reminder_wrapper" class="col3 input_wrapper input_inline days_before_payment_reminder_container row2" style="width:100%">
                                     <label>{{T_DAYS_TO_SEND_PAYMENT_REMINDER}}
                                         <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                                             <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                                                    {{ T_SEND_PAYMENT_REMINDER_DESCRIPTION.replace("@@num_days@@", days) }}
                                             </span>
                                         </i>
                                     </label>
                                     <div class="input">
                                        <input type="number" name="additional_days_reminder{{ loop.index }}" id="additional_days_reminder{{ loop.index }}" min="1" value="{{ days }}">
                                     </div>
                                 </div>
                                 <div class="delete_row">
                                     <i class="fas fa-trash"></i>
                                 </div>
                             </div>
                         {% endfor %}
                     {% endif %}
                 </div>
             </div>

             <div class="input_wrapper">
                <input type="button" id ="add_row_days_reminder" class="btn add_row_days_reminder" value="{{ T_ADD_PAYMENT_REMINDER }}"/>
             </div>
        </div>

        <div class="col1 input_wrapper input_inline checkbox" id="use_pay_link"  {% if configuration.type_rule != "programmatically" %}style="display: none"{% endif %}>
             <input type="checkbox" title="i" name="use_pay_link" {% if configuration.use_pay_link == "on" %}checked{% endif %}>
             <label>{{ T_PAY_USING_PAYMENT_LINK }}
                <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                    <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                        {{ T_PAY_USING_PAYMENT_LINK_DESCRIPTION }}
                    </span>
                </i>
             </label>
         </div>

        <div class="col1 input_wrapper input_inline checkbox">
             <input type="checkbox" title="" name="send_error_payment_channel" {{ "checked" if configuration.get("send_error_payment_channel")}}>
             <label>Notificar al channel en caso de error</label>
         </div>

        <div class="col3 input_wrapper">
            <label>{{ T_USER_TYPE }}</label>
            <select  id="user_type" class="col3" name="user_type" multiple increase-priority="2">
                <option name="anyone" value="all" {% if "all" in user_type %}selected{% endif %}>{{ T_ALL }}</option>
                <option name="agency" value="agency" {% if "agency" in user_type %}selected{% endif %}>{{ T_AGENCY }}</option>
                <option name="callcenter" value="callcenter" {% if "callcenter" in user_type %}selected{% endif %}>Call center</option>
                <option name="webuser" value="web_user" {% if "web_user" in user_type %}selected{% endif %}>Normal user</option>
            </select>
            <div class="input_wrapper input_inline">
                <input type="checkbox" class="col13 seconda" name="negative_user_type" {% if configuration.negative_user_type %}checked{% endif %}>
                <label style="color:gray">{{ T_ALL_EXCEPT }}</label>
            </div>
        </div>
        <div class="col3 input_wrapper" id="wraper_excluded_programmatically_rules">
            <label>{{T_EXCLUDE_PROGRAMMATICALLY_PAYMENT}}
                <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                    <span class="tooltip" style="width: 300px; white-space: normal; line-height: 1.4; padding: 8px;">
                        {{ T_EXCLUDE_PROGRAMMATICALLY_PAYMENT_DESCRIPTION }}
                    </span>
                </i>
            </label>
            <div class="input select">
                <select name="excluded_programmatically_rules" id="excluded_programmatically_rules" class="excluded_programmatically_rules-selection" multiple="multiple">
                    {% for rule in rules %}
                        <option value="{{ rule.id }}"  {% if rule.exclude == "true" %}selected{% endif %}> {{ rule.description }}</option>
                    {% endfor  %}
                </select>
            </div>
        </div>

        <div class="col3 input_wrapper" id="wraper_rule_priority">
            <label>{{T_INCREASE_IN_PRIORITY}}
                <i class="fas fa-info-circle toolkit" style="margin-left: 5px; color: #446ca9; cursor: help;">
                    <span class="tooltip" style="width: 250px; white-space: normal; line-height: 1.4;">
                        {{ T_EACH_CONFIGURED_FIELD_INCREASES_PRIORITY }}
                    </span>
                </i>
            </label>

            <div class="input">
                <input type="number" value="{{ configuration.increase_in_priority }}" id="increase_in_priority" name="increase_in_priority" placeholder="">
            </div>
        </div>

         <div class="col1 input_wrapper" style="text-align: right">
            {% if back_button %}
                 <a class="btn btn_small btn_link" onclick="window.location.href='{{ back_button.href}}';" id="back_button">{{ back_button.label }}</a>
            {% endif %}
            <button class="form_submit btn btn_small" type="submit" value="Submit"  form="new_config_form">{% if configuration_id %} {{ T_SAVE }} {% else %} {{T_CREATE}} {% endif %}</button>
         </div>
         <input id="changes" name="changes" type="hidden" value="">
    </form>

    <span class="col1 aux_increase_priority_info" style="display:none;margin-top: 5px;font-size: 0.8em;color:#446ca9">

        <p> <span></span> {{ T_IN_PRIORITY }}</p>
    </span>

</div>

<script>

    $(window).ready(function() {
        $(".chosen").chosen();
        $("select[name=type_amount]").change();
        $("select[name=type_rule]").change();
        $("input[name=cumulative]").trigger("change");
        $("select.gateways").chosen();
        $("select.room-selection").chosen().on('change', show_priority_info);
        $("select.rate-selection").chosen().on('change', show_priority_info);
        $("select#country").chosen();
        $("select#language").chosen();
        $("select.excluded_programmatically_rules-selection").chosen();
        $("select[name=user_type]").chosen().on('change', show_priority_info);


        $(".apply_priority").on('change', show_priority_info);
        $("#add_dates_range").on('change', function (){
            if (this.checked) {
                let aux_increase_priority_info = $(".aux_increase_priority_info");
                aux_increase_priority_info.show();
                let increase_priority_value = $(this).closest(".input_wrapper").find("[increase-priority]").attr("increase-priority");
                aux_increase_priority_info.find("span").text("+" + increase_priority_value);
                $(this).closest(".input_wrapper").append(aux_increase_priority_info);
            } else{
                $(this).closest(".input_wrapper").children(".aux_increase_priority_info").hide();
            }
        });


        function show_priority_info (){
            let aux_increase_priority_info = $(".aux_increase_priority_info");
            aux_increase_priority_info.show();
            let increase_priority_value = $(this).closest(".input_wrapper").find("[increase-priority]").attr("increase-priority");
            aux_increase_priority_info.find("span").text("+" + increase_priority_value);
            $(this).closest(".input_wrapper").append(aux_increase_priority_info);
        }

    })

</script>