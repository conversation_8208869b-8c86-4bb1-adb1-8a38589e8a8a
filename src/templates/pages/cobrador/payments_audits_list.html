<table>
    <thead>
    <tr>
        <th>Timestamp</th>
        <th>ID Pago</th>
        <th>Reserva</th>
        <th>Pasarela</th>
        <th>Tipo</th>
        <th>ID sesion</th>
        <th>Payload</th>
        <th>Respuesta</th>
    </tr>
    </thead>
    {% for i in audits_map %}
        <tr class="row_audit">
            <td>{{ i.timestamp }}</td>
            <td class="payment_order_id">{{ i.payment_order_id }}</td>
            <td class="reservation_id">{{ i.reservation_id }}</td>
            <td>{{ i.gateway_type }}</td>
            <td class="type">
                {{ i.type if i.type and i.type != "Process payment" else "Process payment" }}
            </td>
            <td class="sid">
                {% if not i.web_sid == "todo_get_sid" %}
                    {{ i.web_sid }}
                {% endif %}
            </td>
            <td>
                {% if i.payload %}
                    <div class="payments_audits_info">
                        <button class="button_response"><i class="fal fa-plus"></i></button>
                        <div class="popup_data payload">{{ i.payload }}</div>
                    </div>
                {% endif %}
            </td>
            <td>
                <div class="payments_audits_info">
                    <button class="button_response"><i class="fal fa-plus"></i></button>
                    <div class="popup_data response">{{ i.response }}</div>
                </div>
            </td>
        </tr>
    {% endfor %}
</table>