<div class="page">

    <div class="cols">
         <h4 class="col1 title">
           {{ T_SEARCH_RESERVATION }}
        </h4>
    </div>

        <input type="hidden" name="sessionKey" id="sessionKey" value="{{session_key}}" />
        <input type="hidden" name="hotel_code" value="{{hotel_code}}" />

        <div class="cols">

            <div class="col3 input_wrapper">
                <label>{{T_LOCALIZADOR}}</label>
                <div class="input">
                     <input type="text" value="" id="identifier" name="identifier" placeholder="" class="reservation_search_element">
                </div>
            </div>

            <div class="col3 input_wrapper">
                <label>{{T_PAYMENT_LOCATOR}}</label>
                <div class="input">
                     <input type="text" value="" id="payment_locator" name="payment_locator" placeholder="" class="reservation_search_element">
                </div>
            </div>

            <div class="col3 input_wrapper">
                <label>{{T_NAME}} / {{T_SURNAME}}</label>
                <div class="input">
                     <input type="text" value="" id="name_surname" name="name_surname" placeholder="" class="reservation_search_element">
                </div>
            </div>

                 <div class="col3 input_wrapper">
                <label>{{ T_TOTAL_AMOUNT }}</label>
                <div class="input">
                     <input type="number" value="" id="prices_reservations" name="prices_reservations" placeholder="" class="reservation_search_element">
                </div>
            </div>
              <div class="col3 input_wrapper">
                <label>{{ T_TOTAL_AMOUNT_PAYMENT }}</label>
                <div class="input">
                     <input type="number" value="" id="amount_payment" name="amount_payment" placeholder="" class="reservation_search_element">
                </div>
            </div>
            <div class="col3 input_wrapper">
                   <label>{{T_TYPE_PAYED}}</label>
                     <div class="input select">
                        <select  name="type_payed" id="type_payed">
                            <option value="all"> {{T_ALL}}</option>
                            <option value="all_payments"> {{T_ALL_PAYMENTS}}</option>
                            <option value="parcial_payments"> {{T_PARTIAL_PAYMENTS}}</option>
                            <option value="no_payments"> {{T_NO_PAYMENTS}}</option>
                        </select>
                    </div>
            </div>
            <div class="col3 input_wrapper input_inline checkbox">
                <br>
                <input type="checkbox" value="" id="has_token" name="has_token">
                <label>{{ T_TOKEN_QUESTION }}</label>
            </div>
            <div class="col3 input_wrapper input_inline checkbox">
                <br>
                <input type="checkbox" value="" id="has_error" name="has_error">
                <label>{{ T_ERROR_QUESTION }}</label>
            </div>
            {% if remote_hotels %}
                <div class="col3 input_wrapper">
                    <label for="remote_hotel_selector">{{ T_HOTEL }}</label>
                    <select  class="chosen col3" name="remote_hotel_selector" multiple>
                        <option value="all">{{ T_ALL }}</option>
                        {% for remote_hotel_code, remote_name_hotel in remote_hotels.items() %}
                            <option value="{{ remote_hotel_code }}">{{ remote_name_hotel }}</option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}



            <div class="col2 input_wrapper input_inline row2 date_filter">
                <label class="date_filter_label">{{T_PAYMENT_RESERVATION_DATE}}:</label>
                <div class="date_input_wrapper">
{#                    <label>{{ T_DESDE }}</label>#}
                    <div class="input hasDatepicker">
                            <i class="fal fa-calendar"></i>
                            <input readonly type="text" value="" id="payment_reservation_date_initial" name="payment_reservation_date_initial" placeholder="" class="datepicker reservation_search_datepicker">
                    </div>
                    <div class="icons_wrapper">
                        <i class="far fa-angle-right"></i><i class="far fa-angle-right"></i><i class="far fa-angle-right"></i>
                    </div>
{#                      <label>{{ T_HASTA }}</label>#}
                    <div class="input hasDatepicker">
                            <i class="fal fa-calendar"></i>
                            <input readonly type="text" value="" id="payment_reservation_date_finish" name="payment_reservation_date_finish" placeholder="" class="datepicker reservation_search_datepicker">
                    </div>
                </div>
            </div>



            <div class="col2 input_wrapper input_inline row2 date_filter">
                <label class="date_filter_label">{{T_BOOK_DATE}}:</label>
                <div class="date_input_wrapper">
{#                    <label>{{ T_DESDE }}</label>#}
                    <div class="input hasDatepicker">
                        <i class="fal fa-calendar"></i>
                        <input readonly type="text" value="" id="book_date_initial" name="book_date_initial" placeholder="" class="datepicker reservation_search_datepicker">
                    </div>
                    <div class="icons_wrapper">
                        <i class="far fa-angle-right"></i><i class="far fa-angle-right"></i><i class="far fa-angle-right"></i>
                    </div>
{#                    <label>{{ T_HASTA }}</label>#}
                    <div class="input hasDatepicker">
                        <i class="fal fa-calendar"></i>
                        <input readonly type="text" value="" id="book_date_finish" name="book_date_finish" placeholder="" class="datepicker reservation_search_datepicker">
                    </div>
                </div>
            </div>
            <div class="col2 input_wrapper input_inline row2 date_filter">
                <label class="date_filter_label">{{T_ENTRY_DATE}}:</label>
                <div class="date_input_wrapper">
{#                     <label>{{ T_DESDE }}</label>#}
                     <div class="input hasDatepicker">
                            <i class="fal fa-calendar"></i>
                            <input readonly type="text" value="" id="start_date_initial" name="start_date_initial" placeholder="" class="datepicker reservation_search_datepicker">
                     </div>
                    <div class="icons_wrapper">
                        <i class="far fa-angle-right"></i><i class="far fa-angle-right"></i><i class="far fa-angle-right"></i>
                    </div>
{#                     <label>{{ T_HASTA }}</label>#}
                        <div class="input hasDatepicker">
                            <i class="fal fa-calendar"></i>
                            <input readonly type="text" value="" id="start_date_finish" name="start_date_finish" placeholder="" class="datepicker reservation_search_datepicker">
                     </div>
                </div>
            </div>
            <div class="col2 input_wrapper input_inline row2 date_filter">
                <label class="date_filter_label">{{T_DEPARTURE_DATE}}:</label>
                <div class="date_input_wrapper">
{#                    <label>{{ T_DESDE }}</label>#}
                    <div class="input hasDatepicker">
                        <i class="fal fa-calendar"></i>
                        <input readonly type="text" value="" id="end_date_initial" name="end_date_initial" placeholder="" class="datepicker reservation_search_datepicker">
                    </div>
                    <div class="icons_wrapper">
                        <i class="far fa-angle-right"></i><i class="far fa-angle-right"></i><i class="far fa-angle-right"></i>
                    </div>
{#                       <label>{{ T_HASTA }}</label>#}
                       <div class="input hasDatepicker">
                        <i class="fal fa-calendar"></i>
                        <input readonly type="text" value="" id="end_date_finish" name="end_date_finish" placeholder="" class="datepicker reservation_search_datepicker">
                    </div>
                </div>
            </div>

             <div class="col1 input_wrapper" style="text-align: center">
                 <form id="pdf-build-form" action="/pages/cobrador/build_pdf_list" method="POST" target="_blank">
                     <input type="hidden" id="content-pdf-to-build" name="content-pdf-to-build">
                     <input type="hidden" name="sessionKey" value="{{session_key}}" />
                     <a class="btn btn_small"  id="reservation_search_button">{{T_SEARCH}}</a>
                     <a id="build_excel_button" class="btn btn_small">{{ T_GENERATE_EXCEL }}</a>
                     <a id="button_gen_pdf" class="btn btn_small">{{ T_GENERATE_PDF }}</a>
                 </form>
             </div>

         </div>

          <div class="col3 input_wrapper" style="text-align: left">
              {% if back_button %}
                  <a class="btn btn_small btn_link" onclick="window.location.href='{{ back_button.href}}';">{{ back_button.label }}</a>
              {% endif %}
              <i class="toolkit fas fa-trash" style="float: right; font-size: 1.4em; color: #325da7; cursor: pointer" id="reset_filters"><span class="tooltip">{{ T_RESET_SEARCH_FILTERS }}</span></i>
          </div>






    <div class="input_wrapper">
        <div class="separator"></div>
    </div>

     <div class="zebra_table_wrapper" id="reservations_table_results">
      {{ table_list_html|safe }}
    </div>


</div>