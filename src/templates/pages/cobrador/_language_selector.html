{% macro build_language_selector(name, values="", multiple=False) -%}
    <select name="{{ name }}" id="{{ name }}" {% if multiple %}multiple{% endif %}>
        <option value="es" {% if "es" in values %}selected{% endif %}>Spanish</option>
        <option value="en" {% if "en" in values %}selected{% endif %}>English</option>
        <option value="de" {% if "de" in values %}selected{% endif %}>German</option>
        <option value="ca" {% if "ca" in values %}selected{% endif %}>catalan</option>
        <option value="fr" {% if "fr" in values %}selected{% endif %}>French</option>
        <option value="pt" {% if "pt" in values %}selected{% endif %}>Portuguese</option>
        <option value="it" {% if "it" in values %}selected{% endif %}>Italian</option>
    </select>
{%- endmacro -%}