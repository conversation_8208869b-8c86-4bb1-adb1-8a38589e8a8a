<table class="zebra_table">
      <tr>
        <th>{{ T_DESCRIPTION }}</th>
        <th>{{ T_PAYMENT_TYPE }}</th>
        <th>{{ T_RATES }}/{{ T_ROOMS }}</th>
        <th>{{ T_DATE_RANGES }}</th>
        <th class="short_td">{{ T_NUM_DAYS_BEFORE_SHORT }}</th>
        <th>{{ T_PAYMENT_AMOUNT }}</th>
        <th class="short_td">{{ T_LIMIT_DAY }}</th>
        <th class="short_td">{{ T_NUM_LIMIT_DAY }}</th>
        <th>&nbsp;</th>
      </tr>

    {% if not all_configurations %}
        <tr>
            <td>{{ T_PAYMENT_NO_RULES }}</td>
        </tr>
    {% else %}

        {% for type_rule, configurations in all_configurations.items() %}
            {% for configuration in configurations %}
                <tr id="deleted_row_{{ configuration.id }}">
                <td>&nbsp;{{ configuration.description|safe }}</td>
                <td>{% if configuration.type_rule in ["in_web", "create_token", "early_payment"] %}<i class="corporative_color fad fa-globe"></i>{% else %}<i class="corporative_color fad fa-clock"></i>{% endif %}&nbsp;{{ configuration.type_rule_text|safe }}&nbsp;</td>
                <td>&nbsp;{{ configuration.rates|safe }}&nbsp;</td>
                <td>&nbsp;{{ configuration.range_date_text|safe }}&nbsp;</td>
                <td>&nbsp;{{ configuration.conditions_days|safe }}&nbsp;</td>
                <td>&nbsp;{{ configuration.amount|safe }}&nbsp; {{ configuration.type_amount_text|safe }} {{ "+ suplementos" if configuration.include_supplements }}</td>
                <td>{{ configuration.limit_dates }}</td>
                <td>{{ configuration.num_limit_dates }}</td>
                <td>
                    <a href="#" class="btn btn_small btn_link recover_data" data_configuration_id="{{ configuration.id }}" data_rule_description="{{ configuration.description|safe }}">
                        <i class="toolkit fad fa-upload"><span class="tooltip">{{ T_RECOVER }}</span></i>
                    </a>
                    <a href="#" class="btn btn_small btn_link delete_data" data_configuration_id="{{ configuration.id }}" data_rule_description="{{ configuration.description|safe }}">
                        <i class="toolkit fad fa-trash-alt"><span class="tooltip">{{ T_DELETE }}</span></i>
                    </a>
                </td>
            </tr>
            {% endfor %}

            {% if not loop.last %}
                 <tr><td colspan="10">&nbsp;</td></tr>
            {% endif %}
        {% endfor %}
    {% endif %}
</table>
<span type="hidden" id="t_are_you_sure_del" style="display:none">{{T_DELETE_CONFIRM}}</span>