<!DOCTYPE html>
<html>
<head>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            color: #333333;
            font-family: 'Open Sans', sans-serif;
            font-size: 16px;
            background: #FFFFFF;
            font-weight: normal;
            line-height: 24px;
        }

        .email_container_outer {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 2px solid #f5f5f5;
        }

        .email_container_inner {
            max-width: 95%;
            width: 475px;
            margin: 40px auto;
        }

        .logo_container {
            width: 375px;
            max-width: 90%;
            margin: 0 auto 35px;
            height: 115px;
        }

        .logo_img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .email_title {
            font-size: 18px;
            font-weight: bold;
        }

        .email_text {
            text-align: left;
            margin: 25px auto;
        }

        .main_content_data {
            margin: 0;
            line-height: 26px;
        }

        .main_content_data_value {
            font-weight: 600;
        }

        .button_container {
            width: 100%;
            background: #002E54;
            margin: 35px auto;
        }

        .payment_link {
            text-decoration: none;
            color: white!important;
            font-size: 22px;
            line-height: 59px;
            font-weight: bold;
            display: table;
            width: 100%;
            text-align: center;
        }
        .imagen_card {
            width:25px;
            margin-right:20px;
        }

        .contact_info {
            margin: 0;
        }
    </style>
</head>

<body>
    <div class="email_container_outer">
        <div class="email_container_inner">
            {% if logo_type %}
            <div class="logo_container">
                <img class="logo_img" src="{{ logo_type|safe }}" width="375" />
            </div>
            {% endif %}
            {% if one_step_to_finish %}
            <h3 class="email_title">
                {{ one_step_to_finish|safe }}
            </h3>
            {% endif %}
            {% if main_text %}
            <div class="email_text">
                {{ main_text|safe }}
            </div>
            {% endif %}
            <div class="email_main_content">
                <p class="main_content_data">
                    <span class="main_content_data_key">{{ T_HOTEL|safe }}:</span>
                    <span class="main_content_data_value">{{ hotelname|safe }}</span>
                </p>
                <p class="main_content_data">
                    <span class="main_content_data_key">{{ T_ENTRY|safe }}:</span>
                    <span class="main_content_data_value">{{ entrydate|safe }}</span>
                </p>
                <p class="main_content_data">
                    <span class="main_content_data_key">{{ T_DEPARTURE|safe }}:</span>
                    <span class="main_content_data_value">{{ departuredate|safe }}</span>
                </p>
                <p class="main_content_data">
                    {% for room in rooms %}
                        <h4 class="main_content_data_key">{{ T_ROOM|safe }} {{ room.index_room }}:</h4>
                        <span class="main_content_data_key">{{ T_ROOM|safe }}:</span>
                        <span class="main_content_data_value">{{ room.name|safe }}</span>
                        <br>
                        <span class="main_content_data_key">{{ T_REGIMEN|safe }}:</span>
                        <span class="main_content_data_value">{{ room.board|safe }}</span>
                        <br>
                        <span class="main_content_data_key">{{ T_RATE|safe }}:</span>
                        <span class="main_content_data_value">{{ room.rate|safe }}</span>
                        <br>
                        <span class="main_content_data_key">{{ T_OCCUPANCY|safe }}:</span>
                        <span class="main_content_data_value">
                            {% if room.adults %}
                                {{ T_RESERVATIONS_ROOMS_ADULTS|safe }}: {{ room.adults }}
                            {% endif %}{% if room.kids %}
                                {{ T_RESERVATIONS_ROOMS_KIDS|safe }}: {{ room.kids }}
                            {% endif %}{% if room.babies %}
                            {{ T_RESERVATIONS_ROOMS_BABIES|safe }}: {{ room.babies }}
                            {% endif %}
                        </span>
                        <br>
                        {% if room.kids_ages %}
                            <span class="main_content_data_key">{{ T_KIDS_AGE|safe }}:</span>
                            <span class="main_content_data_value">
                                    {% for age in room.kids_ages %}
                                        {{ age }}
                                    {% endfor %}
                            </span>
                        {% endif %}
                    {% endfor %}
                </p>
                <p class="main_content_data">
                    <span class="main_content_data_key">{{ T_BOOKING_IDENTIFIER|safe }}:</span>
                    <span class="main_content_data_value">{{ identifier|safe }}</span>
                </p>
                {% if amount_to_pay %}
                    <p class="main_content_data">
                        <span class="main_content_data_key">{{ T_AMOUNT_TO_PAY|safe }}:</span>
                        <span class="main_content_data_value">{{ amount_to_pay|safe }} {{ currency|safe }}</span>
                        {% if currency_conversion %}
                            <span>({{ currency_conversion_amount }} {{ currency_conversion }})</span>
                        {% endif %}
                    </p>
                {% endif %}

                {% if total_pending %}
                    <p class="main_content_data">
                        <span class="main_content_data_key">{{ T_TOTAL_PENDING_RESERVATION|safe }}:</span>
                        <span class="main_content_data_value">{{ total_pending|safe }} {{ currency|safe }}</span>
                    {% if currency_conversion %}
                            <span>({{ pending_amount_currency_conversion_amount }} {{ currency_conversion }})</span>
                    {% endif %}
                    </p>
                {% endif %}

                <p class="main_content_data">
                    <span class="main_content_data_key">{{ T_COMMENTS|safe }}:</span>
                    <span class="main_content_data_value">{{ comments|safe }}</span>
                </p>
            </div>
            {% if payment_link %}
            {% if expire_hours %}
                <p class="main_content_data">
                <span>{{ expire_hours|safe }}</span>
                </p>
            {% endif %}
            <div class="button_container" {% if color1 %}style="background:{{ color1|safe }};" {% endif %}>

                <a class="payment_link" href="{{ payment_link|safe }}">
                    <img class = "imagen_card" src="{{'/static/images/cobrador/credit-card.png'|fixurl}}" alt="credit card" width="12">{{ T_FINISH_PAYMENT|safe }}
                </a>
            </div>
            {% endif %}
            <div class="email_bottom_content">
                <p>
                    {{ T_customer_support_v3|safe }}
                </p>

                <p class="contact_info">{{ T_telefono|safe }}: <strong>{{ phone|safe }}</strong></p>
                {% if whatsapp %}
                    <p class="contact_info">Whatsapp: <strong>{{ whatsapp|safe }}</strong></p>
                {% endif %}
                {% if email %}
                    <p class="contact_info">{{ T_EMAIL|safe }}: <strong>{{ email|safe }}</strong></p>
                {% endif %}
                <p class="greeting_text">{{ greeting_text|safe }}</p>
            </div>
        </div>
    </div>
</body>
</html>