<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style type="text/css">

        body, table, td, a {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        body {
            margin: 0 !important;
            padding: 0 !important;
            font-family: 'Geologica', Arial, sans-serif;
            font-size: 17px;
            background: white;
            font-weight: 100;
            color: #444;
        }
        .mail_content span a{
            color: #002E54;
            font-size: 14px;
            font-family: 'Geologica', Arial, sans-serif;
            font-weight: bold;
            text-decoration: none;
            letter-spacing: 0.55px;
        }

    </style>
</head>
<body style="margin: 0; padding: 0; background-color: white;">
    <table width="650" style="width: 650px; table-layout: fixed; background-color: white;box-sizing: content-box;margin: 0 auto;display: block;text-align: center;">
        <tr>
            <td>
                <table width="650" style="width: 650px; margin: 0 auto;">
                    <tr>
                        <td>
                            <table width="650" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; width: 100%; width: 650px; margin: 0 auto;">

                                <!-- Payment button -->
                                <tr>
                                    <td bgcolor="#FCA025" style="padding: 0; text-align: center;">
                                        <table width="650" cellpadding="0" cellspacing="0" border="0">
                                            <tr>
                                                <td style="width: 650px;padding: 20px; text-align: center; font-family: 'Geologica', Arial, sans-serif;">
                                                    <a href="{{ payment_link }}" style="text-decoration: underline; color: white; font-weight: bold; display: inline-block;">{{ T_PENDING_PAYMENT_LINK }}</a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>

                                <!-- Main content -->
                                <tr>
                                    <td bgcolor="#FFFFFF" style="padding: 20px 30px;" class="mobile-padding">

                                        <!-- Header with logo -->
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width: 100%;">
                                            <tr>
                                                <td width="40%" align="left" style="padding: 10px 0;">
                                                    {% if logo_type %}
                                                        <img src="{{ logo_type|safe }}" width="230" style="display: block; width: 230px;" class="mobile-center" />
                                                    {% endif %}
                                                </td>
                                                <td width="40%" align="right" style="font-family: 'Geologica', Arial, sans-serif; color: #002E54; font-size: 16px; padding: 10px 0;">
                                                    {{ T_ONE_STEP_TO_RESERVE|safe }}
                                                </td>
                                            </tr>
                                        </table>

                                        <!-- Main image -->
                                        {% if prestay_image %}
                                        <table width="650" cellpadding="0" cellspacing="0" border="0" style="width: 650px;">
                                            <tr>
                                                <td style="padding: 10px 0;width: 650px;">
                                                    <img width="650" src="{{ prestay_image|safe }}" alt="{{ prestay_image.altText }}" style="width: 650px; display: block; margin: 0 auto;">
                                                </td>
                                            </tr>
                                        </table>
                                        {% endif %}

                                        <!-- Description -->
                                        {% if main_text %}
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width: 100%;">
                                            <tr>
                                                <td style="color: #707070; font-size: 16px; text-align: left; padding: 10px 25px 10px 25px; font-family: 'Geologica', Arial, sans-serif;">
                                                    {{ main_text }}
                                                </td>
                                            </tr>
                                        </table>
                                        {% endif %}

                                        {% if second_text %}
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width: 100%;">
                                            <tr>
                                                <td style="color: #707070; font-size: 14px; text-align: left; padding: 10px 25px 10px 25px; font-style: italic; font-family: 'Geologica', Arial, sans-serif;">
                                                    {{ second_text }}
                                                </td>
                                            </tr>
                                        </table>
                                        {% endif %}

                                        <!-- Reservation details -->
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width: 100%; border: 1px solid #bfbfbf; margin-bottom: 20px;">
                                            <tr>
                                                <td style="padding: 20px 20px; text-align: left; color: #444F74; font-family: 'Italiana', Arial, sans-serif; font-size: 24px;">
                                                    {{ T_RESERVATION_DETAILS|safe }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 0px 20px 20px 20px;">
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0" class="reservation-table" style="width: 100%; text-align: center; border-spacing: 0; font-size: 16px;border: 1px solid #bfbfbf;padding: 0px 15px 0px 15px;">
                                                        <tr>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-bottom: 1px solid #bfbfbf; border-right: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_YOUR_RESERVATION }}</strong><br>{{ num_nights }} noches, {{ rooms|length }} habitación(es)
                                                            </td>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-bottom: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_LOCALIZADOR }}</strong><br>{{ identifier }}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-bottom: 1px solid #bfbfbf; border-right: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_ROOM }}</strong><br>{% for room in rooms %}{{ room.name|safe }}{% endfor %}
                                                            </td>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-bottom: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_OCCUPANCY }}</strong><br>{% for room in rooms %}{{ room.adults }} adultos, {{ room.kids }} niños{% endfor %}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-bottom: 1px solid #bfbfbf; border-right: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_ENTRY }}</strong><br>{{ entrydate|safe }} - {{ from_hour|safe }}
                                                            </td>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-bottom: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_DEPARTURE }}</strong><br>{{ departuredate|safe }} - {{ until_hour|safe }}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-bottom: 1px solid #bfbfbf; border-right: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_ADDITIONAL_SERVICES }}</strong>
                                                                <br>
                                                                {% if additional_services %}
                                                                    {% for supplement_element in additional_services %}
                                                                        {{ supplement_element.name|safe }}
                                                                        {% if supplement_element.price|float <= 0 %}
                                                                            {{ T_FREE }}
                                                                        {% else %}
                                                                            {{ supplement_element.price|safe }}
                                                                            {% if not default_currency %}
                                                                                Euros
                                                                            {% else %}
                                                                                {% if gateway_currency %}
                                                                                    {{ gateway_currency }}
                                                                                {% else %}
                                                                                    {{ default_currency }}
                                                                                {% endif %}
                                                                            {% endif %}
                                                                        {% endif %}
                                                                        {% if supplement_element.special_tax %}
                                                                            <br>
                                                                            <span>{{ T_SPECIAL_TAX }}: {{ supplement_element.special_tax|safe }}%<br/></span>
                                                                        {% endif %}
                                                                        <br>
                                                                    {% endfor %}
                                                                {% else %}
                                                                    {{ T_NO_SERVICE }}
                                                                {% endif %}
                                                            </td>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-bottom: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_MEAL_PLAN }}</strong>
                                                                <br>
                                                                {% if rooms[0] and rooms[0].board %}
                                                                    {{ rooms[0].board|safe }}
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300; border-right: 1px solid #bfbfbf;">
                                                                <strong style="font-style: normal; font-size: 16px; margin-right: 20px;">
                                                                    {{ T_RESERVATIONS_SERVICES_TOTAL }}
                                                                    {% if iva_included %}<br>
                                                                        {{ T_VAT_INCLUDED }}
                                                                    {% endif %}
                                                                    <span style="white-space: nowrap; margin-left: 40px;">
                                                                        {{ total_price }}{{ currency }}
                                                                    </span>
                                                                </strong>
                                                                <br>
                                                                <span style="color:#869FB2;">
                                                                    <em>{{ T_PENDING_PAYMENT|safe }}:</em>
                                                                    {{ total_pending }}{{ currency }}
                                                                </span>
                                                            </td>
                                                            <td width="50%" style="padding: 20px; text-align: left; font-family: 'Geologica', Arial, sans-serif; font-size: 15px; color: #2E2E2E; font-style: italic; line-height: 20px; letter-spacing: 0.5px; font-weight: 300;">
                                                                <strong style="font-style: normal; font-size: 16px;">{{ T_RATE_CONDITIONS|safe }}</strong>
                                                                <br>
                                                                {% if rooms[0] and rooms[0].rate %}
                                                                    {{ rooms[0].rate|safe }}
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>

                                        <!-- Payment button -->
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background: #002E54; width: 100%; margin-top: 25px;margin-bottom: 25px;">
                                            <tr>
                                                <td height="70" style="height: 70px; text-align: center;">
                                                    <a href="{{ payment_link }}" style="display: inline-block; text-decoration: none; color: white; font-family: 'Geologica', Arial, sans-serif; font-size: 22px; font-weight: 700; letter-spacing: 1.5px;">
                                                        <img src="https://cdn2.paraty.es/psantiago-iv/images/485270b26e2873e=s600" alt="Image" width="32" style="vertical-align: middle; margin-right: 10px;"> {{ T_FINISH_PAYMENT|safe }}
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>

                                        <hr style="border: 0; border-top: 1px solid #d0d0d0; margin: 20px 0;">

                                        <!-- Contact section -->
                                        <table width="590" cellpadding="0" cellspacing="0" border="0" style="width: 100%; margin: 25px 0;">
                                            <tr>
                                                <td style="padding: 10px 0;">
                                                    <h2 style="margin: 0; color: #444F74; font-family: 'Italiana', Arial, sans-serif; font-size: 24px; text-align: left;">{{ T_CONTACT|safe }}</h2>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: left; padding: 10px 0;">
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                        <tr>
                                                            {% if not hide_phone_label %}
                                                                <td width="245" style="padding: 5px 0;" >
                                                                    <span style="display: block; font-size: 15px; color: #2E2E2E; font-family: 'Geologica', Arial, sans-serif; font-weight: normal; letter-spacing: 0.6px;width: 245px;">{{ T_24H_RECEPTION_PHONE }}</span>
                                                                    <br>
                                                                    <span style="display: inline-flex;align-items: center; white-space: nowrap; padding-top: 5px;width: 245px;">
                                                                        <img src="https://storage.googleapis.com/cdn.paraty.es/psantiago-iv/files/telephone_icon.png" width="32" alt="Phone" style="display:block;vertical-align: middle; margin-right: 5px;">
                                                                        <a href="tel:{{ phone|safe }}" style="display:inline-flex;align-self:center;color: #002E54; text-decoration: none; font-size: 14px; font-family: 'Geologica', Arial, sans-serif; font-weight: bold; letter-spacing: 0.55px;">{{ phone|safe }}</a>
                                                                    </span>
                                                                </td>
                                                            {% endif %}
                                                            {% if not hide_mail_label %}
                                                                <td width="245" style="padding: 5px 0;" class="mail_content">
                                                                    <span style="width: 245px;display: block; font-size: 15px; color: #2E2E2E; font-family: 'Geologica', Arial, sans-serif; font-weight: normal; letter-spacing: 0.6px;">{{ T_EMAIL }}</span>
                                                                    <br>
                                                                    <span style="width: 245px;display: inline-flex;align-items: center; white-space: nowrap; padding-top: 5px;">
                                                                        <img src="https://storage.googleapis.com/cdn.paraty.es/psantiago-iv/files/email_icon.png" width="32" alt="Email" style="display:block;vertical-align: middle; margin-right: 5px;">
                                                                        <span style="display:inline-flex;align-self:center;color: #002E54; font-size: 14px; font-family: 'Geologica', Arial, sans-serif; font-weight: bold; letter-spacing: 0.55px;">{{ email|safe }}</span>
                                                                    </span>
                                                                </td>
                                                            {% endif %}
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>

                                        <hr style="border: 0; border-top: 1px solid #d0d0d0; margin: 20px 0;">

                                        <!-- Location section -->
                                        {% if location %}
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width: 100%; margin: 0 auto;">
                                            <tr>
                                                <td style="padding: 10px 0;">
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                        <tr>
                                                            <td width="210" style="padding: 10px 10px 0px 0px; vertical-align: top;">
                                                                <h2 style="margin: 0 0 10px; color: #444F74; font-family: 'Italiana', Arial, sans-serif; font-size: 24px; text-align: left;">{{ T_COMO_LLEGAR|safe }}</h2>
                                                                {% if location.description %}
                                                                <div style="text-align: left; color: #002e54; width: 210px;font-size: 14px;">{{ location.description|safe }}</div>
                                                                {% endif %}
                                                            </td>
                                                            <td width="300" style="padding: 10px 0;">
                                                                {% if location.servingUrl %}
                                                                <img width="300" src="{{ location.servingUrl|safe }}" style="display: inline-flex; margin: 0 auto;width: 100%; height: auto;width: 300px;">
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                        <hr style="border: 0; border-top: 1px solid #d0d0d0; margin: 20px 0;">
                                        {% endif %}

                                        <!-- Farewell message -->
                                        {% if farewell_message %}
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width: 100%; margin: 0 auto;">
                                            <tr>
                                                <td style="padding: 10px 0;">
                                                    <h3 style="color: #444; font-family: 'Helvetica', Arial, sans-serif; font-size: 17px; font-weight: 100; margin: 10px 0;">{{ farewell_message|safe }}</h3>
                                                </td>
                                            </tr>
                                        </table>
                                        <hr style="border: 0; border-top: 1px solid #d0d0d0; margin: 20px 0;">
                                        {% endif %}

                                        <!-- Copyright and legal text -->
                                        {% if not hide_email_legal_text %}
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width: 100%; margin: 0 auto;">
                                            <tr>
                                                <td style="padding: 10px 0; text-align: center;">
                                                    <h3 style="color: #424242; font-size: 16px; font-family: 'Geologica', Arial, sans-serif; font-weight: lighter; letter-spacing: .5px; margin: 10px 0;">{{ legal_text|safe }}</h3>
                                                </td>
                                            </tr>
                                        </table>
                                        {% endif %}

                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

</body>
</html>