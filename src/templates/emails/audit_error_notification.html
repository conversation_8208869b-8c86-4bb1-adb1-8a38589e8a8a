<!DOCTYPE html>
<html>
<head>
    <style>
        /* Tus estilos CSS aquí */
        table {
            border-collapse: collapse;
            width: 100%;
        }

        th, td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h2>Adapter integration error report</h2>

    {% for key, errors in data.items() %}
    <h3>{{ key }}</h3>
    <table>
        <thead>
            <tr>
                <th>Rooms</th>
                <th>Errors</th>
            </tr>
        </thead>
        <tbody>
            {% for room, error_list in errors.items() %}
            <tr>
                <td>{{ room }}</td>
                <td>
                    <ul>
                        {% for error in error_list %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endfor %}
</body>
</html>