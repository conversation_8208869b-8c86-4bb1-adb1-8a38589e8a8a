cron:
- description: Houston we have a problem Finder
  url: /errorfinder
  schedule: every day 07:00
  target: @@MODULE@@
  timezone: Europe/Madrid

- description: Maintenance, i.e. Used for pull integrations so that they can update themselves
  url: /maintenance
  schedule: every 60 minutes
  target: @@MODULE@@
  timezone: Europe/Madrid

- description: Database Cleaner (remove all old unused data)
  url: /clean_audits/
  schedule: every day 09:00
  target: @@MODULE@@
  timezone: Europe/Madrid