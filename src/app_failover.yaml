runtime: python311
entrypoint: gunicorn -b :$PORT -w 1 -t 15 main:app
service: failover

runtime_config:
    operating_system: "ubuntu22"
    runtime_version: "3.11"

handlers:

  # This configures Google App Engine to serve the files in the app's static
  # directory.
- url: /static
  static_dir: paraty/static

  # This handler routes all requests not caught above to your main app. It is
  # required when static routes are defined, but can be omitted (along with
  # the entire handlers section) when there are no static files defined.
- url: /.*
  script: auto
  secure: always