import multiprocessing

'''
See https://github.com/benoitc/gunicorn/blob/master/examples/example_config.py
See http://docs.gunicorn.org/en/0.17.2/configure.html


'''
# #Default
# # workers = multiprocessing.cpu_count() * 2 + 1
# # threads = multiprocessing.cpu_count() * 3
#
# # #High I/O
# workers = multiprocessing.cpu_count() * 1 + 1
# threads = multiprocessing.cpu_count() * 10

# #Small I/O
# #NOTE, THIS DOESNT WORK, REQUESTS DON'T RETURN
# workers = multiprocessing.cpu_count() * 2 + 1
# worker_class = 'gevent'
# worker_connections = 30

# #Max processes
# workers = 10
# threads = 5

#Extreme workers
# workers = 15
# threads = 5

#HIGH Threads
workers = multiprocessing.cpu_count() * 2 + 1
threads = 15

timeout = 25