from paraty.pages.cobrador.cobrador_constants import GATEWAY_SUCESS_RETURNED
from test.gateways.test_base_gateway import UTestBaseGateways

class UTestBanorte(UTestBaseGateways):
    def test_happy_flow_form(self):
        hotel_code = ""
        gateway = "BANORTE COBRADOR_IXTAPA"
        sid = "77e22110-6a89-4c6f-b5a2-ce37aa8516e0"
        url = (f"forms/cobrador/get_gateway_form?hotel_code=parkroyal-ixtapa&payment_order_id=19869738&amount=30543.93&additional_services_amount=0"
               f"&sid={sid}&language=SPANISH&destination_id=destination_cobrador_form&start_date=2025-07-02&end_date=2025-07-06&currency=MXN&country=mx&nrf=True&from_callcenter=False&from_tpv_link=False&num_nights=4"
               f"&force_gateway={gateway}")

        url2 = "forms/cobrador/get_gateway_form?hotel_code=parkroyal-grancancun&payment_order_id=72957042&amount=2172.95&additional_services_amount=0.0&sid=582265e5-3d93-43f5-ad85-41e365676890&language=RUSSIAN&destination_id=destination_cobrador_form&start_date=2025-06-12&end_date=2025-06-19&currency=USD&country=CA&nrf=False&from_callcenter=False&from_tpv_link=False&num_nights=7&force_gateway=BANORTE+COBRADOR_CANCUN"

        payload = {'room_info': [{'room_name': 'ROYAL OCEAN VIEW DOUBLE', 'amount': 16132.05, 'currency': 'MXN',
                                 'rate_name': 'MXN Tarifa No Reembolsable (Loyalty)',
                                 'rate_identifier': 'MXN NRF CLUB'},
                                {'room_name': 'PREMIER OCEAN VIEW DOUBLE', 'amount': 14411.88, 'currency': 'MXN',
                                 'rate_name': 'MXN Tarifa No Reembolsable (Loyalty)',
                                 'rate_identifier': 'MXN NRF CLUB'}], 'location_modification': '', 'gotrip': False,
                  'price_after_tax': None, 'tax': None, 'price_tax': None, 'prices_per_day': {'ahNzfnBhcmstcm95YWwtaG90ZWxzchULEghSb29tVHlwZRiAgIC4jILeCwyiARBwYXJrcm95YWwtaXh0YXBh@@0': [3848.32, 3881.4,4201.17,4201.17],'ahNzfnBhcmstcm95YWwtaG90ZWxzchULEghSb29tVHlwZRiAgICEibqCCwyiARBwYXJrcm95YWwtaXh0YXBh@@1': [3418.27,3451.35, 3771.13,3771.13]}}


        payload2 = {'room_info': [{'room_name': '', 'amount': 2172.95, 'currency': 'USD', 'rate_name': '', 'rate_identifier': 'USD FLEX CLUB'}], 'location_modification': '', 'gotrip': False, 'price_after_tax': None, 'tax': None, 'price_tax': None, 'referer_get_params': {'sid': '582265e5-3d93-43f5-ad85-41e365676890', 'namespace': 'parkroyal-grancancun', '_gl': '1*1b51f9q*_up*MQ..', 'gclid': 'EAIaIQobChMIqOHe1rPZjQMVe9bCBB0BbzkUEAoYASACEgI-ffD_BwE'}, 'prices_per_day': {'ahNzfnBhcmstcm95YWwtaG90ZWxzchULEghSb29tVHlwZRiAgICYvouOCgyiARRwYXJrcm95YWwtZ3JhbmNhbmN1bg@@0': [308.02, 308.02, 308.02, 315.73, 315.73, 315.73, 301.7]}}
        response = self.testapp.post(url2, json=payload2, auth=self.auth)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.mimetype, "text/html")
        self.assertTrue("payment_form" in response.text)
        self.assertTrue('fetch("http://localhost/banorte/redirect"' in response.text)

    def test_happy_flow_process(self):
        hotel_code = "test-backend10"
        gateway = "BANORTE COBRADOR_IXTAPA"
        sid = "d1a984c0-0b63-4594-99ce-c3f68e3868fd"

        url = f"forms/cobrador/proceses_gateway_response?sid={sid}&reservation=88360196&data=VzFmd01SV1BNRjJSNWpNYmxXUXFOMDFmMG01NU9DOFJGZnBHK0t3b0I5dDA4K0tHWTJCd3FlVk1nK1ZSV2lQN05jZDIva1JvdEwwN2ZRNGw4YmhETTlCQ1JCaTFIWjlLK1Y2anlSWGE3RnJvTFh5MkxvNENPVE5PWGd1anBOVzhnN0dCWkVSeDVIMFlEVUxjcXNsTCtFN25VcURYY242WjVrcEU2NGRXemhhUjRQUlJXRzFvTnRyQ2lGL3FOU21teGhyTHRpZ1JaUWJzQVhRL3hPNGVXUUxaZGZZNjQxVC9uNEtoZDl5ckVBanowV0wrWFkzZXFJRXIxQjlLZHJ1ZWFKSyt6V1VleWk3RVhrN2ZVOGV1VGpYUTRTNkw3Z0RGQ2hDTWlRMDJ1VG1iVG9NdFB6c0Z0MGU3UENMT3VEaEVnTUkrN2h5UEFVanNpOVZhR2dFZ0tJTzFzSVVSVTU2aDluNUZXUnMrNy9hZGp2aFRqdFFMM3VJcVRtalZIVWR5M1lSRVI0ZUFFVWxieHNBdGJWcml6VlhaR0pHaE5CcUxlTnc1M0x4cGJ5Nm1YRjdGa1pzbDN2Z0VoMGZpaFB5a0JURmJkSVU4dC9KSDFXQjluRHdiWE9BaGIxNHl1N0xrQXVLTTdhenI3T2l5VXI5TDB6Mk9HUEgzdHc3Y1BCSVNRc0JWRHo4SEhlNkc=&intent=intent_94c64db1-37ea-4e1f-a37d-947db7cd0c05&amount_sent_to_gateway=11999.63&force_gateway={gateway}&force_gateway={gateway}"

        payload = {'response': {'reservation': '88360196',
                               'data': 'VzFmd01SV1BNRjJSNWpNYmxXUXFOMDFmMG01NU9DOFJGZnBHK0t3b0I5dDA4K0tHWTJCd3FlVk1nK1ZSV2lQN05jZDIva1JvdEwwN2ZRNGw4YmhETTlCQ1JCaTFIWjlLK1Y2anlSWGE3RnJvTFh5MkxvNENPVE5PWGd1anBOVzhnN0dCWkVSeDVIMFlEVUxjcXNsTCtFN25VcURYY242WjVrcEU2NGRXemhhUjRQUlJXRzFvTnRyQ2lGL3FOU21teGhyTHRpZ1JaUWJzQVhRL3hPNGVXUUxaZGZZNjQxVC9uNEtoZDl5ckVBanowV0wrWFkzZXFJRXIxQjlLZHJ1ZWFKSyt6V1VleWk3RVhrN2ZVOGV1VGpYUTRTNkw3Z0RGQ2hDTWlRMDJ1VG1iVG9NdFB6c0Z0MGU3UENMT3VEaEVnTUkrN2h5UEFVanNpOVZhR2dFZ0tJTzFzSVVSVTU2aDluNUZXUnMrNy9hZGp2aFRqdFFMM3VJcVRtalZIVWR5M1lSRVI0ZUFFVWxieHNBdGJWcml6VlhaR0pHaE5CcUxlTnc1M0x4cGJ5Nm1YRjdGa1pzbDN2Z0VoMGZpaFB5a0JURmJkSVU4dC9KSDFXQjluRHdiWE9BaGIxNHl1N0xrQXVLTTdhenI3T2l5VXI5TDB6Mk9HUEgzdHc3Y1BCSVNRc0JWRHo4SEhlNkc=',
                               'intent': 'intent_94c64db1-37ea-4e1f-a37d-947db7cd0c05',
                               'sid': sid},
                    'hotel_code': hotel_code,
                  'data_session_form': {'prefix': '', 'personalId': '', 'name': 'Jennifer ', 'lastName1': 'Rivera ',
                                        'lastName2': '', 'country': 'MX', 'province': '', 'postalCode': '',
                                        'city': 'Morelia ', 'address': '', 'id': '', 'email': '<EMAIL>',
                                        'telephone': '+52 4436168757', 'comments': '', 'birthday': '', 'password': '',
                                        'allow_notifications': False, 'user_club_allow_register': False,
                                        'share_info_group_chain': '', 'accept_conditions_and_policies': '',
                                        'billing_name': '', 'billing_cif': '', 'billing_address': '', 'numfactu': '',
                                        'numflight': '', 'hour_flight': '', 'chk_hour_flight': '',
                                        'extra_fields': {'city': 'Morelia ', 'postal_code': '58088',
                                                         'Nombre de Titular de la Tarjeta': ''}, 'billing_data': {},
                                        'name_recipient': '', 'lastName1_recipient': '', 'email_recipient': '',
                                        'send_to_buyer': '', 'send_gift_time': '', 'send_gift_date': '',
                                        'telephone_recipient': '', 'agency_name': '', 'agency_telephone': '',
                                        'agency_identifier': '', 'agency_email': '', 'agency_agent_name': '',
                                        'success_indicator_evo': '', 'transfer_disabled_to': '',
                                        'transfer_disabled_from': '', 'flight_number_to': '', 'flight_number_from': '',
                                        'flight_hour_to': '', 'pickup_hour_from': '', 'date_main': '',
                                        'passengers_total': 0, 'passengers': []}}

        response = self.testapp.post(url, json=payload, auth=self.auth)

        self.assertEqual(200, response.status_code)
        self.assertEqual(response.json.get("CODE"), GATEWAY_SUCESS_RETURNED)
        self.assertTrue('"texto":"Aprobado"' in response.text)