from paraty.pages.cobrador.cobrador_constants import GATEWAY_SUCESS_RETURNED
from test.gateways.test_base_gateway import UTestBaseGateways


class UTestSermepa(UTestBaseGateways):
    def test_happy_flow_form(self):
        hotel_code = "test-backend10"
        gateway = "SERMEPA+TOKEN+COBRADOR"
        payment_order_id = "67767122"
        sid = "1da33fd8-16a0-4058-a061-72b6ffefe588"

        url = f'forms/cobrador/get_gateway_form?hotel_code={hotel_code}&payment_order_id={payment_order_id}&amount=2190.0&additional_services_amount=48.0&sid={sid}&language=ENGLISH&destination_id=destination_cobrador_form&start_date=2025-05-26&end_date=2025-06-09&currency=EUR&country=nl&nrf=True&from_callcenter=False&from_tpv_link=False&num_nights=14&force_gateway={gateway}'
        payload = {'room_info': [{'room_name': 'Double Room ', 'amount': 2142.0, 'currency': 'EUR', 'rate_name': '', 'rate_identifier': ''}],
                    'location_modification': '', 'gotrip': False, 'price_after_tax': None, 'tax': None, 'price_tax': None,
                    'prices_per_day': {
                                        'ahJzfmhvdGVsLXB1ZW50ZXJlYWxyEAsSCFJvb21UeXBlGP_aAQw@@0':   [147.52, 154.2, 154.2, 154.2, 147.52, 140.81, 150.96,
                                                                                                     158.14, 172.52, 172.52, 150.96, 150.96, 143.75, 143.75]
                                        }
                   }

        response = self.testapp.post(url, json=payload, auth=self.auth)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.mimetype, "text/html")
        self.assertTrue("paymentForm" in response.text)


    def test_happy_flow_process(self):
        hotel_code = "test-backend10"
        gateway = "SERMEPA TOKEN COBRADOR"
        sid = "00db2f3d-048c-41f0-bdf0-a18c01915efe"

        url = f"forms/cobrador/proceses_gateway_response?sid={sid}&origin={hotel_code}&force_gateway={gateway}"
        payload = {
            'response': 'Ds_SignatureVersion=HMAC_SHA256_V1&Ds_MerchantParameters=eyJEc19NZXJjaGFudENvZGUiOiIzMzc0MjM4ODMiLCJEc19UZXJtaW5hbCI6IjAwMiIsIkRzX09yZGVyIjoiMTQxNTIxNjQiLCJEc19BbW91bnQiOiIwIiwiRHNfQ3VycmVuY3kiOiI5NzgiLCJEc19EYXRlIjoiMDFcLzA0XC8yMDI1IiwiRHNfSG91ciI6IjIyOjU3IiwiRHNfU2VjdXJlUGF5bWVudCI6IjEiLCJEc19FeHBpcnlEYXRlIjoiMjkxMCIsIkRzX01lcmNoYW50X0lkZW50aWZpZXIiOiI2MjU2NjhkM2Y2YzFmNjc3OGE2NzAwMDUzMGE0OWMzYTQyNGJkOTYxIiwiRHNfQ2FyZF9Db3VudHJ5IjoiNzI0IiwiRHNfUmVzcG9uc2UiOiIwMDAwIiwiRHNfTWVyY2hhbnREYXRhIjoiIiwiRHNfVHJhbnNhY3Rpb25UeXBlIjoiMCIsIkRzX0NvbnN1bWVyTGFuZ3VhZ2UiOiIyIiwiRHNfQXV0aG9yaXNhdGlvbkNvZGUiOiI2ODY1ODIiLCJEc19DYXJkX0JyYW5kIjoiMiIsIkRzX0NhcmRfVHlwb2xvZ3kiOiJDT05TVU1PIiwiRHNfTWVyY2hhbnRfQ29mX1R4bmlkIjoiTURTSVhNTkNKMDQwMSAgIiwiRHNfUHJvY2Vzc2VkUGF5TWV0aG9kIjoiNzkifQ==&Ds_Signature=qg1dqqAtHEftC1jq5ZdPjvGzsefAuRKe6Y4W0ywhMoU=',
            'hotel_code': hotel_code,
            'data_session_form': {'prefix': '', 'personalId': 'Z1276322B', 'name': 'Kieran', 'lastName1': 'Johnson', 'lastName2': '', 'country': 'ES', 'province': '', 'postalCode': '', 'city': '', 'address': '', 'id': '', 'email': '<EMAIL>', 'telephone': '07519898143', 'comments': '', 'birthday': '1995-12-19', 'password': 'Timelord50!', 'allow_notifications': True, 'user_club_allow_register': True, 'share_info_group_chain': '', 'accept_conditions_and_policies': '', 'billing_name': '', 'billing_cif': '', 'billing_address': '', 'numfactu': '', 'numflight': '', 'hour_flight': '', 'chk_hour_flight': '', 'extra_fields': {}, 'billing_data': {}, 'name_recipient': '', 'lastName1_recipient': '', 'email_recipient': '', 'send_to_buyer': '', 'send_gift_time': '', 'send_gift_date': '', 'telephone_recipient': '', 'agency_name': '', 'agency_telephone': '', 'agency_identifier': '', 'agency_email': '', 'agency_agent_name': '', 'success_indicator_evo': '', 'transfer_disabled_to': '', 'transfer_disabled_from': '', 'flight_number_to': '', 'flight_number_from': '', 'flight_hour_to': '', 'pickup_hour_from': '', 'date_main': '', 'passengers_total': 0, 'passengers': []}
        }

        response = self.testapp.post(url, json=payload, auth=self.auth)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json.get("CODE"), GATEWAY_SUCESS_RETURNED)