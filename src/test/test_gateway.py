import json
import unittest

from paraty import app
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration
from paraty.pages.cobrador.gateways.wompi import Wompi

AUTH = ("paratytech", "s3cur1tyRul3s@2021!")


class TestGatewayForm(unittest.TestCase):
    def setUp(self):
        app.config['TESTING'] = True
        self.testapp = app.test_client()

    def test_happy_flow_form(self):
        #URL = "/forms/cobrador/get_gateway_form"
        hotel_code = "casas-bouca-maria"
        gateway = "SIBS2.0 COBRADOR"
        URL = f"/forms/cobrador/get_gateway_form?payment_order_id=43612418&get_form=true&num_nights=1&nrf=False&start_date=2023-07-28&destination_id=destination_cobrador_form&country=ES&hotel_code={hotel_code}&sid=b16023a2-673e-4ac8-a662-1b522939ec3c&currency=EUR&language=SPANISH&amount=7.5&force_gateway={gateway}"
        payload = {
            "hotel_code": "test-backend-dos",
            "amount": "5.00",
            "sid": "TEST",
            "language": "SPANISH",
            "force_gateway": "SIBS COBRADOR",
            "payment_order_id": "123456789",
            "personal_data": {"email": "<EMAIL>", "name": "test dortiz", "country": "AD"}
        }
        #payload = {}

        with app.app_context():
            response = self.testapp.post(URL, data=json.dumps(payload), auth=AUTH, headers={'Content-Type': 'application/json'})
            self.assertEqual(200, response.status_code)

    def test_process_payment_nexi(self):
        identifier = ""
        payment_id = ""
        URL = f"/forms/cobrador/proceses_gateway_response?sid=8709e550-a629-4aee-8db2-77f751deb18d&identifier=11186820&token=tok_test_13689_2d90f48dD80D09E2739cCEd6eC897140&type=CARD&email=<EMAIL>&amount_sent_to_gateway=1500.0"
        body = {
            "hotel_code": "test-backend-dos",
            "response": ""
        }
        with app.app_context():
            response = self.testapp.post(URL, data=json.dumps(body), auth=AUTH, headers={'Content-Type': 'application/json'})
            self.assertEqual(200, response.status_code)

    def test_get_gateway_amout(self):
        URL = "/pages/cobrador/rules_for_reservation?hotel_code=oasishoteles-grandpalm"
        payload = {'reservation': {'startDate': '2023-08-29', 'endDate': '2023-08-31', 'country': 'ES', 'rate': 'ag5zfm9hc2lzaG90ZWxlc3IRCxIEUmF0ZRiAgIDJ-8KOCgyiARZvYXNpc2hvdGVsZXMtZ3JhbmRwYWxt', 'price': 1579.64, 'price_days': {'ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgICA2PqpkAoMogEWb2FzaXNob3RlbGVzLWdyYW5kcGFsbQ@@0': [289.82, 289.82]}, 'supplements': 1000.0}}

        with app.app_context():
            response = self.testapp.post(URL, data=json.dumps(payload), auth=AUTH, headers={'Content-Type': 'application/json'})
            self.assertEqual(200, response.status_code)

    def test_process_payment_multibanco(self):
        identifier = "97333429"
        payment_id = "503462531413431669"
        URL = f"/forms/cobrador/proceses_gateway_response?check_multibanco=true&test=true"
        body = {
            "response": {
                "auth_tag_from_http_header": "B256E2A3A1CA884EE666BDCCA02165AE",
                "http_body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
                "iv_from_http_header": "1AA869D7993D8961D116D3C0"
            },
            "hotel_code": "nau-morgado"
        }
        with app.app_context():
            response = self.testapp.post(URL, data=json.dumps(body), auth=AUTH, headers={'Content-Type': 'application/json'})
            self.assertEqual(200, response.status_code)

    def test_process_payment_multibanco2(self):
        URL = f"/multibanco/webhook?hotel_code=test-backend-dos"
        body = b'4BBrsfZvUVdvVW8diddjr/Bmz+GVcO70TF0NKR/WKDw5VEyBS0NKJjdrvcSMm5Kgou4ijXDhh58tgQxTRfvbi9TW955Ri129zPOs+KW2KKM5XbF5EI+9tGtEBFbWD+JEcnbrFm48aK4e1DsrjtqNO8ZzNleqam80f6LsDKYeH+pkifCVJ/fy6M5VLvKmfO/getKeOy55nT0V9dpjp0y/W307suSHumiH9FRkGYr76YlolZOFRWz7Nh4hvbpmS0Y2KjUGddCLYJn3Npn4iC7qWJlEc5FQ8xoGJP5w/o7QUODxivu/SEQGh5RXbAjYnoLFrfvZJ6No+dZXdaPU8l8oU+Sxt0PsoSMIGnQPGe3x/Oy/qbd5TcTRnd0Soi5jVCIbDz339+L3Bacoy8eZv0G+fvo06Au6gAQSpcUPxn93PVlYLiVyf6TO67P/2PM48J+sx0Q7RLIsUe1oaRuSdSpCnFVs7iiJ4INH21wE3DbfSQCdetuY87dsHYDgQ47N27JlnGbjtY4nmDE+EUqFsucfanD0MWvKyG9JoaaweIbGFIhz6JdBj3Uv'
        headers = {
            "X-Initialization-Vector": "zYiXgSYz7QhBm1Y1",
            "X-Authentication-Tag": "BjBBlx6nv4cIvZO+ddObNA=="
        }
        with app.app_context():
            response = self.testapp.post(URL, data=body, headers=headers)
            self.assertEqual(200, response.status_code)

    def test_multibanco_check(self):
        URL = "/multibanco/check_reservation?hotel_code=test-backend-dos&identifier=88459914&gateway=sibs"

        with app.app_context():
            response = self.testapp.post(URL)
            self.assertEqual(200, response.status_code)

    def test_wompi_payment(self):
        wompi_data = {
            'acceptance_token': 'eyJhbGciOiJIUzI1NiJ9.eyJjb250cmFjdF9pZCI6MjA4LCJwZXJtYWxpbmsiOiJodHRwczovL3dvbXBpLmNvbS9hc3NldHMvZG93bmxvYWRibGUvcmVnbGFtZW50by1Vc3Vhcmlvcy1Db2xvbWJpYS5wZGYiLCJmaWxlX2hhc2giOiJkMWVkMDI3NjhlNDEzZWEyMzFmNzAwMjc0N2Y0N2FhOSIsImppdCI6IjE3Mjc3ODExNDEtNzI1NjciLCJlbWFpbCI6IiIsImV4cCI6MTcyNzc4NDc0MX0.kyveeDRzKA6JWbM4w2WmotI8dH-gQrKJE38avVh1uu4',
            'event_key': 'test_events_vBNIOiNyMbXJ4RdpA7BiJR8yhg04Yhc8', 'gateway_url': 'https://checkout.wompi.co/p/',
            'merchant_url': 'http://test-backend-dos-dot-integration-test-hotel.appspot.com/cobrador/proxy/merchant_url',
            'post_url': 'https://sandbox.wompi.co/v1', 'private_key': 'prv_test_GTtTeZN57Zfe1zwDcRi8I2F3txml5Wfu',
            'public_key': 'pub_test_7oiERWEhB0ZcfaGkfBSDqn0CyorsyUTP',
            'secret_key': 'test_integrity_DSyssexbsm0NBf49Z16EAjhugtjSIZR9',
            'url_ko': 'https://test-backend-dos-dot-integration-test-hotel.appspot.com/booking3',
            'url_ok': 'https://test-backend-dos-dot-integration-test-hotel.appspot.com/booking4'}

        wompi = Wompi(wompi_data)
        wompi.do_payment("-160000", {
            "identifier": "TESTAAAAB",
            "email": "<EMAIL>"
        }, 141703, "test-backend-dos")

    def test_do_payment(self):
        URL = "/pages/cobrador/do_payment"
        hotel_code = "test-backend-dos"
        identifier = "22449117"
        amount = "1500"
        payload = f"hotel_code={hotel_code}&identifier={identifier}&amount={amount}"

        with app.app_context():
            response = self.testapp.post(URL, data=payload)
            self.assertEqual(200, response.status_code)

    def test_do_refund(self):
        URL = "/pages/cobrador/do_refund"
        hotel_code = "test-backend-dos"
        identifier = "22449117"
        amount = "1500"
        order = "22449117.03"
        payload = f"hotel_code={hotel_code}&identifier={identifier}&amount={amount}&order={order}"

        with app.app_context():
            response = self.testapp.post(URL, data=payload)
            self.assertEqual(200, response.status_code)