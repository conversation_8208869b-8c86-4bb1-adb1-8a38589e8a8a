from unittest.mock import patch, MagicMock
import flask
from paraty.pages.cobrador import cobrador
import pytest

@pytest.fixture(scope="module")
def app():
    return flask.Flask(__name__)

@patch('paraty_commons_3.queue_utils.create_task_with_url_get_target')
def test_check_programmatically_payments(mock1, app):

    with app.test_request_context():
        cobrador.check_programmatically_payments(flask.request)
        assert mock1.call_count > 100