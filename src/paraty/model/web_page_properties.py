#See com.paraty.common.services.shared.model.rates.Rate etc at Java project

#We need to know this in order to see what WebPageProperties need to be created depending on the Entity Type
from paraty.communications import datastore_communicator
from paraty.utilities.data_structures.advanced_dictionaries import ImmutableDict
from paraty.utilities.languages import language_utils

WEB_PROPERTIES_PER_ENTITY = ImmutableDict({
	'Rate': [
		{'mainKey': 'rateName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'rateDescription', 'type': 'HTML', 'title': 'Descripcion'}
	],
	'Regimen': [
		{'mainKey': 'regimenName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'regimenDescription', 'type': 'HTML', 'title': 'Descripcion'}
	],
	'Picture': [
		{'mainKey': 'pictureTitle', 'type': 'STRING', 'title': 'Titulo'},
		{'mainKey': 'pictureAlt', 'type': 'STRING', 'title': 'Alt'},
		{'mainKey': 'pictureDescription', 'type': 'HTML', 'title':'Descripcion'}
	],
	'WebSection': [
		{'mainKey': 'WebSectionName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'WebSectionSubtitle', 'type': 'STRING', 'title':'Subtitulo'},
		{'mainKey': 'WebSectionDescription', 'type': 'HTML', 'title': 'Descripcion'}
	],
	'RoomType': [
		{'mainKey': 'roomName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'roomDescription', 'type': 'HTML', 'title': 'Descripcion'}
	],
	'Supplement': [
		{'mainKey': 'supplementName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'supplementDescription', 'type': 'HTML', 'title': 'Descripcion'}
	],
	'News': [
		{'mainKey': 'NewsName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'NewsDescription', 'type': 'HTML', 'title': 'Descripcion'}
	],
	'RateCondition': [
		{'mainKey': 'rateConditionName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'rateConditionDescription', 'type': 'HTML', 'title': 'Descripcion'}
	],
	'Promotion': [
		{'mainKey': 'promotionName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'promotionDescription', 'type': 'HTML', 'title': 'Descripcion'}
	],
	'PriceIncrease': [
		{'mainKey': 'priceIncreaseName', 'type': 'STRING', 'title': 'Nombre'},
		{'mainKey': 'priceIncreaseDescription', 'type': 'HTML', 'title': 'Descripcion'}
	]
})


def add_missing_web_properties(entity_name, entity_key, hotel_code):

	related_entities = WEB_PROPERTIES_PER_ENTITY.get(entity_name, [])

	result = []

	for expected_property in related_entities:
		new_property = dict(expected_property)
		new_property['languageKey'] = language_utils.SPANISH
		new_property['value'] = ''
		new_property['entityKey'] = entity_key
		result.append(new_property)

	datastore_communicator.save_multiple_entities('WebPageProperty', [None for x in related_entities], result, hotel_code)


def get_related_web_properties(entity_name):
	return WEB_PROPERTIES_PER_ENTITY.get(entity_name, [])