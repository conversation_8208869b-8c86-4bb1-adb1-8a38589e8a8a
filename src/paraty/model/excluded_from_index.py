'''
fmatheis, This file needs to be synchroned with REST project, with those entities who have a db.Text()
'''


EXCLUDED_FROM_INDEX_PER_ENTITY = {
	'WebPageProperty': ('value',),
	'WebSection': ('description',),
	'Rate': ('description',),
	'RateCondition': ('description',),
	'RoomType': ('description', 'extraInfo'),
	'Regimen': ('description',),
	'Supplement': ('description',),
	'Picture': ('description',),
	'Agencies': ('password',),
	'GiftBono': ('discount',),
	'PaymentConfiguration': ('amount',),
	'ReservationMetadata': ('extraInfo',)
}

def get_excluded_from_index(entity_name):

	#Just to make sure we don't forget any entity
	if not entity_name in EXCLUDED_FROM_INDEX_PER_ENTITY:
		raise Exception("Missing configuration for entity: %s", entity_name)

	return EXCLUDED_FROM_INDEX_PER_ENTITY.get(entity_name, ())
