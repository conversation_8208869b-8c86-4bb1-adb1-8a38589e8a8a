import logging
import multiprocessing
import sys
import os

from flask import Flask
from google.cloud import ndb

from .config import Config, INTEGRATION_NAME, IN_DEV_ENVIRONMENT

client = ndb.Client(project= Config.PROJECT, namespace=Config.NAMESPACE)


def ndb_wsgi_middleware(wsgi_app):
	def middleware(environ, start_response):

		if Config.TESTING:
			from google.cloud.ndb import context
			if context.get_context(False):
				return wsgi_app(environ, start_response)

		with client.context():
			return wsgi_app(environ, start_response)

	return middleware

def create_app(config_class=Config):
	from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE
	app = Flask(__name__, template_folder=Config.TEMPLATE_FOLDER, static_folder='../static', static_url_path='/static')

	app.config.from_object(config_class)
	app.wsgi_app = ndb_wsgi_middleware(app.wsgi_app)

	return app


#We need to import the pages so that they add the handlers for each route

if Config.TESTING:
	if integration_name := os.environ.get('INTEGRATION_NAME'):
		integration_module = sys.modules[integration_name]
		Config.TEMPLATE_FOLDER = os.path.join(os.path.dirname(integration_module.__file__), 'templates')

	app = create_app()

else:
	if Config.DEV:
		import os
		try:
			exec('from %s.%s_integration import %sIntegration' %(INTEGRATION_NAME, INTEGRATION_NAME, INTEGRATION_NAME.title()))
			from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE
			exec('my_module = %sIntegration' % INTEGRATION_NAME.title())
			Config.TEMPLATE_FOLDER = os.path.abspath(sys.modules[my_module.__module__].__file__).replace('%s_integration.py' % INTEGRATION_NAME, 'templates')
		except Exception as e:
			logging.warning(f"Error loading integration at base-integration (expected if executing from integration seeker): {e}")
		app = create_app()

	else:

		app = create_app()
		#In order to initialize the integration
		exec('from %s.%s_integration import %sIntegration' %(INTEGRATION_NAME, INTEGRATION_NAME, INTEGRATION_NAME.title()))

	try:
		import routes
		routes.build_routes()
	except Exception as e:
		logging.warning(f"Error building routes at base-integration (expected if executing from integration seeker): {e}")


logging.info("CPUs: %s" % multiprocessing.cpu_count())


# Print out all mapped routes
if IN_DEV_ENVIRONMENT:
	logging.info(" ")
	logging.info("ROUTES IN PROJECT ================================================")
	logging.info(" ")
	for rule in app.url_map.iter_rules():
		rule_methods = rule.methods
		rule_methods.discard('OPTIONS')
		rule_methods.discard('HEAD')
		logging.info('%s %s ----> %s', rule.rule, rule.methods, rule.endpoint)
	logging.info(" ")
	logging.info("==================================================================")
	logging.info(" ")