from flask import Flask
from .config import Config

def create_app(config_class=Config):
	app = Flask(__name__, template_folder="../templates", static_url_path='/static')
	app.config.from_object(config_class)
	return app

app = create_app()

# We need to import the pages so that they add the handlers for each route
from paraty.pages.cobrador import gateway_logger
from paraty.pages.scripts import nrf_reservations_paid_checker
from paraty.utilities import template_filters
from paraty.authentication import login_utils
from paraty.pages.cobrador import cobrador, cobrador_gateway_form, cobrador_fallback_endpoint
from paraty.pages.cobrador.handlers import cobrador_rules_handler
from paraty.handlers import common_handler


# Print out all mapped routes
# logging.info(" ")
# logging.info("ROUTES IN PROJECT ================================================")
# logging.info(" ")
# all_rules = [rule for rule in app.url_map.iter_rules()]
# all_rules.sort(key=lambda x: x.rule)
# for rule in all_rules:
# 	rule_methods = set(rule.methods)
# 	rule_methods.discard('OPTIONS')
# 	rule_methods.discard('HEAD')
# 	logging.info('%s %s ----> %s', rule.rule, rule_methods, rule.endpoint)
# logging.info(" ")
# logging.info("==================================================================")
# logging.info(" ")