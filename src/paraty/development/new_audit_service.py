import interface_to_implement
from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

NEW_SEARCH_URL = '/dev/audit/...'

def is_hotel_integrated(hotel_code):
	'''
	True,[] if a hotel is integrated
	If not integrated in the current integration it returns the integrations it has. i.e.

	False, ['siteminder']
	'''
	hotel = get_hotel_by_application_id(hotel_code)

	integration_name = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()

	integration_configurations = get_integration_configuration_of_hotel(hotel, integration_name)

	configuration_for_integration = [x for x in integration_configurations if x.get('name', '').lower() == integration_name.lower()]

	if not configuration_for_integration:

		return False, [x['name'] for x in integration_configurations]

	else:
		return True, []


