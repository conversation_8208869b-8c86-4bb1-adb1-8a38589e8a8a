import logging
import os

basedir = os.path.abspath(os.path.dirname(__file__))

#If this is not defined we know we are testing it locally
IN_DEV_ENVIRONMENT = not os.environ.get('GOOGLE_CLOUD_PROJECT')

if IN_DEV_ENVIRONMENT:
    from paraty.utils.development.dev_utils import load_environment_variables
    load_environment_variables()

namespace_to_use = ''

'''
WARNING If you change this file, before doing it make sure you leave the following:
INTEGRATION_NAME = '@@INTEGRATION_NAME@@'

Otherwise update will break
'''

INTEGRATION_NAME = os.environ.get('INTEGRATION_NAME') or "omnibeespull" or '@@INTEGRATION_NAME@@'

project_to_use = '%s-adapter' % INTEGRATION_NAME

if not IN_DEV_ENVIRONMENT:
    import google.cloud.logging
    client = google.cloud.logging.Client()
    client.setup_logging()
else:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s (%(filename)s:%(lineno)d)')

logging.info("DEV: %s", IN_DEV_ENVIRONMENT)

class Config(object):

    PROFILE = False
    LANGUAGES = ['en', 'es']
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'you-will-never-guess-or-maybe-yes-ancid3cownb3'
    PROJECT = project_to_use
    NAMESPACE = namespace_to_use
    LOCATION = os.environ.get('PROJECT_LOCATION') or "@@LOCATON_REGION@@"
    ACCESS_AUTHORIZATION = '383mvw-3mfmosneuidb8ns;ne;qcpen'
    DEV = IN_DEV_ENVIRONMENT
    TESTING = os.environ.get('UNIT_TEST_EXECUTION')
    TEST_THREADS = []
    TEMPLATE_FOLDER = "../templates"
    MAX_REQUEST_LIMIT = 1200  # Note that with cloud run we can execute tasks for much longer
    CLOUD_RUN_URL = os.environ.get('CLOUD_RUN_URL')

if Config.TESTING:
    namespace_to_use = 'test'

if Config.DEV:
    Config.MAX_REQUEST_LIMIT = 7200


# Why?
# os.environ['GOOGLE_CLOUD_PROJECT'] = Config.PROJECT

