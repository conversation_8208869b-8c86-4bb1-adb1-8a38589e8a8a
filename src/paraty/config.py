import logging
import os

#If this is not defined we know we are testing it locally
IN_DEV_ENVIRONMENT = not os.environ.get('GOOGLE_CLOUD_PROJECT')

if not IN_DEV_ENVIRONMENT:
	import google.cloud.logging
	client = google.cloud.logging.Client()
	client.setup_logging()
else:
	logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s (%(filename)s:%(lineno)d)')
	basedir = os.path.abspath(os.path.dirname(__file__))

logging.info("DEV: %s", IN_DEV_ENVIRONMENT)

class Config(object):

	IMAGES_BUCKET = 'cdn.paraty.es'
	LANGUAGES = ['en', 'es']
	SECRET_KEY = os.environ.get('SECRET_KEY') or 'you-will-never-guess-or-maybe-yes-ancid3cownb3'
	PROJECT = 'payment-seeker'
	NAMESPACE = ''
	LOCATION = 'europe-west1'
	ACCESS_AUTHORIZATION = '383mvw-3mfmosneuidb8ns;ne;qcpen'
	DEV = IN_DEV_ENVIRONMENT
	MANAGER_URL = 'https://admin-hotel.appspot.com'
	MANAGER2_URL = 'https://hotel-manager-2-dot-admin-hotel.appspot.com'
	PAYMENT_SEEKER_URL = 'https://payment-seeker.appspot.com'
	TESTING = False


def get_manager_2_url():
	if IN_DEV_ENVIRONMENT:
		return ""
	return Config.MANAGER2_URL

def get_manager_2_url_bis():
	if IN_DEV_ENVIRONMENT:
		return ""
	return Config.PAYMENT_SEEKER_URL

