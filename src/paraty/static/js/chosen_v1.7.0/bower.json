{"name": "chosen", "description": "Chosen is a JavaScript plugin that makes select boxes user-friendly. It is currently available in both jQuery and Prototype flavors.", "keywords": ["select", "multiselect", "dropdown", "form", "input", "ui"], "homepage": "https://harvesthq.github.io/chosen/", "license": "https://github.com/harvesthq/chosen/blob/master/LICENSE.md", "authors": [{"name": "<PERSON>", "url": "https://github.com/pfiller"}, {"name": "<PERSON>", "url": "https://github.com/stof"}, {"name": "<PERSON>", "url": "https://github.com/kenearley"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/koenpunt"}], "dependencies": {}, "main": ["chosen.jquery.js", "chosen.css", "<EMAIL>", "chosen-sprite.png"], "ignore": [], "repository": {"type": "git", "url": "https://github.com/harvesthq/chosen.git"}}