$("input[name=old_password]").keydown(function () {
    $(this).parent().removeClass("input_ko");
   setTimeout(enable_button,100)
});
$("input[name=new_password_1]").keydown(function () {
   setTimeout(check_requirement_password,100)
});
$("input[name=new_password_2]").keydown(function () {
   setTimeout(check_password_match,100)
});

$("#loginForm").submit(function (e) {
    e.preventDefault();
    let endpoint = $(this).attr("action"),
        form_data = $(this).serialize(),
        pass_old = $("input[name=old_password]"),
        pass_1 = $("input[name=new_password_1]").parent(),
        pass_2 = $("input[name=new_password_2]").parent();

    if(!$(".form_submit").hasClass("btn_loading") && !$(".form_submit").hasClass("btn_disable") &&
        pass_old != "" &&
        pass_1.hasClass("input_ok") && pass_2.hasClass("input_ok")) {
        $(".form_submit").addClass("btn_loading");
        $.post(endpoint, form_data, function () {
            $(".modal_login").slideUp();
            $(".modal_login.login_ok").slideDown();
            $(".form_submit").removeClass("btn_loading");
            })
            .done(function () {
                $(".form_submit").removeClass("btn_loading");
            })
            .error(function () {
                $("input[name=old_password]").parent().addClass("input_ko");
                $(".form_submit").removeClass("btn_loading");
            });
    }
});

function enable_button() {
    let pass_old = $("input[name=old_password]").val(),
        pass_1 = $("input[name=new_password_1]").parent(),
        pass_2 = $("input[name=new_password_2]").parent();

    if(pass_old != "" && pass_1.hasClass("input_ok") && pass_2.hasClass("input_ok")) {
        $(".form_submit").removeClass("btn_disable")
    } else {
        $(".form_submit").addClass("btn_disable")
    }
}
function check_password_match() {
    let pass_1 = $("input[name=new_password_1]").val(),
        pass_2 = $("input[name=new_password_2]").val();
    if(pass_1 == pass_2 && pass_2 != "") {
        $("input[name=new_password_2]").parent().addClass("input_ok");
        $("input[name=new_password_2]").parent().removeClass("input_ko");
    } else {
        $("input[name=new_password_2]").parent().removeClass("input_ok");
        $("input[name=new_password_2]").parent().addClass("input_ko");
    }
    if(pass_2 == "") {
        $("input[name=new_password_2]").parent().removeClass("input_ok");
        $("input[name=new_password_2]").parent().removeClass("input_ko");
    }
    enable_button();
}
function check_requirement_password() {
    let pass_1 = $("input[name=new_password_1]").val();

    if(pass_1.search(/[0-9]/g) >= 0) {
        $(".req_09").addClass("req_ok");
    } else {
        $(".req_09").removeClass("req_ok");
    }
    if(pass_1.search(/[A-Z]/g) >= 0) {
        $(".req_az").addClass("req_ok");
    } else {
        $(".req_az").removeClass("req_ok");
    }
    if(pass_1.length >= 8) {
        $(".req_8").addClass("req_ok");
    } else {
        $(".req_8").removeClass("req_ok");
    }
    if(pass_1.search(/[0-9]/g) >= 0 &&
       pass_1.search(/[A-Z]/g) >= 0 && pass_1.search(/[A-Z]/g) >= 0 &&
       pass_1.length >= 8) {
        $("input[name=new_password_1]").parent().addClass("input_ok");
    } else {
        $("input[name=new_password_1]").parent().removeClass("input_ok");
    }
    enable_button();
}