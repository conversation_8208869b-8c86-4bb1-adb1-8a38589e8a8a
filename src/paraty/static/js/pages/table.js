$(window).on("load", function () {
   zebra_table.config.dragable = true;
   zebra_table.__add_listeners();
   modals.__add_listeners();

   $(".sort").click(function (e) {
      let current_table = $(this).closest("table");
      if(e.metaKey) {
         setTimeout(function () {
            console.log(current_table.find("tr.selected").length);
            if(current_table.find("tr.selected").length > 1) {
               $(".delete_rows").show();
            } else {
               $(".delete_rows").hide();
            }
         },200)
      } else {
         $(".delete_rows").hide();
      }

   })
});