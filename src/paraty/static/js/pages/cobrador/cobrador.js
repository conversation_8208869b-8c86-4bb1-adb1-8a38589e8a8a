//Inicializated zone
$(window).on("load", function () {
    "user strict" ;
    var initial_values_form = get_current_values($('#new_config_form')) // it creates a JSON with the initial data of the form

    if ($(".hasDatepicker input").length){
        let today_date = new Date();
        $(".hasDatepicker input").attr("placeholder",$.datepicker.formatDate("dd/mm/yy", today_date));
        $("select[name=remote_hotel_selector]").chosen();
        var calendar_language = $('#calendar_language').val();
        if (calendar_language === 'PORTUGUESE'){
            $(".hasDatepicker input").datepicker({
                changeMonth: true,
                changeYear: true,
                yearRange: "-80:+20",
                dateFormat: "dd/mm/yy",
                dayNames: ["Domingo", "Segunda", "Terça", "Quarta", "Q<PERSON><PERSON>", "<PERSON><PERSON>", "Sábado"],
                dayNamesMin: ["D", "S", "T", "Q", "Q", "S", "S"],
                dayNamesShort: ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"],
                monthNames: ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"],
                monthNamesShort: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez"],
                nextText: "Próximo",
                prevText: "Anterior"
            });
        }else{
            $(".hasDatepicker input").datepicker({
                changeMonth: true,
                changeYear: true,
                yearRange: "-80:+20"
            });
        }
    }

    document.getElementById('new_config_form').addEventListener('submit', function(event) {
        event.preventDefault();
        currentValues = get_current_values($('#new_config_form'));
        differences = {};

        for (key in currentValues) {
            if (currentValues.hasOwnProperty(key)) {
                if (initial_values_form[key] != currentValues[key]){
                    differences[key] = {before: initial_values_form[key], after:currentValues[key]};
                }
            } else {
                differences[key] = {before: "", after:currentValues[key]};
            }
        }
        $('#changes').val(JSON.stringify(differences))
        console.log($('#changes').val());

        this.submit(); // Enviar el formulario normalmente
    });


    if(typeof($("").select2) === "function"){
        $(".select2").select2();
    }

    if ($("#confirmations_text").length){
        setTimeout(hide_confirmations_texts, 5000);
    }

});

function hide_confirmations_texts(){
    $("#confirmations_text").hide();

}

//TAGS ZONE
$(document).on("click", "#payments_rules_tag", function () {
    var redirect = "/pages/cobrador?sessionKey=" +  $("#sessionKey").val();
    if ($('#locale_language').val() !== ''){
        redirect += ('&language=' + $('#locale_language').val());
    }
    window.location.href = redirect;
});


$(document).on("click", "#payments_zone_tag", function () {
    var redirect = "/pages/cobrador/reservations?sessionKey=" + $("#sessionKey").val();
    if ($('#locale_language').val() !== ''){
        redirect += ('&language=' + $('#locale_language').val());
    }
    window.location.href = redirect;
});

$(document).on("click", "#payments_audit_tag", function () {
    var redirect = "/pages/show_payment_audits?sessionKey=" + $("#sessionKey").val();
    if ($('#locale_language').val() !== ''){
        redirect += ('&language=' + $('#locale_language').val());
    }
    window.location.href = redirect;
});

$(document).on("click", "#rules_history_tag", function () {
    var redirect = "/pages/show_payments_rules_history?sessionKey=" + $("#sessionKey").val();
    if ($('#locale_language').val() !== ''){
        redirect += ('&language=' + $('#locale_language').val());
    }
    window.location.href = redirect;
});

$(document).on("change", "#type_rate_filter", function (e) {
   e.preventDefault();
   let type_filter =  $(this).val();
   if (type_filter == "all"){
        $("#wraper_payment_by_rates").hide();
        $("#wraper_payment_by_words").hide();
        $("#wraper_payment_by_policies").hide();
   }
   else if (type_filter == "by_policies"){
        $("#wraper_payment_by_rates").hide();
        $("#wraper_payment_by_words").hide();
        $("#wraper_payment_by_policies").show();
   }
   else if (type_filter == "by_words"){
        $("#wraper_payment_by_rates").hide();
        $("#wraper_payment_by_words").show();
        $("#wraper_payment_by_policies").hide();
   }
   else if (type_filter == "by_rates"){
        $("#wraper_payment_by_rates").show();
        $("#wraper_payment_by_words").hide();
        $("#wraper_payment_by_policies").hide();
   }

});


$(document).on("change", "#filter_conditions_days", function (e) {
    e.preventDefault();
    let type_filter = $(this).val();
    if (type_filter === "1") {
        $("#conditions_days").show();
          $("#conditions_days").val("");
    }else if (type_filter === "0"){
        $("#conditions_days").hide();
        $("#conditions_days").val("100000");
    }
});


$(document).on("change", "#type_rule", function(e) {
      $(".class_amount").show();
       $(".add_row_dates").hide();
      $(".class_room").hide();
      $(".class_days").show();
      $(".country_restriction").show();
      $(".class_acumutative").hide();
      if($(".select_gateway").length){
            $(".select_gateway").show();
      }

      $(".class_percentage").show();
      $(".early_payment_input_wrapper").hide();
      $(".class_conditions_dates").show();
      $("#conditions_days").attr("required", "required");
      $("#amount").attr("required", "required");
      $("#days_before_payment_reminder_container").hide();
      $("#payment_reminder_container").hide();
      $("input[name=send_error_payment_channel]").parent().hide();
      $("label[for=force_tokenizator]").hide();


    if($(this).val() === "credit_card_conditional") {

         $(".class_amount").hide();
         $(".class_room").show();
         $(".class_days").hide();
         $("#conditions_days").removeAttr("required");
         $("#amount").removeAttr("required");
         $("#use_pay_link").hide();
         $(".class_percentage").hide();
         $(".class_days_limit").hide();
         if($(".select_gateway").length){
            $(".select_gateway").hide();
         }

    }else if ($(this).val() ==="in_web" || $(this).val() === "early_payment" || $(this).val() === "create_token"){

         $(".class_conditions_dates").show();
         if(!$("#limitdates1day").is(':checked')){
             $(".class_days_limit").hide();
         }else{
             $(".class_days_limit").show();
         }
         $(".country_restriction").show();
         $("#use_pay_link").hide();

         if($(".select_gateway").length){
            $(".select_gateway").show();
         }
         $(".gateways").trigger("liszt:updated");


         if ($(this).val() === "early_payment") {
            $(".early_payment_input_wrapper").show();
            $("#use_pay_link").hide();
            $(".optional_token").hide();
        }

        else if($(this).val() === "create_token") {
            $(".optional_token").show();
            $(".class_amount").hide();
            $(".class_percentage").hide();
            $("#amount").val("0");
            $("#use_pay_link").hide();
        } else if ($(this).val() === "in_web") {
            $("label[for=force_tokenizator]").show();
            $(".optional_token").hide()
         }



    }else if($(this).val() === "programmatically"){
        if($(".select_gateway").length){
            $(".select_gateway").hide();
        }

        $("#payment_reminder_container").show();
        if(!$("#payment_reminder").is(':checked')){
             $(".days_before_payment_reminder_container").hide();
         }else{
             $(".days_before_payment_reminder_container").show();
         }

        $(".class_acumutative").show();
        $("#use_pay_link").show();
        $(".optional_token").hide();
        $(".class_dates").show();
        $("input[name=send_error_payment_channel]").parent().show()
    } else if($(this).val() === "flight_hotel") {
        $(".class_amount, .class_percentage").hide();
        $("#amount").val("0");
    }
});


$(document).on("change", "#type_amount", function (e) {
   e.preventDefault();
   let type_config =  $(this).val();
   if (type_config == "num_days"){
        $("#label_num_days").show();
        $("#label_percentage").hide();
        $("#label_fixed_amount").hide();
        $("#fixed_amount_wrapper").hide();
        $("input[name=amount]").show();
        $("select[name=type_fixed_amount]").hide();
        $("label[for=include_supplements]").show();
        $("label[for=average_price_day]").show();
        $("label[for=fake_tokenizator]").hide();
        $("label[for=force_tokenizator]").show();
   }
   else if (type_config == "percentage"){
        $("#label_percentage").show();
        $("#label_num_days").hide();
        $("#label_fixed_amount").hide();
        $("#fixed_amount_wrapper").hide();
        $("input[name=amount]").show();
        $("select[name=type_fixed_amount]").hide();
        $("label[for=include_supplements]").hide();
        $("label[for=average_price_day]").hide();
        $("label[for=fake_tokenizator]").hide();
        $("label[for=force_tokenizator]").show();
   } else if (type_config === "supplement") {
       $("#label_num_days").hide();
       $("#label_percentage").hide();
       $("#label_fixed_amount").hide();
       $("#fixed_amount_wrapper").hide();
       $("input[name=amount]").hide();
       $("input[name=amount]").val("0");
       $("select[name=type_fixed_amount]").hide();
       $("label[for=include_supplements]").hide();
       $("label[for=average_price_day]").hide();
       $("label[for=fake_tokenizator]").hide();
       $("label[for=force_tokenizator]").show();
   } else if (type_config === "fixed_amount" ){
       $("#label_fixed_amount").show();
       $("#fixed_amount_wrapper").show();
       $("#label_num_days").hide();
       $("#label_percentage").hide();
       $("input[name=amount]").show();
       $("select[name=type_fixed_amount]").show();
       $("label[for=include_supplements]").hide();
       $("label[for=average_price_day]").hide();
       $("label[for=fake_tokenizator]").show();
       $("label[for=force_tokenizator]").show();
   }

});

$(document).on("change", "#add_dates_range", function (e) {
   e.preventDefault();
   if ($(this).is(':checked')) {
        $("#class_dates").show();
         $(".add_row_dates").show();

   }
   else {
        $("#class_dates").hide();
         $(".add_row_dates").hide();

   }

});

$(document).on("change", "#cumulative", function (e) {
   e.preventDefault();
   if ($(this).is(':checked')) {
        $("#acumulate_wrapper").show();
        $("#acumulate_rules").val("true");

   }
   else {
        $("#acumulate_wrapper").hide();
         $("#acumulate_rules").val("");


   }

});


$("#payment_reminder").on("change", function (e) {
   e.preventDefault();
   if ($(this).is(':checked')) {
        $("#days_before_payment_reminder_container").show();
   }
   else {
        $("#days_before_payment_reminder_container").hide();
   }
});

$(document).on("change", "#limitdates1day", function (e) {
   e.preventDefault();
   if ($(this).is(':checked')) {
        $("#class_days_limit").show();
   }
   else {
        $("#class_days_limit").hide();
   }
});
 
$(document).on("change", "#send_payment_link_if_error", function (e) {
    $("#options_payment_link_if_error").toggle(function() {
            if ($(this).css("display") != 'none') {
                $(this).css({
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-around',
                    alignItems: 'center',
                    width: '70%'
                });
            }
        });
    e.preventDefault();
});


$(document).on("submit", "#new_config_form", function (e) {
   if ($("#add_dates_range").is(':checked')) {
       for (let i = 1; i <= $(".row").length; i++) {
           var dates_error = false;
           if ($("#start_date" + i).val() != "" && $("#end_date" + i).val() != "") {
               var start_date_aux = $("#start_date" + i).val().split("/");
               var start_date = new Date(parseInt(start_date_aux[2]), parseInt(start_date_aux[1] - 1), parseInt(start_date_aux[0]));

               var end_date_aux = $("#end_date" + i).val().split("/");
               var end_date = new Date(parseInt(end_date_aux[2]), parseInt(end_date_aux[1] - 1), parseInt(end_date_aux[0]));

               dates_error = start_date > end_date;

           }


           if ($("#start_date" + i).val() == "" || $("#end_date" + i).val() == "" || dates_error) {
               $.alert({
                   title: 'Error',
                   closeIcon: true,
                   content: $("#t_error_fechas").html(),
                   columnClass: 'my_confirm',
                   buttons: {
                       confirm: {
                           text: 'OK',
                           btnClass: 'btn btn_small',
                           action: function () {
                               return true;
                           }
                       }
                   }
               });

               return false;
           }
       }
   }




  if ($("#conditions_days_limit").val() !=="" &&  $("#conditions_days").val() !=="" && $("#conditions_days").val() !== "100000"){
            if ($("#conditions_days_limit").val() > $("#conditions_days").val()) {
                $.alert({
                title: 'Error',
                closeIcon: true,
                content: $("#t_error_fechas_limit").html(),
                columnClass: 'my_confirm',
                buttons: {
                    confirm: {
                        text: 'OK',
                        btnClass: 'btn btn_small',
                        action: function () {
                           return true;
                        }
                    }
                }
            });
            return false;
            }

        };
      if (($("#limitdates1day").is(':checked'))  &&  $("#conditions_days_limit").val() === "") {
          $.alert({
              title: 'Error',
              closeIcon: true,
              content: $("#t_error_fechas_limit").html(),
              columnClass: 'my_confirm',
              buttons: {
                  confirm: {
                      text: 'OK',
                      btnClass: 'btn btn_small',
                      action: function () {
                          return false;
                      }
                  }
              }
          });
      }
   return true;

});




$(document).on("click", ".swicth_in_table", function () {
    let configuration_id = $(this).attr("data_configuration_id");
    let description = $(this).attr("data_description");
    let status = "";
    if($(this).prop("checked")){
        status = "on";
    }
    else{
       status = "off";
    }

    $.get("/pages/cobrador/update_status_rule",{"configuration_id": configuration_id, "status": status, "sessionKey": $("#sessionKey").val(), "description": description}, function (data) {
       console.log("rule enabled: " + data);
    });


});


$(document).on("click", ".trash_data", function () {

    input_status_id = 'status_' + $(this).attr("data_configuration_id")
    let status = $(`#${input_status_id}`).is(':checked');

    if (status == true) {

        $.alert({
            title: 'Eliminación denegada',
            closeIcon: true,
            content: 'Por favor, desactive antes la regla.',
            columnClass: 'my_confirm',
            buttons: {
                confirm: {
                    text: 'OK',
                    btnClass: 'btn btn_small',
                    action: function () {
                       return true;
                    }
                }
            }
        });

    } else {

        let configuration_id = $(this).attr("data_configuration_id");
        let rule = $(this).attr("data_rule_description")

        $.confirm({
            title: $("#payments_rules_tag").html(),
            content: $("#t_are_you_sure_del").html(),
            columnClass: 'my_confirm',
            autoClose: false,
            buttons: {
                confirm_button: {
                    text: "OK",
                    btnClass: 'btn btn_small',
                    keys: ['enter', 'shift'],
                    action: function(){
                         $.get("/pages/cobrador/trash_rule",{"configuration_id": configuration_id, "rule": rule}, function (data) {

                             if (data == "OK"){
                                $("#config_row_" + configuration_id).hide(); // to see deleted the rule while with reload the page
                                location.reload(true);
                             } else {
                                 $.alert({
                                    title: 'Problemas encontrados',
                                    closeIcon: true,
                                    content: 'Hubo problemas al intentar borrar.',
                                    columnClass: 'my_confirm',
                                    buttons: {
                                        confirm: {
                                            text: 'OK',
                                            btnClass: 'btn btn_small',
                                            action: function () {
                                               return true;
                                            }
                                        }
                                    }
                                 });
                             }

                         });
                         return true;
                    }
                },
                cancel_button: {
                    text: "CANCEL",
                    btnClass: 'btn btn_small btn_link',
                    action: function() {
                      return true;
                    }
                }
            }
        });
    }
});

$(document).on("click", ".delete_data", function () {
    let configuration_id = $(this).attr("data_configuration_id")
    let rule = $(this).attr("data_rule_description")

    $.confirm({

        title: $("#payments_rules_tag").html(),
        content: $("#t_are_you_sure_del").html(),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: "OK",
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function(){
                     $.get("/pages/cobrador/remove_rule",{"configuration_id": configuration_id, "rule": rule}, function (data) {
                         console.log("confirmation deleted:" + data);
                         if (data == "OK"){
                            $("#config_row_" + configuration_id).hide();
                            location.reload(true);
                         }
                    });
                    return true;
                }
            },
            cancel_button: {
                text: "CANCEL",
                btnClass: 'btn btn_small btn_link',
                action: function(){

                  return true;

                }
            }

        }
    });

});

$(document).on("click", ".recover_data", function () {

    let configuration_id = $(this).attr("data_configuration_id")
    let rule = $(this).attr("data_rule_description")

    $.get("/pages/cobrador/recover_rule",{"configuration_id": configuration_id, "rule": rule}, function (data) {

         title = 'Problemas encontrados';
         message = 'Elemento no recuperado';

         if (data.response == "OK") {
            $("#config_row_" + configuration_id).hide();
            title = 'Regla recuperada';
            message = 'Se recuperó la regla: ' + data.rule;
            location.reload(true);
         }

         $.alert({
            title: title,
            closeIcon: true,
            content: message,
            columnClass: 'my_confirm',
            buttons: {
                confirm: {
                    text: 'OK',
                    btnClass: 'btn btn_small',
                    action: function () {
                       return true;
                    }
                }
            }
         });
    });

});

$(document).on("click", "#reservation_search_button", function () {
    search_for_reservation();
});
$(document).on("click", "#build_excel_button", function (e) {
    build_excel(e);
});
$(document).on("click", "#button_gen_pdf", function (e) {
    build_pdf_list(e);
});
$(document).on("click", "#button_gen_pdf_details", function (e) {
    build_pdf_list_details(e);
});
$(document).on("click", "#reset_filters", function (e) {
    reset_filters(e);
});

$('.reservation_search_element').keypress(function (e) {
 var key = e.which;
 if(key == 13) {
   search_for_reservation();
  }
});

function search_for_reservation(){
    $(".zebra_table tr").slice(5).remove();
    $(".zebra_table").addClass("loading");
    $("#reservation_search_button").addClass("btn_disable").disabled=true;


    let search_params =  {"identifier": $("#identifier").val(),
                        "book_date_initial": $("#book_date_initial").val(),
                        "book_date_finish": $("#book_date_finish").val(),
                        "payment_locator": $("#payment_locator").val(),
                        "start_date_initial": $("#start_date_initial").val(),
                        "start_date_finish": $("#start_date_finish").val(),
                        "end_date_initial": $("#end_date_initial").val(),
                        "end_date_finish": $("#end_date_finish").val(),
                        "name_surname": $("#name_surname").val(),
                        "sessionKey": $("#sessionKey").val(),
                        "prices_reservations": $("#prices_reservations").val(),
                        "amount_payment":  $("#amount_payment").val(),
                        "has_token": $("#has_token").is(":checked"),
                        "has_error": $("#has_error").is(":checked"),
                        "payment_reservation_date_initial": $("#payment_reservation_date_initial").val(),
                        "payment_reservation_date_finish": $("#payment_reservation_date_finish").val(),
                        "type_payed": $("select[name=type_payed]").val(),
                        "language": $('#locale_language').val()
                        };
    var type_payed =  $("select[name=type_payed]").val();
    if (type_payed ==="all"){
        search_params["type_payed"] = "";
    }
    var remote_hotels_selected = $("select[name=remote_hotel_selector]").val();
    if (remote_hotels_selected && remote_hotels_selected[0] === "all"){
        remote_hotels_selected = [];
        $("select[name=remote_hotel_selector] option").each(function(){
            remote_hotels_selected.push($(this).val());
            search_params["remote_hotels"] = remote_hotels_selected.join(";");
        });
    }
    if (remote_hotels_selected && remote_hotels_selected[0] !== "all"){
        search_params["remote_hotels"] = remote_hotels_selected.join(";");
    }


    $.get("/pages/cobrador/get_reservations", search_params, function (data) {
       if (data != "ERROR"){
            $("#reservations_table_results").html(data);
       }
       $("#reservation_search_button").removeClass("btn_disable").disabled=false;
       $("#more_reservations").addClass("btn_disable").prop("disabled", true);
    });
}

function build_pdf_list_details(e) {
    let form = $('#pdf-build-form-details');
    let table1 = document.querySelector('#data1');
    let table2 = document.querySelector('#data2');
    let table_to_clean1 = table1.cloneNode(true);
    let table_to_clean2 = table2.cloneNode(true);
    //CSS
    $(table_to_clean1).css({
        'width': '650px',
        'margin': '0 auto'
    });
    $(table_to_clean2).css({
        'width': '650px',
        'margin': '0 auto'
    });
    $(table_to_clean1).find('th').css({
        'font-size': '13px',
        'text-align': 'left'
    });
    $(table_to_clean2).find('th').css({
        'font-size': '13px',
        'text-align': 'left'
    });
    $(table_to_clean1).find('td').css({
        'font-size': '11px',
        'font-family': 'Roboto'
    });
    $(table_to_clean2).find('td').css({
        'font-size': '11px',
        'font-family': 'Roboto'
    });
    // Deleting the back button
    element_back = table_to_clean2.querySelector('a.btn.btn_link.close_modal_info#back_button');
    if (element_back) {
        element_back.parentNode.removeChild(element_back);
    }

    table = table_to_clean1.outerHTML + '<hr>' + table_to_clean2.outerHTML;
    info_table = form.find("#content-pdf-to-build-details") // This is the hidden field from payment_form.html that takes the 2 tables and goes to flask
    info_table.val(table);
    form.submit();
}

function build_pdf_list(e) {
    let form = $('#pdf-build-form');
    let init_table = document.querySelector('.zebra_table');
    // We need clone the table to don't touch the main data of the screen
    let tableHTML = init_table.cloneNode(true);
    // We delete 'load more' & visual icons
    tableHTML.querySelectorAll('td.visual_icons, th.visual_icons, td#more_reservations, th#more_reservations').forEach(cell => {
        cell.remove();
    });
    tableHTML.querySelectorAll('button#more_reservations').forEach(button => {
        let parentCell = button.closest('td, th');

        if (parentCell) {
            parentCell.remove();
        }
    });
    // Status change (before there was a colored icon)
    tableHTML.querySelectorAll('td span, th span').forEach(span => {
        let parentCell = span.closest('td, th');

        if (parentCell) {
            if (span.classList.contains('circle')) {
                if (span.classList.contains('pending')) {
                    parentCell.textContent = "Pendiente";
                } else if (span.classList.contains('ok')) {
                    parentCell.textContent = "OK";
                } else if (span.classList.contains('ko')) {
                    parentCell.textContent = "Cancelada";
                }
            }
        }
    });
    //CSS
    $(tableHTML).css({
        'width': '650px',
        'margin': '0 auto'
    });
    $(tableHTML).find('th').css({
        'font-size': '13px',
        'text-align': 'left'
    });
    $(tableHTML).find('td').css({
        'font-size': '11px',
        'font-family': 'Roboto'
    });

    table = tableHTML.outerHTML;
    form.find("#content-pdf-to-build").val(table); // This is the hidden field from reservation_search_form.html that takes the table and goes to flask
    form.submit();
}

function build_excel(e){
    var base_url_build_excel = "/pages/cobrador/build_excel";
    var identifier = $("#identifier").val();
    /*var book_date = $("#book_date").val();
    var startDate = $("#start_date").val();
    var endDate = $("#end_date").val();*/

    if (identifier){
        base_url_build_excel = base_url_build_excel + "?identifier=" + identifier;
    } else {
        var all_identifiers = [];
        $("table.zebra_table tr[id]:not('#new-reservations-placeholder')").each(function() {
            all_identifiers.push($(this).attr("id"));
        });

        base_url_build_excel = base_url_build_excel + "?all_identifiers=" + all_identifiers.join(";");
    }

    window.open(base_url_build_excel, "_blank");
}

function reset_filters(e){
    $("#identifier").val("");
    $("#book_date_initial").val("");
    $("#book_date_finish").val("");
    $("#payment_locator").val("");
    $("#name_surname").val("");
    $("#sessionKey").val("");
    $("#prices_reservations").val("");
    $("#amount_payment").val("");
    $("#start_date_initial").val("");
    $("#start_date_finish").val("");
    $("#end_date_initial").val("");
    $("#end_date_finish").val("");
    $("#payment_reservation_date_initial").val("");
    $("#payment_reservation_date_finish").val("");
    $("#type_payed").val("all");

}

$(document).on("change", "#customer_email", function (e) {
   e.preventDefault();

   if ($(this).is(':checked')) {
        $("#comment_email_wrapper").show();

   }
   else {
        $("#comment_email_wrapper").hide();
   }

});



$(document).on("submit", "#new_payment_form", function (e) {


  if (!$("#extra_payment").is(':checked') && parseFloat($("#amount").val()) > parseFloat($("#max_permited_amount").val())){
        $.alert({
                title: 'Error',
                closeIcon: true,
                content: $("#t_error_max_amount").html(),
                columnClass: 'my_confirm',
                buttons: {
                    confirm: {
                        text: 'OK',
                        btnClass: 'btn btn_small',
                        action: function () {
                           return true;
                        }
                    }
                }
            });


        return false;
  }


    $("#btn_submit_new_payment").addClass("btn_disable").addClass("btn_loading");
    $("#btn_submit_new_payment").attr('disabled', 'disabled');

    e.preventDefault();
    var form = $(this)[0];

     $.confirm({

        title: $("#new_payment_title").html(),
        content: $("#t_are_you_sure").html().replace("@@amount@@", $("#amount").val()).replace("@@currency@@", CURRENCY),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: $("#btn_submit_new_payment").html(),
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function(){
                    var array = $(form).serializeArray();
                    var data_to_send = {};
                    $.each(array, function () {
                        if (data_to_send[this.name]==null){
                            data_to_send[this.name] = this.value || "";

                        }else {
                            data_to_send[this.name] = data_to_send[this.name]  + "||" +  this.value;

                        }
                    });



                    var url_form = $(form).attr("action");
                    $.post(url_form,data_to_send, function (data) {
                        show_popup(data);
                    });
                }
            },
            cancel_button: {
                text: $("#back_button").html(),
                btnClass: 'btn btn_small btn_link',
                action: function(){
                $("#btn_submit_new_payment").removeClass("btn_disable").removeClass("btn_loading");
                $("#btn_submit_new_payment").removeAttr('disabled');
                  return true;

                }
            }

        }
    });

});


$(document).on("submit", "#new_refund_form", function (e) {


    if ($("#max_permited_amount").val() && parseFloat($("#amount").val()) > parseFloat($("#max_permited_amount").val()) ){
        $.alert({
                title: 'Error',
                closeIcon: true,
                content: $("#t_error_max_amount").html(),
                columnClass: 'my_confirm',
                buttons: {
                    confirm: {
                        text: 'OK',
                        btnClass: 'btn btn_small',
                        action: function () {
                           return true;
                        }
                    }
                }
            });


        return false;
  }
    $("#btn_submit_new_payment").addClass("btn_disable").addClass("btn_loading");
    $("#btn_submit_new_payment").attr('disabled', 'disabled');

    e.preventDefault();
    var form = $(this)[0];

     $.confirm({

        title: $("#new_payment_title").html(),
        content: $("#t_are_you_sure").html().replace("@@amount@@", $("#amount").val()).replace("@@currency@@", CURRENCY),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: $("#btn_submit_new_payment").html(),
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function(){

                    var array = $(form).serializeArray();
                    var data_to_send = {};
                    $.each(array, function () {
                        data_to_send[this.name] = this.value || "";
                    });
                    var url_form = $(form).attr("action");
                    $.post(url_form,data_to_send, function (data) {
                        show_popup(data);
                    });
                }
            },
            cancel_button: {
                text: $("#back_button").html(),
                btnClass: 'btn btn_small btn_link',
                action: function(){
                $("#btn_submit_new_payment").removeClass("btn_disable").removeClass("btn_loading");
                $("#btn_submit_new_payment").removeAttr('disabled');
                  return true;

                }
            }

        }
    });

});


function validate_form(id_form){
    $("form#"+id_form+":input, select, textarea").each(function(){
         var input = $(this); // This is the jquery object of the input, do what you will
         alert('Type: ' + input.attr('type') + 'Name: ' + input.attr('name') + 'Value: ' + input.val());
    });

}


$(document).on("change", "#type_link", function (e) {
   e.preventDefault();

   $("#wrapper_generated_link").hide();
   $("#generated_link").val("");


   if ($(this).val() == "token") {
        $("#amount").attr("required", "required");
        $(".check_links_restrictions_wrappers").hide();
   }
   else{
        $(".check_links_restrictions_wrappers").show();
   }


   if ($(this).val() == "price") {
         $("#amount").attr("required", "required");
        $("#new_link_amount").show();

   }
   else {
        $("#new_link_amount").hide();
         $("#amount").removeAttr("required")
        $("#amount").val("");

   }

});

$(document).on("change", "#amount", function (e) {
   e.preventDefault();
   var current_amount_to_pay = parseFloat($("#amount").val());
   var pending_amount = parseFloat($("#pending_amount_info").text().replace("€","").trim());
   var form_id = $("form").attr("id");

   if (current_amount_to_pay > pending_amount && form_id === "new_link_form"){
       $.alert({
           title: 'Error',
           closeIcon: true,
           content: $(".error_limit_amount_link").html(),
           columnClass: 'my_confirm',
           buttons: {
               confirm: {
                   text: 'Continuar',
                   btnClass: 'btn btn_small',
                   action: function () {

                   }
               },
               limit:{
                   text: 'Limitar',
                   btnClass: 'btn btn_small',
                   action: function () {
                       $("#amount").val(pending_amount.toString());
                   }
               }
           }
       });

   }


});


$(document).on("submit", "#new_link_form", function (e) {
    e.preventDefault();

    if($("#type_link").val() === "price" && parseFloat($("#amount").val()) <= 0){
        $.alert({
            title: 'Error',
            closeIcon: true,
            content: $("#t_error_0_amount_link").html(),
            columnClass: 'my_confirm',
            buttons: {
                confirm: {
                    text: 'OK',
                    btnClass: 'btn btn_small',
                }
            }
        });
        return false;
    }

    let total_payed = parseFloat($("#total_payed").html().trim().split(" ")[0]);

   if($("#type_link").val() === "token" &&  total_payed > 0){
       if (!$("#allow_tokenize_with_payments").val() || !$("#allow_tokenize_with_payments").val() == "True"){
        $.alert({
            title: 'Error',
            closeIcon: true,
            content: $("#t_error_token_link_if_payed").html(),
            columnClass: 'my_confirm',
            buttons: {
                confirm: {
                    text: 'OK',
                    btnClass: 'btn btn_small',
                }
            }
        });
        return false;
       }
    }

    $("#submit_button").addClass("btn_disable").addClass("btn_loading");
    $("#submit_button").attr('disabled', 'disabled');

    var form = $(this)[0];
    $.confirm({
        title: $("#new_link_title").html(),
        content: $("#t_are_you_sure").html(),
        columnClass: 'my_confirm',
        autoClose: false,
        buttons: {
            confirm_button: {
                text: $("#submit_button").html(),
                btnClass: 'btn btn_small',
                keys: ['enter', 'shift'],
                action: function(){
                    var array = $(form).serializeArray();
                    var data_to_send = {};
                    $.each(array, function () {
                        data_to_send[this.name] = this.value || "";
                    });
                    var url_form = $(form).attr("action");
                    $.post(url_form,data_to_send, function (data) {
                        show_popup(data);
                        $("#submit_button").removeClass("btn_disable").removeClass("btn_loading");
                        $("#submit_button").removeAttr('disabled');
                    });
                    return true;
                }
            },
            cancel_button: {
                text: $("#back_button").html(),
                btnClass: 'btn btn_small btn_link',
                action: function(){
                  $("#submit_button").removeClass("btn_disable").removeClass("btn_loading");
                  $("#submit_button").removeAttr('disabled');
                  return true;

                }
            }

        }
    });



});

$(document).on("click", "#generate_payment_link", function () {

    let configuration_id = $(this).attr("data_configuration_id");

   let params = {"identifier": $("#identifier").val(),
                "amount": $("#amount").val(),
                "type_link": $("#type_link").val(),
                "sessionKey": $("#sessionKey").val(),
                "hotel_code":$("#hotel_code").val()}


    let total_payed = parseFloat($("#total_payed").html().trim().split(" ")[0]);

   if(params.type_link === "token" &&  total_payed > 0 && $("#multiple_tokenization").val() == "False" ){
       if (!$("#allow_tokenize_with_payments").val() || !$("#allow_tokenize_with_payments").val() == "True"){
           $.alert({
            title: 'Error',
            closeIcon: true,
            content: $("#t_error_token_link_if_payed").html(),
            columnClass: 'my_confirm',
            buttons: {
                confirm: {
                    text: 'OK',
                    btnClass: 'btn btn_small',
                }
            }
        });
        return;
       }

    }

    if(params.type_link === "price" &&  params.amount === "0"){
        $.alert({
            title: 'Error',
            closeIcon: true,
            content: $("#t_error_0_amount_link").html(),
            columnClass: 'my_confirm',
            buttons: {
                confirm: {
                    text: 'OK',
                    btnClass: 'btn btn_small',
                }
            }
        });
        return;
    }

    $.get("/pages/cobrador/build_new_link", params, function (data) {
        $("#generated_link").val(data);

        $("#check_link").attr("href", data);
        $("#wrapper_generated_link").show();

    });


});

$(document).on("click", "#copy_link", function () {
    copyToClipboard($("#generated_link"));
});

function copyToClipboard(element) {
    var $temp = $("<input>");
    $("body").append($temp);
    $temp.val($(element).val()).select();
    document.execCommand("copy");
    $temp.remove();
}

function error_info(){
    $.alert({
        title: 'Error',
        closeIcon: true,
        content: $(".popup_error_info").html(),
        columnClass: 'my_confirm',
        buttons: {
            confirm: {
                text: 'OK',
                btnClass: 'btn btn_small',
                action: function () {
                   return true;
                }
            }
        }
    });
}

function info(data){
    $.alert({
        title: 'Rules',
        closeIcon: true,
        content: $(".popup_info_p_"+data).html(),
        columnClass: 'my_confirm',
        buttons: {
            confirm: {
                text: 'OK',
                btnClass: 'btn btn_small',
                action: function () {
                   return true;
                }
            }
        }
    });
}

$(document).on("click", ".show_modal_info", function () {
    var current_tr_id = $(this).closest("tr").attr("id");
    var url_to_get = $(this).attr("url");
    $.get(url_to_get, function (data) {
        $(".popup_info_wrapper").css("display", "block");
        $(".modal_info_content").html(data);
        $(".modal_info_content").attr("id",current_tr_id);
        $(".modal_info_content").find(".chosen").chosen();
        $(".close_modal_info").on("click", function (){
            $(".modal_info_content").empty();
            $(".popup_info_wrapper").css("display", "none");
        });
        $(".edit_comment").on("click", function (){

            var identifier = $(this).data("identifier");
            var hotel_code = $(this).data("field_hotel_code");
            var button = $(this);
            var old_comment = '';
            var id_comment = $(this).data("id_comment");
            var user = $(this).data("user");
            var date = new Date();
            var datetime = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}`;
            var inputHtml = '<input id="new-text" type="text" style="width: 100%">'; //Here goes the new comment
            var urlActual = window.location.href;

            // Removing the unic key (Name/id) from the object 'Payment'
            var original_key = $(this).data("field_key");
            //let regex = /Entity\('PaymentsReservation',\s*(\d+)/;
            let regex = /Entity\('(PaymentsReservation(?:ExtraInfo)?)',\s*(\d+)/;
            let coincidencia = original_key.match(regex);
            let key = coincidencia ? coincidencia[2] : null;
            let original_entity = coincidencia ? coincidencia[1] : null;
            let original_key_hotel_code = $(this).data("field_hotel_code");

            // We go to datastore to get the last comment
            var data_to_send = {
                "hotel_code": hotel_code,
                "key": key,
                "original_entity": original_entity

            };
            $.get("/pages/cobrador/get_comments",data_to_send, function (response) {
                old_comment = response.message
            });

            if (key !== null) {
                $.alert({
                    title: $("#t_edit").html(),
                    closeIcon: true,
                    content: inputHtml,
                    columnClass: 'my_confirm',
                    buttons: {
                        confirm: {
                            text: 'Guardar',
                            btnClass: 'btn btn_small',
                            action: function () {
                                var new_comment = '<b>' + user + ' (' + datetime + '):</b><br>' + $("#new-text").val() + '<br>' + old_comment;

                                var data_to_send = {
                                    "new_text": new_comment,
                                    "key": key,
                                    "hotel_code":original_key_hotel_code,
                                    "original_entity": original_entity
                                };
                                $.get("/pages/cobrador/update_comment",data_to_send, function (response) {
                                    if (response.status === "OK"){
                                        $(`#${id_comment}`).html(`${new_comment}`);
                                        $('#oculto').val(new_comment);
                                    }
                                });
                                return true;
                            }
                        }
                    }
                });
            } else {
               	response = {
                    "message": "Transacción no encontrada",
                    "status": "KO"
                };
                show_popup(response);
            }
        });
        $(".delete_historic_payment, .recover_historic_payment").on("click", function (){
            var button = $(this);
            var action_type = button.attr("type");
            var identifier = $(".modal_info_content").attr("id");
            var payed_by_tpv_link_index = button.closest("a[payed_by_tpv_link_index]").attr("payed_by_tpv_link_index");
            let historic_payment_id = button.closest("a[payment_internal_id]").attr("payment_internal_id");
            var data_to_send = {
                "historic_payment_id": historic_payment_id,
                "payed_by_tpv_link_index": payed_by_tpv_link_index,
                "identifier": identifier,
                "action_type": action_type,
                "sessionKey": $("#sessionKey").val()
            };
            $.alert({
                title: $("#t_delete").html(),
                closeIcon: true,
                content: $("#t_delete_historic_payment").html(),
                columnClass: 'my_confirm',
                buttons: {
                    confirm: {
                        text: 'OK',
                        btnClass: 'btn btn_small',
                        action: function () {
                            $.get("/pages/cobrador/update_historic_payment",data_to_send, function (response) {
                                if (action_type === "delete" && response.status === "OK"){
                                    button.css("display", "none");
                                    button.siblings(".fa-recycle").css("display", "inline-block");
                                    button.closest("tr").addClass("logical_removed");
                                }
                                if (action_type === "recover" && response.status === "OK") {
                                    button.css("display","none");
                                    button.siblings(".fa-trash").css("display", "inline-block");
                                    button.closest("tr").removeClass("logical_removed");
                                }
                                show_popup(response);
                            });
                            return true;
                        }
                    }
                }
            });
        });
    });
});

function show_popup(data){
    var confirmation_popup_content = $(".popup_confirmation_wrapper");
    $("#btn_submit_new_payment").removeClass("btn_disable").removeClass("btn_loading");
    $("#btn_submit_new_payment").removeAttr('disabled');
    if (data.status === "OK"){
        $(".confirmation_info_content").attr("id","successful_payment");
        $(".confirmation_info_content").html(data.message);
        if (data.new_total_payed_by_cobrador || data.new_total_extra_payed_by_cobrador){
            $(".showed_modal_info").find($("#total_payed")).text(parseFloat(data.new_total_payed_by_cobrador).toFixed(1));
            $(".showed_modal_info").find($("#total_extra_payed")).text(parseFloat(data.new_total_extra_payed_by_cobrador).toFixed(1));
            var reservation_id = $(".modal_info_content").attr("id");
            $("#total_payed_"+reservation_id).text(parseFloat(data.new_total_payed_by_cobrador).toFixed(1) +"€");
            $("#total_extra_payed_"+reservation_id).text(parseFloat(data.new_total_extra_payed_by_cobrador).toFixed(1));
        }
    }else{
        $(".confirmation_info_content").attr("id","wrong_payment");
        $(".confirmation_info_content").html(data.message);
    }
    confirmation_popup_content.fadeIn();

    setTimeout(function (){
        $(".confirmation_info_content").empty();
        $(".confirmation_info_content").attr("id","");
        confirmation_popup_content.fadeOut();
    }, 3000);
};

$(document).on("click", ".add_row_dates", function (event) {
    event.preventDefault();
     const cloned_row = $(".dates .row_to_copy").clone();
    const row_length = $(".row").length;
    cloned_row.removeClass("row_to_copy");
    cloned_row.find("#start_date").attr("id","start_date" + row_length).attr("name","start_date"+ row_length);
    cloned_row.find("#end_date").attr("id","end_date" + row_length).attr("name","end_date"+ row_length);
    cloned_row.find("#1dayinrange").attr("id","1dayinrange" + row_length).attr("name","1dayinrange"+ row_length);
    cloned_row.find("#payed_day_range").attr("id","payed_day_range" + row_length).attr("name","payed_day_range"+ row_length);
    cloned_row.find("#dates_range_wrapper").attr("style", "");
    cloned_row.show();
    $(".show_rows").append(cloned_row);
    inicializarDatepicker("#start_date" + row_length);
    inicializarDatepicker("#end_date" + row_length);

});

function inicializarDatepicker(selector) {
    if ($(selector).length) {
        let today_date = new Date();
        $(selector).attr("placeholder", $.datepicker.formatDate("dd/mm/yy", today_date));
        $("select[name=remote_hotel_selector]").chosen();
        var calendar_language = $('#calendar_language').val();
        if (calendar_language === 'PORTUGUESE') {
            $(selector).datepicker({
                changeMonth: true,
                changeYear: true,
                yearRange: "-80:+20",
                dateFormat: "dd/mm/yy",
                dayNames: ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"],
                dayNamesMin: ["D", "S", "T", "Q", "Q", "S", "S"],
                dayNamesShort: ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"],
                monthNames: ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"],
                monthNamesShort: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez"],
                nextText: "Próximo",
                prevText: "Anterior"
            });
        } else {
            $(selector).datepicker({
                changeMonth: true,
                changeYear: true,
                yearRange: "-80:+20"
            });
        }
    }
}

$(".delete_row").on("click", function (event){
    const button = $(event.target);
    if (button.closest('.rows_container').find(".row").length > 1){
        button.closest(".row").detach();
    }
});

$("#recycle_button").on("click", function (event){
    $('#deleted_rules_div').slideToggle();
});

function get_current_values(form) {
    newJSON = {};

    $('#new_config_form input').each(function() {

        var field_type = $(this).attr('type');

        if (field_type != 'hidden' && field_type != 'button'){

            if ($(this).attr('name')) {

                current_id = $(this).attr('id');

                switch (field_type) {
                    case 'text':
                    case 'number':
                    case 'select-one':
                        newJSON[$(this).attr('name')] = $(this).val();
                        break;
                    case 'checkbox':
                         newJSON[$(this).attr('name')] = $(this).is(':checked');
                        break;
                }
            }
        }
     });

     //Now the fields with div and data inside
     newJSON['country'] = $('select#country').val() ? $('select#country').val().join(", ") : '';
     newJSON['language'] = $('select#language').val() ? $('select#language').val().join(", ") : '';
     newJSON['user_type'] = $('select#user_type').val() ? $('select#user_type').val().join(", ") : '';
     // Fields that is more useful the name than the id
     newJSON['rooms'] = $('select[name="rooms"]').map(function() {
        return $(this).find("option:selected").text();
     }).get().join(", ");
     newJSON['type_rule'] = $('select[name="type_rule"]').map(function() {
        return $(this).find("option:selected").text();
     }).get().join(", ");
     newJSON['rates'] = $('select[name="rates"]').map(function() {
        return $(this).find("option:selected").text();
     }).get().join(", ");
     // Values created with jinja2, not included at form
     newJSON['type_amount'] = $('#type_amount').val();
     newJSON['filter_conditions_days'] = $('#filter_conditions_days option:selected').text();
     newJSON['rate_policies'] = $('#rate_policies option:selected').text();
     newJSON['type_rate_filter'] = $('#type_rate_filter option:selected').text();
     //More hidden divs to be added
     newJSON['email_if_error'] = $('#email_if_error').val() ? $('#email_if_error').val() : '';
     newJSON['phone_payment_link_if_error'] = $('#phone_payment_link_if_error').val() ? $('#phone_payment_link_if_error').val() : '';
     newJSON['hours_payment_link_if_error'] = $('#hours_payment_link_if_error').val() ? $('#hours_payment_link_if_error').val() : '';
     newJSON['excluded_programmatically_rules'] = $('select[name="excluded_programmatically_rules"]').map(function() {
        return $(this).find("option:selected").text();
     }).get().join(", ");
     newJSON['increase_in_priority'] = $('#increase_in_priority').val();
     return newJSON;
}

const tabList = document.querySelector('.tab-list');
const inputs = document.querySelectorAll('.input-container input');
tabList.addEventListener('click', (event) => {
  event.preventDefault();

  if (event.target.tagName === 'A') {
    const targetId = event.target.getAttribute('href').substring(1) +"_booking_3_gateway_text";

    inputs.forEach(input => {
      input.style.display = 'none';
    });

    document.getElementById(targetId).style.display = 'block';

    const tabs = tabList.querySelectorAll('li');
    tabs.forEach(tab => tab.classList.remove('active'));

    event.target.parentElement.classList.add('active');
  }
});