var modals = function(){
    return {
        config: {
            title: '',
            entity: '',
            check_duplicates: '',
            languages: [],
            fields_in_lang: [],
            saved: false
        },
        init: function() {
            this.__add_listeners();
            $(".modal_create_wrapper").each(function () {
                modals.build_modal_create_edit($(this), "CREATE");
            });
            $(".modal_edit_wrapper").each(function () {
                modals.build_modal_create_edit($(this), "EDIT");
            });
            $(".modal_delete_wrapper").each(function () {
                modals.build_modal_delete($(this));
            });
            if (Html2CSV && typeof Html2CSV === 'function') {
                Html2CSV('zebra_table', 'myfilename', 'download_csv');
            }
        },
        create_modal: function(x) {
            let modal_wrapper = $("<div></div>").addClass("modal").addClass(x),
                modal_content = $("<div></div>").addClass("modal_content"),
                modal_title = $("<div>" + this.config.title + "</div>").addClass("modal_title"),
                modal_close = $("<div></div>").addClass("close close_modal").append("<i class='fal fa-times'></i>");

            modal_wrapper.append(modal_content.append(modal_close).append(modal_title));
            $("body").append(modal_wrapper);
        },
        build_modal_create_edit: function (current_modal_wrapper, modal_type) {
            debugger;
            let fields = current_modal_wrapper.attr("data-fields").split("@;@"),
                buttons = current_modal_wrapper.attr("data-buttons").split("@;@"),
                new_modal = "modal_" + Date.now();

            this.config.title = current_modal_wrapper.attr("data-title");
            this.create_modal(new_modal);
            this.config.title = "";
            if(modal_type == "CREATE") $("."+new_modal).attr("id","create_modal");
            if(modal_type == "EDIT") $("."+new_modal).attr("id","edit_modal");

            if(current_modal_wrapper.attr("data-entity")){
                $("."+new_modal).attr("data-entity", current_modal_wrapper.attr("data-entity"));
            }
            if(current_modal_wrapper.attr("data-fields")){
                $("."+new_modal).attr("data-fields", current_modal_wrapper.attr("data-fields"));
            }

            $("."+new_modal).find(".modal_content").append("<input type='hidden' name='item_id'>");

            $("."+new_modal).find(".modal_content").append($("<div></div>").addClass("form_cols cols"));
            let data = comunicator.config.data_load[0];

                if (comunicator.config.languages.length > 1) {
                    let lang_tabs = modals.build_lang_tabs();
                    lang_tabs.insertBefore("." + new_modal + " .modal_content .form_cols");
                }
                $.each(fields, function (x, field) {
                    let input = modals.build_input(field),
                        field_info = field.split("|");
                    if (comunicator.config.fields_in_lang.includes(field_info[0])) {
                        $.each(comunicator.config.languages, function (x, lang) {
                            let label = $("<label>" + field_info[2] + " (" + lang + ")</label>"),
                                input_clone = input.clone();
                            input_clone.find("label").detach();
                            input_clone.prepend(label);
                            if (field_info[1] == "tiny") input_clone.find("textarea").attr("id", new_modal + lang + field_info[0]);
                            if (lang != "SPANISH") input_clone.find(".extra_buttons").append("<i class='fad fa-language'></i>");
                            $("." + new_modal).find(".tab_content[data-lang=" + lang + "]").append(input_clone);
                        });
                    } else {
                        $("." + new_modal).find(".modal_content .form_cols").append(input);
                    }
                });
                if (buttons.length > 0) {
                    modals.build_buttons(buttons, new_modal);
                }
                if ($(".language.tabs").length > 0) {
                    $(".language.tabs .tab:first-of-type").click();
                }
        },
        build_modal_delete: function (current_modal_wrapper) {
            let text = current_modal_wrapper.attr("data-msg"),
                buttons = current_modal_wrapper.attr("data-buttons").split("@;@"),
                new_modal = "modal_" + Date.now();

            this.config.title = current_modal_wrapper.attr("data-title");
            this.create_modal(new_modal);
            this.config.title = "";
            $("."+new_modal).attr("id","delete_modal");

            if(current_modal_wrapper.attr("data-entity")){
                $("."+new_modal).attr("data-entity", current_modal_wrapper.attr("data-entity"));
            }

            $("."+new_modal).find(".modal_content").append("<input type='hidden' name='item_id'>");
            $("."+new_modal).find(".modal_content").append("<div class='modal_desc'>" + text + "</div>");

            if(buttons.length > 0) {
                this.build_buttons(buttons, new_modal);
            }
        },
        build_lang_tabs: function(){
            var lang_tabs = $("<div></div>"),
                tabs_wrapper = $("<div></div>").addClass("language tabs"),
                tab = $("<div></div>").addClass("tab"),
                tab_content_wrapper = $("<div></div>").addClass("language tab_content_wrapper"),
                tab_content = $("<div></div>").addClass("tab_content");
            $.each(comunicator.config.languages, function (x, lang) {
                let new_tab = tab.clone();
                new_tab.attr("data-lang", lang).html(lang);
                tabs_wrapper.append(new_tab);
                let new_tab_content = tab_content.clone();
                new_tab_content.attr("data-lang", lang);
                tab_content_wrapper.append(new_tab_content);
            });
            return lang_tabs.append(tabs_wrapper).append(tab_content_wrapper);
        },
        build_input: function(field) {
            let field_info = field.split("|");
            if (field_info.length > 1) {
                let input_wrapper = $("<div></div>").addClass("input_wrapper"),
                    label = $("<label>" + field_info[0] + "</label>"),
                    input = $("<div></div>").addClass("input"),
                    extra_buttons = $("<div></div>").addClass("extra_buttons"),
                    input_field = $("<input type='text'>").attr("name", field_info[0]),
                    type_input = field_info[1],
                    col_class = "col1";

                if (field_info.length > 2) label = $("<label>" + field_info[2] + "</label>");

                if (field_info[1].split(";").length > 1){
                    type_input = field_info[1].split(";")[0];
                    col_class = field_info[1].split(";")[1];
                }

                if (type_input == "title") {
                    input.addClass("title");
                    input_field = "";
                }

                if (type_input == "tiny") {
                    input.addClass("textarea");
                    extra_buttons.append("<i class='fad fa-code'></i>");
                    input_field = $("<textarea>").attr("name", field_info[0]).addClass("tiny");
                }

                if (type_input == "switch") {
                    input_wrapper.addClass("input_inline");
                    input.addClass("switch");
                    input_field.attr("type","checkbox").addClass("swicth")
                }

                if (type_input == "checkbox") {
                    input_wrapper.addClass("input_inline_checkbox");
                    input.addClass("checkbox");
                    input_field.attr("type","checkbox");
                }

                if (type_input == "link") {
                    input_field = $("<a></a>").attr("name", field_info[0]);
                    if (field_info.length > 2){
                          input_field.html(field_info[2]);
                    }
                    else{
                        input_field.html("click here");
                    }
                    label = "";
                }

                if (type_input == "password") {
                    input_field.attr("type","password");
                }

                if (type_input == "hidden") {
                    input_field.attr("type","hidden");
                    input_wrapper.hide();
                }

                if (type_input == "iframe") {
                    input_field = $("<iframe></iframe>")
                    input_field.attr("type", "iframe");
                    input_field.attr("id", field_info[0]);
                    input_field.attr("name", field_info[0]);
                    input_field.attr("type", type_input);
                    input_field.attr("title", field_info[3]);
                    input_field.attr("width", field_info[4]);
                    input_field.attr("height", field_info[5]);
                    input_field.attr("src", field_info[6]);
                    input_field.attr("cols", "1");
                    input_field.attr("validate", "");
                    input_field.attr("order_in_form", "nn");

                    extra_buttons = $("<span style='color:red; font-size:0.8em; margin-bottom:3px;top: -2px;position: relative;'></span>");
                    extra_buttons.html(field_info[7]);

                }

                if (type_input == "select" || type_input == "select2") {
                    input.addClass("select");
                    input_field = $("<select></select>").attr("name", field_info[0]);
                    if (type_input == "select2"){
                        input_field.addClass("select2");
                        input_field.attr("multiple","multiple");
                    }
                    let options_list = field_info[3].split("@@");
                    $.each(options_list, function (x, y) {
                        let value_label = y.split(";"),
                            option = $("<option value='"+value_label[1]+"'>"+value_label[0]+"</option>");
                        input_field.append(option);
                    })
                }
                if (!modals.config.fields_in_lang.includes(field_info[0])) {
                    input_wrapper.addClass(col_class);
                }
                return input_wrapper.append(label).append(input.append(extra_buttons.clone()).append(input_field));
            }
        },
        fill_input: function(modal_id, field, data, lang_field) {
            this.config.saved = false;
            let field_info = field.split("|");
            if(field_info[0] != "") {
                if(comunicator.config.fields_in_lang.includes(field_info[0])) {
                    if(lang_field != ""){
                        if(field_info[0] == lang_field.mainKey){
                            let lang_wrapper = $(modal_id).find(".tab_content[data-lang="+lang_field.languageKey+"]"),
                                current_input = lang_wrapper.find("input[name="+field_info[0]+"]"),
                                type_input = field_info[1];
                            if (field_info[1].split(";").length > 1) type_input = field_info[1].split(";")[0];
                            if(type_input == "tiny") {
                                current_input = lang_wrapper.find("textarea[name="+field_info[0]+"]");
                                current_input.html(modals.htmlDecode(lang_field.value))
                                    .attr("data-languageKey",lang_field.languageKey)
                                    .attr("data-type",lang_field.type)
                                    .attr("data-title",lang_field.title)
                                    .attr("data-mainkey",lang_field.mainKey);
                            } else {
                                current_input.val(modals.htmlDecode(lang_field.value))
                                    .attr("data-languageKey",lang_field.languageKey)
                                    .attr("data-type",lang_field.type)
                                    .attr("data-title",lang_field.title)
                                    .attr("data-mainkey",lang_field.mainKey);
                            }
                        }
                    } else {
                        $.each(comunicator.config.languages, function (x, lang) {
                            let lang_wrapper = $(modal_id).find(".tab_content[data-lang="+lang+"]"),
                            current_input = lang_wrapper.find("input[name="+field_info[0]+"]"),
                            type_input = field_info[1];
                            if (field_info[1].split(";").length > 1) type_input = field_info[1].split(";")[0];
                            if(type_input == "tiny") {
                                current_input = lang_wrapper.find("textarea[name="+field_info[0]+"]");
                                current_input.html("").attr("data-languageKey",lang)
                                    .attr("data-type","HTML")
                                    .attr("data-mainkey",field_info[0]);
                            } else {
                                current_input.val("").attr("data-languageKey",lang)
                                    .attr("data-type","STRING")
                                    .attr("data-mainkey",field_info[0]);
                            }
                        });
                    }
                } else {
                    let current_input= $(modal_id).find("[name="+field_info[0]+"]"),
                        type_input = field_info[1];
                    if (field_info[1].split(";").length > 1){
                        type_input = field_info[1].split(";")[0];
                    }
                    if(type_input == "switch" || type_input == "checkbox") {
                        if(data[field_info[0]]) {
                            current_input.prop("checked", true);
                        } else {
                            current_input.prop("checked", false);
                        }
                    } else if(type_input == "password") {
                        current_input.val("");
                        current_input.attr("data-old",data[field_info[0]]);
                    } else if(type_input == "hidden") {
                        if (field_info.length > 2 && field_info[2]){
                            current_input.val(field_info[2]);
                        }
                        else{
                            current_input.val(data[field_info[0]]);
                        }

                    }

                    else if(type_input == "link") {
                        console.log(data[field_info[0]]);
                        console.log(typeof(data[field_info[0]]));
                        //&& data[field_info[0]]
                        if (typeof(data[field_info[0]]) == "undefined" || !data[field_info[0]]){
                             current_input.css("display", "none");
                        }
                        else{
                            current_input.attr("href", data[field_info[0]]);
                            current_input.attr("target", "_blank");
                            current_input.css("display", "block");

                        }

                    }
                     else if(type_input == "select" || type_input == "select2") {
                        current_input= $(modal_id).find("select[name="+field_info[0]+"]");
                        current_input.val(data[field_info[0]]);
                        current_input.trigger('change');
                    } else {
                        current_input.val(data[field_info[0]]);
                    }
                }
            }
        },
        build_buttons: function (buttons, new_modal) {
            var input_wrapper = $("<div></div>").addClass("input_wrapper input_inline");
            $.each(buttons, function (x, btn) {
                let button = $("<button></button>").addClass("btn");
                    label = btn,
                    class_name = '';
                    if(label.indexOf("|") >= 0){
                        label = btn.split("|")[0];
                        class_name = btn.split("|")[1];
                    }
                button.addClass(class_name).html(label);
                input_wrapper.append(button);
            });
            $("."+new_modal).find(".modal_content").append(input_wrapper);
        },
        update_data_schema_and_properties: function(modal_id, item_id, data_schema, fields) {
            let fields_schema = [],
                properties = comunicator.config.web_properties[item_id];
            if(typeof(properties) == "undefined") {
                comunicator.update_web_properties(item_id);
                setTimeout(function () {
                    result = modals.update_data_schema_and_properties(modal_id, item_id, data_schema, fields);
                }, 2000);
            } else {
                $.each(fields, function ( x, field) {
                    let field_info = field.split("|");
                    if(field_info[0] != "") {
                        if(comunicator.config.fields_in_lang.includes(field_info[0])) {
                            $.each(comunicator.config.languages, function (x, lang) {
                                var current_input = modal_id.find(".tab_content[data-lang="+lang+"]").find("input[name="+field_info[0]+"]");
                                if(field_info[1] == "tiny") {
                                    current_input = modal_id.find(".tab_content[data-lang="+lang+"]").find("textarea[name="+field_info[0]+"]");
                                }
                                if(current_input.attr("data-languagekey") == "SPANISH"){
                                    data_schema[field_info[0].toLowerCase().replace(comunicator.config.entity.toLowerCase(),"")] = current_input.val();
                                }
                                $.each(properties, function (x, current_schema) {
                                    if(current_schema.languageKey == current_input.attr("data-languagekey") &&
                                        current_schema.mainKey == field_info[0]) {
                                        current_schema.value = current_input.val();
                                        fields_schema.push(current_schema);
                                    }
                                });
                            });
                        } else {
                            data_schema = modals.update_data_schema(modal_id, data_schema, field)
                        }
                    }
                });
                result = modals.save_entity(data_schema, item_id);
                if (result) {
                    comunicator.save_web_properties(fields_schema, item_id);
                }
                return result;
            }
        },
        update_data_schema: function(modal_id, data_schema, field) {
            let field_info = field.split("|");
            if(field_info[0] != "") {
                let current_input= modal_id.find("input[name="+field_info[0]+"]"),
                    type_input = field_info[1],
                    defaul_entity_value = "";
                if(current_input.closest(".tab_content[data-lang=SPANISH]").length){
                    defaul_entity_value = field_info[0].toLowerCase().replace(modals.config.entity.toLowerCase(),"");
                }
                if (field_info[1].split(";").length > 1){
                    type_input = field_info[1].split(";")[0];
                }
                 if(type_input == "switch" || type_input == "checkbox") {
                    if(current_input.prop("checked")) {
                        data_schema[field_info[0]] = true;
                    } else {
                        data_schema[field_info[0]] = false;
                    }
                } else if(type_input == "select" || type_input == "select2") {
                    current_input= $(modal_id).find("select[name="+field_info[0]+"]");
                    if(type_input == "select2") {
                        data_schema[field_info[0]] = current_input.select2('val');
                    } else {
                        data_schema[field_info[0]] = current_input.val();
                        if(defaul_entity_value != "") data_schema[defaul_entity_value] = current_input.val();
                    }
                } else if(type_input == "password") {
                    if(current_input.val() != "") {
                        data_schema[field_info[0]] = $.md5(current_input.val());
                    } else {
                        data_schema[field_info[0]] = current_input.attr("data-old");
                    }
                } else {
                    data_schema[field_info[0]] = current_input.val();
                    if(defaul_entity_value != "") data_schema[defaul_entity_value] = current_input.val();
                }
            }
             return data_schema;
        },
        save_entity: function(data_schema, item_id) {

            if  (this.config.saved) {
                return;
            }

            var save = true;
            var url_last_new_posible="";
            if (typeof pre_verify_entity === 'function') {
                url_last_new_posible = data_schema.new_posible_image_licence;
                save = pre_verify_entity(data_schema, url_last_new_posible);
                console.log("save: " + save);
                if (save) {
                    data_schema['new_posible_image_licence'] = "";
                }

            }

            if (save) {
                if(typeof(item_id) != "undefined" && item_id != "") {
                    comunicator.config.call_back = "zebra_table.update_row('"+item_id+"')";
                    comunicator.update_entity(data_schema);
                } else {
                    comunicator.config.call_back = "zebra_table.build_zebra_table()";
                    comunicator.create_entity(data_schema);
                }

                this.config.saved = true;

                if (typeof post_verify_entity === 'function') {
                    post_verify_entity(data_schema, url_last_new_posible);
                }

                return true;
            }

            return false;

        },

        init_tinymce: function (t_id) {
            tinymce.init({
                selector: "#"+t_id,
                branding: false,
                height: 300,
                plugins: "lists link image code emoticons media hr visualblocks wordcount table preview",
                toolbar1: 'destroy_tiny | code | undo redo | preview visualblocks',
                toolbar2: 'styleselect | bold italic | forecolor backcolor | alignleft aligncenter alignright | numlist bullist | table | link media image emoticons hr',
                menubar: false,
                resize: "both",
                forced_root_block : 'p',
                extended_valid_elements : "script[src|async|defer|type|charset]",
                toolbar_items_size : 'small',
                setup: (editor) => {
                    editor.ui.registry.addButton('destroy_tiny', {
                      text: '<i class="far fa-times" style="font-family: \'Font Awesome 5 Pro\'"></i>',
                      onAction: function(_) {
                          editor.destroy();
                      }
                    });
                  }
            });
        },
        htmlDecode: function (input){
          var e = document.createElement('textarea');
          e.innerHTML = input;
          return e.childNodes.length === 0 ? "" : e.childNodes[0].nodeValue;
        },
        __add_listeners: function () {
            $(document).on("click", ".open_in_modal", function(e){
                e.preventDefault();
                let modal_id = $(this).attr("href"),
                    modal_class = modal_id.replace("#",""),
                    modal_content = $(modal_id);
                if($(".modal."+modal_class).length) {
                    $(".modal."+modal_class).detach();
                }
                modals.create_modal(modal_class);

                $(".modal."+modal_class).find(".modal_title").html(modal_content.data('title'));
                $(".modal."+modal_class).find(".modal_content").append(modal_content.html());
                $(".modal."+modal_class).fadeIn();
            });
            $(document).on("click", ".open_modal", function(e){
                e.preventDefault();
                let modal_id = $(this).attr("href"),
                    item_id = $(this).closest("tr").attr("id");
                comunicator.config.entity = $(modal_id).attr("data-entity");
                if ($(".select2").length > 0) {
                    $(".select2").select2();
                }
                $(modal_id).find(".input_ko").removeClass("input_ko");
                $(modal_id).find(".input_ok").removeClass("input_ok");
                if(modal_id == "#create_modal") {
                    let fields = $(modal_id).attr("data-fields").split("@;@");
                    $.each(fields, function ( x, field) {
                        modals.fill_input(modal_id, field, "", "");
                    });
                }
                if(modal_id == "#edit_modal") {
                    $(modal_id).find("input[name=item_id]").val(item_id);
                    let fields = $(modal_id).attr("data-fields").split("@;@");
                    $.each(fields, function ( x, field) {
                        modals.fill_input(modal_id, field, "", "");
                    });
                    let data = {},
                        properties ={};
                    $.each(comunicator.config.data_load, function (x, y) {
                        if (y.id == item_id) {
                            data = y;
                            properties = comunicator.config.web_properties[item_id]
                        }
                    });
                    if(typeof(properties) == "undefined") {
                        comunicator.update_web_properties(item_id);
                        setTimeout(function () {
                            properties = comunicator.config.web_properties[item_id];
                            $.each(properties, function (x, lang_field) {
                                $.each(fields, function ( x, field) {
                                    modals.fill_input(modal_id, field, data, lang_field);
                                });
                            });
                        }, 2000);
                    }
                    if(typeof(properties) != "undefined" && properties.length) {
                        $.each(properties, function (x, lang_field) {
                            $.each(fields, function ( x, field) {
                                modals.fill_input(modal_id, field, data, lang_field);
                            });
                        });
                    } else {
                        $.each(fields, function ( x, field) {
                            modals.fill_input(modal_id, field, data, "");
                        });
                    }
                    $(".language.tabs .tab:first-of-type").click();

                }
                if(modal_id == "#delete_modal") {
                    $(modal_id).find("input[name=item_id]").val(item_id);
                }
                $(modal_id).fadeIn();
            });
            $(document).on("click", ".modal .delete_confirm", function(e){
                e.preventDefault();
                let modal_id = $(this).closest(".modal"),
                    item_id = modal_id.find("input[name=item_id]").val();
                comunicator.config.call_back = "$(\"tr#"+item_id+"\").detach();$(\".modal\").fadeOut();";
                comunicator.delete_entity(item_id);
            });
            $(document).on("click", ".modal .save_data", function(e){
                e.preventDefault();
                var modal_id = $(this).closest(".modal"),
                    item_id = modal_id.find("input[name=item_id]").val(),
                    data_schema = {},
                    fields = $(".modal_edit_wrapper").attr("data-fields").split("@;@");
                if($("textarea.tiny").length){
                    tinyMCE.triggerSave();
                }
                if(item_id == "") item_id = 0;
                if($("tr#"+item_id).length){
                    data_schema = JSON.parse($("tr#"+item_id).find("#data_schema").val())
                }
                if(item_id){
                    result = modals.update_data_schema_and_properties(modal_id, item_id, data_schema, fields);
                    if (result) {
                        modal_id.fadeOut();
                    }
                } else {
                    $.each(fields, function ( x, field) {
                        data_schema = modals.update_data_schema(modal_id, data_schema, field);
                    });
                    if(modals.config.check_duplicates != "") {
                        $.get("/api/entities/" + comunicator.config.entity +"?"+modals.config.check_duplicates+"="+data_schema[modals.config.check_duplicates],function (data) {
                            if(data.length < 1) {
                                result = modals.save_entity(data_schema, "");
                                if (result) {
                                    modal_id.fadeOut();
                                }
                            } else {
                                modal_id.find("input[name="+modals.config.check_duplicates+"]").parent().addClass("input_ko");
                            }
                        })
                    } else {
                        result = modals.save_entity(data_schema, "");
                        if (result) {
                            modal_id.fadeOut();
                        }
                        return result;
                    }
                }
            });
            $(document).on("click", ".extra_buttons .fa-language", function (){
                let target = $(this).closest(".input"),
                    input_field = target.find("input"),
                    target_lang = $(this).closest(".tab_content").attr("data-lang");
                if(target.hasClass("textarea")) input_field = target.find("textarea");
                let name_input = input_field.attr("name"),
                    spanish_input = $(".tab_content[data-lang=SPANISH] input[name="+name_input+"]");
                if(target.hasClass("textarea")) spanish_input = $(".tab_content[data-lang=SPANISH] textarea[name="+name_input+"]");
                let payload = {
                    'text': spanish_input.val(),
                    'language_source': 'SPANISH',
                    'language_target': target_lang,
                };
                console.log(payload);
                // $.ajax({
                //     url: '/api/utils/translate',
                //     data: JSON.stringify(payload),
                //     contentType: "application/json",
                //     type: 'POST',
                //     success: function(result) {
                //         console.log(result);
                //     }
                // });


            });
            $(document).on("click", ".extra_buttons .fa-code", function (){
                let tiny_target = $(this).closest(".input").find("textarea"),
                    tiny_target_id = tiny_target.attr("id");
                modals.init_tinymce(tiny_target_id);
            });
            $(document).on("click", ".language.tabs .tab", function (){
                let this_lang = $(this).attr("data-lang");
                $(this).addClass("active").siblings().removeClass("active");
                tinyMCE.triggerSave();
                $(".language.tab_content_wrapper .tab_content").each(function () {
                    let content_lang = $(this).attr("data-lang");
                    if(content_lang == this_lang) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                })
            });
            // $(document).on("click", ".modal", function (e){
            //     if(e.target == $("div.modal")[0] || $(e.target).closest(".modal_content").length < 1) {
            //         $(this).fadeOut();
            //     }
            // });
            $(document).on("click", ".modal .close_modal", function (){
                $(this).closest(".modal").fadeOut();
            });
        }
    }
}();
