var zebra_table = function () {
    return {
        config: {
            headers: [],
            entity: '',
            options: 'EDIT|DELETE',
            dragable: false
        },
        init: function () {
            if($(".zebra_table_wrapper").length > 0){
                if($(".zebra_table_wrapper").attr("data-headers")){
                    var list_headers = $(".zebra_table_wrapper").attr("data-headers");
                    if(typeof(list_headers) == "string"){
                        list_headers = list_headers.split("@;@");
                        if(list_headers[list_headers.length-1] == ""){
                            list_headers.pop();
                        }
                        this.config.headers = list_headers;
                    }
                }
                if($(".zebra_table_wrapper").attr("data-entity")){
                    this.config.entity = $(".zebra_table_wrapper").attr("data-entity");
                }
                if($(".zebra_table_wrapper").attr("data-options")){
                    this.config.options = $(".zebra_table_wrapper").attr("data-options");
                }
                this.build_zebra_table();
                this.__add_listeners();
            }
        },
        __add_listeners: function () {
            if(this.config.dragable){
                $(document).on("click", ".zebra_table .sort", function (e) {
                    $(this).addClass("clicked");
                    if(!e.metaKey) {
                        $(".zebra_table .sort:not(.clicked)").closest("tr").removeClass("selected");
                    }
                    if(!$(this).closest("tr").hasClass("selected")){
                        $(this).closest("tr").addClass("selected");
                        if($(this).closest(".zebra_table").parent().hasClass("zebra_table_restore")) {
                            $(this).closest(".zebra_table").parent().find(".zebra_restore_btn").removeClass("btn_disable");
                        }
                    } else {
                        $(this).closest("tr").removeClass("selected");
                        if($(this).closest(".zebra_table").parent().hasClass("zebra_table_restore")) {
                            $(this).closest(".zebra_table").parent().find(".zebra_restore_btn").addClass("btn_disable");
                        }
                    }
                    $(this).removeClass("clicked");
                });
                $(".zebra_table .sort_all").click(function (e) {
                    var one_selected = false;
                    $(".zebra_table .sort").each(function () {
                        if($(this).closest("tr").hasClass("selected")) {
                            one_selected = true;
                        }
                    });
                    if(one_selected) {
                        $(this).closest(".zebra_table").find(".sort").closest("tr").removeClass("selected");
                        if($(this).closest(".zebra_table").parent().hasClass("zebra_table_restore")) {
                            $(this).closest(".zebra_table").parent().find(".zebra_restore_btn").addClass("btn_disable");
                        }
                    } else {
                        $(this).closest(".zebra_table").find(".sort").closest("tr").addClass("selected");
                        if($(this).closest(".zebra_table").parent().hasClass("zebra_table_restore")) {
                            $(this).closest(".zebra_table").parent().find(".zebra_restore_btn").removeClass("btn_disable");
                        }
                    }
                });
                $(".zebra_table tbody").sortable({
                    handle: ".sort",
                    containment: "parent",
                    placeholder: "tr-placeholder"
                });
            }
            $(document).on("click", ".zebra_table_toggle", function () {
                $(this).next().slideToggle();
            });
            $(document).on("keydown", ".zebra_table .editable input", function (e) {
                if(e.keyCode === 13) $(this).blur();
            });
            $(document).on("click", ".zebra_table .editable input[type=checkbox]", function (e) {
                $(this).blur();
            });
            $(document).on("blur", ".zebra_table .editable input", function (e) {
                let id = $(this).closest("tr").attr("id"),
                    data_schema = JSON.parse($(this).closest("tr").find("#data_schema").val()),
                    key_value = $(this).attr("name"),
                    new_value = $(this).val();
                if($(this).attr("type") == "checkbox") {
                    if($(this).prop("checked")) {
                        new_value = true;
                    } else {
                        new_value = false;
                    }
                }
                data_schema[key_value] = new_value;

                comunicator.config.call_back = "zebra_table.update_row('"+data_schema.id+"')";
                comunicator.update_entity(data_schema);
            });
            $(document).on("click", ".zebra_table .delete_data", function () {
                let id = $(this).closest("tr").attr("id"),
                    data_schema = JSON.parse($(this).closest("tr").find("#data_schema").val());
                data_schema.removed = true;

                comunicator.config.call_back = "zebra_table.update_row('"+data_schema.id+"')";
                comunicator.update_entity(data_schema);
            });
            $(document).on("click", ".zebra_table .restore_data", function () {
                let id = $(this).closest("tr").attr("id"),
                    data_schema = JSON.parse($(this).closest("tr").find("#data_schema").val());
                data_schema.removed = false;

                comunicator.config.call_back = "zebra_table.update_row('"+data_schema.id+"')";
                comunicator.update_entity(data_schema);
            });
        },
        build_zebra_table: function () {
            $(".zebra_table_wrapper").html(this.build_html_table("loading",""));
            let data = comunicator.config.data_load;
            var rows = [],
                rows_trash = [];
            for(let i = 0; i < data.length; i++) {
                var row = [],
                    row_trash = [];
                $.each(zebra_table.config.headers, function( y, key ) {
                  let key_split = key.split("|"),
                      search_key = key_split[1],
                      data_row = {
                        id:data[i].id,
                        target:search_key,
                        data_schema: JSON.stringify(data[i])
                        };
                  if(key_split.length > 2) {
                    data_row.type = key_split[2];
                  }
                  if(search_key != "None") {
                      if(key_split.length > 3) {
                          data_row.extra_class = key_split[3];
                      }
                      if(data[i][search_key] != null){
                          data_row.value = data[i][search_key];

                          if(typeof(data[i].removed) === "undefined" || !data[i].removed){
                            row.push(data_row);
                          } else {
                            row_trash.push(data_row);
                          }
                      } else {
                          data_row.value = "";
                          if(typeof(data[i].removed) === "undefined" || !data[i].removed){
                            row.push(data_row);
                          } else {
                            row_trash.push(data_row);
                          }
                      }
                  } else {
                      if(typeof(data[i].removed) === "undefined" || !data[i].removed){
                        row.push({id:data[i].id,value: zebra_table.build_table_options()});
                      } else {
                        let current_options = zebra_table.config.options;
                        zebra_table.config.options = "RESTORE|DELETE_FULL";
                        row_trash.push({value: zebra_table.build_table_options()});
                        zebra_table.config.options = current_options;
                      }
                  }
                });
                if(row_trash.length == 0) rows.push(row);
                if(row_trash.length > 0) rows_trash.push(row_trash);
            }
            $(".zebra_table_wrapper").html(zebra_table.build_html_table(rows,""));

            if(rows_trash.length > 0) {
                let table_retore = $(".zebra_table_wrapper").clone().toggleClass("zebra_table_wrapper zebra_table_restore"),
                    table_togle = $("<i class='fad fa-recycle'></i>").addClass("zebra_table_toggle");
                table_retore.html(zebra_table.build_html_table(rows_trash,"restore_table"));
                $(".zebra_table_wrapper").append(table_togle);
                $(".zebra_table_wrapper").append(table_retore);
            }
        },
        build_html_table: function (data, extra_class) {
            let table_wrapper = $("<table></table>").addClass("zebra_table").addClass(extra_class),
                tr = $("<tr></tr>"),
                th = $("<th></th>"),
                td = $("<td></td>");

            let tr_head = tr.clone();

            if(this.config.dragable) {
                let new_th = th.clone();
                new_th.addClass("sort_all").html('<i class="fas fa-sort"></i>');
                tr_head.append(new_th);
            }

            $.each(this.config.headers, function( index, value ) {
              let new_th = th.clone();
              new_th.html(value.split("|")[0]);
              if(value.split("|").length > 3) {
                  new_th.addClass(value.split("|")[3]);
              }
              tr_head.append(new_th);
            });

            table_wrapper.append(tr_head);

            if(data == "loading") {
                table_wrapper.addClass("loading");
                for(let i = 1; i < 10; i++) {
                    let tr_body = tr.clone();
                    if(this.config.dragable) {
                        tr_body.append(td.clone().addClass("sort").html('<i class="fas fa-grip-vertical"></i>'));
                    }
                    for(let x = 0; x < this.config.headers.length; x++) {
                        if(x == this.config.headers.length - 1){
                            tr_body.append(td.clone().addClass("loading_opt"));
                        } else {
                            tr_body.append(td.clone().addClass("loading").html(x + " " + this.config.headers.length));
                        }
                    }
                    table_wrapper.append(tr_body);
                }
            } else {
                $.each(data, function( x, row ) {
                    table_wrapper.append(zebra_table.build_tr(row));
                });
            }
            return table_wrapper;
        },
        build_tr: function(row) {
            let tr = $("<tr></tr>"),
                th = $("<th></th>"),
                td = $("<td></td>"),
                tr_body = tr.clone();
            var content = row[0].data_schema.replace("'", '&#39;');
            tr_body.html("<input type='hidden' id='data_schema' value='" + content + "'>");
            if(zebra_table.config.dragable) {
                tr_body.append(td.clone().addClass("sort").html('<i class="fas fa-grip-vertical"></i>'));
            }
            $.each(row, function( y, item ) {
                let new_td = td.clone();
                tr_body.attr("id",item.id);
                if(typeof(item.extra_class) != "undefined") new_td.addClass(item.extra_class);
                if ((item.value != "" && item.value != null) || item.value === false) {
                    if (item.type) {
                        if (item.type == "desc") {
                            item.value = "<div href='#edit_modal' class='crop open_modal'>"+item.value+"</div>";
                        } else if (item.type == "pic") {
                            item.value = "<div href='#edit_modal' class='pic open_modal'><img src='"+item.value+"=s60'></div>";
                        } else if (item.type == "edit") {
                            item.value = "<div class='editable'>" +
                                "<input type='text' name='"+item.target+"' value='"+item.value+"'>" +
                                "</div>";
                        } else if (item.type == "list") {
                            let value_list = item.value;
                            item.value = "<div class='list'>";
                            $.each(value_list, function (x, y) {
                                item.value += "<div class='item_list'>" + y + "</div>";
                            });
                            item.value += "</div>";
                        } else if (item.type == "switch") {
                            let siwtch = '<div class="editable"><input type="checkbox" name="'+item.target+'" class="swicth"></div>';
                            if(item.value) {
                                siwtch = '<div class="editable"><input type="checkbox" name="'+item.target+'" class="swicth" checked="checked"></div>';
                            }
                            item.value = siwtch;
                        }
                    }
                    new_td.html(item.value);
                } else {
                    if (item.type == "switch") {
                        item.value = '<div class="editable default_swicth"><input type="checkbox" name="'+item.target+'" class="swicth"></div>';
                        new_td.html(item.value);
                    }
                }
                tr_body.append(new_td);
            });
            return tr_body;
        },
        build_table_options: function () {
            let options = this.config.options.split("|");
            var html_result = "";
            $.each(options, function( x, option ) {
                let html_wraaper = $("<div></div>");

                html_wraaper.append(zebra_table.build_option(option));

                html_result += html_wraaper.html();
            });
            return html_result;
        },
        build_option(option) {
            let icon = '',
                link = '#',
                btn_class = '';
            if(option == "CONFIG"){
                icon = "fa-cogs";
                link = "#config_modal";
                btn_class = "open_modal";
            }
            if(option == "EDIT") {
                icon = "fa-edit";
                link = "#edit_modal";
                btn_class = "open_modal";
            }
            if(option == "RESTORE") {
                icon = "fa-upload";
                btn_class = "restore_data";
            }
            if(option == "DELETE"){
                icon = "fa-trash-alt";
                btn_class = "delete_data";
            }
            if(option == "DELETE_FULL"){
                icon = "fa-trash-alt";
                link = "#delete_modal";
                btn_class = "open_modal";
            }
            if(option.indexOf("LINK") > -1 ){
                let link_options = option.split(";");
                link = link_options[1];
                btn_class = link_options.length > 2 ? link_options[2] : "open_link";
                icon = link_options.length > 3 ? link_options[3] : "fa-link";
            }
            let a = $("<a></a>").attr("href",link),
                i = $("<i></i>").addClass("fad");
            i.addClass(icon);
            a.addClass("btn btn_small btn_link").addClass(btn_class).append(i);
            return a;
        },
        update_row: function (item_id) {
            let tr_changed = $("tr#"+item_id);
            tr_changed.addClass("changing");
            let all_data = comunicator.config.data_load,
                data = {};
            $.each(all_data, function (x, y) {
                if (y.id == item_id)data = y
            });
            let row = [],
                row_trash = [];
            $.each(zebra_table.config.headers, function( y, key ) {
                let key_split = key.split("|"),
                    search_key = key_split[1],
                    data_row = {
                        id:data.id,
                        target:search_key,
                        data_schema: JSON.stringify(data)
                    };
                if(key_split.length > 2) {
                    data_row.type = key_split[2];
                }
                if(search_key != "None") {
                    if(data[search_key] != null){
                          data_row.value = data[search_key];
                          if(typeof(data.removed) === "undefined" || !data.removed){
                            row.push(data_row);
                          } else {
                            row_trash.push(data_row);
                          }
                      } else {
                        data_row.value = "";
                        if(typeof(data.removed) === "undefined" || !data.removed){
                            row.push(data_row);
                        } else {
                            row_trash.push(data_row);
                        }
                      }
                } else {
                      if(typeof(data.removed) === "undefined" || !data.removed){
                          data_row.value= zebra_table.build_table_options();
                          row.push(data_row);
                      } else {
                        let current_options = zebra_table.config.options;
                        zebra_table.config.options = "RESTORE|DELETE_FULL";
                        data_row.value= zebra_table.build_table_options();
                        row_trash.push(data_row);
                        zebra_table.config.options = current_options;
                      }
                  }
            });
            if(row.length > 0) {
                if(tr_changed.closest(".zebra_table_restore").length) {
                    tr_changed.removeClass("changing").slideUp().promise().done(function () {
                        tr_changed.detach();
                        let new_tr = zebra_table.build_tr(row);
                        $(".zebra_table:not(.restore_table) tbody").append(new_tr);
                        if($(".restore_table tr").length < 2) {
                            $(".zebra_table_restore").detach();
                            $(".zebra_table_toggle").detach();
                        }
                    });
                } else {
                    let new_tr = zebra_table.build_tr(row);
                    tr_changed.html(new_tr.html());
                    tr_changed.removeClass("changing");
                }

            }
            if(row_trash.length > 0){
                tr_changed.removeClass("changing").slideUp().promise().done(function () {
                    tr_changed.detach();
                    let new_tr = zebra_table.build_tr(row_trash);
                    if($(".zebra_table_restore").length) {
                        $(".zebra_table_restore .zebra_table tbody").append(new_tr);
                    } else {
                        let table_retore = $(".zebra_table_wrapper").clone().toggleClass("zebra_table_wrapper zebra_table_restore"),
                            table_togle = $("<i class='fad fa-recycle'></i>").addClass("zebra_table_toggle");
                        table_retore.html(zebra_table.build_html_table([row_trash],"restore_table"));
                        $(".zebra_table_wrapper").append(table_togle);
                        $(".zebra_table_wrapper").append(table_retore);
                    }
                });
            }
        }
    }
}();