!function(s){function l(s){return s instanceof Object&&Object.keys(s).length>0}s.fn.jsonViewer=function(e,n){return n=n||{},this.each(function(){var a=function s(e,n){var a="";if("string"==typeof e)e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),/^(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/.test(e)?a+='<a href="'+e+'" class="json-string json-url">"'+e+'"</a>':a+='<span class="json-string">"'+e+'"</span>';else if("number"==typeof e)a+='<span class="json-literal json-literal-number">'+e+"</span>";else if("boolean"==typeof e)a+='<span class="json-literal json-literal-boolean">'+e+"</span>";else if(null===e)a+='<span class="json-literal json-literal-null">null</span>';else if(e instanceof Array)if(e.length>0){a+='[<ol class="json-array">';for(var t=0;t<e.length;++t)a+="<li>",l(e[t])&&(a+='<a href class="json-toggle"></a>'),a+=s(e[t],n),t<e.length-1&&(a+=","),a+="</li>";a+="</ol>]"}else a+="[]";else if("object"==typeof e){var o=Object.keys(e).length;if(o>0){for(var i in a+='{<ul class="json-dict">',e)if(e.hasOwnProperty(i)){a+="<li>";var r=n.withQuotes?'<span class="json-string json-property">"'+i+'"</span>':'<span class="json-property">'+i+"</span>";l(e[i])?a+='<a href class="json-toggle"></a>'+r:a+=r,a+=": "+s(e[i],n),--o>0&&(a+=","),a+="</li>"}a+="</ul>}"}else a+="{}"}return a}(e,n);l(e)&&(a='<a href class="json-toggle"></a>'+a),s(this).html(a),s(this).off("click"),s(this).on("click","a.json-toggle",function(){var l=s(this).toggleClass("collapsed").siblings("ul.json-dict, ol.json-array");if(l.toggle(),l.is(":visible"))l.siblings(".json-placeholder").remove();else{var e=l.children("li").length,n=e+(e>1?" items":" item");l.after('<a href class="json-placeholder">'+n+"</a>")}return!1}),s(this).on("click","a.json-placeholder",function(){return s(this).siblings("a.json-toggle").click(),!1}),1==n.collapsed&&s(this).find("a.json-toggle").click()})}}(jQuery);