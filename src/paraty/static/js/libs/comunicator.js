var comunicator = function () {
    return {
        config : {
            entity: "",
            endpoint : "",
            type : "",
            data_load: [],
            data_schema : {},
            web_properties : {},
            languages: [],
            fields_in_lang: [],
            call_back : "this.default_call_back()",
            search_params: {}
        },
        init: function(){
            comunicator.update_data_load();
        },
        update_data_load: function(){
            $.get("/api/entities/"+this.config.entity, this.config.search_params, function (data) {
                comunicator.config.data_load = data;
                if(comunicator.config.data_load.length > 0) {
                    comunicator.config.data_schema = data[0];
                    $.get("/api/entities/"+comunicator.config.entity+"/"+data[0].id+"/web_properties",{}, function (properties) {
                        comunicator.config.web_properties[data[0].id] = properties;
                        $.each(properties, function (x, language) {
                            if (!comunicator.config.languages.includes(language.languageKey)) {
                                comunicator.config.languages.push(language.languageKey);
                            }
                            let field = language.mainKey;
                            if (!comunicator.config.fields_in_lang.includes(field)) {
                                comunicator.config.fields_in_lang.push(field);
                            }
                        });
                        eval(comunicator.config.call_back);
                    });
                } else {
                    eval(comunicator.config.call_back);
                }
            });
        },
        update_entity: function(data_schema) {
            $.ajax({
                url: "/api/entities/"+this.config.entity+"/"+data_schema.id,
                data: JSON.stringify(data_schema),
                contentType: "application/json",
                type: 'PUT',
                success: function(result) {
                    comunicator.update_data_load();
                }
            });
        },
        create_entity: function(data_schema) {
            $.ajax({
                url: "/api/entities/"+this.config.entity,
                data: JSON.stringify(data_schema),
                contentType: "application/json",
                type: 'POST',
                success: function(result) {
                    comunicator.update_data_load();
                }
            });
        },
        delete_entity: function(item_id) {
            $.ajax({
                url: "/api/entities/"+this.config.entity+"/"+item_id,
                data: JSON.stringify({}),
                contentType: "application/json",
                type: 'DELETE',
                success: function(result) {
                    comunicator.update_data_load();
                }
            });
        },
        update_web_properties: function(item_id) {
            $.get("/api/entities/"+this.config.entity+"/"+item_id+"/web_properties",{}, function (data) {
                comunicator.config.web_properties[''+item_id] = data;
                $.each(data, function (x, language) {
                    if (!comunicator.config.languages.includes(language.languageKey)) {
                        comunicator.config.languages.push(language.languageKey);
                    }
                    let field = language.mainKey;
                    if (!comunicator.config.fields_in_lang.includes(field)) {
                        comunicator.config.fields_in_lang.push(field);
                    }
                });
            });
        },
        save_web_properties: function(fields_schema, item_id) {
            $.ajax({
                url: "/api/entities/WebPageProperty",
                data: JSON.stringify(fields_schema),
                contentType: "application/json",
                type: "POST",
                success: function(result) {
                    if(item_id != ""){
                        comunicator.update_web_properties(item_id);
                    }
                }
            });
        },
        ajax_call: function () {
            console.log(this.config);
            $.ajax({
                url: this.config.endpoint,
                data: JSON.stringify(this.config.data_schema),
                contentType: "application/json",
                type: this.config.type,
                success: function(result) {
                    comunicator.update_data_load();
                    comunicator.clean_config();
                }
            });
        },
        clean_config: function(){
            this.config.endpoint = "";
            this.config.type = "";
            this.config.data_schema = {};
            this.config.call_back = "this.default_call_back()";
        },

        getSearchParams: function (ignore_params) {
            var sPageURL = decodeURIComponent(window.location.search.substring(1)),
                sURLVariables = sPageURL.split('&'),
                args = {},
                sParameterName,
                i;

            for (i = 0; i < sURLVariables.length; i++) {
                sParameterName = sURLVariables[i].split('=');

                if (sParameterName.length === 2 && ignore_params.indexOf(sParameterName[0]) === -1) {
                    args[sParameterName[0]] = sParameterName[1]
                }
            }

            comunicator.config.search_params = args;
        }
    }
}();