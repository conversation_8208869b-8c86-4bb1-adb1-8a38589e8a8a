class ParatyLogger {
    constructor(url, session, namespace=""){
        this.session = session;
        this.url = url;
        this.namespace = namespace;
    }

    __emit(level, message){
        var request_body = {
            level: level,
            message: this.namespace + message,
            session: this.session
        };

        try {
            navigator.sendBeacon(this.url, JSON.stringify(request_body));
        }catch (e) {
            console.error("Error sending logs", e);
        }

    }

    info(message){
        console.log(message);
        this.__emit("info", message);
    }
    
    error(message){
        console.error(message);
        this.__emit("error", message);
    }

    warn(message){
        console.warn(message);
        this.__emit("warning", message);
    }
}
