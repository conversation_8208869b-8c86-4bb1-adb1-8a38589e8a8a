tinymce.init({
    selector: 'textarea.tiny',
    branding: false,
    height: 300,
    plugins: "lists link image code emoticons media hr visualblocks wordcount table preview",
    toolbar1: 'code | undo redo | preview visualblocks',
    toolbar2: 'styleselect | bold italic | forecolor backcolor | alignleft aligncenter alignright | numlist bullist | table | link media image emoticons hr',
    menubar: false,
    forced_root_block : 'p',
    extended_valid_elements : "script[src|async|defer|type|charset]",
    toolbar_items_size : 'small',
});