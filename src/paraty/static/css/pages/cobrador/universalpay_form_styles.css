
.center_xy {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
}

.center_xy_before:before {
    position: absolute;
    top: 60%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.center_x {
    position: absolute;
    left: 50%;
    -webkit-transform: translate(-50%, 0%);
    -moz-transform: translate(-50%, 0%);
    -ms-transform: translate(-50%, 0%);
    -o-transform: translate(-50%, 0%);
    transform: translate(-50%, 0%);
}

.center_y {
    position: absolute;
    top: 50%;
    -webkit-transform: translate(0%, -50%);
    -moz-transform: translate(0%, -50%);
    -ms-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
}

.center_y_before:before {
    position: absolute;
    top: 50%;
    -webkit-transform: translate(0%, -50%);
    -moz-transform: translate(0%, -50%);
    -ms-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
}

.center_image {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    min-width: 100%;
    min-height: 100%;
    max-width: none;
}

.fs:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
}

.icon-xcross:before, .icon-xcross:after {
    content: '';
    width: 100%;
    height: 2px;
    background: white;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    -moz-transform: translate(-50%, -50%) rotate(45deg);
    -ms-transform: translate(-50%, -50%) rotate(45deg);
    -o-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
}

.icon-xcross:after {
    -webkit-transform: translate(-50%, -50%) rotate(-45deg);
    -moz-transform: translate(-50%, -50%) rotate(-45deg);
    -ms-transform: translate(-50%, -50%) rotate(-45deg);
    -o-transform: translate(-50%, -50%) rotate(-45deg);
    transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
    0%, 40%, 100% {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleY(1);
    }
}

@keyframes sk-stretchdelay {
    0%, 40%, 100% {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleY(1);
    }
}

.display_flex {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    -moz-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    -o-flex-wrap: wrap;
    flex-wrap: wrap;
}

.slider_countdown {
    margin: 40px 20px 20px;
    text-align: center;
    position: absolute;
    left: 50%;
    -webkit-transform: translate(-50%, 0%);
    -moz-transform: translate(-50%, 0%);
    -ms-transform: translate(-50%, 0%);
    -o-transform: translate(-50%, 0%);
    transform: translate(-50%, 0%);
    bottom: 180px;
    left: 48% !important;
    color: white;
}

.slider_countdown div {
    display: inline-block;
    font-size: 16px;
    list-style-type: none;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 0;
    padding-bottom: 0;
    text-transform: uppercase;
}

.slider_countdown .days {
    font-weight: 600;
}

.slider_countdown .date {
    display: block;
    font-size: 4.5rem;
}

.slider_countdown .title_format {
    font-weight: 600;
}

@media screen and (max-width: 800px) {
    .slider_countdown {
        display: inline-block;
        width: 380px;
        top: 330px;
        left: 45% !important;
    }
    .slider_countdown div {
        font-size: 10px;
    }
    .slider_countdown .date {
        font-size: 2.5rem;
    }
}

#popup_upay_payment_wrapper {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.5);
}

#popup_upay_payment_wrapper:before {
    z-index: 0;
    background-color: black;
    opacity: 0.3;
    display: block;
    content: "";
}

#popup_upay_payment_wrapper .close_popup_upay {
    position: absolute;
    top: 40px;
    right: 40px;
    font-size: 32px;
    color: white;
}

#popup_upay_payment_wrapper .center_content {
    z-index: 3;
}

@media only screen and (max-width: 425px) {
    #popup_upay_payment_wrapper iframe {
        width: 300px;
        height: 400px;
    }
}


