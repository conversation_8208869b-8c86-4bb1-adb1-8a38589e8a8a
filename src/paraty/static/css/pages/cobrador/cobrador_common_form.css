/* line 15, ../../../../sass/commons/_colors.scss */
:root {
  --info: #4285F4;
  --success: #35A853;
  --danger: #EA4336;
  --warning: #FBBC03;
}

/* line 32, ../../../../sass/libs/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 41, ../../../../sass/libs/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 50, ../../../../sass/libs/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 60, ../../../../sass/libs/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 1, ../../../../sass/commons/_modals.scss */
.modal {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  position: fixed;
  background: rgba(237, 237, 237, 0.8);
}
/* line 8, ../../../../sass/commons/_modals.scss */
.modal#delete_modal .modal_title {
  border-bottom-color: #EA4336;
  color: #EA4336;
}
/* line 12, ../../../../sass/commons/_modals.scss */
.modal#delete_modal .btn {
  background: #EA4336;
  border-color: #EA4336;
}
/* line 15, ../../../../sass/commons/_modals.scss */
.modal#delete_modal .btn.btn_link {
  background: transparent;
  color: #EA4336;
}
/* line 21, ../../../../sass/commons/_modals.scss */
.modal .modal_content {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: white;
  min-width: 600px;
  max-width: 80%;
  max-height: 95vh;
  overflow: auto;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
}
/* line 31, ../../../../sass/commons/_modals.scss */
.modal .modal_content .close {
  position: sticky;
  top: -10px;
  z-index: 100;
  float: right;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-right: -10px;
  cursor: pointer;
}
/* line 41, ../../../../sass/commons/_modals.scss */
.modal .modal_content .close i {
  font-size: 20px;
}
/* line 45, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_title {
  position: sticky;
  top: -20px;
  z-index: 20;
  background: #fafafa;
  border-bottom: 1px solid #446ca9;
  color: #446ca9;
  font-weight: bold;
  margin: -20px -20px 0;
  padding: 10px;
}
/* line 56, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_desc {
  padding: 10px;
}
/* line 59, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic {
  position: relative;
  width: 100%;
  height: 150px;
  text-align: center;
  overflow: hidden;
  border: 1px solid #fafafa;
}
/* line 67, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic:hover:after {
  opacity: 1;
}
/* line 70, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic:hover .updload {
  opacity: 1;
}
/* line 72, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic:hover .updload i {
  margin-top: 0;
}
/* line 77, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1000px;
  height: 1000px;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
  z-index: 0;
  background: linear-gradient(-45deg, #fafafa, #fafafa 25%, transparent 25%, transparent 50%, transparent 50%, transparent 75%, #fafafa 75%, #fafafa), linear-gradient(45deg, #fafafa, #fafafa 25%, transparent 25%, transparent 50%, transparent 50%, transparent 75%, #fafafa 75%, #fafafa);
  background-size: 14px 14px;
}
/* line 98, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  z-index: 5;
  background: rgba(37, 37, 37, 0.8);
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 106, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic .updload {
  opacity: 0;
  cursor: pointer;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 10;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 112, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic .updload i {
  margin-top: 20px;
  font-size: 70px;
  color: white;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 118, ../../../../sass/commons/_modals.scss */
.modal .modal_content .modal_pic img {
  vertical-align: middle;
  max-width: 100%;
  max-height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 2;
}

/* line 128, ../../../../sass/commons/_modals.scss */
.tabs {
  background: #fafafa;
  padding: 10px 10px 0;
}
/* line 131, ../../../../sass/commons/_modals.scss */
.tabs.tabs-transparent {
  background: transparent;
  border-bottom: 2px solid #ededed;
}
/* line 134, ../../../../sass/commons/_modals.scss */
.tabs.tabs-transparent .tab {
  border-width: 2px 2px 0 2px;
  border-style: solid;
  border-color: #ededed;
}
/* line 138, ../../../../sass/commons/_modals.scss */
.tabs.tabs-transparent .tab.active {
  margin-bottom: -2px;
}
/* line 143, ../../../../sass/commons/_modals.scss */
.tabs .tab {
  display: inline-block;
  vertical-align: bottom;
  background: #ededed;
  margin-right: 5px;
  padding: 5px 10px;
  border-radius: 5px 5px 0 0;
  cursor: pointer;
}
/* line 151, ../../../../sass/commons/_modals.scss */
.tabs .tab:hover {
  background: #cdcdcd;
}
/* line 154, ../../../../sass/commons/_modals.scss */
.tabs .tab.active {
  background: white;
}
/* line 157, ../../../../sass/commons/_modals.scss */
.tabs .tab.tab-big {
  padding: 10px 20px;
  margin-right: 10px;
  font-size: 16px;
  color: #446ca9;
}
/* line 163, ../../../../sass/commons/_modals.scss */
.tabs .tab i {
  margin-right: 8px;
}

/* line 168, ../../../../sass/commons/_modals.scss */
.tab_content_wrapper .tab_content {
  display: none;
}
/* line 170, ../../../../sass/commons/_modals.scss */
.tab_content_wrapper .tab_content:first-of-type {
  display: block;
}

/* line 176, ../../../../sass/commons/_modals.scss */
.my_confirm {
  width: 50%;
  margin: auto;
}

@media (max-width: 600px) {
  /* line 184, ../../../../sass/commons/_modals.scss */
  body .modal .modal_content {
    min-width: auto;
    width: 100%;
  }
}
/* line 1, ../../../../sass/commons/_forms.scss */
.message {
  display: block;
  padding: 7px 14px;
  background: #fafafa;
  border-radius: 3px;
  margin: 5px auto;
}
/* line 7, ../../../../sass/commons/_forms.scss */
.message.m_ok {
  color: #35A853;
}
/* line 10, ../../../../sass/commons/_forms.scss */
.message.m_ko {
  color: #FBBC03;
}

/* line 14, ../../../../sass/commons/_forms.scss */
.input_wrapper {
  padding: 10px 5px;
}
/* line 16, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline {
  display: inline-block;
  vertical-align: middle;
}
/* line 19, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline label {
  display: inline-block;
  vertical-align: middle;
  padding: 5px 10px 5px 0;
}
/* line 24, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline .input {
  display: inline-block;
  vertical-align: middle;
}
/* line 27, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline .input + label {
  padding-left: 10px;
}
/* line 31, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline .input:not(.checkbox) {
  width: 200px;
}
/* line 33, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline .input:not(.checkbox).input_small {
  width: 100px;
}
/* line 37, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline.input_center {
  text-align: center;
  width: 100%;
}
/* line 41, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline .btn {
  margin: 0 3px;
}
/* line 45, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline_checkbox {
  position: relative;
}
/* line 47, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline_checkbox label {
  display: block;
  margin-left: 30px;
  padding: 0;
}
/* line 52, ../../../../sass/commons/_forms.scss */
.input_wrapper.input_inline_checkbox .input {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
}
/* line 60, ../../../../sass/commons/_forms.scss */
.input_wrapper label {
  display: block;
  padding-bottom: 5px;
  font-size: 14px;
  color: #446ca9;
}
/* line 66, ../../../../sass/commons/_forms.scss */
.input_wrapper .input {
  position: relative;
}
/* line 69, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.input_ok input, .input_wrapper .input.input_ok input:focus {
  border-color: #35A853;
}
/* line 72, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.input_ok:after {
  content: '\f058';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #35A853;
}
/* line 82, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.input_ko input, .input_wrapper .input.input_ko input:focus {
  border-color: #EA4336;
}
/* line 85, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.input_ko:after {
  content: '\f06a';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #EA4336;
}
/* line 94, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.select {
  position: relative;
}
/* line 96, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.select:after {
  content: '\f107';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #446ca9;
}
/* line 104, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.select select {
  position: relative;
  z-index: 2;
}
/* line 108, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.select .select2.select2-container {
  width: 100% !important;
}
/* line 110, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.select .select2.select2-container .select2-selection {
  border-color: #ededed;
}
/* line 113, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.select .select2.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice {
  border-color: #ededed;
  background: #fafafa;
  padding: 3px 5px;
  margin: 5px 5px 3px 0;
}
/* line 118, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.select .select2.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove {
  float: right;
  margin-left: 5px;
}
/* line 128, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.textarea .extra_buttons {
  top: 7px;
  -webkit-transform: unset;
  -moz-transform: unset;
  -ms-transform: unset;
  -o-transform: unset;
  transform: unset;
}
/* line 137, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.search {
  position: relative;
}
/* line 139, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.search:after {
  content: '\f002';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 300;
  font-size: 20px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #446ca9;
}
/* line 148, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.search input {
  position: relative;
  z-index: 2;
}
/* line 154, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.switch input[type=checkbox] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: #ededed;
  border-width: 0;
  width: 22px;
  height: 12px;
  border-radius: 8px;
  position: relative;
  outline: none;
  cursor: pointer;
}
/* line 166, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.switch input[type=checkbox]:before {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #EA4336;
  position: absolute;
  top: 2px;
  left: 3px;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 179, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.switch input[type=checkbox]:checked:before {
  content: '';
  background: #35A853;
  left: auto;
  right: 3px;
}
/* line 188, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) {
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
}
/* line 192, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) input[type=checkbox] {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid #cdcdcd;
  border-radius: 0;
  width: 16px;
  height: 16px;
  padding: 0;
  margin: 0 0 0 10px;
  outline: none;
}
/* line 207, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) input[type=checkbox]:checked:before {
  content: '\f00c';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  color: #4285F4;
  font-size: 10px;
  line-height: 15px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 217, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) input[type=checkbox]:hover {
  border-color: #4285F4;
}
/* line 220, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) input[type=checkbox] + span {
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 45px);
  margin-left: 10px;
}
/* line 228, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.hasDatepicker {
  position: relative;
}
/* line 230, ../../../../sass/commons/_forms.scss */
.input_wrapper .input.hasDatepicker i {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #446ca9;
}
/* line 236, ../../../../sass/commons/_forms.scss */
.input_wrapper .input .extra_buttons {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 7px;
}
/* line 239, ../../../../sass/commons/_forms.scss */
.input_wrapper .input .extra_buttons i {
  color: #446ca9;
  cursor: pointer;
  margin: 0 0 0 5px;
}
/* line 245, ../../../../sass/commons/_forms.scss */
.input_wrapper .input input, .input_wrapper .input select, .input_wrapper .input textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  font-size: 14px;
  background: transparent;
  border-radius: 3px;
  border: 1px solid #ededed;
  padding: 7px 14px;
  outline: none;
}
/* line 256, ../../../../sass/commons/_forms.scss */
.input_wrapper .input input:focus, .input_wrapper .input select:focus, .input_wrapper .input textarea:focus {
  border-color: #446ca9;
}
/* line 260, ../../../../sass/commons/_forms.scss */
.input_wrapper .input textarea {
  width: 100%;
  height: 100px;
  resize: vertical;
}
/* line 265, ../../../../sass/commons/_forms.scss */
.input_wrapper .input select {
  padding-right: 30px;
}
/* line 269, ../../../../sass/commons/_forms.scss */
.input_wrapper .separator {
  display: block;
  height: 1px;
  width: 100%;
  background: #ededed;
}

/* line 276, ../../../../sass/commons/_forms.scss */
.row {
  position: relative;
  display: table;
  width: 100%;
}
/* line 280, ../../../../sass/commons/_forms.scss */
.row.row_to_copy {
  display: none;
}
/* line 283, ../../../../sass/commons/_forms.scss */
.row .delete_row {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 10px;
  color: #EA4336;
  cursor: pointer;
}

/* line 290, ../../../../sass/commons/_forms.scss */
.btn_wrapper {
  padding: 10px 5px;
  text-align: center;
}
/* line 293, ../../../../sass/commons/_forms.scss */
.btn_wrapper .btn {
  width: 100%;
}

/* line 297, ../../../../sass/commons/_forms.scss */
.btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: #325da7;
  border-color: #325da7;
  border-style: solid;
  color: white;
  padding: 10px 25px;
  border-radius: 3px;
  text-transform: uppercase;
  font-weight: bold;
  letter-spacing: 1px;
  cursor: pointer;
  outline: none;
  position: relative;
  z-index: 2;
}
/* line 75, ../../../../sass/libs/_mixins.scss */
.btn:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 0;
  background: transparent;
  -webkit-transition: height 0.6s;
  -moz-transition: height 0.6s;
  -ms-transition: height 0.6s;
  -o-transition: height 0.6s;
  transition: height 0.6s;
}
/* line 85, ../../../../sass/libs/_mixins.scss */
.btn:hover:before, .btn.active:before {
  height: 100%;
  background: rgba(0, 0, 0, 0.15);
}
/* line 313, ../../../../sass/commons/_forms.scss */
.btn.btn_inline {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
  margin-bottom: 10px;
}
/* line 319, ../../../../sass/commons/_forms.scss */
.btn.btn_small {
  padding: 5px 10px;
}
/* line 322, ../../../../sass/commons/_forms.scss */
.btn.btn_link {
  background: transparent;
  color: #446ca9;
  border-width: 0;
}
/* line 327, ../../../../sass/commons/_forms.scss */
.btn.btn_disable {
  background: #cdcdcd;
  border-color: #ededed;
  cursor: not-allowed;
}
/* line 332, ../../../../sass/commons/_forms.scss */
.btn.btn_switch {
  background: transparent;
  color: #446ca9;
}
/* line 335, ../../../../sass/commons/_forms.scss */
.btn.btn_switch.active {
  background: #446ca9;
  color: white;
}
/* line 340, ../../../../sass/commons/_forms.scss */
.btn.btn_loading {
  opacity: 0.5;
  cursor: not-allowed;
  position: relative;
  color: transparent;
  font-size: 0;
  overflow: hidden;
}
/* line 347, ../../../../sass/commons/_forms.scss */
.btn.btn_loading:after {
  display: block;
  color: white;
  content: '\f1ce';
  font-size: 18px;
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  -webkit-animation: btn-spin 2s linear infinite;
  animation: btn-spin 2s linear infinite;
}

@-webkit-keyframes btn-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@keyframes btn-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@-webkit-keyframes background-loading {
  0% {
    left: -10%;
  }
  50% {
    left: 100%;
  }
  to {
    left: -10%;
  }
}
@keyframes background-loading {
  0% {
    left: -10%;
  }
  50% {
    left: 100%;
  }
  to {
    left: -10%;
  }
}
/* line 1, ../../../../sass/commons/_tooltips.scss */
.toolkit {
  position: relative;
}
/* line 3, ../../../../sass/commons/_tooltips.scss */
.toolkit .tooltip {
  position: absolute;
  opacity: 0;
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  white-space: nowrap;
  border-radius: 3px;
  color: white;
  background: #999999;
  border: 1px solid #999999;
  z-index: -1;
  top: 100%;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
/* line 20, ../../../../sass/commons/_tooltips.scss */
.toolkit:hover .tooltip {
  display: block;
  opacity: 1;
  margin-top: 4px;
  z-index: 1001;
}
/* line 28, ../../../../sass/commons/_tooltips.scss */
.toolkit.top .tooltip {
  bottom: 100%;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}
/* line 34, ../../../../sass/commons/_tooltips.scss */
.toolkit.top-left .tooltip {
  top: auto;
  bottom: 100%;
  left: auto;
  right: 0;
  transform: translate(0, 0);
}
/* line 43, ../../../../sass/commons/_tooltips.scss */
.toolkit.top-right .tooltip {
  bottom: 100%;
  right: 0;
  left: auto;
}
/* line 50, ../../../../sass/commons/_tooltips.scss */
.toolkit.bottom .tooltip {
  top: 100%;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}
/* line 56, ../../../../sass/commons/_tooltips.scss */
.toolkit.bottom-left .tooltip {
  top: 100%;
  left: 0;
}
/* line 61, ../../../../sass/commons/_tooltips.scss */
.toolkit.bottom-right .tooltip {
  top: 100%;
  right: 0;
  left: auto;
}
/* line 66, ../../../../sass/commons/_tooltips.scss */
.toolkit.tooltip-transparent .tooltip {
  background: transparent;
}
/* line 69, ../../../../sass/commons/_tooltips.scss */
.toolkit.tooltip-info .tooltip {
  background: linear-gradient(to bottom, var(--info-light), var(--info));
  border-color: var(--info);
}
/* line 73, ../../../../sass/commons/_tooltips.scss */
.toolkit.tooltip-warning .tooltip {
  background: linear-gradient(to bottom, var(--warn-light), var(--warn));
  border-color: var(--warn);
  color: var(--primary);
}
/* line 78, ../../../../sass/commons/_tooltips.scss */
.toolkit.tooltip-danger .tooltip {
  background: linear-gradient(to bottom, var(--danger-light), var(--danger));
  border-color: var(--danger);
}
/* line 82, ../../../../sass/commons/_tooltips.scss */
.toolkit.tooltip-success .tooltip {
  background: linear-gradient(to bottom, var(--success-light), var(--success));
  border-color: var(--success);
}

/* line 1, ../../../../sass/commons/_zebra_table.scss */
.zebra_table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
}
/* line 5, ../../../../sass/commons/_zebra_table.scss */
.zebra_table.loading {
  position: relative;
  overflow: hidden;
}
/* line 8, ../../../../sass/commons/_zebra_table.scss */
.zebra_table.loading:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100px;
  height: 200%;
  transform: skew(15deg) translateY(-50%);
  background: linear-gradient(to right, white, #fafafa, white);
  -webkit-animation: background-loading 5s linear infinite;
  animation: background-loading 5s linear infinite;
}
/* line 21, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr {
  -webkit-transition: background 0.6s;
  -moz-transition: background 0.6s;
  -ms-transition: background 0.6s;
  -o-transition: background 0.6s;
  transition: background 0.6s;
}
/* line 23, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr:nth-child(odd) {
  background: #fafafa;
}
/* line 26, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr:nth-child(even) {
  background: white;
}
/* line 29, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr.changing {
  background: #ededed !important;
  -webkit-transition: background 1s;
  -moz-transition: background 1s;
  -ms-transition: background 1s;
  -o-transition: background 1s;
  transition: background 1s;
}
/* line 33, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr.selected {
  background: #ededed;
}
/* line 35, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr.selected:nth-child(odd) {
  background: #e0e0e0;
}
/* line 38, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr.selected .sort {
  background: #4285F4;
  color: white;
}
/* line 43, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr.tr-placeholder {
  background: rgba(68, 108, 169, 0.5);
  height: 70px;
}
/* line 47, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr th {
  font-weight: bold;
  background: white;
  border-bottom: 1px solid #DDD;
  margin: 0;
  padding: 10px 0;
  position: sticky;
  top: 0;
  z-index: 50;
}
/* line 57, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td {
  text-align: center;
  margin: 0;
  padding: 10px 0;
}
/* line 61, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td strong {
  color: #446ca9;
}
/* line 64, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td .pic {
  width: 60px;
  height: 50px;
}
/* line 67, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td .pic img {
  max-width: 100%;
  max-height: 100%;
}
/* line 72, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td .crop {
  text-align: left;
  width: 100px;
  min-width: 100%;
  max-height: 70px;
  overflow: hidden;
  word-break: break-word;
  padding: 0 10px;
}
/* line 82, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td .editable input:not([type=checkbox]) {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: transparent;
  border-color: transparent;
  text-align: center;
}
/* line 89, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td .editable input:not([type=checkbox]):focus {
  border-color: #446ca9;
  background: white;
}
/* line 95, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td.sort {
  padding: 0 5px;
  color: #cdcdcd;
}
/* line 99, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td.loading {
  height: 40px;
  position: relative;
}
/* line 102, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td.loading:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 80%;
  height: 15px;
  background: #ededed;
}
/* line 110, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td.loading_opt {
  height: 40px;
  position: relative;
}
/* line 113, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr td.loading_opt:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 80%;
  height: 15px;
  background: linear-gradient(to right, #ededed, #ededed 20px, transparent 20px, transparent 40px, #ededed 40px, #ededed 60px, transparent 60px, transparent, #ededed 40px, #ededed 60px, transparent 60px, transparent 80px, #ededed 80px, #ededed 100px, transparent 100px, transparent);
  background-size: 120px;
  background-repeat: no-repeat;
  background-position: center center;
}
/* line 133, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr .circle {
  display: inline-block;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #CCC;
}
/* line 139, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr .circle.ok {
  background: #35A853;
}
/* line 142, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr .circle.ko {
  background: #EA4336;
}
/* line 145, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr .circle.kok {
  background: #999999;
}
/* line 148, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr .circle.pending {
  background: #f39200;
}
/* line 153, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr input[type=checkbox] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: #DDD;
  width: 22px;
  height: 12px;
  border-radius: 5px;
  position: relative;
  outline: none;
  cursor: pointer;
}
/* line 164, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr input[type=checkbox]:before {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #EA4336;
  position: absolute;
  top: 1px;
  left: 1px;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 177, ../../../../sass/commons/_zebra_table.scss */
.zebra_table tr input[type=checkbox]:checked:before {
  background: #35A853;
  left: auto;
  right: 1px;
}

/* line 186, ../../../../sass/commons/_zebra_table.scss */
.zebra_table_toggle {
  cursor: pointer;
  padding: 10px 0;
  color: #446ca9;
}

/* line 191, ../../../../sass/commons/_zebra_table.scss */
.zebra_table_restore {
  display: none;
}

@media (max-width: 850px) {
  /* line 196, ../../../../sass/commons/_zebra_table.scss */
  body .md-hide {
    display: none;
  }
}
@media (max-width: 600px) {
  /* line 203, ../../../../sass/commons/_zebra_table.scss */
  body .sm-hide {
    display: none;
  }
  /* line 206, ../../../../sass/commons/_zebra_table.scss */
  body .zebra_table_toggle {
    font-size: 30px;
  }
}
/* line 1, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div {
  display: none;
  z-index: 9999 !important;
  background: white;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
}
/* line 7, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header {
  position: relative;
  background: #446ca9;
  padding: 15px 10px 10px;
  text-align: center;
  color: white;
  border-radius: 5px 5px 0 0;
}
/* line 14, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header .ui-corner-all {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  font-size: 0;
  padding: 10px;
  cursor: pointer;
}
/* line 19, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header .ui-corner-all:before {
  font-size: 30px;
  color: white;
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 300;
  color: white;
}
/* line 26, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header .ui-corner-all.ui-datepicker-prev {
  left: 0;
}
/* line 28, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header .ui-corner-all.ui-datepicker-prev:before {
  content: '\f104';
}
/* line 32, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header .ui-corner-all.ui-datepicker-next {
  right: 0;
}
/* line 34, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header .ui-corner-all.ui-datepicker-next:before {
  content: '\f105';
}
/* line 39, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header .ui-datepicker-title {
  text-transform: uppercase;
}
/* line 41, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-widget-header .ui-datepicker-title .ui-datepicker-year {
  font-weight: 700;
}
/* line 46, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar {
  border-spacing: 5px;
  border-collapse: collapse;
}
/* line 49, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar th, #ui-datepicker-div .ui-datepicker-calendar td {
  padding: 5px;
  border: 0;
  margin: 0;
  text-align: center;
}
/* line 54, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar th a, #ui-datepicker-div .ui-datepicker-calendar td a {
  display: block;
  padding: 5px;
  color: #252525;
  position: relative;
  z-index: 2;
}
/* line 75, ../../../../sass/libs/_mixins.scss */
#ui-datepicker-div .ui-datepicker-calendar th a:before, #ui-datepicker-div .ui-datepicker-calendar td a:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 0;
  background: transparent;
  -webkit-transition: height 0.6s;
  -moz-transition: height 0.6s;
  -ms-transition: height 0.6s;
  -o-transition: height 0.6s;
  transition: height 0.6s;
}
/* line 85, ../../../../sass/libs/_mixins.scss */
#ui-datepicker-div .ui-datepicker-calendar th a:hover:before, #ui-datepicker-div .ui-datepicker-calendar th a.active:before, #ui-datepicker-div .ui-datepicker-calendar td a:hover:before, #ui-datepicker-div .ui-datepicker-calendar td a.active:before {
  height: 100%;
  background: rgba(0, 0, 0, 0.15);
}
/* line 59, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar th a:hover, #ui-datepicker-div .ui-datepicker-calendar td a:hover {
  text-decoration: none;
}
/* line 62, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar th a.ui-state-active, #ui-datepicker-div .ui-datepicker-calendar td a.ui-state-active {
  background: #f39200;
  color: white !important;
}
/* line 68, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar th.ui-datepicker-week-end a, #ui-datepicker-div .ui-datepicker-calendar td.ui-datepicker-week-end a {
  background-color: #fafafa;
}
/* line 73, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar th.ui-state-disabled span, #ui-datepicker-div .ui-datepicker-calendar td.ui-state-disabled span {
  color: #cdcdcd;
}
/* line 78, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar tbody tr {
  border-top: 1px solid #f3f3f3;
}
/* line 80, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar tbody tr:first-of-type {
  border-top-width: 0;
}
/* line 84, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar thead {
  color: #cdcdcd;
}
/* line 86, ../../../../sass/libs/_datepicker.scss */
#ui-datepicker-div .ui-datepicker-calendar thead tr th {
  padding: 5px;
}

/* line 12, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system,BlinkMacSystemFont,segoe ui,Roboto,Helvetica,Arial,sans-serif,apple color emoji,segoe ui emoji,segoe ui symbol;
  font-size: 12px;
}
/* line 17, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
body * {
  box-sizing: border-box;
}

/* line 21, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
a.btn {
  display: inline-block;
  border-width: 2px;
}

/* line 25, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
a.btn:hover {
  text-decoration: none;
}

/* line 28, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.confirmations_text {
  color: #35A853;
}

/* line 31, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.pay_status_color_ok {
  color: #35A853 !important;
}

/* line 34, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.pay_status_color_oko {
  color: #f39200 !important;
}

/* line 37, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.pay_status_color_ko {
  color: #EA4336 !important;
}

/* line 40, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.search_filter_text {
  color: #f39200;
}

/* line 43, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
th.short_td {
  width: 100px;
}

/* line 46, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.corporative_color {
  color: #446ca9;
}

/* line 50, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.form_message_wrapper {
  position: fixed;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  z-index: 1002;
  border-radius: 8px;
  background: white;
  border: 1px solid #fafafa;
  text-align: center;
  padding: 20px;
}
/* line 62, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.form_message_wrapper .close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  color: #446ca9;
}
/* line 69, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.form_message_wrapper p {
  margin: 0;
  text-align: left;
}
/* line 73, ../../../../sass/pages/cobrador/cobrador_common_form.scss */
.form_message_wrapper img {
  display: inline-block;
  vertical-align: middle;
  margin: 4px auto;
}
