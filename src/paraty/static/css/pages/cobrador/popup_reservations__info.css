/* line 15, ../../../../sass/commons/_colors.scss */
:root {
  --info: #4285F4;
  --success: #35A853;
  --danger: #EA4336;
  --warning: #FBBC03;
}

/* line 4, ../../../../sass/pages/cobrador/popup_reservations__info.scss */
.showed_modal_info {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100vh;
  background-size: 100% 100%;
  background-color: #999999;
  border-radius: 10px;
  z-index: 52;
  display: block;
}
/* line 16, ../../../../sass/pages/cobrador/popup_reservations__info.scss */
.showed_modal_info .modal_info_content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 90%;
  border-radius: 10px;
  background-color: white;
  display: flex;
  z-index: 53;
  overflow: scroll;
  border: transparent;
}
/* line 30, ../../../../sass/pages/cobrador/popup_reservations__info.scss */
.showed_modal_info .modal_info_content .content_page {
  position: fixed;
}
