/* line 15, ../../../../sass/commons/_colors.scss */
:root {
  --info: #4285F4;
  --success: #35A853;
  --danger: #EA4336;
  --warning: #FBBC03;
}

/* line 4, ../../../../sass/pages/cobrador/popup_reservations_info.scss */
.popup_info_wrapper {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100vh;
  background-size: 100% 100%;
  background-color: #999999;
  border-radius: 10px;
  z-index: 52;
  display: block;
}
/* line 16, ../../../../sass/pages/cobrador/popup_reservations_info.scss */
.popup_info_wrapper .modal_info_content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 90%;
  border-radius: 10px;
  background-color: white;
  display: flex;
  z-index: 53;
  overflow: scroll;
  border: transparent;
}
/* line 30, ../../../../sass/pages/cobrador/popup_reservations_info.scss */
.popup_info_wrapper .modal_info_content .content_page {
  position: fixed;
}

/* line 37, ../../../../sass/pages/cobrador/popup_reservations_info.scss */
.popup_confirmation_wrapper {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100vh;
  background-size: 100% 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 52;
  display: block;
}
/* line 48, ../../../../sass/pages/cobrador/popup_reservations_info.scss */
.popup_confirmation_wrapper .confirmation_info_content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 30%;
  height: fit-content;
  background-color: #fafafa;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  font-family: "Roboto", sans-serif;
  letter-spacing: 0.4px;
  font-size: 24px;
  padding: 20px;
}

/* line 69, ../../../../sass/pages/cobrador/popup_reservations_info.scss */
#successful_payment {
  box-shadow: 0 0 20px rgba(53, 168, 83, 0.5);
}

/* line 72, ../../../../sass/pages/cobrador/popup_reservations_info.scss */
#wrong_payment {
  box-shadow: 0 0 20px #dc1111;
}

/* line 76, ../../../../sass/pages/cobrador/popup_reservations_info.scss */
.extra_payed {
  color: #325da7;
}
