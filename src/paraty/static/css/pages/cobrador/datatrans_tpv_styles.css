/* line 2, ../../../../sass/pages/cobrador/datatrans_tpv_styles.scss */
.datatrans_payment_selector .payment_gateway_info_box {
  padding: 0;
}
/* line 6, ../../../../sass/pages/cobrador/datatrans_tpv_styles.scss */
.datatrans_payment_selector #card_number, .datatrans_payment_selector #card_cvv {
  background: white;
  margin-bottom: 10px;
  height: 35px;
  border-radius: 5px;
  padding: 0 18px;
}
/* line 14, ../../../../sass/pages/cobrador/datatrans_tpv_styles.scss */
.datatrans_payment_selector .expiry_dates {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
}
/* line 20, ../../../../sass/pages/cobrador/datatrans_tpv_styles.scss */
.datatrans_payment_selector .expiry_dates input {
  width: calc(50% - 5px);
}
/* line 23, ../../../../sass/pages/cobrador/datatrans_tpv_styles.scss */
.datatrans_payment_selector .expiry_dates input.invalid {
  border: 1px solid red;
}
