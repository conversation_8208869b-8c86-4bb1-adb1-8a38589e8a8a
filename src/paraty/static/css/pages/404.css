/* line 15, ../../../sass/commons/_colors.scss */
:root {
  --info: #4285F4;
  --success: #35A853;
  --danger: #EA4336;
  --warning: #FBBC03;
}

/* line 32, ../../../sass/libs/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 41, ../../../sass/libs/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 50, ../../../sass/libs/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 60, ../../../sass/libs/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 4, ../../../sass/pages/404.scss */
body {
  background: #fafafa;
}
/* line 6, ../../../sass/pages/404.scss */
body .bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  z-index: -1;
  overflow: hidden;
  opacity: .5;
}
/* line 13, ../../../sass/pages/404.scss */
body svg {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 100%;
}
/* line 16, ../../../sass/pages/404.scss */
body svg * {
  fill: white;
}
/* line 20, ../../../sass/pages/404.scss */
body .window {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 600px;
  max-width: 90%;
  text-align: center;
  background: white;
  border-radius: 10px;
  padding: 0 20px 40px;
  box-shadow: 0 0 25px rgba(200, 200, 200, 0.3);
}
/* line 29, ../../../sass/pages/404.scss */
body .window .top_bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: #ededed;
  height: 30px;
  border-radius: 10px 10px 0 0;
}
/* line 37, ../../../sass/pages/404.scss */
body .window .top_bar .dots, body .window .top_bar .dots:before, body .window .top_bar .dots:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 10px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #fafafa;
}
/* line 46, ../../../sass/pages/404.scss */
body .window .top_bar .dots:before {
  left: 20px;
}
/* line 49, ../../../sass/pages/404.scss */
body .window .top_bar .dots:after {
  left: 40px;
}
/* line 53, ../../../sass/pages/404.scss */
body .window .numbers {
  font-size: 70px;
  font-weight: bolder;
  padding: 40px 0 20px;
  color: #FBBC03;
}
/* line 59, ../../../sass/pages/404.scss */
body .window h3 {
  text-transform: uppercase;
  color: #666;
  margin-bottom: 15px;
}
/* line 64, ../../../sass/pages/404.scss */
body .window p {
  color: #666;
}

@media (max-width: 750px) {
  /* line 73, ../../../sass/pages/404.scss */
  body .bg-pattern svg {
    width: auto;
    height: 100%;
  }
}
