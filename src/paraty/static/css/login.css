@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,900;1,400&display=swap");
/* line 15, ../../sass/commons/_colors.scss */
:root {
  --info: #4285F4;
  --success: #35A853;
  --danger: #EA4336;
  --warning: #FBBC03;
}

/* line 32, ../../sass/libs/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 41, ../../sass/libs/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 50, ../../sass/libs/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 60, ../../sass/libs/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 1, ../../sass/commons/_base.scss */
body {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: #252525;
}
/* line 6, ../../sass/commons/_base.scss */
body * {
  box-sizing: border-box;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font-family: "Roboto", sans-serif;
  letter-spacing: 0.4px;
}
/* line 13, ../../sass/commons/_base.scss */
body .page {
  width: 1040px;
  max-width: 95%;
  margin: 50px auto;
}
/* line 19, ../../sass/commons/_base.scss */
body .cols .col1, body .cols .col2, body .cols .col3, body .cols .col4, body .cols .col5 {
  display: inline-block;
  vertical-align: top;
}
/* line 23, ../../sass/commons/_base.scss */
body .cols .col1 {
  width: 100%;
}
/* line 26, ../../sass/commons/_base.scss */
body .cols .col2 {
  width: calc(50% - 3px);
}
/* line 29, ../../sass/commons/_base.scss */
body .cols .col3 {
  width: calc((100% / 3) - 3px);
}
/* line 33, ../../sass/commons/_base.scss */
body .list_table {
  border: 1px solid #ededed;
}
/* line 35, ../../sass/commons/_base.scss */
body .list_table .table_title {
  font-size: 16px;
  padding: 10px;
  border-bottom: 1px solid #ededed;
  color: #446ca9;
}
/* line 41, ../../sass/commons/_base.scss */
body .list_table .element {
  font-size: 12px;
  padding: 10px;
  border-top: 1px solid #ededed;
}
/* line 47, ../../sass/commons/_base.scss */
body h1, body h2, body h3, body h4, body h5, body h6, body p {
  margin: 0;
}
/* line 50, ../../sass/commons/_base.scss */
body h1 {
  font-size: 40px;
}
/* line 53, ../../sass/commons/_base.scss */
body h2 {
  font-size: 35px;
  font-weight: normal;
}
/* line 57, ../../sass/commons/_base.scss */
body h3 {
  font-size: 28px;
}
/* line 60, ../../sass/commons/_base.scss */
body h4 {
  font-size: 24px;
  font-weight: normal;
}
/* line 64, ../../sass/commons/_base.scss */
body h5 {
  font-size: 20px;
}
/* line 67, ../../sass/commons/_base.scss */
body h6 {
  font-size: 20px;
  font-weight: normal;
}
/* line 71, ../../sass/commons/_base.scss */
body ul {
  padding: 0;
}
/* line 73, ../../sass/commons/_base.scss */
body ul li {
  list-style: none;
}
/* line 77, ../../sass/commons/_base.scss */
body a {
  text-decoration: none;
  color: #446ca9;
}
/* line 80, ../../sass/commons/_base.scss */
body a:hover {
  text-decoration: underline;
}
/* line 85, ../../sass/commons/_base.scss */
body .title {
  color: #446ca9;
  text-align: center;
  padding: 10px;
}
/* line 90, ../../sass/commons/_base.scss */
body .warning {
  color: #FBBC03;
}

@media (max-width: 850px) {
  /* line 98, ../../sass/commons/_base.scss */
  body .cols .col2, body .cols .col3, body .cols .col4, body .cols .col5 {
    display: block;
    width: 95%;
    margin: 5px auto;
  }
  /* line 104, ../../sass/commons/_base.scss */
  body .md-break {
    display: block;
    margin: 5px 0;
  }
}
/* line 1, ../../sass/commons/_forms.scss */
.message {
  display: block;
  padding: 7px 14px;
  background: #fafafa;
  border-radius: 3px;
  margin: 5px auto;
}
/* line 7, ../../sass/commons/_forms.scss */
.message.m_ok {
  color: #35A853;
}
/* line 10, ../../sass/commons/_forms.scss */
.message.m_ko {
  color: #FBBC03;
}

/* line 14, ../../sass/commons/_forms.scss */
.input_wrapper {
  padding: 10px 5px;
}
/* line 16, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline {
  display: inline-block;
  vertical-align: middle;
}
/* line 19, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline label {
  display: inline-block;
  vertical-align: middle;
  padding: 5px 10px 5px 0;
}
/* line 24, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline .input {
  display: inline-block;
  vertical-align: middle;
}
/* line 27, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline .input + label {
  padding-left: 10px;
}
/* line 31, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline .input:not(.checkbox) {
  width: 200px;
}
/* line 33, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline .input:not(.checkbox).input_small {
  width: 100px;
}
/* line 37, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline.input_center {
  text-align: center;
  width: 100%;
}
/* line 41, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline .btn {
  margin: 0 3px;
}
/* line 45, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline_checkbox {
  position: relative;
}
/* line 47, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline_checkbox label {
  display: block;
  margin-left: 30px;
  padding: 0;
}
/* line 52, ../../sass/commons/_forms.scss */
.input_wrapper.input_inline_checkbox .input {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  left: 0;
}
/* line 60, ../../sass/commons/_forms.scss */
.input_wrapper label {
  display: block;
  padding-bottom: 5px;
  font-size: 14px;
  color: #446ca9;
}
/* line 66, ../../sass/commons/_forms.scss */
.input_wrapper .input {
  position: relative;
}
/* line 69, ../../sass/commons/_forms.scss */
.input_wrapper .input.input_ok input, .input_wrapper .input.input_ok input:focus {
  border-color: #35A853;
}
/* line 72, ../../sass/commons/_forms.scss */
.input_wrapper .input.input_ok:after {
  content: '\f058';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #35A853;
}
/* line 82, ../../sass/commons/_forms.scss */
.input_wrapper .input.input_ko input, .input_wrapper .input.input_ko input:focus {
  border-color: #EA4336;
}
/* line 85, ../../sass/commons/_forms.scss */
.input_wrapper .input.input_ko:after {
  content: '\f06a';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #EA4336;
}
/* line 94, ../../sass/commons/_forms.scss */
.input_wrapper .input.select {
  position: relative;
}
/* line 96, ../../sass/commons/_forms.scss */
.input_wrapper .input.select:after {
  content: '\f107';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #446ca9;
}
/* line 104, ../../sass/commons/_forms.scss */
.input_wrapper .input.select select {
  position: relative;
  z-index: 2;
}
/* line 108, ../../sass/commons/_forms.scss */
.input_wrapper .input.select .select2.select2-container {
  width: 100% !important;
}
/* line 110, ../../sass/commons/_forms.scss */
.input_wrapper .input.select .select2.select2-container .select2-selection {
  border-color: #ededed;
}
/* line 113, ../../sass/commons/_forms.scss */
.input_wrapper .input.select .select2.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice {
  border-color: #ededed;
  background: #fafafa;
  padding: 3px 5px;
  margin: 5px 5px 3px 0;
}
/* line 118, ../../sass/commons/_forms.scss */
.input_wrapper .input.select .select2.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove {
  float: right;
  margin-left: 5px;
}
/* line 128, ../../sass/commons/_forms.scss */
.input_wrapper .input.textarea .extra_buttons {
  top: 7px;
  -webkit-transform: unset;
  -moz-transform: unset;
  -ms-transform: unset;
  -o-transform: unset;
  transform: unset;
}
/* line 137, ../../sass/commons/_forms.scss */
.input_wrapper .input.search {
  position: relative;
}
/* line 139, ../../sass/commons/_forms.scss */
.input_wrapper .input.search:after {
  content: '\f002';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 300;
  font-size: 20px;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #446ca9;
}
/* line 148, ../../sass/commons/_forms.scss */
.input_wrapper .input.search input {
  position: relative;
  z-index: 2;
}
/* line 154, ../../sass/commons/_forms.scss */
.input_wrapper .input.switch input[type=checkbox] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: #ededed;
  border-width: 0;
  width: 22px;
  height: 12px;
  border-radius: 8px;
  position: relative;
  outline: none;
  cursor: pointer;
}
/* line 166, ../../sass/commons/_forms.scss */
.input_wrapper .input.switch input[type=checkbox]:before {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #EA4336;
  position: absolute;
  top: 2px;
  left: 3px;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 179, ../../sass/commons/_forms.scss */
.input_wrapper .input.switch input[type=checkbox]:checked:before {
  content: '';
  background: #35A853;
  left: auto;
  right: 3px;
}
/* line 188, ../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) {
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
}
/* line 192, ../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) input[type=checkbox] {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid #cdcdcd;
  border-radius: 0;
  width: 16px;
  height: 16px;
  padding: 0;
  margin: 0 0 0 10px;
  outline: none;
}
/* line 207, ../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) input[type=checkbox]:checked:before {
  content: '\f00c';
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  color: #4285F4;
  font-size: 10px;
  line-height: 15px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 217, ../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) input[type=checkbox]:hover {
  border-color: #4285F4;
}
/* line 220, ../../sass/commons/_forms.scss */
.input_wrapper .input.checkbox:not(.switch) input[type=checkbox] + span {
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 45px);
  margin-left: 10px;
}
/* line 228, ../../sass/commons/_forms.scss */
.input_wrapper .input.hasDatepicker {
  position: relative;
}
/* line 230, ../../sass/commons/_forms.scss */
.input_wrapper .input.hasDatepicker i {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 5px;
  color: #446ca9;
}
/* line 236, ../../sass/commons/_forms.scss */
.input_wrapper .input .extra_buttons {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 7px;
}
/* line 239, ../../sass/commons/_forms.scss */
.input_wrapper .input .extra_buttons i {
  color: #446ca9;
  cursor: pointer;
  margin: 0 0 0 5px;
}
/* line 245, ../../sass/commons/_forms.scss */
.input_wrapper .input input, .input_wrapper .input select, .input_wrapper .input textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  font-size: 14px;
  background: transparent;
  border-radius: 3px;
  border: 1px solid #ededed;
  padding: 7px 14px;
  outline: none;
}
/* line 256, ../../sass/commons/_forms.scss */
.input_wrapper .input input:focus, .input_wrapper .input select:focus, .input_wrapper .input textarea:focus {
  border-color: #446ca9;
}
/* line 260, ../../sass/commons/_forms.scss */
.input_wrapper .input textarea {
  width: 100%;
  height: 100px;
  resize: vertical;
}
/* line 265, ../../sass/commons/_forms.scss */
.input_wrapper .input select {
  padding-right: 30px;
}
/* line 269, ../../sass/commons/_forms.scss */
.input_wrapper .separator {
  display: block;
  height: 1px;
  width: 100%;
  background: #ededed;
}

/* line 276, ../../sass/commons/_forms.scss */
.row {
  position: relative;
  display: table;
  width: 100%;
}
/* line 280, ../../sass/commons/_forms.scss */
.row.row_to_copy {
  display: none;
}
/* line 283, ../../sass/commons/_forms.scss */
.row .delete_row {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  right: 10px;
  color: #EA4336;
  cursor: pointer;
}

/* line 290, ../../sass/commons/_forms.scss */
.btn_wrapper {
  padding: 10px 5px;
  text-align: center;
}
/* line 293, ../../sass/commons/_forms.scss */
.btn_wrapper .btn {
  width: 100%;
}

/* line 297, ../../sass/commons/_forms.scss */
.btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: #325da7;
  border-color: #325da7;
  border-style: solid;
  color: white;
  padding: 10px 25px;
  border-radius: 3px;
  text-transform: uppercase;
  font-weight: bold;
  letter-spacing: 1px;
  cursor: pointer;
  outline: none;
  position: relative;
  z-index: 2;
}
/* line 75, ../../sass/libs/_mixins.scss */
.btn:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 0;
  background: transparent;
  -webkit-transition: height 0.6s;
  -moz-transition: height 0.6s;
  -ms-transition: height 0.6s;
  -o-transition: height 0.6s;
  transition: height 0.6s;
}
/* line 85, ../../sass/libs/_mixins.scss */
.btn:hover:before, .btn.active:before {
  height: 100%;
  background: rgba(0, 0, 0, 0.15);
}
/* line 313, ../../sass/commons/_forms.scss */
.btn.btn_inline {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
  margin-bottom: 10px;
}
/* line 319, ../../sass/commons/_forms.scss */
.btn.btn_small {
  padding: 5px 10px;
}
/* line 322, ../../sass/commons/_forms.scss */
.btn.btn_link {
  background: transparent;
  color: #446ca9;
  border-width: 0;
}
/* line 327, ../../sass/commons/_forms.scss */
.btn.btn_disable {
  background: #cdcdcd;
  border-color: #ededed;
  cursor: not-allowed;
}
/* line 332, ../../sass/commons/_forms.scss */
.btn.btn_switch {
  background: transparent;
  color: #446ca9;
}
/* line 335, ../../sass/commons/_forms.scss */
.btn.btn_switch.active {
  background: #446ca9;
  color: white;
}
/* line 340, ../../sass/commons/_forms.scss */
.btn.btn_loading {
  opacity: 0.5;
  cursor: not-allowed;
  position: relative;
  color: transparent;
  font-size: 0;
  overflow: hidden;
}
/* line 347, ../../sass/commons/_forms.scss */
.btn.btn_loading:after {
  display: block;
  color: white;
  content: '\f1ce';
  font-size: 18px;
  font-family: "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  -webkit-animation: btn-spin 2s linear infinite;
  animation: btn-spin 2s linear infinite;
}

@-webkit-keyframes btn-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@keyframes btn-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@-webkit-keyframes background-loading {
  0% {
    left: -10%;
  }
  50% {
    left: 100%;
  }
  to {
    left: -10%;
  }
}
@keyframes background-loading {
  0% {
    left: -10%;
  }
  50% {
    left: 100%;
  }
  to {
    left: -10%;
  }
}
/* line 10, ../../sass/login.scss */
body {
  background: white;
}
/* line 12, ../../sass/login.scss */
body .modal_login {
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 700px;
  max-width: 90%;
  max-height: 90%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
}
/* line 23, ../../sass/login.scss */
body .modal_login .title {
  color: #446ca9;
  padding-bottom: 10px;
}
/* line 27, ../../sass/login.scss */
body .modal_login .subtitle {
  padding-bottom: 10px;
}
/* line 30, ../../sass/login.scss */
body .modal_login #loginForm {
  width: calc(100% - 265px);
  display: inline-block;
  vertical-align: top;
  margin-top: 20px;
}
/* line 36, ../../sass/login.scss */
body .modal_login .form_details {
  margin-top: 20px;
  padding: 20px 0 0 20px;
  width: 260px;
  display: inline-block;
  vertical-align: top;
}
/* line 42, ../../sass/login.scss */
body .modal_login .form_details li {
  font-size: 14px;
  padding-bottom: 5px;
}
/* line 45, ../../sass/login.scss */
body .modal_login .form_details li.req_ok {
  color: #35A853;
  text-decoration: line-through;
}
/* line 51, ../../sass/login.scss */
body .modal_login.login_ok {
  text-align: center;
  padding: 40px 150px;
}
/* line 54, ../../sass/login.scss */
body .modal_login.login_ok i {
  color: #35A853;
  margin-bottom: 20px;
  font-size: 70px;
}

@media (max-width: 600px) {
  /* line 66, ../../sass/login.scss */
  body .modal_login {
    padding: 40px 20px;
  }
  /* line 68, ../../sass/login.scss */
  body .modal_login .title {
    position: sticky;
    top: -40px;
    background: white;
    z-index: 5;
  }
  /* line 74, ../../sass/login.scss */
  body .modal_login #loginForm, body .modal_login .form_details {
    display: block;
    width: 100%;
    padding: 20px 0;
  }
}
