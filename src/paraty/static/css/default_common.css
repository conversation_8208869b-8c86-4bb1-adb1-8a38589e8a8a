@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,900;1,400&display=swap");
/* line 15, ../../sass/commons/_colors.scss */
:root {
  --info: #4285F4;
  --success: #35A853;
  --danger: #EA4336;
  --warning: #FBBC03;
}

/* line 32, ../../sass/libs/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 41, ../../sass/libs/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 50, ../../sass/libs/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 60, ../../sass/libs/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 1, ../../sass/commons/_tooltips.scss */
.toolkit {
  position: relative;
}
/* line 3, ../../sass/commons/_tooltips.scss */
.toolkit .tooltip {
  position: absolute;
  opacity: 0;
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  white-space: nowrap;
  border-radius: 3px;
  color: white;
  background: #999999;
  border: 1px solid #999999;
  z-index: -1;
  top: 100%;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
/* line 20, ../../sass/commons/_tooltips.scss */
.toolkit:hover .tooltip {
  display: block;
  opacity: 1;
  margin-top: 4px;
  z-index: 1001;
}
/* line 28, ../../sass/commons/_tooltips.scss */
.toolkit.top .tooltip {
  bottom: 100%;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}
/* line 34, ../../sass/commons/_tooltips.scss */
.toolkit.top-left .tooltip {
  top: auto;
  bottom: 100%;
  left: auto;
  right: 0;
  transform: translate(0, 0);
}
/* line 43, ../../sass/commons/_tooltips.scss */
.toolkit.top-right .tooltip {
  bottom: 100%;
  right: 0;
  left: auto;
}
/* line 50, ../../sass/commons/_tooltips.scss */
.toolkit.bottom .tooltip {
  top: 100%;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}
/* line 56, ../../sass/commons/_tooltips.scss */
.toolkit.bottom-left .tooltip {
  top: 100%;
  left: 0;
}
/* line 61, ../../sass/commons/_tooltips.scss */
.toolkit.bottom-right .tooltip {
  top: 100%;
  right: 0;
  left: auto;
}
/* line 66, ../../sass/commons/_tooltips.scss */
.toolkit.tooltip-transparent .tooltip {
  background: transparent;
}
/* line 69, ../../sass/commons/_tooltips.scss */
.toolkit.tooltip-info .tooltip {
  background: linear-gradient(to bottom, var(--info-light), var(--info));
  border-color: var(--info);
}
/* line 73, ../../sass/commons/_tooltips.scss */
.toolkit.tooltip-warning .tooltip {
  background: linear-gradient(to bottom, var(--warn-light), var(--warn));
  border-color: var(--warn);
  color: var(--primary);
}
/* line 78, ../../sass/commons/_tooltips.scss */
.toolkit.tooltip-danger .tooltip {
  background: linear-gradient(to bottom, var(--danger-light), var(--danger));
  border-color: var(--danger);
}
/* line 82, ../../sass/commons/_tooltips.scss */
.toolkit.tooltip-success .tooltip {
  background: linear-gradient(to bottom, var(--success-light), var(--success));
  border-color: var(--success);
}

/* line 1, ../../sass/commons/_base.scss */
body {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: #252525;
}
/* line 6, ../../sass/commons/_base.scss */
body * {
  box-sizing: border-box;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font-family: "Roboto", sans-serif;
  letter-spacing: 0.4px;
}
/* line 13, ../../sass/commons/_base.scss */
body .page {
  width: 1040px;
  max-width: 95%;
  margin: 50px auto;
}
/* line 19, ../../sass/commons/_base.scss */
body .cols .col1, body .cols .col2, body .cols .col3, body .cols .col4, body .cols .col5 {
  display: inline-block;
  vertical-align: top;
}
/* line 23, ../../sass/commons/_base.scss */
body .cols .col1 {
  width: 100%;
}
/* line 26, ../../sass/commons/_base.scss */
body .cols .col2 {
  width: calc(50% - 3px);
}
/* line 29, ../../sass/commons/_base.scss */
body .cols .col3 {
  width: calc((100% / 3) - 3px);
}
/* line 33, ../../sass/commons/_base.scss */
body .list_table {
  border: 1px solid #ededed;
}
/* line 35, ../../sass/commons/_base.scss */
body .list_table .table_title {
  font-size: 16px;
  padding: 10px;
  border-bottom: 1px solid #ededed;
  color: #446ca9;
}
/* line 41, ../../sass/commons/_base.scss */
body .list_table .element {
  font-size: 12px;
  padding: 10px;
  border-top: 1px solid #ededed;
}
/* line 47, ../../sass/commons/_base.scss */
body h1, body h2, body h3, body h4, body h5, body h6, body p {
  margin: 0;
}
/* line 50, ../../sass/commons/_base.scss */
body h1 {
  font-size: 40px;
}
/* line 53, ../../sass/commons/_base.scss */
body h2 {
  font-size: 35px;
  font-weight: normal;
}
/* line 57, ../../sass/commons/_base.scss */
body h3 {
  font-size: 28px;
}
/* line 60, ../../sass/commons/_base.scss */
body h4 {
  font-size: 24px;
  font-weight: normal;
}
/* line 64, ../../sass/commons/_base.scss */
body h5 {
  font-size: 20px;
}
/* line 67, ../../sass/commons/_base.scss */
body h6 {
  font-size: 20px;
  font-weight: normal;
}
/* line 71, ../../sass/commons/_base.scss */
body ul {
  padding: 0;
}
/* line 73, ../../sass/commons/_base.scss */
body ul li {
  list-style: none;
}
/* line 77, ../../sass/commons/_base.scss */
body a {
  text-decoration: none;
  color: #446ca9;
}
/* line 80, ../../sass/commons/_base.scss */
body a:hover {
  text-decoration: underline;
}
/* line 85, ../../sass/commons/_base.scss */
body .title {
  color: #446ca9;
  text-align: center;
  padding: 10px;
}
/* line 90, ../../sass/commons/_base.scss */
body .warning {
  color: #FBBC03;
}

@media (max-width: 850px) {
  /* line 98, ../../sass/commons/_base.scss */
  body .cols .col2, body .cols .col3, body .cols .col4, body .cols .col5 {
    display: block;
    width: 95%;
    margin: 5px auto;
  }
  /* line 104, ../../sass/commons/_base.scss */
  body .md-break {
    display: block;
    margin: 5px 0;
  }
}
