.simpleXML ul
{
    list-style: none;
    margin: 0px;
    padding-left: 32px;
}

.simpleXML
{
    font-family: 'Courier New';
}

.simpleXML-comment
{
    color: green;
}

.simpleXML-tagHeader
{
    color: blue;
}

.simpleXML-cdata
{
    color: gray;
}

.simpleXML-tagValue
{
    color: darkred;
}

.simpleXML-collapsedText
{
    color: lightgray;
}

.simpleXML-attrName {
	color: red;
}

.simpleXML-attrValue {
	color: blue;
}

.simpleXML span.simpleXML-expander, .simpleXML span.simpleXML-expanderClose
{
    height: 12px;
    width: 12px;
    display: inline-block;
}

.simpleXML span.simpleXML-expanderHeader
{
    cursor: pointer;
}

.simpleXML span.simpleXML-expander-expanded
{
    background-image: url( "../images/simpleXML-images.png" );
}

.simpleXML span.simpleXML-expander-collapsed
{
    background-image: url( "../images/simpleXML-images.png" );
    background-position-x: -12px;
}