import json
import logging
from functools import wraps
from urllib.parse import urlparse, parse_qs

import flask
from flask import g, request, Response

from werkzeug.utils import redirect

from paraty import app
from paraty.communications import datastore_communicator

from paraty.utilities import session_utils, manager_utils, hotel_utils, encryption_utils, date_utils
from paraty.utilities.languages import language_utils
from paraty.utilities.languages.language_utils import SPANISH, ENGLISH, ITALIAN
from paraty.utilities.session_utils import UserSession
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.security_utils import get_secret, SECURITY_PROJECT


def _dev_load_session():
    session_utils.set_current_session(UserSession({}, session_id='TEST_SESSION'))
    session_utils.get_current_session().set_value("hotel_code", 'palmeramar')
    session_utils.get_current_session().set_value("language", SPANISH)
    session_utils.get_current_session().set_value('user_name', 'nhaunstetter')


def login_from_sessionKey(func):

    @wraps(func)
    def decorated_view(*args, **kwargs):
        if getattr(g, 'auth_success', False):
            return func(*args, **kwargs)

        request_params = request.values

        session_key = request_params.get('sessionKey')
        language = request_params.get("language")

        not_authorized = _login_using_url_params(session_key, language)
        if not_authorized:
            logging.info("Not authorized login for sessionKey %s" % session_key)

            return not_authorized

        return func(*args, **kwargs)

    return decorated_view


def _login_using_url_params(session_key, language):

    if app.config['DEV']:
        _dev_load_session()
        return

    if not session_key:
        return unauthorized()

    forced_hotel_code = ""
    if "_@@_" in session_key:
        forced_hotel_code = session_key.split("_@@_")[1]
        session_key = session_key.split("_@@_")[0]

    my_session = session_utils.get_session_from_datastore(session_key)

    if not my_session:
        return expired_session()

    hotel_application = datastore_communicator.get_entity("HotelApplication", my_session.get_value('currentAppId'), hotel_code="admin-hotel")
    hotel_code = hotel_application.get('applicationId')

    if forced_hotel_code:
        if not user_with_permissions(my_session.content, forced_hotel_code):
            return unauthorized()
        session_utils.get_current_session().set_value("hotel_code", forced_hotel_code)
        session_utils.get_current_session().set_value("session_id", session_key+"_@@_"+forced_hotel_code)
    else:
        session_utils.get_current_session().set_value("hotel_code", hotel_code)
        session_utils.get_current_session().set_value("session_id", session_key)

    session_utils.get_current_session().set_value("user_name", my_session.content['userName'])
    if not my_session.get_value("language"):
        session_utils.get_current_session().set_value("language", language_utils.get_language_in_manager_based_on_locale(language))


def login_using_referrer(func):
    @wraps(func)
    def decorated_view(*args, **kwargs):
        if getattr(g, 'auth_success', False):
            return func(*args, **kwargs)

        referer = request.headers.get("Referer")

        #Sometimes it is easier to set it
        if not referer:
            referer = request.values.get('referer')

        logging.info(referer)
        logging.info(request.url)

        query_params = parse_qs(urlparse(referer).query)
        session_key = query_params.get('sessionKey', [''])[0]
        language = query_params.get('language', [''])[0]

        if (not session_key and not language) and hasattr(session_utils.get_current_session(),
                                                          "is_authenticated") and session_utils.get_current_session().is_authenticated:
            return func(*args, **kwargs)

        not_authorized = _login_using_url_params(session_key, language)
        if not_authorized:
            logging.info("Not authorized login for sessionKey %s" % session_key)

            return not_authorized

        return func(*args, **kwargs)

    return decorated_view

def login_using_secret_key(func):
    @wraps(func)
    def decorated_view(*args, **kwargs):
        secret_key = request.headers.get("Authentication")

        if secret_key:
            try:
                expected_secret = get_secret(SECURITY_PROJECT, 'hotel_webs_to_payment_seeker')
                if secret_key == expected_secret:
                    g.auth_success = True
                    logging.debug("Authentication successful via secret key")
            except Exception as e:
                logging.error(f"Error accessing secret: {str(e)}")

        return func(*args, **kwargs)

    return decorated_view

# @logged_call This logs the request and the status code returned
@app.route("/has_access_to_cc", methods=['POST'])
def user_has_access_to_cc():
    try:
        logging.info("Entering user_has_access_to_cc")
        body = request.get_json()

        username = body['user']
        password_cc = body['password_cc']
        hotel_code = body['hotel_code']

        current_user = list(datastore_communicator.get_using_entity_and_params('ParatyUser', [('name', '=', username)], hotel_code='admin-hotel'))
        if not current_user or not current_user[0]['enabled']:
            logging.info("Access denied, user not found: %s", username)
            return flask.Response('Access not allowed', status=401)

        if not hotel_code in manager_utils.get_hotel_codes_from_ids(current_user[0]['accesibleApplications']) and not 'admin' in current_user[0]['permission']:
            logging.info("Access denied, hotel code not accesible for the given user: %s, %s", username, hotel_code)
            return flask.Response('Access not allowed', status=401)

        hotel_cc_password = hotel_utils.get_config_property_value(hotel_code, 'Password Tarjetas')

        if hotel_cc_password != password_cc:
            logging.info("Access denied, wrong password_cc provided")
            return flask.Response('Access not allowed', status=401)

        return 'OK'
    except Exception as e:
        logging.error("Fatal error in main loop", exc_info=True)

@app.route("/user/change_password", methods=['GET'])
@login_from_sessionKey
def show_password_change_window():
    return build_template('login/index.html', {})

@app.route("/user/change_password", methods=['POST'])
@login_using_referrer
def change_user_password():

    username = session_utils.get_current_session().get_value("user_name")
    old_password = request.values['old_password']
    new_password_1 = request.values['new_password_1']
    new_password_2 = request.values['new_password_2']

    if new_password_1 != new_password_2:
        return flask.Response("New passwords don't match", status=400)

    if len(new_password_1) < 8:
        return flask.Response("Min length 8", status=400)

    num_digits = len([x for x in new_password_1 if x.isdigit()])
    if num_digits == 0 or num_digits == 8:
        return flask.Response("Password must contain numbers and letters", status=400)

    affected_user = list(datastore_communicator.get_using_entity_and_params('ParatyUser', [('name', '=', username)], hotel_code='admin-hotel'))

    if not affected_user:
        return flask.Response('User/Password incorrect', status=404)

    expected_password = affected_user[0]['password']

    # if encryption_utils.hash_to_md5(old_password) != expected_password:
    #     return flask.Response('User/Password incorrect', status=404)

    affected_user[0]['password'] = encryption_utils.hash_to_md5(new_password_1)
    affected_user[0]['lastPasswordChange'] = date_utils.get_timestamp()

    datastore_communicator.save_entity(affected_user[0])

    return 'OK'


def unauthorized():
    """Redirect unauthorized users to Login page."""

    return Response('Unauthorized access', 404)


def expired_session():
    """Redirect expired session users to Login page."""

    return Response('Expired session, please reload site', 404)


def check_permission_to_manager(permission):
    user_name = session_utils.get_current_session().get_value('user_name')
    user = list(datastore_communicator.get_using_entity_and_params("ParatyUser", hotel_code='admin-hotel', search_params=[("name", "=", user_name)]))
    is_valid = False
    if user and user[0].get("permission") and permission in user[0]['permission']:
        is_valid = True

    return is_valid

def user_with_permissions(session_content, hotel_code):

    if session_content.get("adminUser"):
        return True

    user = list(datastore_communicator.get_using_entity_and_params("ParatyUser", search_params=[("name", "=", session_content["userName"])], hotel_code="admin-hotel"))
    if not user:
        return False
    user = user[0]

    hotel = hotel_utils.get_hotel_by_application_id(hotel_code)

    if hotel.get("id") in user.get("accesibleApplications", []):
        return True

    return False

