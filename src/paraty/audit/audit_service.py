import datetime
import json
import logging
import time

from google.appengine.ext import ndb

import interface_to_implement
from model.audit import EndpointCallAuditEvent, AuditResult
from paraty.utils import date_utils
from paraty.utils.date_utils import datetime_to_string
from paraty_common.dev.dev_utils import DEV
from libs import requests


def find_operations(audit_id, hotel_code, search_filter):

	#Note that we need the integration to be set before using it
	if not interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE:
		import main

	#First get all the required XML
	if DEV:
		related_xml_calls = _get_operations_remotely(hotel_code, search_filter)

		#We need to convert this to Audit entities

		result = []

		for current_xml in related_xml_calls:
			new_entry = EndpointCallAuditEvent()
			new_entry.request = current_xml['request']
			new_entry.request_id = current_xml.get('request_id')
			new_entry.hotel_code = current_xml.get('hotel_code')
			new_entry.timestamp = date_utils.string_to_date(current_xml.get('timestamp'), format="%d/%m/%Y %H:%M:%S")
			new_entry.key = ndb.Key('EndpointCallAuditEvent', new_entry.request_id)
			result.append(new_entry)

		related_xml_calls = result

	else:
		related_xml_calls = _get_operations_from_datastore(hotel_code, search_filter)

	logging.info("Found operations: %s", len(related_xml_calls))

	#Get the availability Items and store results
	_get_availability_items_and_save_audits(hotel_code, related_xml_calls, search_filter, audit_id)


def _get_operations_remotely(hotel_code, search_filter):

	max_days = int(search_filter.get('max_days'))
	production_url = search_filter.get('production_url')

	today = datetime.date.today()
	start_date = today - datetime.timedelta(days=max_days)
	audit_calls_requests = _get_audit_calls(hotel_code, start_date, production_url)

	# From most recent to oldest
	return reversed(audit_calls_requests)


def _get_audit_calls(hotel_code, start_date, production_url):
	"""This function is aimed to get audit calls"""

	offset = 0
	start_date_string = date_utils.date_to_string(start_date, "%Y-%m-%d")
	num_elements = 500

	result = []
	while True:
		query = "dev?action=rest&hotel=" + hotel_code + "&offset=" + str(offset) + "&startDate=" + start_date_string + "&numElements=" + str(num_elements)

		if production_url[-1] == '/':
			url = '%s%s' % (production_url, query)
		else:
			url = '%s/%s' % (production_url, query)

		response = requests.get(url)
		obtained_entities = json.loads(response.content)
		result.extend(obtained_entities)
		offset = offset + len(obtained_entities)

		if len(obtained_entities) < num_elements:
			break

	# Note that the method returns the data from most recent to oldest
	return reversed(result)


def _filter_valid_availability_items(availability_items, search_filter):
	result = []
	for current_availability_item in availability_items:

		# Check the board
		if search_filter.get('boards') and not 'all' in search_filter.get('boards'):
			if not str(current_availability_item['boardId']) in search_filter.get('boards'):
				continue

		# Check the room
		if search_filter.get('rooms') and not 'all' in search_filter.get('rooms'):
			if not str(current_availability_item['roomId']) in search_filter.get('rooms'):
				continue

		# Check the rate
		if search_filter.get('rates') and not 'all' in search_filter.get('rates'):
			if not str(current_availability_item['rateId']) in search_filter.get('rates'):
				continue

		# check a specific day
		if search_filter.get('price_day') and not search_filter.get('price_day_to'):
			if not current_availability_item['day'] == search_filter.get('price_day'):
				continue

		# check a range dates
		if search_filter.get('price_day') and search_filter.get('price_day_to'):
			if (current_availability_item['day'] < search_filter.get('price_day')) or (current_availability_item['day'] > search_filter.get('price_day_to')):
				continue

		if search_filter.get('capacities'):
			if not current_availability_item['capacity'] == search_filter.get('capacities'):
				continue

		something_interesting = False
		if search_filter.get('price_property'):
			if current_availability_item['price']:
				something_interesting = True

		if search_filter.get('status_property'):
			if current_availability_item['status']:
				something_interesting = True

		if search_filter.get('available_property'):
			if current_availability_item['quantity']:
				something_interesting = True

		if search_filter.get('min_stay_property'):
			if current_availability_item['minimumStay']:
				something_interesting = True

		if search_filter.get('max_stay_property'):
			if current_availability_item['maximumStay']:
				something_interesting = True

		if search_filter.get('release_property'):
			if current_availability_item['release']:
				something_interesting = True

		if not something_interesting:
			continue

		result.append(current_availability_item)
	return result


def _is_found_especific_data(filtered_items, search_filter):
	# You are looking for something specific if you have defined:
	# - Day, Board, Rate, Room -> Status, Min_stay, MaxStay, Release, Availability
	# - Day, Board, Rate, Room, Capacity -> Price
	return False


def _get_start_and_end_dates(availability_items):
	start_date = None
	end_date = None

	for item in availability_items:
		# Initialize this with the first item
		if not start_date:
			start_date = item['day']
			end_date = item['day']
			continue

		if item['day'] < start_date:
			start_date = item['day']

		elif end_date < item['day']:
			end_date = item['day']

	return (
		date_utils.string_to_date(start_date, format=interface_to_implement.AVAILABILITY_ITEM_DATE_FORMAT),
		date_utils.string_to_date(end_date, format=interface_to_implement.AVAILABILITY_ITEM_DATE_FORMAT)
	)


def _get_availability_items_and_save_audits(hotel_code, operations, search_filter, audit_id):
	result = []
	page = 0
	items_per_page = 500
	start = time.time()
	found_last = False
	last_result = None
	
	for i, op in enumerate(operations):
		logging.info("Request Id: %s", op.request_id)
		availability_items = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_availability_items(op, "")
		filtered_items = _filter_valid_availability_items(availability_items, search_filter)

		# Set the timestamp
		for item in filtered_items:
			item['timestamp'] = op.timestamp
			item['request_id'] = op.request_id

			# For legacy audits we use the audit Id instead
			if not item['request_id']:
				item['request_id'] = op.key.id()
		result.extend(filtered_items)

		# If found what we are looking for stop the search or update the results
		found_specific_search = _is_found_especific_data(filtered_items, search_filter)
		is_last_operation = (i == len(operations) - 1)
		lapse = time.time() - start

		if result and (found_specific_search or (len(result) > items_per_page*(page+1)) or is_last_operation or (result and lapse > 60)):
			min_date, max_date = _get_start_and_end_dates(result)
			my_result = AuditResult(key=ndb.Key("AuditResult", audit_id + str(page)))
			my_result.request = json.dumps(search_filter)
			my_result.response = _format_results(result)
			my_result.request_id = audit_id
			my_result.page = page
			my_result.min_date = min_date
			my_result.max_date = max_date
			my_result.hotel_code = hotel_code

			if i == len(operations) -1:
				my_result.is_last = True
				found_last = True

			last_result = my_result
			my_result.put()
			page += 1
			start = time.time()
			result = []

	# We need to make sure that the search always finish
	if not found_last:
		if last_result:
			last_result.is_last = True
			last_result.put()

		# Nothing found for the current search
		else:
			my_result = AuditResult(key=ndb.Key("AuditResult", audit_id + str(page)))
			my_result.request = json.dumps(search_filter)
			my_result.response = []
			my_result.request_id = audit_id
			my_result.page = 0
			my_result.min_date = None
			my_result.max_date = None
			my_result.hotel_code = hotel_code
			my_result.is_last = True


def _format_results(result):
	# Last first
	for item in result:
		if item.get('timestamp'):
			item['timestamp'] = datetime_to_string(item['timestamp'], format="%Y-%m-%d %H:%M:%S")
	return result


def _get_operations_from_datastore(hotel_code, search_filter):
	max_days = search_filter.get('max_days')
	date_price = search_filter.get('price_day')
	date_price_to = search_filter.get('price_day_to')
	legacy = False

	min_valid_time = ''
	if max_days:
		min_valid_time = datetime.datetime.now() - datetime.timedelta(days=int(max_days))

	# Note that only one inequality is allowed by GAE
	if date_price:

		query = EndpointCallAuditEvent.query(EndpointCallAuditEvent.path == interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_modify_url(),
											 EndpointCallAuditEvent.hotel_code == hotel_code,
											 EndpointCallAuditEvent.end_date >= date_price)
	else:

		# i.e. for Dingus, Parity, Siteminder
		if legacy:
			query = EndpointCallAuditEvent.query(EndpointCallAuditEvent.operation == 'HotelAvailRateUpdate',
												 EndpointCallAuditEvent.hotel_code == hotel_code,
												 EndpointCallAuditEvent.request_timestamp >= min_valid_time)
		else:
			query = EndpointCallAuditEvent.query(EndpointCallAuditEvent.path == interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_modify_url(),
												 EndpointCallAuditEvent.hotel_code == hotel_code,
												 EndpointCallAuditEvent.timestamp >= min_valid_time)
	all_operations_found = []
	more = True
	next_curs = None
	# Note that we do the filter for every 100 to prevent memory issues
	while more:

		results, next_curs, more = query.fetch_page(100, start_cursor=next_curs)
		if results:
			# Filtering operations by startdate and end_date
			operations_filtered = []
			for op in results:

				if legacy:
					op.timestamp = op.request_timestamp

				if date_price and op.end_date < date_price:
					continue

				if date_price and (not date_price_to) and op.start_date > date_price:
					continue

				if date_price_to and op.start_date > date_price_to:
					continue

				operations_filtered.append(op)

			# Remove operations for pass dates
			no_pass_operations = []

			dprice_date = ''
			if date_price and (not date_price_to):
				dprice_date = date_utils.string_to_date(date_price + " 23:59:59", format="%Y-%m-%d %H:%M:%S")
				logging.info("Filtering by specific date: %s", dprice_date)

			for op in operations_filtered:
				if dprice_date  and (op.timestamp > dprice_date):
					#discard!
					logging.log("discard operation because operation TS: %s", op.timestamp)
					continue

				no_pass_operations.append(op)

			# If filter defined we are only interested in operations that happened in the last MAX_DAYS days
			if max_days:
				no_pass_operations = [x for x in no_pass_operations if x.timestamp > min_valid_time]

			all_operations_found.extend(no_pass_operations)
	operations_filtered = sorted(all_operations_found, key=lambda x: x.timestamp, reverse=True)
	return operations_filtered
