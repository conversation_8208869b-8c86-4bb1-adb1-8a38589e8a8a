"""
This file will contain the logic to validate the XML data received form the adapters
"""
import datetime
import json
import zlib
from typing import Optional

from mock.mock import Mock

from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE
from model.price_model import RoomTypeStatus, FinalPriceDay
from paraty import Config
from paraty.integration import modify_prices_batch
from paraty.integration.integration_data_cache import IntegrationDataCache
from paraty.utils.checker_utils import _get_start_and_end_dates
from paraty.utils.data_management.endpoint_call_audit_event_utils import get_hotel_audit_event
from paraty.utils.data_management.rooms_utils import get_room_info
from paraty.utils.email_utils import send_amazon_email
from paraty.utils.rendering import render_base_integration_template
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels


def validate_xml_consolidation_from_n_time(days: int=0, hours: int=0, hotel_code: Optional[str]=None):
    """
    Validate the XML
    """
    actual_utc_time = datetime.datetime.now()
    from_date = actual_utc_time - datetime.timedelta(days=days, hours=hours)

    entities_received = get_hotel_audit_event(from_date, hotel_code=hotel_code)

    all_hotels = get_all_valid_hotels()
    all_hotels_codes = [hotel['applicationId'] for hotel in all_hotels]

    # Groups entities by hotel_code
    entities_by_hotel_code = {}
    for entity in entities_received:
        if entity['hotel_code'] not in all_hotels_codes:
            continue

        entities_by_hotel_code.setdefault(entity['hotel_code'], []).append(entity)

    is_valid_results = True
    for hotel_code, entities_received in entities_by_hotel_code.items():
        if not _is_valid_hotel_data_from_audit_event(hotel_code, entities_received):
            is_valid_results = False

    return is_valid_results

def _is_valid_hotel_data_from_audit_event(hotel_code, entities_received):
    availability_items = []

    for entity_element in entities_received[:1]:
        # Used mock to avoid usage of ndb in tests
        operation = Mock(**dict(entity_element))
        operation.request = zlib.decompress(entity_element['request']).decode('utf-8')
        results = SPECIFIC_INTEGRATION_INTERFACE.get_availability_items(operation, "audit_assistant")
        availability_items.extend(results)

    if not availability_items:
        return

    start_date, end_date = _get_start_and_end_dates(availability_items)
    cache = IntegrationDataCache(hotel_code, "audit_assistant_xml_consolidation")
    modify_prices_batch.execute_modify_prices_batch(hotel_code, availability_items, start_date, end_date, "TEST REQUEST ID ACABEZA", cache)

    errors = {}
    for room_id, room_data_dict in cache._cache.items():
        # Check RoomTypeStatus
        for date, rts in room_data_dict['RoomTypeStatus'].items():
            if rts.dirty:
                rts_errors = _get_differences_rts_against_production(hotel_code, rts)
                if rts_errors:
                    errors['RoomTypeStatus'] = rts_errors


        # Check FinalPriceDay
        for date, fpd in room_data_dict['FinalPriceDay'].items():
            if fpd.dirty:
                fpd_errors = _get_differences_fpd_against_production(hotel_code, fpd)
                if fpd_errors:
                    errors['FinalPriceDay'] = fpd_errors

    if errors:
        _notify_exceptions(errors, hotel_code)
        return False

    return True


def _calculate_error_naming(entity_element, hotel_code):
    room_info = get_room_info(entity_element.roomKey, hotel_code)
    room_name = room_info.get('name') if room_info else entity_element.roomKey
    room_name = f'{room_name} ({entity_element.date})'
    return room_name


def _get_differences_rts_against_production(hotel_code: str, root_type_status: RoomTypeStatus):
    """
    Get the differences between the RoomTypeStatus and the production
    """
    filter_params = [
        ('date', '=', root_type_status.date),
        ('roomKey', '=', root_type_status.roomKey)
    ]
    production_data = get_using_entity_and_params(
        'RoomTypeStatus',
        filter_params,
        hotel_code=hotel_code
    )

    room_type_status_errors = {}

    if not production_data:
        room_name = _calculate_error_naming(root_type_status, hotel_code)
        room_type_status_errors.setdefault(room_name, []).append("Production data not found")
        return room_type_status_errors

    production_data = production_data[0]
    differences = []
    attrs_to_avoid = ['dirty', 'key']
    for attr in vars(root_type_status):
        if attr in attrs_to_avoid:
            continue

        production_value = production_data.get(attr)
        current_value = getattr(root_type_status, attr)

        if not production_value and not current_value:
            continue

        if attr == 'availability':
            # Added to availability a margin of 2 rooms difference to avoid false positives (Requested by fmatheis)
            if abs(production_value - current_value) > 2:
                differences.append(f"{attr}: (production) {production_value} vs  (adapter) {current_value}")

        else:
            if production_value != current_value:
                differences.append(f"{attr}: (production) {production_value} vs  (adapter) {current_value}")

    if differences:
        room_name = _calculate_error_naming(root_type_status, hotel_code)
        room_type_status_errors.setdefault(room_name, []).extend(differences)

    return room_type_status_errors



def _get_differences_fpd_against_production(hotel_code: str, final_price_day: FinalPriceDay):
    """
    Get the differences between the FinalPriceDay and the production
    """
    filter_params = [
        ('date', '=', final_price_day.date),
        ('roomKey', '=', final_price_day.roomKey)
    ]

    production_data = get_using_entity_and_params(
        'FinalPriceDay',
        filter_params,
        hotel_code=hotel_code
    )

    final_price_day_errors = {}

    if not production_data:
        room_name = _calculate_error_naming(final_price_day, hotel_code)
        final_price_day_errors.setdefault(room_name, []).append("Production data not found")
        return final_price_day_errors

    production_data = production_data[0]
    differences = []

    production_content = production_data.get('content')
    production_content = json.loads(production_content) if production_content else {}
    adapter_content = final_price_day.content

    for key, prices_per_day in production_content.items():
        for day_index, production_price_element in enumerate(prices_per_day):
            adapter_price_element = adapter_content.get(key, [])[day_index]
            if production_price_element != adapter_price_element:
                differences.append(f"{key} - {day_index}: (production):{production_price_element} vs (adapter):{adapter_price_element}")

    if differences:
        room_name = _calculate_error_naming(final_price_day, hotel_code)
        final_price_day_errors.setdefault(room_name, []).extend(differences)

    return final_price_day_errors



def _notify_exceptions(errors, hotel_code):
    content_html = render_base_integration_template('emails/audit_error_notification.html', data=errors)
    target_email = '<EMAIL>'
    if Config.DEV:
        from paraty.utils.dev_utils import get_developer_email
        target_email = get_developer_email()

    send_amazon_email(target_email, f'Integration Adapter Mismatch Alert [{hotel_code}]', content_html)
