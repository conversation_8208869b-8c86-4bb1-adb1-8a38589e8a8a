import logging
import interface_to_implement
from model import availability_item_model

from model.audit import EndpointCallAuditEvent

import datetime
def audit(request_id, path, request, response, hotel, operation=""):
	logging.debug('Auditing request')

	# TODO: better handling of str and unicode string

	try:

		#No need to chunk as the field is compressed
		# request_chunks = split_str_in_chunks(request)

		request_chunks = [request]

		if hotel and not isinstance(hotel, str):
			hotel = str(hotel)

		for i in range(len(request_chunks)):

			event = EndpointCallAuditEvent()
			event.request_id = request_id
			event.processsed = False
			event.chunk_order = i
			event.path = path
			event.operation = operation
			event.hotel_code = hotel
			event.request = request_chunks[i]
			if i == 0:
				# we don't need to split response bodies at the moment
				if not isinstance(response, str):
					event.response = response.encode('utf-8')
				else:
					event.response = response

			event.put()

		logging.debug('Request audited')

	except Exception as e:
		logging.warning('Endpoint call could not be audited')
		logging.warning('We are going to retry, if it fails more than 2 times it will throw and error')

		#If the request is too big we will break XML case by case i.e. Split in two complete XML
		logging.info("response: %s", response)
		logging.info("request: %s", request)

		raise e



def get_audit_event_by_request_id(request_id):
	return EndpointCallAuditEvent.query_by_request_id(request_id).fetch()


def split_str_in_chunks(text, size=500000):
	return [text[i:i + size] for i in range(0, len(text), size)]