import logging
from datetime import <PERSON><PERSON><PERSON>, datetime

import interface_to_implement
from interface_to_implement import OPEN_STATUS, CLOSED_STATUS
from model.price_model import HOTEL_MANAGER_PARENT_ROOM_CAPACITY, RATE_BOARD_SEPARATOR, RoomTypeStatus, \
	get_year_and_month, FinalPriceDay, DAYS_IN_CONTENT, CAPACITY_FOR_MAX_STAY, CAPACITY_FOR_CLOSED_DEPARTURE, CAPACITY_FOR_CLOSED_ARRIVAL, BOARD_FOR_BASE_PRICES, \
	CAPACITY_FOR_RELEASE
from paraty.integration.base_service import BaseService
from paraty.integration.exception import ParatyException, BusinessEntityNotFoundException, ERROR_CODE_INVALID_RATE
from paraty.integration.integration_data_cache import IntegrationDataCache

from paraty.utils.hotel_manager_utils import hotel_requires_closing_virtual_rates, RATE_MAP_SEPARATOR, ISO_DATE_FORMAT, \
	VIRTUAL_MIN_STAYS_RESTRICTION, VIRTUAL_MAX_STAYS_RESTRICTION, VIRTUAL_DISPO_RESTRICTION, \
	get_room_of_hotel_by_room_name, \
	hotel_has_virtual_rooms_restrictions, hotel_requires_closing_mapped_rates, hotel_has_virtual_rooms
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_boards_of_hotel, \
	get_integration_configuration_of_hotel
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.hotel_manager import hotel_manager_utils

FAKE_PRICE = '9999.00'

DATE_FORMAT = '%Y-%m-%d'
MONTH_DATE_FORMAT = '%Y-%m'


SEP_VIRTUAL_ROOMS = "---"
SEP_LOGIC_VIRTUAL_ROOMS = "@@"
PAX_INDICATOR_VIRTUAL_ROOMS = "PAX"


# Ugly solution in order to disabled prices modification for some hotels.
HOTEL_NOT_AVAILABLES_FOR_PRICES_MODIFICATION = ['Nuriasol']


@timed_cache(hours=24)
def get_virtual_rates_of_rate(hotel, rate_id):

	if not rate_id:
		return []

	result = []

	all_rates = get_rates_of_hotel(hotel, include_removed=True)
	for rate in all_rates:
		if rate.get('virtualRateOf'):
			id = alphanumeric_to_id(rate.get('virtualRateOf'))
			if id == rate_id:
				result.append(rate)
	return result



def apply_changes_also_to_virtual_rates(hotel_code, availability_items):
	'''
	Note that we only take into account status, not the price of restrictions
	'''

	new_items_to_add = []

	hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)

	for item in availability_items:

		if not item.get('rateId'):
			continue

		virtual_rates_of_rate = get_virtual_rates_of_rate(hotel, int(item.get('rateId')))

		for rate in virtual_rates_of_rate:
			new_item = {
				'day': item['day'],
				'roomId': item.get('roomId'),
				'boardId': item.get('boardId'),
				'rateId': alphanumeric_to_id(rate['key']),
				'closedToArrival': item.get('closedToArrival'),
				'closedToDeparture': item.get('closedToDeparture'),
				'status': item.get('status'),
				'quantity_rate_board': item.get('quantity_rate_board')
			}
			new_items_to_add.append(new_item)

	logging.info("Creating new items: %s", len(new_items_to_add))

	availability_items.extend(new_items_to_add)


def apply_changes_also_to_mapped_rates(hotel_code, availability_items):
	new_items_to_add = []
	hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
	rates_mapped_xml = get_integration_configuration_of_hotel(hotel, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())
	if not rates_mapped_xml:
		return
	rates_mapped_xml = rates_mapped_xml[0].get("rateMap")
	rates_mapped_xml = {x.split(RATE_MAP_SEPARATOR)[0]: x.split(" @@ ")[1] for x in rates_mapped_xml if len(x.split(RATE_MAP_SEPARATOR)) == 2}
	rates_mapped = {}
	for rate_mapped, rate in rates_mapped_xml.items():
		if rates_mapped.get(rate):
			rates_mapped[rate].append(rate_mapped)
		else:
			rates_mapped[rate] = [rate_mapped]
	for item in availability_items:
		if not item.get('rateId') or not rates_mapped.get(str(item['rateId'])):
			continue
		for rate in rates_mapped[str(item['rateId'])]:
			new_item = {
				'day': item['day'],
				'roomId': item.get('roomId'),
				'boardId': item.get('boardId'),
				'rateId': alphanumeric_to_id(rate),
				'closedToArrival': item.get('closedToArrival'),
				'closedToDeparture': item.get('closedToDeparture'),
				'status': item.get('status')
			}
			new_items_to_add.append(new_item)
	logging.info("Creating new items: %s", len(new_items_to_add))
	availability_items.extend(new_items_to_add)


def percentage_of(percent, whole):
	return (percent * whole) / 100.0

def build_virtual_price(original_price, logic, num_persons=None):
	'''

	:param original_price: Or 45 Or -45 or 45% or -45% or 45PAX or -45PAX or 45%PAX or -45%PAX
	:param logic:
	:return: virtual price
	'''

	logging.info("virtual logic %s fro persons: %s", logic, num_persons)

	multiplicator = 1
	if num_persons and PAX_INDICATOR_VIRTUAL_ROOMS.lower() in logic.lower():
		logic = logic.replace(PAX_INDICATOR_VIRTUAL_ROOMS, "").replace(PAX_INDICATOR_VIRTUAL_ROOMS.lower(), "")
		multiplicator = int(num_persons)


	if '%' in logic:
		#percentage method
		percentage = float(logic.replace("%", ""))
		amount_diff = percentage_of(percentage, float(original_price))
		return  float(original_price) + (float(amount_diff) * multiplicator)

	else:
		return float(original_price) + (float(logic) * multiplicator)

def get_persons_from_capacity(capacity):
	info_cap=capacity.split("-")
	return sum(int(i) for i in info_cap)

def apply_prices_also_to_virtual_rooms(hotel_code, availability_items, virtual_rooms_logic):
	'''
	Note that we only take into prices, not the  status or  restrictions
	unless we have ahoter configuration!: virtual rooms restrictions

	virtual_rooms_logic = HAB_DESTINO1@@45@@HAB_ORIGEN1---HAB_DESTINO2@@-15%@@HAB_ORIGEN2

	Real Example:
	NUEVA! SUITE@@45@@NUEVA! Habitacion Doble - 1 o 2 camas---NUEVA! Habitacion Doble Economica@@-15%@@NUEVA! Habitacion Doble - 1 o 2 camas

	New logic: BY PAX!!!
	Ie:
	NUEVA! SUITE@@45PAX@@NUEVA! Habitacion Doble - 1 o 2 camas

	Posible  values by pax:
		45PAX o -45PAX

		45%PAX o -24%PAX



	virtual rooms restrictions -> Posible values: "Estancias Minimas;Estancias Maximas;Disponibilidad"


	'''


	virtual_rooms_restrictions = hotel_has_virtual_rooms_restrictions(hotel_code)


	new_items_to_add = []
	hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)

	for my_virtual_room_config in virtual_rooms_logic.split(SEP_VIRTUAL_ROOMS):
		room_config = my_virtual_room_config.split(SEP_LOGIC_VIRTUAL_ROOMS)

		logging.info("virtual room room_config!: %s", room_config)

		if len(room_config) == 3:
			target_room_name = room_config[0]
			logic_price = room_config[1]
			source_room_name = room_config[2]

			logging.info("virtual room info config: target_room_name %s  logic_price %s  source_room_name %s", target_room_name, logic_price, source_room_name)

			source_room = get_room_of_hotel_by_room_name(hotel, source_room_name)
			target_room = get_room_of_hotel_by_room_name(hotel, target_room_name)

			logging.info("virtual room info rooms: source_room %s  target_room %s ", source_room, target_room)

			if source_room:
				for item in availability_items:
					#only modify items with prices!

					#SPAM!!!! Too many logs
					# logging.info("searching and comparing: availability item troom id %s  against room id source %s ", int(item.get('roomId', 0)), hotel_manager_utils.alphanumeric_to_id(source_room["key"]))

					new_item = {}
					if item.get('price') and int(item.get('roomId', 0)) == alphanumeric_to_id(source_room["key"]):

						logging.info("virtual room found!: FROM %s TO VIRTUAL %s", source_room_name, target_room_name)

						num_persons = get_persons_from_capacity(item.get("capacity"))
						virtual_price = build_virtual_price(item.get('price'), logic_price, num_persons=num_persons)
						logging.info("virtual price calculated %s", virtual_price)
						new_item = {
							'capacity': item['capacity'],
							'day': item['day'],
							'roomId': alphanumeric_to_id(target_room["key"]),
							'boardId': item.get('boardId'),
							'rateId': item.get('rateId'),
							'price': str(virtual_price)
						}



					if item.get('price') and int(item.get('roomId', 0)) == alphanumeric_to_id(target_room["key"]):
						#we never save prices for virtual rooms
						item['price'] = None

					if virtual_rooms_restrictions and int(item.get('roomId', 0)) == alphanumeric_to_id(source_room["key"]):


						logging.info("virtual room RESTRICTITION found!: FROM %s TO VIRTUAL %s", source_room_name, target_room_name)

						if not new_item:
							new_item = {
								'capacity': item['capacity'],
								'day': item['day'],
								'roomId': alphanumeric_to_id(target_room["key"]),
								'boardId': item.get('boardId'),
								'rateId': item.get('rateId'),
							}

						if VIRTUAL_MIN_STAYS_RESTRICTION.lower() in virtual_rooms_restrictions.lower() and item.get('minimumStay'):
							new_item["minimumStay"] = item.get('minimumStay')

						if VIRTUAL_MAX_STAYS_RESTRICTION.lower() in virtual_rooms_restrictions.lower() and item.get('maximumStay'):
							new_item["maximumStay"] = item.get('maximumStay')

						if VIRTUAL_DISPO_RESTRICTION.lower() in virtual_rooms_restrictions.lower() and item.get('status'):
							new_item["status"] = item.get('status')

						if VIRTUAL_DISPO_RESTRICTION.lower() in virtual_rooms_restrictions.lower() and item.get('closedToArrival'):
							new_item["closedToArrival"] = item.get('closedToArrival')

						if VIRTUAL_DISPO_RESTRICTION.lower() in virtual_rooms_restrictions.lower() and item.get('closedToDeparture'):
							new_item["closedToDeparture"] = item.get('closedToDeparture')

					if new_item:
						new_items_to_add.append(new_item)

		else:
			logging.warning("Virtual Room Configuration BAD configured: %s", my_virtual_room_config)


	# logging.info("Creating new items for virtual room: %s", len(new_items_to_add))

	availability_items.extend(new_items_to_add)


def execute_modify_prices_batch(hotel_code, availability_items, start_date, end_date, request_id, cache):
	'''

	The main difference with the standard modify_prices is that this one uses a cache to read and write entities
	'''

	logging.debug("MODIFYING PRICES FOR REQUEST_ID: %s", request_id)

	if str(hotel_code) in HOTEL_NOT_AVAILABLES_FOR_PRICES_MODIFICATION:
		logging.info('Ignoring price modification: Prices modification disabled for Hotel: %s', hotel_code)
		return

	if hotel_requires_closing_virtual_rates(hotel_code):
		#Apply changes also to virtual rates
		logging.info("Applying status also to virtual rates")
		apply_changes_also_to_virtual_rates(hotel_code, availability_items)

	if hotel_requires_closing_mapped_rates(hotel_code):
		logging.info("Applying status also to mapped rates")
		apply_changes_also_to_mapped_rates(hotel_code, availability_items)

	virtual_rooms_logic = hotel_has_virtual_rooms(hotel_code)
	if virtual_rooms_logic:
		#Apply changes also to virtual rates
		logging.info("Applying prices also to virtual rooms with configuration: %s", virtual_rooms_logic)
		apply_prices_also_to_virtual_rooms(hotel_code, availability_items, virtual_rooms_logic)

	room_id_to_availability_items = {}

	#Process line by line, grouping changes by room
	for availability_item in availability_items:

		room_id = availability_item['roomId']
		availability_items_of_room = room_id_to_availability_items.get(room_id)

		if not availability_items_of_room:
			availability_items_of_room = []
			room_id_to_availability_items[room_id] = availability_items_of_room

		availability_items_of_room.append(availability_item)

	#Modify prices, room by room

	for room_id, availability_items_of_room in room_id_to_availability_items.items():
		try:
			myService = ReusablePricesService(hotel_code, room_id, start_date, end_date, request_id, cache)
			myService.execute(availability_items_of_room)

		except BusinessEntityNotFoundException as e:
			logging.warning("Ignoring invalid Room Code: %s, at hotel-code: %s", room_id, hotel_code)


#We give each rateId 1 chance to be reloaded before failing (i.e. if we have just created the rate and the cache is not updated yet)
reloaded_rates = set()

class ReusablePricesService(BaseService):
	'''
	This implementation has the following differences:

	It does not save anything

	Works always against the cache, going to the datastore only to fetch missing data

	'''

	def __init__(self, hotel_code, room_id, start_date, end_date, request_id, cache):
		BaseService.__init__(self, hotel_code, room_id, start_date, end_date, request_id)
		self.cache = cache

		#Flags to indicate if the cache has already been populated for the period
		self._room_status_fetched = False
		self._final_price_fetched = False


	def __get_date(self, availability):
		try:
			return datetime.strptime(availability['day'], ISO_DATE_FORMAT)
		except ValueError:
			raise ParatyException('Invalid date')

	def __get_rate(self, rate_id):

		try:
			return self.rates[str(rate_id)]
		except KeyError:

			if not rate_id in reloaded_rates:
				try:
					reloaded_rates.add(rate_id)
					self._reload_rates_without_cache()
					return self.rates[str(rate_id)]
				except KeyError:
					logging.warning("After reloading cache we still don't find rate: %s", rate_id)
					raise BusinessEntityNotFoundException(ERROR_CODE_INVALID_RATE, "Invalid rate")

			else:
				raise BusinessEntityNotFoundException(ERROR_CODE_INVALID_RATE, "Invalid rate")


	def __get_quantity(self, availability):

		#Be careful with 0 as quantity
		if availability.get('quantity') is None or availability['quantity'] == '':
			return None
		try:
			return int(availability.get('quantity'))
		except ValueError:
			raise ParatyException('Invalid quantity value')

	def __get_quantity_rate_board(self, availability_item):
		# Be careful with 0 as quantity
		if availability_item.get('quantity_rate_board') is None or availability_item['quantity_rate_board'] == '':
			return None
		return availability_item.get('quantity_rate_board')

	def __get_status(self, availability):

		# TODO: check status value

		if availability.get('status'):
			return availability['status']
		else:
			return None


	def __get_price(self, availability):

		if not availability.get('price'):
			return None
		try:
			price = float(availability.get('price'))
			if '%.2f' % price == FAKE_PRICE:
				return None
			else:
				return price
		except ValueError:
			raise ParatyException('Invalid price value')

	def __get_min_stay(self, availability):

		#0 min stay is valid
		if availability.get('minimumStay') is None or availability.get('minimumStay') == '':
			return None
		if availability.get('minimumStay') == '':
			return ""
		try:
			return str(int(availability['minimumStay']))
		except ValueError:
			raise ParatyException('Invalid minimumStay value')

	def __get_max_stay(self, availability):

		#0 max stay is valid
		if availability.get('maximumStay') is None or availability.get('maximumStay') == '':
			return None
		try:
			return str(int(availability['maximumStay']))
		except ValueError:
			raise ParatyException('Invalid maximumStay value')

	def __get_release(self, availability):

		#0 release is valid
		if availability.get('release') is None:
			return None
		if availability.get('release') == '':
			return ""
		try:
			return str(int(availability['release']))
		except ValueError:
			raise ParatyException('Invalid release value')

	def __get_closed_arrival(self, availability):

		#0 min stay is valid
		if availability.get('closedToArrival') is None or availability.get('closedToArrival') == '':
			return None
		try:
			return availability['closedToArrival']
		except ValueError:
			raise ParatyException('Invalid closedToArrival value')


	def __get_closed_departure(self, availability):

		#0 min stay is valid
		if availability.get('closedToDeparture') is None or availability.get('closedToDeparture') == '':
			return None
		try:
			return availability['closedToDeparture']
		except ValueError:
			raise ParatyException('Invalid closedToDeparture value')

	def _get_final_price_day(self, date, item_key):

		if not self._final_price_fetched:
			str_start_year_and_month = get_year_and_month(self.start_date)
			str_end_year_and_month = get_year_and_month(self.end_date)
			self._get_data_for_start_and_end(str_start_year_and_month, str_end_year_and_month, IntegrationDataCache.FINAL_PRICE_DAY, BaseService._get_final_price_day_for_room_and_start_and_end_month)
			self._final_price_fetched = True

		str_year_and_month = get_year_and_month(date)

		fpd = self.cache.get_multiple_data(self.room_id, 'FinalPriceDay').get(str_year_and_month)

		if not fpd:
			fpd = FinalPriceDay(date=str_year_and_month, roomKey=self.room['key'])
			self._set_new_final_price_to_cache(str_year_and_month, fpd)

		if item_key not in fpd.content:
			fpd.content[item_key] = [''] * DAYS_IN_CONTENT

		return fpd

	def _set_new_final_price_to_cache(self, str_date, status):
		logging.info("_set_new_final_price_to_cache")
		self.cache.set_data_to_cache(self.room_id, IntegrationDataCache.FINAL_PRICE_DAY, str_date, status)


	def __get_board(self, board_id):
		try:

			#Note that if we are using base prices, boards are not required
			if board_id == BOARD_FOR_BASE_PRICES:
				return {'key': ''}

			return self.boards[str(board_id)]

		except KeyError:
			raise BusinessEntityNotFoundException(ERROR_CODE_INVALID_RATE, "Invalid rate")

	def __is_rate_closed(self, rate, room_type_status):
		room_type_status.closedRate2 = room_type_status.closedRate2 or ''
		return rate['key'] in room_type_status.closedRate2

	def __close_rate_board(self, rate, board, room_type_status):

		board_rate_key = rate['key'] + RATE_BOARD_SEPARATOR + board['key']

		room_type_status.closedRateBoard = room_type_status.closedRateBoard or ''

		#If already closed we do nothing
		if board_rate_key in room_type_status.closedRateBoard:
			return

		board_rate_value = board_rate_key + ';'

		if board_rate_key not in room_type_status.closedRateBoard:
			room_type_status.closedRateBoard += board_rate_value

		room_type_status.dirty = True

	def __open_rate(self, rate, room_type_status):
		rate_value = rate['key']
		room_type_status.closedRate2 = room_type_status.closedRate2 or ''
		room_type_status.closedRate2 = room_type_status.closedRate2.replace(rate_value, '')
		#Remove remainder ;
		room_type_status.closedRate2 = room_type_status.closedRate2.replace(";;", ';')
		if room_type_status.closedRate2 == ';':
			room_type_status.closedRate2 = ''

	def _get_room_type_status(self, date):

		str_date = date.strftime(ISO_DATE_FORMAT)

		#If not done already we need to populate the cache
		if not self._room_status_fetched:

			start_date_str = self.start_date.strftime(ISO_DATE_FORMAT)
			end_date_str = self.end_date.strftime(ISO_DATE_FORMAT)

			self._get_data_for_start_and_end(start_date_str, end_date_str, IntegrationDataCache.ROOM_TYPE_STATUS, BaseService._get_room_type_status_for_room_and_start_and_end_date)
			self._room_status_fetched = True

		#Does it exist already?
		status = self.cache.get_multiple_data(self.room_id, 'RoomTypeStatus').get(str_date)

		#We have it in memory, no need to create a new one
		if status:
			return status

		status = RoomTypeStatus()
		status.date = str_date
		status.roomKey = self.room['key']
		status.availability = self.room['amount']
		status.open = True
		status.closedRate2 = ''


		#Note that we don't need to update self._room_type_status because it is already pointing to the cache
		#FIXME, That is not true if it is empty
		self._set_new_room_type_to_cache(str_date, status)

		return status

	def _set_new_room_type_to_cache(self, str_date, status):
		self.cache.set_data_to_cache(self.room_id, IntegrationDataCache.ROOM_TYPE_STATUS, str_date, status)

	def __open_rate_board(self, rate, board, room_type_status):

		board_rate_value = rate['key'] + RATE_BOARD_SEPARATOR + board['key']

		room_type_status.closedRateBoard = room_type_status.closedRateBoard or ''

		#Do nothing if it is already open
		if not board_rate_value in room_type_status.closedRateBoard:
			return

		room_type_status.closedRateBoard = room_type_status.closedRateBoard.replace(board_rate_value, '')

		room_type_status.closedRateBoard = room_type_status.closedRateBoard.replace(';;', ';')

		if room_type_status.closedRateBoard == ';':
			room_type_status.closedRateBoard = ''

		room_type_status.dirty = True


	def get_date_format_based_on_data_type(self, data_type):
		date_format = DATE_FORMAT

		if data_type == IntegrationDataCache.FINAL_PRICE_DAY:
			date_format = MONTH_DATE_FORMAT
		return date_format

	def _add_final_prices_for_availability_item(self, availability_item):

		date = self.__get_date(availability_item)

		# TODO: Exception
		assert self.room_id == availability_item['roomId']

		rate_id = availability_item.get('rateId')
		rate = None

		if rate_id:
			try:
				rate = self.__get_rate(rate_id)
			except:
				logging.warning("Ignoring availability item with non existing rate_id: %s", rate_id)
				return

		board = None
		board_id = availability_item.get('boardId')
		if board_id:
			try:
				board = self.__get_board(board_id)
			except:
				logging.warning("Ignoring availability item with non existing board_id: %s", board_id)
				return

		capacity = availability_item.get('capacity')


		price = self.__get_price(availability_item)
		min_stay = self.__get_min_stay(availability_item)
		max_stay = self.__get_max_stay(availability_item)
		quantity = self.__get_quantity(availability_item)
		quantity_rate_board = self.__get_quantity_rate_board(availability_item)
		status = self.__get_status(availability_item)

		closed_to_arrival = self.__get_closed_arrival(availability_item)
		closed_to_departure = self.__get_closed_departure(availability_item)

		release = self.__get_release(availability_item)

		# logging.debug("__add_final_prices_for_availability_item PRICE: %s", price)

		#Prices stored at FinalPrices
		if price is not None:

			#Note that by mistake we might be adding a 0-0-0 capacity, but that is reserved for min-Stay
			if capacity == HOTEL_MANAGER_PARENT_ROOM_CAPACITY:
				raise Exception("Invalid capacity 0-0-0")

			item_key = self.build_final_price_item_key(rate, capacity, board)
			final_price_day = self._get_final_price_day(date, item_key)

			if final_price_day.content[item_key][date.day - 1] != '%.2f' % price:
				final_price_day.content[item_key][date.day - 1] = '%.2f' % price
				self.dict_date_to_final_price_days_to_save[final_price_day.date] = final_price_day
				final_price_day.dirty = True

		#Min Stays to be stored at FinalPriceDays
		if min_stay is not None:

			item_key = self.build_final_price_item_key(rate, HOTEL_MANAGER_PARENT_ROOM_CAPACITY, board)
			final_price_day = self._get_final_price_day(date, item_key)

			is_restrictive_min_stay = min_stay != "0" and min_stay != "1"
			useful_min_stay = final_price_day.content[item_key][date.day - 1] or is_restrictive_min_stay
			different_min_stay = final_price_day.content[item_key][date.day - 1] != min_stay

			if useful_min_stay and different_min_stay:
				final_price_day.content[item_key][date.day - 1] = min_stay
				self.dict_date_to_final_price_days_to_save[final_price_day.date] = final_price_day
				final_price_day.dirty = True

		#Max Stays to be stored at FinalPriceDays
		if max_stay is not None:

			item_key = self.build_final_price_item_key(rate, CAPACITY_FOR_MAX_STAY, board)
			final_price_day = self._get_final_price_day(date, item_key)

			useful_max_stay = final_price_day.content[item_key][date.day - 1] or max_stay != "0"
			different_max_stay = final_price_day.content[item_key][date.day - 1] != max_stay

			if useful_max_stay and different_max_stay:
				final_price_day.content[item_key][date.day - 1] = max_stay
				self.dict_date_to_final_price_days_to_save[final_price_day.date] = final_price_day
				final_price_day.dirty = True

		#Closed to departure to be stored at FinalPriceDays
		if closed_to_departure is not None:

			item_key = self.build_final_price_item_key(rate, CAPACITY_FOR_CLOSED_DEPARTURE, board)
			final_price_day = self._get_final_price_day(date, item_key)

			if str(closed_to_departure).lower() == "false":
				#FPD needs an empty string as a False!
				closed_to_departure = ""

			if final_price_day.content[item_key][date.day - 1] != str(closed_to_departure):
				final_price_day.content[item_key][date.day - 1] = str(closed_to_departure)
				self.dict_date_to_final_price_days_to_save[final_price_day.date] = final_price_day
				final_price_day.dirty = True

		#Max Stays to be stored at FinalPriceDays
		if closed_to_arrival is not None:

			item_key = self.build_final_price_item_key(rate, CAPACITY_FOR_CLOSED_ARRIVAL, board)
			final_price_day = self._get_final_price_day(date, item_key)

			if str(closed_to_arrival).lower() == "false":
				#FPD needs an empty string as a False!
				closed_to_arrival = ""

			if final_price_day.content[item_key][date.day - 1] != str(closed_to_arrival):
				final_price_day.content[item_key][date.day - 1] = str(closed_to_arrival)
				self.dict_date_to_final_price_days_to_save[final_price_day.date] = final_price_day
				final_price_day.dirty = True


		#Release to be stored at FinalPriceDays
		if release is not None:

			item_key = self.build_final_price_item_key(rate, CAPACITY_FOR_RELEASE, board)
			final_price_day = self._get_final_price_day(date, item_key)

			useful_release = final_price_day.content[item_key][date.day - 1] or release != "0"
			different_release = final_price_day.content[item_key][date.day - 1] != release

			if useful_release and different_release:
				final_price_day.content[item_key][date.day - 1] = release
				self.dict_date_to_final_price_days_to_save[final_price_day.date] = final_price_day
				final_price_day.dirty = True

		# Amounts and open/close in RoomTypeStatus. ALSO quantityRateBoard
		if (quantity is not None) or (status is not None) or (quantity_rate_board is not None):

			room_type_status = self._get_room_type_status(date)

			if quantity is not None:
				if room_type_status.availability != quantity:
					room_type_status.availability = quantity
					room_type_status.dirty = True

			if status is not None:

				# logging.info("Modify status: %s", status)

				if status.lower() == OPEN_STATUS:

					#For legacy reasons, if the rate is closed, we first close all the boards before opening it
					#and then we change to a board-rate closing implementation, i.e. closing/opening each rate-board
					if self.__is_rate_closed(rate, room_type_status):
						logging.info("Modify opening full rate: %s", rate['key'])
						self.__open_rate(rate, room_type_status)
						all_boards = get_boards_of_hotel(self.hotel, include_removed=True)
						for current_board in all_boards:
							logging.info("Modify closing rate board: %s", current_board['key'])
							self.__close_rate_board(rate, current_board, room_type_status)

						#Let's play it safe
						room_type_status.dirty = True

					# logging.info("Modify opening rate board: %s", rate['key'] + RATE_BOARD_SEPARATOR + board['key'])
					self.__open_rate_board(rate, board, room_type_status)

				elif status.lower() == CLOSED_STATUS:
					# logging.info("Modify closing rate board: %s", rate['key'] + RATE_BOARD_SEPARATOR + board['key'])
					self.__close_rate_board(rate, board, room_type_status)

				else:
					logging.warning('Invalid status "%s". It will be ignored' % status)

			if quantity_rate_board is not None:
				self._update_quantity_rate_board(quantity_rate_board, rate_id, board_id, room_type_status)

			self.dict_date_to_room_type_status_to_save[room_type_status.date] = room_type_status

	def _update_quantity_rate_board(self, quantity_rate_board, rate_id, board_id, room_type_status):
		'''
		format of availabilityRateBoard:
		RATE_KEY_@_BOARD_KEY:X;RATE_KEY_@_BOARD_KEY:Y;RATE_KEY_@_BOARD_KEY:Z;
		'''
		board = self.__get_board(board_id)
		rate = self.__get_rate(rate_id)
		rate_board_key = rate['key'] + RATE_BOARD_SEPARATOR + board['key']
		new_entry = rate_board_key + ":%s" % quantity_rate_board

		# Empty
		if not room_type_status.availabilityRateBoard:
			room_type_status.availabilityRateBoard = new_entry

		# Not empty, but our rate boards doesn't exist
		elif not rate_board_key in room_type_status.availabilityRateBoard:
			room_type_status.availabilityRateBoard += ';' + new_entry

		# We need to replace the quantity for the rate_board
		else:
			new_availability_rate_board = ""
			for availability_rate_board in room_type_status.availabilityRateBoard.split(";"):
				if rate_board_key in availability_rate_board:
					# found!!! updating new value
					new_availability_rate_board += new_entry + ";"
				else:
					# is other, keep it!
					new_availability_rate_board += availability_rate_board + ";"
			room_type_status.availabilityRateBoard = new_availability_rate_board.strip(";")
		logging.info("RTS created with quantity_by_rate_board. rate_board_key: %s quantity: %s", rate_board_key, quantity_rate_board)
		room_type_status.dirty = True

	def _get_missing_dates(self, end_str, start_str, data_type):

		date_format = self.get_date_format_based_on_data_type(data_type)

		end = datetime.strptime(end_str, date_format).date()
		start = datetime.strptime(start_str, date_format).date()

		missing_dates = []
		delta = end - start
		for i in range(delta.days + 1):
			current_date = start + timedelta(days=i)
			current_date_str = current_date.strftime(date_format)

			#We only add each date once
			if current_date_str in missing_dates:
				continue

			entity = self.cache.get_data(self.room_id, data_type, current_date_str)
			if not entity:
				# logging.info("Missing something!!!!")
				# logging.info(self.room_id)
				# logging.info(data_type)
				# logging.info(current_date_str)

				missing_dates.append(current_date_str)

		return missing_dates

	def _get_data_for_start_and_end(self, start, end, data_type, datastore_provider):
		'''
		First tries to get the data from the cache,

		If anything is missing, it goes to the datastore to fetch it and completes the cache with it
		'''
		missing_dates = self._get_missing_dates(end, start, data_type)

		logging.info("Missing_dates:%s", str(missing_dates))

		#If missing data at cache we go for it in the datastore

		if missing_dates:

			#TODO Check that several dates in the same month don't have different finalPrice
			data = datastore_provider(self, start, end)

			#Complete cache with the missing data
			for current_date in missing_dates:
				if data.get(current_date):
					self.cache.set_data_to_cache(self.room_id, data_type, current_date, data.get(current_date))

		return self.cache.get_multiple_data(self.room_id, data_type)


	def execute(self, availability_items):
		for availability_item in availability_items:
			self._add_final_prices_for_availability_item(availability_item)