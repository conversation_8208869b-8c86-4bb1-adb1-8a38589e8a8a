import logging

from paraty.integration.exception import ParatyException, BusinessEntityNotFoundException, ERROR_CODE_INVALID_ROOM

from model.price_model import RoomTypeStatus, FinalPriceDay
from paraty.utils.hotel_manager_utils import get_room_type_status_of_hotel_and_room, get_final_price_days_of_hotel_and_room
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_boards_of_hotel, get_room_of_hotel_by_room_id
from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.decorators.cache import timebased_cache
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


class BaseService():
	def __init__(self, hotel_code, room_id, start_date, end_date, request_id="NA"):

		self.hotel_code = hotel_code
		self.room_id = room_id
		self.start_date = start_date
		self.end_date = end_date
		self.request_id = request_id

		self.dict_date_to_final_price_days_to_save = {}
		self.dict_date_to_room_type_status_to_save = {}


		self.__init_hotel()
		self.__init_room()
		self.rates = {str(datastore_utils.alphanumeric_to_id(x['key'])): x for x in get_rates_of_hotel(self.hotel, include_removed=True)}
		self.boards = {str(datastore_utils.alphanumeric_to_id(x['key'])): x for x in get_boards_of_hotel(self.hotel, include_removed=True)}


	def _reload_rates_without_cache(self):
		timebased_cache.clear_cache()
		self.rates = {str(datastore_utils.alphanumeric_to_id(x['key'])): x for x in get_rates_of_hotel(self.hotel, include_removed=True)}

	def __init_hotel(self):

		self.hotel = get_hotel_by_application_id(self.hotel_code)

		if not self.hotel:
			logging.warning('Request_Id: %s, Invalid hotelCode', self.request_id)
			raise ParatyException('Invalid hotel code')

	def __init_room(self):

		self.room = get_room_of_hotel_by_room_id(self.hotel, self.room_id)

		if not self.room:
			logging.warning('Request_Id: %s, Invalid roomId (room not found)', self.request_id)
			raise BusinessEntityNotFoundException(ERROR_CODE_INVALID_ROOM, self.room_id)

	def _get_room_type_status_for_room_and_start_and_end_date(self, start_date_str, end_date_str):

		return {x['date']: RoomTypeStatus(x) for x in get_room_type_status_of_hotel_and_room(self.hotel, self.room, start_date_str, end_date_str)}


	def _get_final_price_day_for_room_and_start_and_end_month(self, start_str, end_str):
		'''
		Note: This method expects strings
		'''
		return {x['date']: FinalPriceDay(x) for x in get_final_price_days_of_hotel_and_room(self.hotel, self.room, start_str, end_str)}


	def build_final_price_item_key(self, rate, capacity, board):
		return '%s@@%s@@%s' % (rate['key'], capacity, board['key'])

