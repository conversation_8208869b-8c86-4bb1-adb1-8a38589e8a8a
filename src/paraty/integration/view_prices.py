# -*- coding: utf-8 -*-
import calendar
import logging
import datetime

from interface_to_implement import OPEN_STATUS, CLOSED_STATUS, AVAILABILITY_ITEM_DATE_FORMAT
from model.price_model import FinalPriceDay, get_year_and_month, RoomTypeStatus, RATE_BOARD_SEPARATOR, get_first_day_of_year_and_month, HOTEL_MANAGER_PARENT_ROOM_CAPACITY, \
	CAPACITY_FOR_BASE_PRICES, BASE_PRICES_CONFIG_PROPERTY, CAPACITY_FOR_MAX_STAY, CAPACITY_FOR_CLOSED_DEPARTURE, CAPACITY_FOR_CLOSED_ARRIVAL, \
	CAPACITY_FOR_RELEASE, SPECIAL_CAPACITIES_FPD
from paraty.utils import date_utils
import paraty
from paraty.utils.hotel_manager_utils import setup_base_price_integration, get_room_type_status_of_hotel, get_final_price_days_of_hotel
from paraty_commons_3.common_data.common_data_provider import get_boards_of_hotel, get_rooms_of_hotel, get_rates_of_hotel, get_hotel_advance_config_item, ISO_DATE_FORMAT
from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.decorators.timerdecorator import timeit
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

CURRENCY = 'EUR'


def create_rate_id(rate_key, board_key):
	return '%s_%s' % (datastore_utils.alphanumeric_to_id(rate_key), datastore_utils.alphanumeric_to_id(board_key))

'''
============================
fmatheis: 2020-05-31
DEPRECATED: We suppose this no longer used and will remove it in a few months
============================
'''


def _get_room_capacities(r):
	if 'capacities' not in r:
		return []

	capacities = r['capacities']

	return capacities

class ViewPricesService():

	@timeit
	def __init__(self, hotel_code, start_date, end_date, rate_id=None, room_id=None, board_id=None, available_room_capacities=()):
		self.hotel_code = hotel_code
		self.start_date = start_date
		self.end_date = end_date
		self.rate_id = rate_id
		self.room_id = room_id
		self.board_id = board_id
		self.room_status_by_date = None
		self.available_room_capacities = available_room_capacities

		self.is_base_price_integration = setup_base_price_integration(hotel_code)

	def get_availability(self):

		#Id will coincide with the HotelApplication one at Hotel Manager
		self.hotel = get_hotel_by_application_id(self.hotel_code)

		#RoomType at the hotel's datastore
		self.rooms = {x['key']: x for x in get_rooms_of_hotel(self.hotel)}

		# is posible that we have still removed rooms in RTS. Lets filter them
		room_type_status_list = [x for x in get_room_type_status_of_hotel(self.hotel, self.start_date, self.end_date) if x.get("roomKey") in list(self.rooms.keys())]

		# RoomTypeStatus in the requested date period
		self.room_status_by_date = {x['date'] + x['roomKey']: RoomTypeStatus(x) for x in room_type_status_list}

		#Note that final Price is only one per Month and roomType
		list_final_price_days = [FinalPriceDay(x) for x in get_final_price_days_of_hotel(self.hotel, get_year_and_month(self.start_date), get_year_and_month(self.end_date))]

		valid_final_price_days = [x for x in list_final_price_days if self._is_room_valid(x.roomKey)]

		#filter lines of room whitch capacity is not valid anymore
		for my_fpd in valid_final_price_days:
			room_key = my_fpd.roomKey
			new_content = {}
			for my_key, my_content in my_fpd.content.items():
				key_info = my_key.split("@@")
				capacity = key_info[1]
				if self._is_valid_capacity(room_key, capacity):
					new_content[my_key] = my_content
			my_fpd.content = new_content


		#room_rate_board_date -> Availability_item
		intermediate_table = {}

		#Process each available final_price_days in the given period
		for final_price_day in valid_final_price_days:
			self._extract_stuff_from_fpd(intermediate_table, final_price_day, available_room_capacities=self.available_room_capacities)

		self.boards = {x['key']: x for x in get_boards_of_hotel(self.hotel)}

		date_room_view = {}
		for current_item in list(intermediate_table.values()):
			key = current_item['day'] + str(current_item['roomId'])
			if key not in date_room_view:
				date_room_view[key] = []
			date_room_view[key].append(current_item)

		items_with_only_availability = []
		for current_room_status in list(self.room_status_by_date.values()):
			items_with_only_availability.extend(self._extract_stuff_from_rts(current_room_status, date_room_view))

		result = [x for x in list(intermediate_table.values())]

		result.extend(items_with_only_availability)


		#If we have explicitly defined board, rate or room we filter all the items that don't belong to them


		return result

	def get_hotel_inventory(self):
		return {
			'boards': {datastore_utils.alphanumeric_to_id(x['key']): x for x in get_boards_of_hotel(self.hotel)},
			'rooms': {datastore_utils.alphanumeric_to_id(x['key']): x for x in get_rooms_of_hotel(self.hotel)},
			'rates': {datastore_utils.alphanumeric_to_id(x['key']): x for x in get_rates_of_hotel(self.hotel)}
		}



	def _is_valid_capacity(self, room_key, capacity):
		if capacity in SPECIAL_CAPACITIES_FPD:
			return True

		room_capacities = _get_room_capacities(self.rooms.get(room_key, {}))
		return capacity in room_capacities

	def _extract_stuff_from_fpd(self, intermediate_table, final_price_day, available_room_capacities=()):

		self._extract_field_from_fpd(final_price_day, intermediate_table, False, available_room_capacities=available_room_capacities)

		self._extract_field_from_fpd(final_price_day, intermediate_table, True, available_room_capacities=available_room_capacities)


	def _extract_field_from_fpd(self, final_price_day, intermediate_table, restrictions, available_room_capacities=()):

		hotel = get_hotel_by_application_id(self.hotel_code)
		is_base_price_integration = get_hotel_advance_config_item(hotel, BASE_PRICES_CONFIG_PROPERTY)
		if is_base_price_integration:
			logging.info("Base prices config property found!")

		first_day_of_month = get_first_day_of_year_and_month(final_price_day.date)
		days_of_month = calendar.monthrange(first_day_of_month.year, first_day_of_month.month)[1]

		# Rate@@Capacity@@board -> [Price1, .., PriceN]
		for key, values in final_price_day.content.items():

			rate_key, capacity, board_key = key.split('@@')

			# Round 1, we just take care of the prices
			if restrictions and capacity not in [HOTEL_MANAGER_PARENT_ROOM_CAPACITY, CAPACITY_FOR_MAX_STAY, CAPACITY_FOR_CLOSED_DEPARTURE, CAPACITY_FOR_CLOSED_ARRIVAL, CAPACITY_FOR_RELEASE]:
				continue

			if not restrictions and capacity in [HOTEL_MANAGER_PARENT_ROOM_CAPACITY, CAPACITY_FOR_MAX_STAY, CAPACITY_FOR_CLOSED_DEPARTURE, CAPACITY_FOR_CLOSED_ARRIVAL, CAPACITY_FOR_RELEASE]:
				continue

			if not restrictions and available_room_capacities and capacity not in available_room_capacities:
				continue  # The capacity is not in the list of allowed capacities

			room_key = final_price_day.roomKey

			for day_index in range(days_of_month):

				date = datetime.date(first_day_of_month.year, first_day_of_month.month, day_index + 1)

				# Check that we are inside the range
				if date < self.start_date or date > self.end_date:
					continue

				room_id = alphanumeric_to_id(room_key)
				rate_id = alphanumeric_to_id(rate_key)

				if board_key:
					board_id = alphanumeric_to_id(board_key)
				else:
					board_id = self.board_id

				if restrictions:

					capacities_in_fpd = [x.split("@@")[1] for x in list(final_price_day.content.keys()) if x.split("@@")[0] == rate_key and x.split("@@")[2] == board_key]

					#if '0-0-0' in capacities_in_fpd: capacities_in_fpd.remove('0-0-0')

					if HOTEL_MANAGER_PARENT_ROOM_CAPACITY in capacities_in_fpd:
						capacities_in_fpd.remove(HOTEL_MANAGER_PARENT_ROOM_CAPACITY)

					if CAPACITY_FOR_MAX_STAY in capacities_in_fpd:
						capacities_in_fpd.remove(CAPACITY_FOR_MAX_STAY)

					if CAPACITY_FOR_CLOSED_DEPARTURE in capacities_in_fpd:
						capacities_in_fpd.remove(CAPACITY_FOR_CLOSED_DEPARTURE)

					if CAPACITY_FOR_CLOSED_ARRIVAL in capacities_in_fpd:
						capacities_in_fpd.remove(CAPACITY_FOR_CLOSED_ARRIVAL)

					if CAPACITY_FOR_RELEASE in capacities_in_fpd:
						capacities_in_fpd.remove(CAPACITY_FOR_RELEASE)


					#If this is a base price we will not have multiple prices for each capacity
					if is_base_price_integration:
						capacities_in_fpd = [CAPACITY_FOR_BASE_PRICES]

					#In case we have only defined min Stays before defining anything else (rare, but could happen)
					#We have to create a fake result only with the minStay for the board and rate
					if not capacities_in_fpd:
						#here we are pretty sure tha capacity = 0-0-0 or MAX
						item_key = self._build_intermediate_key(room_id, rate_id, board_id, date, capacity)

						if values[day_index]:
							available_item = {'roomId': room_id,
											  'day': date_utils.date_to_string(date, format=AVAILABILITY_ITEM_DATE_FORMAT),
											  'rateId': rate_id,
											  'boardId': board_id,
											  'capacity': capacity,

											  }

							if capacity == HOTEL_MANAGER_PARENT_ROOM_CAPACITY:
								available_item['minimumStay'] = int(values[day_index])
							elif capacity == CAPACITY_FOR_MAX_STAY:
								available_item['maximumStay'] = int(values[day_index])
							elif capacity == CAPACITY_FOR_CLOSED_DEPARTURE:
								available_item['closedToDeparture'] = values[day_index]
							elif capacity == CAPACITY_FOR_CLOSED_ARRIVAL:
								available_item['closedToArrival'] = values[day_index]
							elif capacity == CAPACITY_FOR_RELEASE:
								available_item['release'] = int(values[day_index])
							else:
								logging.error('Special CAPACITY not found when inicializating capacities_in_fpd: %s', capacity)
								notify_exception_by_email(Exception('Trying to assign twice the same data, as we are working in the cache'), make_traceback())



							intermediate_table[item_key] = available_item

					for current_capacity in capacities_in_fpd:
						current_item_key = self._build_intermediate_key(room_id, rate_id, board_id, date, current_capacity)

						#Note that this always has to exist as first we go for the prices
						if values[day_index]:

							#Note that the board is not defined in the prices for BASE_PRICE we use self.board_id
							if is_base_price_integration and not intermediate_table.get(current_item_key):
								current_item_key = self._build_intermediate_key(room_id, rate_id, self.board_id, date, current_capacity)

							if intermediate_table.get(current_item_key):

								if capacity == HOTEL_MANAGER_PARENT_ROOM_CAPACITY:
									intermediate_table.get(current_item_key)['minimumStay'] = int(values[day_index])
								elif capacity == CAPACITY_FOR_MAX_STAY:
									intermediate_table.get(current_item_key)['maximumStay'] = int(values[day_index])
								elif capacity == CAPACITY_FOR_CLOSED_DEPARTURE:
									intermediate_table.get(current_item_key)['closedToDeparture'] = values[day_index]
								elif capacity == CAPACITY_FOR_CLOSED_ARRIVAL:
									intermediate_table.get(current_item_key)['closedToArrival'] = values[day_index]
								elif capacity == CAPACITY_FOR_RELEASE:
									intermediate_table.get(current_item_key)['release'] = int(values[day_index])
								else:
									logging.error('Special CAPACITY not found: %s', capacity)
									notify_exception_by_email(Exception('Trying to assign twice the same data, as we are working in the cache'), make_traceback())
							else:
								logging.warning('current_item_key: %s NOT FOUND IN intermediate_table. Maybe not inventory load for this key', current_item_key)


				else:

					if is_base_price_integration:
						final_capacity = CAPACITY_FOR_BASE_PRICES
					else:
						final_capacity = capacity

					item_key = self._build_intermediate_key(room_id, rate_id, board_id, date, final_capacity)
					available_item = intermediate_table.get(item_key)
					if not available_item:
						available_item = {'roomId': room_id,
										  'day': date_utils.date_to_string(date, format=AVAILABILITY_ITEM_DATE_FORMAT),
										  'rateId': rate_id,
										  'boardId': board_id,
										  'capacity': capacity,
										  'status': OPEN_STATUS,
										  }
						intermediate_table[item_key] = available_item

					available_item['price'] = values[day_index]




	def _build_intermediate_key(self, room_id, rate_id, board_id, date, capacity):

		return '_@_'.join([str(room_id), str(rate_id), str(board_id), date_utils.date_to_string(date), capacity])


	def _is_room_valid(self, room_key):

		if self.room_id:
			return alphanumeric_to_id(room_key) == int(self.room_id)

		return room_key in self.rooms


	def __get_room_type_status(self, date, room_key):
		return self.room_status_by_date.get(date.strftime(ISO_DATE_FORMAT) + room_key)

	def _extract_stuff_from_rts(self, current_room_status, date_room_view):

		closed_rate_board_combinations = set()

		closed_rb = []
		if current_room_status.closedRateBoard:
			closed_rb = current_room_status.closedRateBoard.split(";")
		while '' in closed_rb: closed_rb.remove('')

		for current in closed_rb:
			rate_key, board_key = current.split("_@_")
			closed_rate_board_combinations.add((alphanumeric_to_id(rate_key), alphanumeric_to_id(board_key)))

		availability = current_room_status.availability

		# Save availabilityRateBoard in dictionary SRB
		availability_rate_board = {}
		if current_room_status.availabilityRateBoard:
			for x in current_room_status.availabilityRateBoard.split(";"):
				rate_board_key, rate_board_quantity = x.split(":")
				rate_key, board_key = rate_board_key.split("_@_")
				availability_rate_board["%s_%s" % (alphanumeric_to_id(rate_key), alphanumeric_to_id(board_key))] = int(
					rate_board_quantity)

		current_key = current_room_status.date + str(alphanumeric_to_id(current_room_status.roomKey))

		new_availability_items = []

		#What happens if we haven't defined any price previously?
		if current_key not in date_room_view:

			#We can put any capacity because we don't have price anyway, so this will only have status and quantity
			valid_capacity = '2-0-0'

			date_room_view[current_key] = []

			for board_key in self.boards:
				new_item = {
					'roomId': alphanumeric_to_id(current_room_status.roomKey),
					'day': current_room_status.date,
					'capacity': valid_capacity,
					'boardId': alphanumeric_to_id(board_key),
					'rateId': self.rate_id,
				}

				if new_item['rateId']:
					new_item['rateId'] = int(new_item['rateId'])

				date_room_view[current_key].append(new_item)
				new_availability_items.append(new_item)


		for current_item in date_room_view.get(current_key, []):

			status = not (current_item['rateId'], current_item['boardId']) in closed_rate_board_combinations
			rate_board_key = "%s_@_%s" % (current_item['rateId'], current_item['boardId'])

			if status:
				status_value = OPEN_STATUS
			else:
				status_value = CLOSED_STATUS

			current_item['status'] = status_value
			current_item['quantity'] = availability

			if current_item['rateId'] and rate_board_key in availability_rate_board:
				current_item['quantity'] = availability_rate_board.get(rate_board_key)

		return new_availability_items