import json
import logging

from paraty.constants import BUGSEEKER_TRYING_TO_ASSIGN_TWICE_TIME_SAME_DATA, BUGSEEKER_DUPLICATED_FINALPRICEDAY
from paraty.utils.email_utils import notifyExceptionByEmail
from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.bugseeker.bugseeker import send_notification_to_bug_seeker
from paraty_commons_3.datastore import datastore_communicator, datastore_utils
from paraty_commons_3.decorators.cache.managers_cache.entities_utils import refresh_entity_timestamp

MAX_ENTITIES_IN_GAE_SINGLE_CALL = 400

def chunks(l, n):
	for i in range(0, len(l), n):
		yield l[i:i+n]

def _get_project_id_and_namespace(hotel):
	namespace = None
	project_id = hotel['applicationId']
	if 'multi' in hotel['url']:
		project_id = hotel['url'][17:]
		project_id = project_id[:project_id.find('.appspot')]
		namespace = hotel['applicationId']
	return project_id, namespace


class IntegrationDataCache:
	'''
	Cache of data related to the integration

	Note that the data is structure in the following manner:

		room1 ->
			FinalPriceDay
				day1 -> finalPriceDay1
				dayN -> finalPriceDayN
			RoomTypeStatus
				day1 -> roomTypeStatus1
				dayN -> roomTypeStatusN
		roomN ->
			FinalPriceDay
				day1 -> finalPriceDay1
				dayN -> finalPriceDayN
			RoomTypeStatus
				day1 -> roomTypeStatus1
				dayN -> roomTypeStatusN

	'''
	ROOM_TYPE_STATUS = 'RoomTypeStatus'
	FINAL_PRICE_DAY = 'FinalPriceDay'

	def __init__(self, hotel, request_id):
		self._cache = {}

		#Hotel for which we are storing data
		self.hotel = hotel

		#First request that is causing all the changes to happen
		self.request_id = request_id

	def set_data_to_cache(self, room_id, data_type, current_date_str, data):

		if room_id not in self._cache:
			self._cache[room_id] = {'RoomTypeStatus':{}, 'FinalPriceDay': {}}

		if not self._cache.get(room_id).get(data_type).get(current_date_str):
			self._cache.get(room_id).get(data_type)[current_date_str] = data

		else:
			logging.info("Datatype: %s", data_type)
			logging.info("current_date_str: %s", current_date_str)
			logging.error('Trying to assign twice the same data, as we are working in the cache')
			notifyExceptionByEmail(Exception('Trying to assign twice the same data, as we are working in the cache'), make_traceback())
			message = make_traceback()
			send_notification_to_bug_seeker(BUGSEEKER_TRYING_TO_ASSIGN_TWICE_TIME_SAME_DATA, message)

	def get_room_ids(self):
		return list(self._cache.keys())

	def get_data(self, room_id, data_type, current_date):
		return self._cache.get(room_id, {}).get(data_type, {}).get(current_date)

	def get_multiple_data(self, room_id, data_type):
		return self._cache.get(room_id, {}).get(data_type, {})
	
	def get_dirty_summary(self):
		'''
		Get a summary of the dirty data in the cache
		'''
		dirty_summary = {}
		for room_id in self._cache.keys():
			for data_type in self._cache[room_id].keys():
				dirty_summary[data_type] = [x for x in self.get_multiple_data(room_id, data_type).values() if x.dirty]
		return dirty_summary

	def save_data_to_datastore(self):
		'''
		Save everything related to the processed modifications
		'''
		has_fpd_changes = False
		has_rts_changes = False

		for roomId in list(self._cache.keys()):
			finalPriceDays = list(self.get_multiple_data(roomId, IntegrationDataCache.FINAL_PRICE_DAY).values())
			roomTypeStatus = list(self.get_multiple_data(roomId, IntegrationDataCache.ROOM_TYPE_STATUS).values())

			#Uncomment this line if duplicated FinalPriceDays start to appear
			# double_check_fpd(finalPriceDays, self.hotel)

			logging.info('Request_ID: %s, FinalPriceDay entities to save: %d', self.request_id, len(finalPriceDays))
			logging.info('Request_ID: %s, RoomTypeStatus entities to save: %d', self.request_id, len(roomTypeStatus))

			all_dirty_fpd = [x.to_dict() for x in finalPriceDays if x.dirty]
			all_dirty_rts = [x.to_dict() for x in roomTypeStatus if x.dirty]


			dirty_fpd_batches = chunks(all_dirty_fpd, MAX_ENTITIES_IN_GAE_SINGLE_CALL)
			batch_count = 1
			for dirty_fpd in dirty_fpd_batches:
				total_batch =  len(dirty_fpd)
				logging.info("dirty_fpd Procecessin batch %s. Len of batch: %s", batch_count, total_batch)

				fpd_ids = _build_ids_from_cache_entities(dirty_fpd)

				logging.info('Request_ID: %s, DIRTY FinalPriceDay entities to save: %d', self.request_id, total_batch)
				try:
					# client = datastore_rest_api_client.Client(project_id=project_id, namespace=namespace)
					# client.upsert_multi(dirty_fpd, batch_size=50)

					datastore_communicator.save_multiple_entities('FinalPriceDay', fpd_ids, dirty_fpd, hotel_code=self.hotel['applicationId'])

				except:

					logging.exception("Error trying to use Datastore API to save FinalPriceDay for: %s, retrying", self.hotel['applicationId'])

					#Note that something might have failed in the middle, so some entities might have been created in the way
					double_check_fpd(dirty_fpd, self.hotel)

					datastore_communicator.save_multiple_entities('FinalPriceDay', [x.get('key') for x in dirty_fpd], dirty_fpd, hotel_code=self.hotel['applicationId'])

					# hotel_manager_utils.save_entity_multi(self.hotel, 'FinalPriceDay', dirty_fpd, batch_size=2, request_id=self.request_id)

				logging.info("Finished dirty_fpd BATCH %s!", batch_count)
				batch_count += 1


			logging.info('Request_ID: %s, DIRTY RoomTypeStatus entities to save: %d', self.request_id, len(all_dirty_rts))

			dirty_rts_batches = chunks(all_dirty_rts, MAX_ENTITIES_IN_GAE_SINGLE_CALL)
			batch_count = 1
			for dirty_rts in dirty_rts_batches:
				total_batch =  len(dirty_rts)
				logging.info("dirty_rts Procecessin batch %s. Len of batch: %s", batch_count, total_batch)
				rts_ids = _build_ids_from_cache_entities(dirty_rts)

				try:
					# client = datastore_rest_api_client.Client(project_id=project_id, namespace=namespace)
					# client.upsert_multi(dirty_rts, batch_size=200)

					datastore_communicator.save_multiple_entities('RoomTypeStatus', rts_ids, dirty_rts, hotel_code=self.hotel['applicationId'])

				except:
					batch_size = 100
					logging.warning("Error trying to use Datastore API to save RoomTypeStatus for: %s, retrying with batch size %s", self.hotel['applicationId'], batch_size)

					# Note that something might have failed in the middle, so some entities might have been created in the way
					# hotel_manager_utils.save_entity_multi(self.hotel, 'RoomTypeStatus', dirty_rts, request_id=self.request_id)
					try:
						datastore_communicator.save_multiple_entities('RoomTypeStatus', rts_ids, dirty_rts, hotel_code=self.hotel['applicationId'], batch_size=batch_size)

					except:
						batch_size = 25
						logging.warning("Error trying to use Datastore API to save RoomTypeStatus for: %s, retrying with batch size %s",self.hotel['applicationId'], batch_size)
						try:
							datastore_communicator.save_multiple_entities('RoomTypeStatus', rts_ids, dirty_rts,hotel_code=self.hotel['applicationId'],batch_size=batch_size)

						except:
							batch_size = 10
							logging.exception("Error trying to use Datastore API to save RoomTypeStatus for: %s, retrying with batch size %s",self.hotel['applicationId'], batch_size)
							try:
								datastore_communicator.save_multiple_entities('RoomTypeStatus', rts_ids, dirty_rts,hotel_code=self.hotel['applicationId'],batch_size=batch_size)

							except:
								logging.exception("Error trying to use Datastore API to save RoomTypeStatus for: %s, Finshed retries with batch size %s",self.hotel['applicationId'], batch_size)

				logging.info("Finished dirty_rts BATCH %s!", batch_count)
				batch_count += 1

		if has_fpd_changes:
			refresh_entity_timestamp(self.hotel['applicationId'], 'FinalPriceDay')

		if has_rts_changes:
			refresh_entity_timestamp(self.hotel['applicationId'], 'RoomTypeStatus')

		logging.info("Finished ALL dirty_rts and dirty_fpd BATCHES")

def _build_ids_from_cache_entities(entities):
	result = []
	for entity in entities:
		if entity.get('key'):
			result.append(entity.get('key').id)
		else:
			result.append(0)

	return result


def double_check_fpd(fpds_to_add, hotel):
	'''
	Receives a list of FinalPriceDay and verifies that they really don't exist
	'''

	for fpd in fpds_to_add:

		# We ignore old tests
		if fpd.get('date') < '2020':
			continue

		# entities = rest_client.get(hotel['url'] + "/rest/", "FinalPriceDay", "?feq_date=%s&feq_roomKey=%s" % (fpd.date, fpd.roomKey))

		entities = datastore_communicator.get_using_entity_and_params('FinalPriceDay', [('date', '=', fpd.get('date')),
		                                                                                ('roomKey', '=',
		                                                                                 fpd.get('roomKey'))],
		                                                              hotel_code=hotel['applicationId'])

		if entities:

			notifyExceptionByEmail(Exception("Duplicated FinalPriceDay"),
			                       "TO BE REVIEWED, Found duplicated at: %s, for: %s %s" % (
			                       hotel['applicationId'], fpd.get('date'), fpd.get('roomKey')))

			message = make_traceback()
			send_notification_to_bug_seeker(BUGSEEKER_DUPLICATED_FINALPRICEDAY, message)

			# We need to merge the entities
			current_content = json.loads(entities[0]['content'])
			new_content = fpd.get('content')

			for k, v in new_content.items():

				# If it is a completetly new entity we just copy it
				if k not in current_content:
					current_content[k] = v
					continue

				# Else, we add data
				for i, value in enumerate(v):

					if not value:
						continue

					if value != current_content[k][i]:
						current_content[k][i] = value

			# We set it in the new entity that will be updated after this
			fpd['content'] = current_content
			fpd['key'] = entities[0]['key']
