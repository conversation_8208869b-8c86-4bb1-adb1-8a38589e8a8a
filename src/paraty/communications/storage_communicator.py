from io import BytesIO

from PIL import Image
from google.cloud import storage
from paraty import Config


def _get_storage_client():

	storage_client = storage.Client()
	return storage_client



def _get_image_frames_duration(image_obj):
	""" Returns the average framerate of a PIL Image object """
	durations = []
	for i in range(image_obj.n_frames):
		image_obj.seek(i)
		durations.append(image_obj.info.get('duration', 0))
	return durations


#See Webspped-seeker paraty.api.images_api.save_image_to_bytes_io
def save_image_to_bytes_io(pil_image):
	img_byte_array = BytesIO()

	if hasattr(pil_image, 'is_animated') and pil_image.is_animated:
		duration = _get_image_frames_duration(pil_image)
		pil_image.save(img_byte_array, save_all=True, format=pil_image.format, duration=duration, optimize=False)

	else:
		pil_image.save(img_byte_array, format=pil_image.format, quality=95)

	return img_byte_array


def send_image_to_storage(file_name, file_object, content_type_to_use=None):

	storage_client = _get_storage_client()

	bucket = storage_client.bucket(Config.IMAGES_BUCKET)

	thumbnail_blob = bucket.blob(file_name)

	thumbnail_blob.cache_control = 'max-age=31536000'

	if not content_type_to_use:
		content_type_to_use = file_object.content_type

	thumbnail_blob.upload_from_file(file_object, content_type=content_type_to_use)
	thumbnail_blob.make_public()

	return thumbnail_blob.public_url

