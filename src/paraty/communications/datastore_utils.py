import array
import base64

from google.cloud.datastore import Key

from paraty.communications.legacy_protocol_buffer import Decoder
from paraty_commons_3.datastore.datastore_utils import get_location_prefix

DEFAULT_AUTHORIZATION = "Basic cGFibDA6bWF0aGUxcw=="


def id_to_entity_key(hotel_code, entity_key):
	return entity_key.to_legacy_urlsafe(location_prefix=get_location_prefix(hotel_code)).decode('utf-8')

def legacy_key_to_id(legacy_key):
	my_key = Key.from_legacy_urlsafe(legacy_key)
	return my_key.id


def __decode_alphanumeric_key(alphanumericKey):
	modulo = len(alphanumericKey) % 4
	if modulo != 0:
		alphanumericKey += ('=' * (4 - modulo))

	_str = str(alphanumericKey)
	encoded_pb = base64.urlsafe_b64decode(_str)
	a = array.array('B')
	a.fromstring(encoded_pb)
	d = Decoder(a, 0, len(a))
	data = __decode_data(d)
	return data


def __decode_data(d):
	output = {}
	while d.avail() > 0:
		tt = d.getVarInt32()
		if tt == 114:
			length = d.getVarInt32()
			tmp = Decoder(d.buffer(), d.pos(), d.pos() + length)
			d.skip(length)

			while tmp.avail() > 0:
				tt2 = tmp.getVarInt32()
				if tt2 == 11:
					while 1:
						tt3 = tmp.getVarInt32()
						if tt3 == 12: break
						if tt3 == 18:
							output['type'] = tmp.getPrefixedString()
							continue
						if tt3 == 24:
							output['id'] = tmp.getVarInt64()
							continue
						if tt3 == 34:
							output['name'] = tmp.getPrefixedString()
							continue

						tmp.skipData(tt3)

			continue

		if tt == 106:
			output['app'] = d.getPrefixedString()
			continue

		if tt == 162:
			output['namespace'] = d.getPrefixedString()
			continue

		if tt == 186:
			output['database'] = d.getPrefixedString()
			continue

		d.skipData(tt)

	return output


if __name__ == '__main__':
	# result = get_location_prefix('marinas-de-nerja')
	# print(result)

	print(legacy_key_to_id('agxzfnRlc3QtaG90ZWxyDgsSB1BpY3R1cmUYhgEM'))

