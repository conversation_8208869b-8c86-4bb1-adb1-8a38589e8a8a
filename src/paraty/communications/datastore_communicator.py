import logging

from cachetools import TTLCache
from google.auth import compute_engine
from google.cloud import datastore
from google.cloud.datastore import Entity

from paraty import Config, app
from paraty.model import excluded_from_index
from paraty.utilities import manager_utils


def build_key(entity_name, id, hotel_code=None):
	my_client = _get_datastore_client(hotel_code)

	if id:
		new_key = my_client.key(entity_name, id)
	else:
		new_key = my_client.key(entity_name)

	return new_key


def save_to_datastore(entity_name, id, properties, hotel_code=None):

	new_key = build_key(entity_name, id, hotel_code)

	exclude_from_indexes = excluded_from_index.get_excluded_from_index(entity_name)

	new_entity = Entity(key=new_key, exclude_from_indexes=exclude_from_indexes)

	new_entity.update(properties)

	my_client = _get_datastore_client(hotel_code)
	my_client.put(new_entity)

	return new_entity.key


def save_multiple_entities(entity_name, ids, entities_properties, hotel_code=None):

	exclude_from_indexes = excluded_from_index.get_excluded_from_index(entity_name)

	entities = []

	for i, currenty_properties in enumerate(entities_properties):
		new_key = build_key(entity_name, ids[i], hotel_code)
		new_entity = Entity(key=new_key, exclude_from_indexes=exclude_from_indexes)
		new_entity.update(currenty_properties)

		entities.append(new_entity)

	my_client = _get_datastore_client(hotel_code)
	my_client.put_multi(entities)

	return [x.key.id for x in entities]


def save_entity(new_entity, hotel_code=None):

	my_client = _get_datastore_client(hotel_code=hotel_code)
	return my_client.put(new_entity)


def get_using_entity_and_params(entity_name, search_params=[], keys_only=False, projections=None, hotel_code=None):
	'''
	i.e.
	get_using_entity_and_params('UserModel', [('timestamp','>', '2019')], keys_only=False, projections=None)

	Returns an iterator
	'''

	# logging.info("get_using_entity_and_params: %s, %s", entity_name, search_params)

	client = _get_datastore_client(hotel_code)

	query = client.query(kind=entity_name)

	if projections:
		query.projection = projections

	for current_param in search_params:

		query.add_filter(current_param[0], current_param[1], current_param[2])

	if keys_only:
		query.keys_only()

	return query.fetch()


def delete_entity(class_name, entity_id, hotel_code=None):

	client = _get_datastore_client(hotel_code=hotel_code)

	my_key = client.key(class_name, entity_id)

	client.delete(my_key)


def get_entity(class_name, entity_id, hotel_code=None):

	client = _get_datastore_client(hotel_code=hotel_code)

	my_key = client.key(class_name, entity_id)

	# The kind for the new entity
	entity = client.get(my_key)

	return entity


#Based on this, the tokens last up to 1 hour: https://cloud.google.com/iam/docs/creating-short-lived-service-account-credentials
cache = TTLCache(maxsize=100, ttl=1800)

def _get_datastore_client(hotel_code=None, force_renew_client=False):

	#Current project by default
	if not hotel_code:
		project = Config.PROJECT
		namespace = Config.NAMESPACE

	#Hotel Manager entities
	elif hotel_code == 'admin-hotel':
		project = 'admin-hotel'
		namespace = ''

	else:
		project, namespace = manager_utils.get_hotel_project_and_namespace(hotel_code)

	cache_key = '%s_%s' % (project, namespace)
	if not force_renew_client and cache_key in cache:
		return cache[cache_key]

	if app.config['DEV']:
		new_client = datastore.Client(project=project, namespace=namespace)
	else:
		credentials = compute_engine.Credentials()
		new_client = datastore.Client(project=project, namespace=namespace, credentials=credentials)

	cache[cache_key] = new_client
	return new_client