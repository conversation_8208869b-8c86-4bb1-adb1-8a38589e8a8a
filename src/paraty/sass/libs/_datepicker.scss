#ui-datepicker-div {
  display: none;
  z-index: 9999 !important;
  background: white;
  box-shadow: 0 0 15px rgba(0,0,0,.15);
  border-radius: 5px;
  .ui-widget-header {
    position: relative;
    background: $corporate;
    padding: 15px 10px 10px;
    text-align: center;
    color: white;
    border-radius: 5px 5px 0 0;
    .ui-corner-all {
      @include center_y;
      font-size: 0;
      padding: 10px;
      cursor: pointer;
      &:before {
        font-size: 30px;
        color: white;
        font-family: "Font Awesome 5 Pro", sans-serif;
        font-weight: 300;
        color: white;
      }
      &.ui-datepicker-prev {
        left:0;
        &:before {
          content: '\f104';
        }
      }
      &.ui-datepicker-next {
        right: 0;
        &:before {
          content: '\f105';
        }
      }
    }
    .ui-datepicker-title {
      text-transform: uppercase;
      .ui-datepicker-year {
        font-weight: 700;
      }
    }
  }
  .ui-datepicker-calendar {
    border-spacing: 5px;
    border-collapse: collapse;
    th, td {
      padding: 5px;
      border: 0;
      margin: 0;
      text-align: center;
      a {
        display: block;
        padding: 5px;
        color: $color_text;
        @include hover_1;
        &:hover {
          text-decoration: none;
        }
        &.ui-state-active {
          background: $orange;
          color: white !important;
        }
      }
      &.ui-datepicker-week-end {
        a {
          background-color: $lightgrey;
        }
      }
      &.ui-state-disabled {
        span {
          color: $grey;
        }
      }
    }
    tbody tr {
      border-top: 1px solid lighten($grey, 15%);
      &:first-of-type {
        border-top-width: 0;
      }
    }
    thead {
      color: $grey;
      tr th {
        padding: 5px;
      }
    }
  }
}