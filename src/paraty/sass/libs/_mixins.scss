
@mixin transition($prop: all, $time: 1s) {
  -webkit-transition: $prop $time;
  -moz-transition: $prop $time;
  -ms-transition: $prop $time;
  -o-transition: $prop $time;
  transition: $prop $time;
}

@mixin translate($x: 0, $y: 0) {
  -webkit-transform: translate($x, $y);
  -moz-transform: translate($x, $y);
  -ms-transform: translate($x, $y);
  -o-transform: translate($x, $y);
  transform: translate($x, $y);
}

@mixin transform($transform) {
  -webkit-transform: $transform;
  -moz-transform: $transform;
  -ms-transform: $transform;
  -o-transform: $transform;
  transform: $transform;
}

@mixin center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  @include translate(-50%, -50%);
}
.center_xy {
  @include center_xy;
}

@mixin center_x {
  position: absolute;
  left: 50%;
  @include translate(-50%, 0%);
}
.center_x {
  @include center_x;
}

@mixin center_y {
  position: absolute;
  top: 50%;
  @include translate(0%, -50%);
}
.center_y {
  @include center_y;
}

@mixin center_image {
  @include center_xy;
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
.center_image {
  @include center_image;
}

@mixin full_size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

@mixin hover_1 {
  position: relative;
  z-index: 2;
  &:before {
    content: '';
    @include center_xy;
    z-index: -1;
    width: 100%;
    height: 0;
    background: rgba(0,0,0,0);
    @include transition(height, .6s);
  }
  &:hover, &.active {
    &:before {
      height: 100%;
      background: rgba(0,0,0,0.15);
    }
  }
}