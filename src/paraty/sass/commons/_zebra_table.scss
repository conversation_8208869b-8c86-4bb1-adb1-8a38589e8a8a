.zebra_table {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
    &.loading {
        position: relative;
        overflow: hidden;
        &:before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100px;
            height: 200%;
            transform: skew(15deg) translateY(-50%);
            background: linear-gradient(to right, white, $lightgrey, white);
            -webkit-animation: background-loading 5s linear infinite;
            animation: background-loading 5s linear infinite;
        }
    }
    tr {
        @include transition(background, .6s);
        &:nth-child(odd) {
            background: $lightgrey;
        }
        &:nth-child(even) {
            background: white;
        }
        &.changing {
            background: $gray !important;
            @include transition(background, 1s);
        }
        &.selected {
            background: $gray;
            &:nth-child(odd) {
                background: darken($gray, 5%);
            }
            .sort {
                background: $blue;
                color: white;
            }
        }
        &.tr-placeholder {
            background: rgba($corporate, .5);
            height: 70px;
        }
        th {
            font-weight: bold;
            background: white;
            border-bottom: 1px solid #DDD;
            margin: 0;
            padding: 10px 0;
            position: sticky;
            top: 0;
            z-index: 50;
        }
        td {
            text-align: center;
            margin: 0;
            padding: 10px 0;
            strong {
                color: $corporate;
            }
            .pic {
                width: 60px;
                height: 50px;
                img {
                    max-width: 100%;
                    max-height: 100%;
                }
            }
            .crop {
                text-align: left;
                width: 100px;
                min-width: 100%;
                max-height: 70px;
                overflow: hidden;
                word-break: break-word;
                padding: 0 10px;
            }
            .editable {
                input:not([type=checkbox]) {
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    appearance: none;
                    background: transparent;
                    border-color: transparent;
                    text-align: center;
                    &:focus {
                        border-color: $corporate;
                        background: white;
                    }
                }
            }
            &.sort {
                padding: 0 5px;
                color: $grey;
            }
            &.loading {
                height: 40px;
                position: relative;
                &:before {
                    content: '';
                    @include center_xy;
                    width: 80%;
                    height: 15px;
                    background: $gray;
                }
            }
            &.loading_opt {
                height: 40px;
                position: relative;
                &:before {
                    content: '';
                    @include center_xy;
                    width: 80%;
                    height: 15px;
                    background: linear-gradient(to right,
                                    $gray, $gray 20px,
                                    transparent 20px, transparent 40px,
                                    $gray 40px, $gray 60px,
                                    transparent 60px, transparent,
                                    $gray 40px, $gray 60px,
                                    transparent 60px, transparent 80px,
                                    $gray 80px, $gray 100px,
                                    transparent 100px, transparent);
                    background-size: 120px;
                    background-repeat: no-repeat;
                    background-position: center center;
                }
            }
        }
        .circle {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #CCC;
            &.ok {
                background: $green;
            }
            &.ko {
                background: $red;
            }
            &.kok {
                background: $garkgrey;
            }
            &.pending {
                background: $orange;
            }
        }

        input[type=checkbox] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: #DDD;
            width: 22px;
            height: 12px;
            border-radius: 5px;
            position: relative;
            outline: none;
            cursor: pointer;
            &:before {
                content: '';
                display: block;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background: $red;
                position: absolute;
                top: 1px;
                left: 1px;
                @include transition(all, .6s);
            }
            &:checked {
                &:before {
                    background: $green;
                    left: auto;
                    right: 1px;
                }
            }
        }
    }
}
.zebra_table_toggle {
    cursor: pointer;
    padding: 10px 0;
    color: $corporate;
}
.zebra_table_restore {
    display: none;
}
@media (max-width: 850px) {
  body {
      .md-hide {
          display: none;
      }
  }
}
@media (max-width: 600px) {
  body {
      .sm-hide {
          display: none;
      }
      .zebra_table_toggle {
          font-size:30px;
      }
  }
}