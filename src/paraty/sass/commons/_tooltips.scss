.toolkit {
  position: relative;
  .tooltip {
    position: absolute;
    opacity: 0;
    display: inline-block;
    padding: 2px 8px;
    font-size: 12px;
    white-space: nowrap;
    border-radius: 3px;
    color: white;
    background: $garkgrey;
    border: 1px solid $garkgrey;
    z-index: -1;
    top: 100%;
    @include center_x;
    @include transition(all,.3s);
  }
  &:hover {
    .tooltip {
      display: block;
      opacity: 1;
      margin-top: 4px;
      z-index: 1001;
    }
  }
  &.top {
    .tooltip {
      bottom: 100%;
      @include center_x;
    }
  }
  &.top-left {
    .tooltip {
      top: auto;
      bottom: 100%;
      left: auto;
      right: 0;
      transform: translate(0,0);
    }
  }
  &.top-right {
    .tooltip {
      bottom: 100%;
      right: 0;
      left: auto;
    }
  }
  &.bottom {
    .tooltip {
      top: 100%;
      @include center_x;
    }
  }
  &.bottom-left {
    .tooltip {
      top: 100%;
      left: 0;
    }
  }
  &.bottom-right .tooltip {
      top: 100%;
      right: 0;
      left: auto;
  }
  &.tooltip-transparent .tooltip {
    background: transparent;
  }
  &.tooltip-info .tooltip {
    background: linear-gradient(to bottom, var(--info-light), var(--info));
    border-color: var(--info);
  }
  &.tooltip-warning .tooltip {
    background: linear-gradient(to bottom, var(--warn-light), var(--warn));
    border-color: var(--warn);
    color: var(--primary)
  }
  &.tooltip-danger .tooltip {
    background: linear-gradient(to bottom, var(--danger-light), var(--danger));
    border-color: var(--danger);
  }
  &.tooltip-success .tooltip {
    background: linear-gradient(to bottom, var(--success-light), var(--success));
    border-color: var(--success);
  }
}