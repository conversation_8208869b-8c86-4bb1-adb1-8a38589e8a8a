body {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: $black;
  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    font-family: $text_family;
    letter-spacing: 0.4px;
  }
  .page {
    width: 1040px;
    max-width: 95%;
    margin: 50px auto;
  }
  .cols {
    .col1, .col2, .col3, .col4, .col5 {
      display: inline-block;
      vertical-align: top;
    }
    .col1 {
      width: 100%;
    }
    .col2 {
      width: calc(50% - 3px);
    }
    .col3 {
      width: calc((100% / 3) - 3px);
    }
  }
  .list_table {
    border: 1px solid $gray;
    .table_title {
      font-size: 16px;
      padding: 10px;
      border-bottom: 1px solid $gray;
      color: $corporate;
    }
    .element {
      font-size: 12px;
      padding: 10px;
      border-top: 1px solid $gray;
    }
  }
  h1, h2, h3, h4, h5, h6, p {
    margin: 0;
  }
  h1 {
    font-size: 40px;
  }
  h2 {
    font-size: 35px;
    font-weight: normal;
  }
  h3 {
    font-size: 28px;
  }
  h4 {
    font-size: 24px;
    font-weight: normal;
  }
  h5 {
    font-size: 20px;
  }
  h6 {
    font-size: 20px;
    font-weight: normal;
  }
  ul {
    padding: 0;
    li {
      list-style: none;
    }
  }
  a {
    text-decoration: none;
    color: $corporate;
    &:hover {
      text-decoration: underline;
    }
  }

  .title{
    color: $corporate;
    text-align: center;
    padding: 10px;
  }
  .warning{
    color: $yellow;
  }
}

@media (max-width: 850px) {
  body {
    .cols {
      .col2, .col3, .col4, .col5 {
        display: block;
        width: 95%;
        margin: 5px auto;
      }
    }
    .md-break {
      display: block;
      margin: 5px 0;
    }
  }
}
