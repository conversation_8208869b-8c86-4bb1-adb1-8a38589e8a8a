.message {
  display: block;
  padding: 7px 14px;
  background: $lightgrey;
  border-radius: 3px;
  margin: 5px auto;
  &.m_ok {
    color: $green;
  }
  &.m_ko {
    color: $yellow;
  }
}
.input_wrapper {
  padding: 10px 5px;
  &.input_inline {
    display: inline-block;
    vertical-align: middle;
    label {
      display: inline-block;
      vertical-align: middle;
      padding: 5px 10px 5px 0;
    }
    .input {
      display: inline-block;
      vertical-align: middle;
      & + label {
        padding-left: 10px;
      }
    }
    .input:not(.checkbox) {
      width: 200px;
      &.input_small {
        width: 100px;
      }
    }
    &.input_center {
      text-align: center;
      width: 100%;
    }
    .btn {
      margin: 0 3px;
    }
  }
  &.input_inline_checkbox {
    position: relative;
    label {
      display: block;
      margin-left: 30px;
      padding: 0;
    }
    .input {
      @include center_y;
      left: 0;
      input[type=checkbox] {

      }
    }
  }
  label {
    display: block;
    padding-bottom: 5px;
    font-size: 14px;
    color: $corporate;
  }
  .input {
    position: relative;
    &.input_ok {
      input, input:focus {
        border-color: $green;
      }
      &:after {
        content: '\f058';
        font-family: "Font Awesome 5 Pro", sans-serif;
        font-weight: 900;
        @include center_y;
        right: 5px;
        color: $green;
      }
    }
    &.input_ko {
      input, input:focus {
        border-color: $red;
      }
      &:after {
        content: '\f06a';
        font-family: "Font Awesome 5 Pro", sans-serif;
        font-weight: 900;
        @include center_y;
        right: 5px;
        color: $red;
      }
    }
    &.select {
      position: relative;
      &:after {
        content: '\f107';
        font-family: "Font Awesome 5 Pro", sans-serif;
        font-weight: 900;
        @include center_y;
        right: 5px;
        color: $corporate;
      }
      select {
        position: relative;
        z-index: 2;
      }
      .select2.select2-container {
        width: 100% !important;
        .select2-selection {
          border-color: $gray;
          .select2-selection__rendered {
            .select2-selection__choice {
              border-color: $gray;
              background: $lightgrey;
              padding: 3px 5px;
              margin: 5px 5px 3px 0;
              .select2-selection__choice__remove {
                float: right;
                margin-left: 5px;
              }
            }
          }
        }
      }
    }
    &.textarea {
      .extra_buttons {
        top: 7px;
        -webkit-transform: unset;
        -moz-transform: unset;
        -ms-transform: unset;
        -o-transform: unset;
        transform: unset;
      }
    }
    &.search {
      position: relative;
      &:after {
        content: '\f002';
        font-family: "Font Awesome 5 Pro", sans-serif;
        font-weight: 300;
        font-size: 20px;
        @include center_y;
        right: 5px;
        color: $corporate;
      }
      input {
        position: relative;
        z-index: 2;
      }
    }
    &.switch {
      input[type=checkbox] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: $gray;
        border-width: 0;
        width: 22px;
        height: 12px;
        border-radius: 8px;
        position: relative;
        outline: none;
        cursor: pointer;
        &:before {
            content: '';
            display: block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: $red;
            position: absolute;
            top: 2px;
            left: 3px;
            @include transition(all, .6s);
        }
        &:checked {
            &:before {
                content: '';
                background: $green;
                left: auto;
                right: 3px;
            }
        }
      }
    }
    &.checkbox:not(.switch) {
      display: inline-block;
      vertical-align: middle;
      white-space: nowrap;
      input[type=checkbox] {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border: 1px solid $grey;
        border-radius: 0;
        width: 16px;
        height: 16px;
        padding: 0;
        margin: 0 0 0 10px;
        outline: none;
        &:checked {
          &:before {
            content: '\f00c';
            font-family: "Font Awesome 5 Pro", sans-serif;
            font-weight: 900;
            color: $blue;
            font-size: 10px;
            line-height: 15px;
            @include center_xy;
          }
        }
        &:hover {
          border-color:$blue;
        }
        & + span {
          display: inline-block;
          vertical-align: middle;
          width: calc(100% - 45px);
          margin-left: 10px;
        }
      }
    }
    &.hasDatepicker {
      position: relative;
      i {
        @include center_y;
        right: 5px;
        color: $corporate;
      }
    }
    .extra_buttons {
      @include center_y;
      right: 7px;
      i {
        color: $corporate;
        cursor: pointer;
        margin: 0 0 0 5px;
      }
    }
    input, select, textarea {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      width: 100%;
      font-size: 14px;
      background: transparent;
      border-radius: 3px;
      border: 1px solid $gray;
      padding: 7px 14px;
      outline: none;
      &:focus {
        border-color: $corporate;
      }
    }
    textarea {
      width: 100%;
      height: 100px;
      resize: vertical;
    }
    select {
      padding-right: 30px;
    }
  }
  .separator {
    display: block;
    height: 1px;
    width: 100%;
    background: $gray;
  }
}
.row {
  position: relative;
  display: table;
  width: 100%;
  &.row_to_copy {
    display: none;
  }
  .delete_row {
    @include center_y;
    right: 10px;
    color: $red;
    cursor: pointer;
  }
}
.btn_wrapper {
  padding: 10px 5px;
  text-align: center;
  .btn {
    width: 100%;
  }
}
.btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: $corporate_admin;
  border-color: $corporate_admin;
  border-style: solid;
  color: white;
  padding: 10px 25px;
  border-radius: 3px;
  text-transform: uppercase;
  font-weight: bold;
  letter-spacing: 1px;
  cursor: pointer;
  outline: none;
  @include hover_1;
  &.btn_inline {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    margin-bottom: 10px;
  }
  &.btn_small {
    padding: 5px 10px;
  }
  &.btn_link {
    background: transparent;
    color: $corporate;
    border-width: 0;
  }
  &.btn_disable {
    background: $grey;
    border-color: $gray;
    cursor: not-allowed;
  }
  &.btn_switch {
    background: transparent;
    color: $corporate;
    &.active {
      background: $corporate;
      color: white;
    }
  }
  &.btn_loading {
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;
    color: transparent;
    font-size: 0;
    overflow: hidden;
    &:after {
      display: block;
      color: white;
      content: '\f1ce';
      font-size: 18px;
      font-family: "Font Awesome 5 Pro", sans-serif;
      font-weight: 900;
      -webkit-animation: btn-spin 2s linear infinite;
      animation: btn-spin 2s linear infinite;
    }
  }
}

@-webkit-keyframes btn-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}
@keyframes btn-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}
@-webkit-keyframes background-loading {
    0% {left: -10%;}
    50% {left: 100%;}
    to {left: -10%;}
}
@keyframes background-loading {
    0% {left: -10%;}
    50% {left: 100%;}
    to {left: -10%;}
}

