.modal {
  display: none;
  @include full_size;
  z-index: 1000;
  position: fixed;
  background: rgba($gray,.8);
  &#delete_modal {
    .modal_title {
      border-bottom-color: $red;
      color: $red;
    }
    .btn {
      background: $red;
      border-color: $red;
      &.btn_link {
        background: transparent;
        color: $red;
      }
    }
  }
  .modal_content {
    @include center_xy;
    background: white;
    min-width: 600px;
    max-width: 80%;
    max-height: 95vh;
    overflow: auto;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0,0,0,.15);
    .close {
      position: sticky;
      top: -10px;
      z-index: 100;
      float: right;
      width: 20px;
      height: 20px;
      margin-top: -10px;
      margin-right: -10px;
      cursor: pointer;
      i {
        font-size: 20px;
      }
    }
    .modal_title {
      position: sticky;
      top: -20px;
      z-index: 20;
      background: $lightgrey;
      border-bottom: 1px solid $corporate;
      color: $corporate;
      font-weight: bold;
      margin: -20px -20px 0;
      padding: 10px;
    }
    .modal_desc {
      padding: 10px;
    }
    .modal_pic {
      position: relative;
      width: 100%;
      height: 150px;
      text-align: center;
      overflow: hidden;
      border: 1px solid $lightgrey;
      &:hover {
        &:after {
          opacity: 1;
        }
        .updload {
          opacity: 1;
          i {
            margin-top: 0;
          }
        }
      }
      &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 1000px;
        height: 1000px;
        @include transform(translate(-50%,-50%) rotate(45deg));
        z-index: 0;
        background: linear-gradient(-45deg,
                      $lightgrey, $lightgrey 25%,
                      transparent 25%, transparent 50%,
                      transparent 50%, transparent 75%,
                      $lightgrey 75%, $lightgrey),
                  linear-gradient(45deg,
                      $lightgrey, $lightgrey 25%,
                      transparent 25%, transparent 50%,
                      transparent 50%, transparent 75%,
                      $lightgrey 75%, $lightgrey);
      background-size: 14px 14px;
      }
      &:after {
        content: '';
        @include full_size();
        opacity: 0;
        z-index: 5;
        background: rgba($black,.8);
        @include transition(all, .6s);
      }
      .updload {
        opacity: 0;
        cursor: pointer;
        @include center_xy;
        z-index: 10;
        @include transition(all, .6s);
        i {
          margin-top: 20px;
          font-size: 70px;
          color: white;@include transition(all, .6s);
        }
      }
      img {
        vertical-align: middle;
        max-width: 100%;
        max-height: 100%;
        @include center_xy;
        z-index: 2;
      }
    }
  }
}
.tabs {
  background: $lightgrey;
  padding: 10px 10px 0;
  &.tabs-transparent {
    background: transparent;
    border-bottom: 2px solid $gray;
    .tab {
      border-width: 2px 2px 0 2px;
      border-style: solid;
      border-color: $gray;
      &.active {
        margin-bottom: -2px;
      }
    }
  }
  .tab {
    display: inline-block;
    vertical-align: bottom;
    background: $gray;
    margin-right: 5px;
    padding: 5px 10px;
    border-radius: 5px 5px 0 0;
    cursor: pointer;
    &:hover {
        background: $grey;
    }
    &.active {
        background: white;
    }
    &.tab-big {
      padding: 10px 20px;
      margin-right: 10px;
      font-size: 16px;
      color: $corporate;
    }
    i{ margin-right: 8px }

  }
}
.tab_content_wrapper {
  .tab_content {
    display: none;
    &:first-of-type {
      display: block;
    }
  }
}

.my_confirm{
    width: 50%;
    margin: auto;
}

@media (max-width: 600px) {
  body {
      .modal {
          .modal_content {
            min-width: auto;
            width: 100%;
          }
      }
  }
}