@import "commons/colors";


.popup_info_wrapper {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100vh;
  background-size: 100% 100%;
  background-color: $garkgrey;
  border-radius: 10px;
  z-index: 52;
  display: block;
  .modal_info_content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 90%;
    border-radius: 10px;
    background-color: white;
    display: flex;

    z-index: 53;
    overflow: scroll;
    border: transparent;
    .content_page{
      position: fixed;
    }
  }
}


.popup_confirmation_wrapper {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100vh;
  background-size: 100% 100%;
  background-color: rgba(0,0,0,0.5);
  z-index: 52;
  display: block;
  .confirmation_info_content {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 30%;
    height: fit-content;
    background-color: #fafafa;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    font-family: "Roboto", sans-serif;
    letter-spacing: 0.4px;
    font-size: 24px;
    padding: 20px;
  }
}

#successful_payment{
  box-shadow: 0 0 20px rgba(53, 168, 83, 0.5);
}
#wrong_payment{
  box-shadow: 0 0 20px rgb(220, 17, 17);
}

.extra_payed{
  color: $corporate_admin;
}

