@import "commons/colors";


.showed_modal_info {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100vh;
  background-size: 100% 100%;
  background-color: $garkgrey;
  border-radius: 10px;
  z-index: 52;
  display: block;
  .modal_info_content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 90%;
    border-radius: 10px;
    background-color: white;
    display: flex;

    z-index: 53;
    overflow: scroll;
    border: transparent;
    .content_page{
      position: fixed;
    }
  }
}