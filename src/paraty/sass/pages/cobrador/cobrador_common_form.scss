@import "commons/colors";
@import "libs/mixins";

@import "commons/modals";
@import "commons/forms";
@import "commons/tooltips";
@import "commons/zebra_table";

$color_text: $black;
@import "libs/datepicker";

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system,BlinkMacSystemFont,segoe ui,Roboto,Helvetica,Arial,sans-serif,apple color emoji,segoe ui emoji,segoe ui symbol;
  font-size: 12px;
  * {
    box-sizing: border-box;
  }
}
a.btn{
    display: inline-block;
    border-width: 2px;
}
a.btn:hover{
     text-decoration: none;
}
.confirmations_text{
    color: $green;
}
.pay_status_color_ok{
    color: $green!important;
}
.pay_status_color_oko{
   color: $orange!important;
}
.pay_status_color_ko{
   color: $red!important;
}
.search_filter_text{
    color: $orange;
}
th.short_td{
    width: 100px;
}
.corporative_color{
    color: $corporate;
}

.form_message_wrapper {
  position: fixed;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  z-index: 1002;
  border-radius: 8px;
  background: white;
  border: 1px solid $lightgrey;
  text-align: center;
  padding: 20px;
  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    color: $corporate;
  }
  p {
    margin: 0;
    text-align: left
  }
  img {
    display: inline-block;
    vertical-align: middle;
    margin: 4px auto;
  }
}
