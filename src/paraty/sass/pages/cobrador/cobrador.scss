@import "commons/colors";
@import "libs/mixins";

@import "commons/modals";
@import "commons/forms";
@import "commons/zebra_table";

$color_text: $black;
@import "libs/datepicker";


a.btn{
    display: inline-block;
    border-width: 2px;

}
a.btn:hover{
     text-decoration: none;
}

.confirmations_text{
    color: $green;
}
.pay_status_color_ok{
    color: $green!important;
}
.pay_status_color_oko{
   color: $orange!important;
}
.pay_status_color_ko{
   color: $red!important;
}
.search_filter_text{
    color: $orange;
}

th.short_td{
    width: 100px;
}

.corporative_color{
    color: $corporate;
}

.chosen-container
{
    width: 100% !important;
}

html .input .chosen-choices {
  border: 1px solid #ededed;
  border-radius: 3px;
  padding: 7px 14px;
  background-image: none;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: left;

  input {
    height: auto!important;
    color: black!important;
  }

  .search-choice {
    margin: 0!important;
  }
}

body .input_wrapper.class_percentage {
  display: inline-flex;
  margin: auto;
  width: calc(2 * (100% / 3) - 3px);
  align-items: center;
  flex-wrap: wrap;
  column-gap: 8px;

  #label_num_days, #label_percentage, #label_fixed_amount, #type_fixed_amount{
    width: 100%;
  }

  label[for=include_supplements], label[for=fake_tokenizator] {
    display: inline-flex;
    flex-wrap: nowrap;
    align-items: center;
    gap: 5px;
    margin-left: 5px;

    input {
      padding: 0;
      margin: 0;
    }
  }
}

.logical_removed{
  text-decoration:line-through;
  text-decoration-color:red;
}

  .date_filter {
    display: flex !important;
    flex-direction: column !important;
    float: right;
    width: 60%;
    margin: 10px 0;
    .date_filter_label{
      margin-left: 20px;
    }
  }



.date_input_wrapper {
  margin: 1px 0;
  box-sizing: border-box;
  display: inline-flex;
  justify-content: center;

  .icons_wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
    color: $corporate;
  }
}

hr{
  height: 1px;
  background-color: rgba(91, 91, 91, 0.1);
  border: 0;
}

