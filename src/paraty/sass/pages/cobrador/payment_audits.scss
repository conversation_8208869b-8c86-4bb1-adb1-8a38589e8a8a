.inputs_wrapper {
  margin: 20px auto;
}

table{
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  margin-top: 40px;
}
thead{
  border-bottom: solid 1px rgba(0,0,0,0.3);
}


th{
  position: relative;
  width: 20%;
  font-size: 14px;
  text-align: center;
  padding-bottom: 8px;
}

tbody{
  border-bottom: solid 1px rgba(0,0,0,0.3);
}
tr{
  background-color: rgba(215,215,215,0.2);
}

tr:nth-child(odd) {
  background-color: white;

}

td{
  margin-left: 0;
  margin-right: 0;
  width: 20%;
  max-width: 20%;
  padding: 10px;
  text-align: center;
  font-size: 14px;
}

.popup_data {
  display: none;
}

.button_response{
  appearance: none;
  border: 0;
  background-color: transparent;
  cursor: pointer;
  font-size: 18px;
  color: #446ca9;
}

.popup_wrapper {
  display: flex;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  justify-content: center;
  align-items: center;

  .background {
    background-color: rgba(black, .4);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  .popup_body {
    background-color: #fff;
    position: relative;
    z-index: 1;
    max-width: 600px;
    width: 95%;
    padding: 16px;
    border-radius: 4px;
    
    .popup_title {
      font-size: 14px;
      color: #446ca9;
      border-bottom: 1px solid black;
      padding-bottom: 4px;
      margin-bottom: 12px;
    }

    .popup_content {
      overflow: scroll;
      max-height: 300px;
    }
  }
}