@import "commons/colors";
@import "libs/mixins";

body {
  background: $lightgrey;
  .bg-pattern {
    @include full_size;
    position: fixed;
    z-index: -1;
    overflow: hidden;
    opacity: .5;
  }
  svg {
    @include center_xy;
    width: 100%;
    * {
      fill: white;
    }
  }
  .window {
    @include center_xy;
    width: 600px;
    max-width: 90%;
    text-align: center;
    background: white;
    border-radius: 10px;
    padding: 0 20px 40px;
    box-shadow: 0 0 25px rgba(200,200,200,0.3);
    .top_bar {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      background: $gray;
      height: 30px;
      border-radius: 10px 10px 0 0;
      .dots, .dots:before, .dots:after {
        content: '';
        @include center_y;
        left: 10px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background: $lightgrey;
      }
      .dots:before {
        left: 20px;
      }
      .dots:after {
        left: 40px;
      }
    }
    .numbers {
      font-size: 70px;
      font-weight: bolder;
      padding: 40px 0 20px;
      color: $yellow;
    }
    h3 {
      text-transform: uppercase;
      color: #666;
      margin-bottom: 15px;
    }
    p {
      color: #666;
    }
  }
}

@media (max-width: 750px) {
  body {
    .bg-pattern {
      svg {
        width: auto;
        height: 100%;
      }
    }
  }
}