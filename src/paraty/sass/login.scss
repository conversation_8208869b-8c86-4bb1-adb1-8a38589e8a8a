@import "fonts/roboto";
@import "commons/colors";
@import "libs/mixins";

$text_family: "Roboto", sans-serif;

@import "commons/base";
@import "commons/forms";

body {
  background: white;
  .modal_login {
    background: white;
    @include center_xy;
    width: 700px;
    max-width: 90%;
    max-height: 90%;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    .title {
      color: $corporate;
      padding-bottom: 10px;
    }
    .subtitle {
      padding-bottom: 10px;
    }
    #loginForm {
      width: calc(100% - 265px);
      display: inline-block;
      vertical-align: top;
      margin-top: 20px;
    }
    .form_details {
      margin-top: 20px;
      padding: 20px 0 0 20px;
      width: 260px;
      display: inline-block;
      vertical-align: top;
      li {
        font-size: 14px;
        padding-bottom: 5px;
        &.req_ok {
          color: $green;
          text-decoration: line-through;
        }
      }
    }
    &.login_ok {
      text-align: center;
      padding: 40px 150px;
      i {
        color: $green;
        margin-bottom: 20px;
        font-size: 70px;
      }
    }
  }
}


@media (max-width: 600px) {
  body {
    .modal_login {
      padding: 40px 20px;
      .title {
        position: sticky;
        top: -40px;
        background: white;
        z-index: 5;
      }
      #loginForm, .form_details {
        display: block;
        width: 100%;
        padding: 20px 0;
      }
    }
  }
}