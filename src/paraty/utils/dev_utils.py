import subprocess


def get_developer_email(global_config=False):
    project_config = "git config user.email"
    project_global_config = "git config --global user.email"
    target_command = project_config if not global_config else project_global_config
    proc = subprocess.Popen(target_command, stdout=subprocess.PIPE, shell=True)
    (out, err) = proc.communicate()
    return out.decode('utf-8').strip()