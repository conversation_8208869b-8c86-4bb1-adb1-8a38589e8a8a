import requests
import logging

TIMEOUT = 30


PROXY_URL = 'http://192.241.251.186:9999'
PROXY_URL_TLS_1_2 = 'http://35.233.0.182'

# Datatrans data inline
PCI_TOKEN = "PCI_TOKEN"


DATATRANS_SANDBOX_MERCH_ID = '1100006518'
DATATATRANS_SANDBOX_SIGN = '180524170410126559'
DATATRANS_SANDBOX_PROXY_PULL = "https://sandbox.pci-proxy.com/v1/pull"

DATATATRANS_PRODUCTION_SIGN = '180629113427710606'
DATATRANS_PRODUCTION_MERCH_ID = '3000011516'
DATATRANS_PRODUCTION_PROXY_PULL = "https://api.pci-proxy.com/v1/pull"



#DATATRANS_INLINE_MERCH_ID = DATATRANS_SANDBOX_MERCH_ID
#DATATATRANS_SIGN = DATATATRANS_SANDBOX_SIGN
#DATATRANS_PROXY_PULL = DATATRANS_SANDBOX_PROXY_PULL


DATATRANS_INLINE_MERCH_ID = DATATRANS_PRODUCTION_MERCH_ID
DATATATRANS_SIGN = DATATATRANS_PRODUCTION_SIGN
DATATRANS_PROXY_PULL = DATATRANS_PRODUCTION_PROXY_PULL



def post_using_generic_proxy(target_url, body, headers={}, timeout=TIMEOUT):

	headers['targetUrl'] = target_url
	result = requests.post(PROXY_URL, data=body, headers=headers, timeout=timeout)
	return result

def delete_using_generic_proxy(target_url, body, headers={}, timeout=TIMEOUT):
	headers['targetUrl'] = target_url
	result = requests.delete(PROXY_URL, data=body, headers=headers, timeout=timeout)
	return result

def post_using_generic_proxy_tls_1_2(target_url, body, headers={}, timeout=TIMEOUT):

	headers['targetUrl'] = target_url

	result = requests.post(PROXY_URL_TLS_1_2, data=body, headers=headers, timeout=timeout)
	return result

def delete_using_generic_proxy_tls_1_2(target_url, body, headers={}, timeout=TIMEOUT):
	headers['targetUrl'] = target_url
	result = requests.delete(PROXY_URL_TLS_1_2, data=body, headers=headers, timeout=timeout)
	return result

def get_datatrans_sign(cc_alias):

	return DATATATRANS_SIGN

def post_using_datatrans_proxy(body, headers={}, timeout=TIMEOUT) -> requests.Response:

	# Let's create a http session
	session = requests.session()

	#response = session.post("https://www.howsmyssl.com/a/check", "", headers={})

	logging.info("using DATATRANS_PROXY_PULL: %s", DATATRANS_PROXY_PULL)

	# fmatheis, TODO, TEST THIS TO MAKE SURE IT KEEPS WORKING ON python 3.7
	response = session.post(DATATRANS_PROXY_PULL, body.encode("utf-8"), headers=headers)

	return response