import json
import requests
import logging

from paraty.utils import date_utils
import queue
import requests

from paraty.constants import URL_IMPORT_SEARCH_LOG_ENTRY
from paraty_commons_3.date_utils import get_timestamp

USER_ENTRY_BUFFER_SIZE = 50

search_log_entry_queue = queue.Queue()


def flush_queue():
    flushed_elements = []
    while not search_log_entry_queue.empty():
        element = search_log_entry_queue.get()
        flushed_elements.append(element)
        search_log_entry_queue.task_done()  # Signal that the task is done

    return flushed_elements


def add_log_entry(hotel_code, source, result, club_id=None):
    try:
        occupancies = ""
        for room in result['search']['rooms']:
            occupancies += '<br/>rooms: %s-%s-%s' % (room['numAdults'], room['numKids'], room['numBabies'])
        occupancies += '<br/>'

        if len(result['result'][0]) > 0:
            result_status = 'OK'
        else:
            result_status = 'NO_AVAILABILITY'

        # TODO, find out how to discriminate when there is not capacity

        new_audit = {
            "startDate": result['search']['startDate'],
            "endDate": result['search']['endDate'],
            "country": result['search']['countryCode'],
            "occupancy": occupancies,
            "result": result_status,
            "timestamp": get_timestamp(),
            "applicationId": hotel_code,
            "source": source,
            "promocode": result.get('search', {}).get('promoCode', "")
        }

        if club_id:
            new_audit["clubId"] = club_id

        headers = {
            'Content-Type': 'application/json'
        }

        logging.info("Adding to queue search log entry: %s", str(new_audit))

        search_log_entry_queue.put(new_audit)

        if search_log_entry_queue.qsize() >= USER_ENTRY_BUFFER_SIZE:
            flushed_elements = flush_queue()
            body_str = json.dumps(flushed_elements)
            logging.info("flushing queue and send to cloud function import search log entry")
            response = requests.post(URL_IMPORT_SEARCH_LOG_ENTRY, data=body_str, headers=headers, timeout=40)
            logging.info("Response: %s", response.text)

    except Exception:
        logging.warning("Problem adding Search logs: add_log_entry")

