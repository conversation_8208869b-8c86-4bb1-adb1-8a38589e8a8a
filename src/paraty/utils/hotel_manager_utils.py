import datetime
import uuid
import logging

from google.cloud.datastore import Entity

from paraty.utils.queues.queues_utils import defer
from paraty_commons_3.common_data.data_management.rate_periods_utils import get_all_rate_periods
from paraty_commons_3.datastore.datastore_utils import entity_id_to_alphanumeric_key, alphanumeric_to_id

import requests

import interface_to_implement
from model.price_model import BASE_PRICES_CONFIG_PROPERTY, BASE_PRICES_XML_CONFIG_PROPERTY, DISCOUNT_SUPLEMENT_CONFIG, BASE_BOARD_RESTRICTIONS_XML_CONFIG_PROPERTY
from paraty.utils import date_utils
from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, get_rates_of_hotel, get_boards_of_hotel, get_multirates_of_hotel, get_rooms_of_hotel, get_hotel_advance_config_item
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.decorators.retry import retry
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

VIRTUAL_ROOMS_CONFIG_KEY = "virtual rooms"
VIRTUAL_ROOMS_RESTRICTIONS_CONFIG_KEY = "virtual rooms restrictions"

VIRTUAL_MIN_STAYS_RESTRICTION = "Estancias Minimas"
VIRTUAL_MAX_STAYS_RESTRICTION = "Estancias Maximas"
VIRTUAL_DISPO_RESTRICTION = "Disponibilidad"

FILTER_RESERVATION_RATE = "filter reservation by rate"

PRICE_KIDS_FROM_ADULTS = "price for kids from adults"

ADD_SERVICES_AMOUNT_IN_DAILY_ROOMS = "add services amount in daily rooms"

#if populate, we are going to send the amount by day, and by quantity
SERVICE_DAYS_AS_QUANTITY = "service days as quantity"

BASE_PRICE_FOR_ADULTS_AS_MAX = "base price for adults as max"

USE_ONLY_CONFIGURED_ROOMS = "show only configured rooms"

SHOW_VIRTUAL_ROOMS_CAPACITY = 'Virtual Rooms'
DISCARD_BABIES_VIRTUAL_ROOMS_CAPACITY = 'Discard babies in Virtual Rooms'

ADMIN_HOTEL_URL = 'https://admin-hotel.appspot.com/'
HOTEL_MANAGER_DATE_FORMAT = '%d/%m/%Y'

FILTER_MAP_ROOMS_KEY = "filter map rooms"
FILTER_MAP_RATES_KEY = "filter map rates"
FILTER_MAP_BOARDS_KEY = "filter map boards"

ISO_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S.%f'
ISO_DATE_FORMAT = '%Y-%m-%d'
RATE_MAP_SEPARATOR = " @@ "
PACKAGE = "PACKAGE"
PACKAGE_SEPARATOR = "_@_"

CACHE_MINUTES = 30

CACHE_HOURS = 24

SHOW_CREDIT_CARD_BASIC_COMMENTS = "show credit card in comments"

USE_ISO_FORMAT_COUNTRIES = "use iso format countries"
SEND_CITY_AS_PROVINCE = "send city as province"
SEND_POSTAL_CODE_FROM_EXTRA_DATA = "send postal code from extra data"

USE_RATEMAP_FOR_BOOKING_RETRIEVAL = "use ratemap for push reservation"
USE_BOARDMAP_FOR_BOOKING_RETRIEVAL = "use boardmap for push reservation"
USE_ROOM_FOR_BOOKING_RETRIEVAL = "use roommap for push reservation"

NEVER_SENT_PAYMENT_AMOUNT = "never send payment amount"
NEVER_SENT_PAYMENT_IN_MODIFICATIONS = "never sent payments in modifications"
SHOW_PAYMENT_ORDER_ID_IN_COMMENTS = "show payment order id in comments"
SHOW_ADDITIONAL_SERVICES_IN_COMMENTS = "show additional services in comments"
SEND_PACKAGE_AS_SERVICE = "send package as service"
SEND_ADDITIONAL_SERVICES_BY_TYPE = "send additional services by type"
SEND_SPECIFIC_CLUB_INFO_IN_COMMENTS = "send specific club info in comments"
TOP_PRIORITY_CLUB_INFO_IN_COMMENTS = "top priority club info in comments"

SEND_CC_TOKEN_IN_XML = "send cc token in xml"

FILTER_AVAILABILITY_ITEMS_FIELDS = "Filter Availability Items Fields"

@timed_cache(hours=CACHE_HOURS)
def hotel_has_virtual_rooms(hotel_code):
	'''
	Indicates if the hotel is configured so that if we close a Rate, we should close
	all the virtual rates that depend on it
	'''

	hotel = get_hotel_by_application_id(hotel_code)

	myIntegrationConfiguration = get_integration_configuration_of_hotel(hotel, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())

	if not myIntegrationConfiguration or len(myIntegrationConfiguration) == 0:
		logging.info("Not found integrationConfiguration for hotel: %s" % hotel['applicationId'])
		return False

	configurations = myIntegrationConfiguration[0].get('configurations', [])

	#In case of only one we need to patch it
	if not type(configurations) == list:
		configurations = [configurations]

	myConfigs = {}
	for config in configurations:
		key, value = config.split(RATE_MAP_SEPARATOR)
		myConfigs[key] = value

	return myConfigs.get(VIRTUAL_ROOMS_CONFIG_KEY)


@timed_cache(hours=CACHE_HOURS, key_builder=lambda x: "get_mapping_redirection_occupancy_" + x[0]['applicationId'])
def get_mapping_redirection_occupancy(hotel):

	result_entities = datastore_communicator.get_using_entity_and_params('Redirection', search_params=[], hotel_code=hotel['applicationId'])
	return result_entities


@timed_cache(hours=24)
def hotel_requires_closing_virtual_rates(hotel_code):
	'''
	Indicates if the hotel is configured so that if we close a Rate, we should close
	all the virtual rates that depend on it
	'''

	hotel = get_hotel_by_application_id(hotel_code)

	myIntegrationConfiguration = get_integration_configuration_of_hotel(hotel, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())

	if not myIntegrationConfiguration or len(myIntegrationConfiguration) == 0:
		logging.info("Not found integrationConfiguration for hotel: %s" % hotel['applicationId'])
		return False

	configurations = myIntegrationConfiguration[0].get('configurations', [])

	#In case of only one we need to patch it
	if not type(configurations) == list:
		configurations = [configurations]

	myConfigs = {}
	for config in configurations:
		key, value = config.split(RATE_MAP_SEPARATOR)
		myConfigs[key] = value

	return myConfigs.get("CLOSE VIRTUAL RATES") and myConfigs.get("CLOSE VIRTUAL RATES").lower() == 'true'


@retry(Exception, tries=3, delay=3, backoff=2)
def get_final_price_days_of_hotel(hotel, str_start_year_and_month, str_end_year_and_month):

	result_entities = datastore_communicator.get_using_entity_and_params('FinalPriceDay', search_params=[('date', '>=', str_start_year_and_month),
	                                                                                                      ('date', '<=', str_end_year_and_month)], hotel_code=hotel['applicationId'])

	return result_entities


@retry(Exception, tries=3, delay=3, backoff=2)
def get_final_price_days_of_hotel_and_room(hotel: dict, room: dict, str_start_year_and_month: str, str_end_year_and_month: str) -> list[Entity]:


	result_entities = datastore_communicator.get_using_entity_and_params('FinalPriceDay', search_params=[('roomKey', '=', room['key']),
	                                                                                                      ('date', '>=', str_start_year_and_month),
	                                                                                                      ('date', '<=', str_end_year_and_month)], hotel_code=hotel['applicationId'])

	return result_entities



@retry(Exception, tries=3, delay=3, backoff=2)
def get_room_type_status_of_hotel(hotel, start_date, end_date):
	start_date_str = start_date.strftime(ISO_DATE_FORMAT)
	end_date_str = end_date.strftime(ISO_DATE_FORMAT)

	result_entities = datastore_communicator.get_using_entity_and_params('RoomTypeStatus', search_params=[('date', '>=', start_date_str),
	                                                                                                      ('date', '<=', end_date_str)], hotel_code=hotel['applicationId'])

	return result_entities


@retry(Exception, tries=3, delay=3, backoff=2)
def get_room_type_status_of_hotel_and_room(hotel, room, start_date_str, end_date_str):

	result_entities = datastore_communicator.get_using_entity_and_params('RoomTypeStatus', search_params=[('roomKey', '=', room['key']),
	                                                                                                      ('date', '>=', start_date_str),
	                                                                                                      ('date', '<=', end_date_str)], hotel_code=hotel['applicationId'])

	return result_entities


@timed_cache(minutes=CACHE_MINUTES)
def filtered_get_mutirates_of_hotel(hotel, language="SPANISH", only_enabled=False):

	#call paraty_commons function
	all_multirates = get_multirates_of_hotel(hotel, language=language, only_enabled=only_enabled)

	words = get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), FILTER_MAP_RATES_KEY)
	if not words:
		return all_multirates

	filtered_rates = []
	for word in words.split(","):
		filtered_rates += filter(lambda x: word.strip().lower() in x.get("name", "").lower(), all_multirates)

	return filtered_rates


@timed_cache(minutes=CACHE_MINUTES)
def filtered_get_boards_of_hotel(hotel, language="SPANISH"):

	#it is posible that we want to filter by word

	#call paraty_commons function
	all_boards = get_boards_of_hotel(hotel, language=language)
	words = get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), FILTER_MAP_BOARDS_KEY)

	if not words:
		return all_boards


	filtered_boards = []
	for word in words.split(","):
		filtered_boards += filter(lambda x: word.strip().lower() in x.get("name", "").lower(), all_boards)

	return filtered_boards


@timed_cache(minutes=CACHE_MINUTES)
def filtered_get_rooms_of_hotel(hotel, language="SPANISH"):

	#it is posible that we want to filter by word

	#call paraty_commons function
	all_rooms = get_rooms_of_hotel(hotel, language=language)
	words = get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), FILTER_MAP_ROOMS_KEY)

	if not words:
		return all_rooms

	filtered_rooms = []
	for word in words.split(","):
		filtered_rooms += filter(lambda x: word.strip().lower() in x.get("name", "").lower(), all_rooms)

	return filtered_rooms

def setup_base_price_integration(hotel_code):
	hotel = get_hotel_by_application_id(hotel_code)
	is_base_price_integration = get_hotel_advance_config_item(hotel, BASE_PRICES_CONFIG_PROPERTY)

	if not is_base_price_integration:
		#another posibility to be configured: (this is because maybe we want to receive prices but not to see them in the manager yet)
		is_base_price_integration = get_integration_configuration_by_key(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), BASE_PRICES_XML_CONFIG_PROPERTY)
		#logging.info("Searching for XML Configuration %s: %s ", BASE_PRICES_XML_CONFIG_PROPERTY, is_base_price_integration)

	return is_base_price_integration


def get_room_of_hotel_by_room_name(hotel, room_name):

	for room in get_rooms_of_hotel(hotel, include_removed=True):
		if room['name'] == room_name:
			return room
	return None

@timed_cache(hours=24)
def hotel_has_virtual_rooms_restrictions(hotel_code):
	'''
	Indicates if the hotel is configured so that if we close a Rate, we should close
	all the virtual rates that depend on it
	'''

	hotel = get_hotel_by_application_id(hotel_code)

	myIntegrationConfiguration = get_integration_configuration_of_hotel(hotel, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())

	if not myIntegrationConfiguration or len(myIntegrationConfiguration) == 0:
		logging.info("Not found integrationConfiguration for hotel: %s" % hotel['applicationId'])
		return False

	configurations = myIntegrationConfiguration[0].get('configurations', {})

	#In case of only one we need to patch it
	if not type(configurations) == list:
		configurations = [configurations]

	myConfigs = {}
	for config in configurations:
		key, value = config.split(RATE_MAP_SEPARATOR)
		myConfigs[key] = value

	return myConfigs.get(VIRTUAL_ROOMS_RESTRICTIONS_CONFIG_KEY)


def hotel_has_filter_reservation_rate(hotel_code, forced_adapter_name=""):

	if forced_adapter_name:
		adapter = forced_adapter_name
	else:
		adapter = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()

	return get_integration_configuration_by_key(hotel_code, adapter, FILTER_RESERVATION_RATE)


@timed_cache(minutes=CACHE_MINUTES)
def filtered_get_rates_of_hotel(hotel, language="SPANISH", only_enabled=False, include_removed=False, forced_adapter_name=""):

	#it is posible that we want to filter by word

	#call paraty_commons function
	all_rates = get_rates_of_hotel(hotel, language=language, only_enabled=only_enabled, include_removed=include_removed)

	if forced_adapter_name:
		adapter = forced_adapter_name
	else:
		adapter = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()

	words = get_integration_configuration_by_key(hotel['applicationId'], adapter, FILTER_MAP_RATES_KEY)
	if not words:
		return all_rates

	filtered_rates = []
	for word in words.split(","):
		filtered_rates += filter(lambda x: x.get("name", "") and (word.strip().lower() in x.get("name", "").lower() or word.strip().lower() in x.get("localName", "").lower()), all_rates)

	return filtered_rates


# TODO, fmatheis, get_conditions of hotel, see https://bitbucket.org/paraty/paraty-common/commits/d6368b8c06dbf2b707a1a6ecf8df8cfe9c375296


def get_properties_at_integration_configuration(hotel_code, integration_name, field="configurations"):
	hotel = get_hotel_by_application_id(hotel_code)

	my_integration_config_list = get_integration_configuration_of_hotel(hotel, integration_name)

	if not my_integration_config_list:
		return {}

	integration_configuration = my_integration_config_list[0]
	configurations = integration_configuration.get(field, [])

	if not isinstance(configurations, list):
		configurations = [configurations]

	myConfigs = {}
	for config in configurations:
		key, value = config.split(RATE_MAP_SEPARATOR)
		myConfigs[key] = value

	return myConfigs


def get_integration_configuration_by_key(hotel_code, integration_name, my_configuration_key):
	'''
	Indicates if the hotel is configured so that if we close a Rate, we should close
	all the virtual rates that depend on it
	'''

	myConfigs = get_properties_at_integration_configuration(hotel_code, integration_name)

	res = myConfigs.get(my_configuration_key, '')
	if res:
		logging.info("Hotel code: %s Integration %s by Key %s Found in %s", hotel_code, integration_name, my_configuration_key, myConfigs)

	return res


def reset_hotel_manager_cache(hotel: dict, first_modified_date: datetime.datetime, last_modified_date: datetime.datetime):

	task_name = 'reset_cache_%s_%s' % (hotel.get('applicationId'), str(uuid.uuid4()))

	try:
		_do_reset_hotel_manager_cache(hotel, first_modified_date, last_modified_date)

		#Double call just in case, i.e. Bed4U bug
		logging.info("Queueing another remove cache in 120 seconds, just in case")
		defer(_do_reset_hotel_manager_cache, hotel, first_modified_date, last_modified_date, countdown=120, task_name=task_name)

	except Exception as e:
		logging.error("Exception clearing Hotel Manager entities: %s" % str(e))

		defer(_do_reset_hotel_manager_cache, hotel, first_modified_date, last_modified_date, countdown=150, task_name=task_name)

		logging.warning("Added new : %s" % str(e))

		# deferred.defer(_do_reset_hotel_manager_cache, hotel, first_modified_date, last_modified_date, _countdown=150)


def _do_reset_hotel_manager_cache(hotel, first_modified_date: datetime.datetime, last_modified_date: datetime.datetime):

	start_date = date_utils.datetime_to_string(first_modified_date, format="%Y-%m-%d")
	end_date = date_utils.datetime_to_string(last_modified_date, format="%Y-%m-%d")

	return _do_reset_hotel_manager_cache_string_params(hotel, start_date, end_date)


def _do_reset_hotel_manager_cache_string_params(hotel, start_date, end_date):

	application_id = hotel.get('applicationId')

	try:
		update_cache_url = "https://admin-hotel.appspot.com/integrations?action=integrationChange&applicationId=%s&startDate=%s&endDate=%s" % (application_id, start_date, end_date)
		requests.get(update_cache_url, timeout=20)
		logging.info('hotel manager cache (admin-hotel) reset: %s', update_cache_url)

		#To make sure there are no strange race conditions
		update_cache_url3 = "https://admin-hotel3.appspot.com/integrations?action=integrationChange&applicationId=%s&startDate=%s&endDate=%s" % (application_id, start_date, end_date)
		requests.get(update_cache_url3, timeout=20)
		logging.info('hotel manager cache (admin-hotel3) reset: %s', update_cache_url3)

	except Exception as e:

		#We fall back to the failover server
		failback_url = "https://standard-dot-admin-hotel.appspot.com/integrations?action=integrationChange&applicationId=%s&startDate=%s&endDate=%s" % (application_id, start_date, end_date)
		requests.get(failback_url, timeout=20)

		logging.info('admin-hotel cache reset using failorver server: %s', failback_url)


@timed_cache(minutes=CACHE_MINUTES)
def is_base_board_restriction_integration(hotel_code):
	return get_integration_configuration_by_key(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), BASE_BOARD_RESTRICTIONS_XML_CONFIG_PROPERTY)

@timed_cache(minutes=CACHE_MINUTES)
def discount_supplements_in_total_reservation(hotel_code):
	return get_integration_configuration_by_key(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), DISCOUNT_SUPLEMENT_CONFIG)

@timed_cache(minutes=CACHE_MINUTES)
def get_real_room_from_mapping(hotel, room_key):
	real_key = room_key
	hotel_code = hotel['applicationId']

	if get_integration_configuration_by_key(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), USE_ROOM_FOR_BOOKING_RETRIEVAL):
		rooms_map = get_properties_at_integration_configuration(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "roomMap")

		if rooms_map and rooms_map.get(room_key):
			logging.info("Converting board: %s to %s because of boardMaps", room_key, rooms_map.get(room_key))
			numeric_key = rooms_map.get(room_key)
			if numeric_key:
				if isinstance(numeric_key, str) and numeric_key.isdigit():
					real_key = int(numeric_key)
				else:
					logging.error("numeric_key '%s' is not a valid digit string for room_key '%s'", numeric_key, room_key)
			real_key = entity_id_to_alphanumeric_key(real_key, "RoomType", hotel_code)

	return real_key


@timed_cache(hours=24)
def hotel_requires_closing_mapped_rates(hotel_code):
	hotel = get_hotel_by_application_id(hotel_code)
	myIntegrationConfiguration = get_integration_configuration_of_hotel(hotel, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())
	if not myIntegrationConfiguration or len(myIntegrationConfiguration) == 0:
		logging.info("Not found integrationConfiguration for hotel: %s" % hotel['applicationId'])
		return False
	configurations = myIntegrationConfiguration[0].get('configurations', [])
	#In case of only one we need to patch it
	if not type(configurations) == list:
		configurations = [configurations]
	if not configurations:
		return False
	myConfigs = {x.split(RATE_MAP_SEPARATOR)[0]: x.split(RATE_MAP_SEPARATOR)[1] for x in configurations if len(x.split(RATE_MAP_SEPARATOR)) == 2}
	return myConfigs.get("CLOSE MAPPED RATES") and myConfigs.get("CLOSE MAPPED RATES").lower() == 'true'


@managers_cache(hotel_code_provider=lambda f, a, k: a[0], entities='Rate,RatePeriod')
def filtered_get_rooms_boards_rates_of_hotel(hotel_code: str, only_enabled: bool = False):
	rates_period = get_all_rate_periods(hotel_code)
	if only_enabled:
		rates_period = list(filter(lambda x: x.get('enabled', False), rates_period))

	result = []

	for rate_period in rates_period:

		rooms_period = []
		boards_period = []

		# Get boards period rate

		aux_board = {}
		supplements = rate_period.get('supplementsJson2', [])
		if rate_period.get('supplementsJson2', []):
			for base in supplements:
				configs = base.split(',')[0:2]
				key_rates = base.split(',')[2:]
				for config in configs:
					key = config.split(':')[0]
					value = config.split(':')[1]
					aux_board[key] = value
				if aux_board.get('type','') == 'Single':
					for key_rate in key_rates:
						key = key_rate.split(':')[0]
						value = key_rate.split(':')[1]
						if value != '' and not alphanumeric_to_id(key) in boards_period:
							boards_period.append(alphanumeric_to_id(key))
					break

		# Get rooms period rate

		aux_room = {}
		basePrices = rate_period.get('basePricesJson',[])
		for base in basePrices:
			configs = base.split(',')
			for config in configs:
				key = config.split(':')[0]
				value = config.split(':')[1]
				aux_room[key] = value
			if aux_room.get('basePrice',''):
				# room_rate['rate'] = rate_period['rateKey']
				rooms_period.append(alphanumeric_to_id(aux_room.get('room','')))

		# Cartesian product for rooms and boards obtained

		for board in boards_period:
			for room in rooms_period:
				board_room_rate = {}
				board_room_rate['room'] = room
				board_room_rate['board'] = board
				board_room_rate['rate'] = alphanumeric_to_id(rate_period.get('rateKey'))
				result.append(board_room_rate)

	return result


def filter_availability_items_fields(hotel_code, availability_items):
	integration_config = get_properties_at_integration_configuration(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())
	fields_filter = integration_config.get(FILTER_AVAILABILITY_ITEMS_FIELDS)
	if fields_filter:
		fields_filter = fields_filter.strip(",").split(",")
		for availability_item in availability_items:
			for field_filter in fields_filter:
				if field_filter in availability_item:
					availability_item[field_filter] = None