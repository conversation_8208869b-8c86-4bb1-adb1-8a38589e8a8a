import logging

from paraty import Config
from paraty_commons_3.redis.redis_communicator import build_redis_client

REDIS_HOST_EU = "************" # Redis web seeker EU
REDIS_HOST_US = "***********" # Redis web seeker US
REDIS_PASSWORD = "nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ"


def _get_target_host() -> str:
    if 'europe' in Config.LOCATION:
        return REDIS_HOST_EU

    return REDIS_HOST_US


def get_entry_from_integration(entry_key: str) -> str | None:
    try:
        entry_key = f'{Config.PROJECT}_{entry_key}'

        redis_client = build_redis_client(_get_target_host(), REDIS_PASSWORD, port=6666)
        result = redis_client.get(entry_key)
        return result
    except Exception as ex:
        logging.error(f"Error getting entry from redis integration: {ex}")
        return None


def set_entry_in_integration(entry_key: str, entry_value: str | bytes, ttl: int) -> None:
    try:
        entry_key = f'{Config.PROJECT}_{entry_key}'

        redis_client = build_redis_client(_get_target_host(), REDIS_PASSWORD, port=6666)
        redis_client.setex(entry_key, ttl, entry_value)
    except Exception as ex:
        logging.error(f"Error setting entry in redis integration: {ex}")


def remove_entry_from_integration(entry_key: str) -> None:
    try:
        entry_key = f'{Config.PROJECT}_{entry_key}'

        redis_client = build_redis_client(_get_target_host(), REDIS_PASSWORD, port=6666)
        redis_client.delete(entry_key)
    except Exception as ex:
        logging.error(f"Error removing entry from redis integration: {ex}")