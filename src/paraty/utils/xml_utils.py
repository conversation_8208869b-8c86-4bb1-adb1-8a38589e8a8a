__author__ = 'fmatheis'

from bs4 import BeautifulSoup


class XmlFinder(object):
    '''
    Based on bs4, it returns BS4 elements and datatypes.

    Copied from project 'siteminder'. Added find_elements method later on.
    TODO: share code for XmlFinder
    '''

    def __init__(self, xmlContent, from_encoding=None, features='lxml'):
        self.bs = BeautifulSoup(xmlContent, from_encoding=from_encoding, features=features)

    def find_element(self, elementName, conditions={}):
        result = self.bs.find(elementName.lower(), conditions)
        return result

    def find_elements(self, element):
        return self.bs.find_all(element)

    def find_attribute(self, elementName, attributeName, conditions={}):
        return self.find_element(elementName, conditions).get(attributeName.lower())

    def find_attribute_anywhere(self, attributeName):
        return self.bs.find(attrs={attributeName.lower(): True}).get(attributeName.lower())

def get_tag(elem, tag):
    tag_found = elem.find(tag)
    if not tag_found:
        raise Exception('Missing mandatory tag: %s' % tag)
    return tag_found
