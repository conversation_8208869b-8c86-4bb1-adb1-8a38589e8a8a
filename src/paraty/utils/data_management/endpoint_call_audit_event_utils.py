import datetime
from typing import List, Optional

from google.cloud.datastore import Entity

import interface_to_implement
from paraty import INTEGRATION_NAME
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params


def get_hotel_audit_event(
        from_date: datetime.datetime,
        to_date: Optional[datetime.datetime] = None,
        hotel_code: Optional[str] = None
) -> List[Entity]:
    """
    Get the EndpointCallAuditEvent list for a hotel in a specific date range
    Is too slow due to the indexed properties in datastore, cant be combined some filters
    """
    search_params = [
        ('timestamp', '>=', from_date)
    ]

    target_path_to_check = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_modify_url()
    satisfied_datastore_index = all([from_date, hotel_code]) # Satisfy indexed properties, will be faster
    if satisfied_datastore_index:
        search_params.append(('hotel_code', '=', hotel_code))
        search_params.append(('path', '=', target_path_to_check))

    results = get_using_entity_and_params('EndpointCallAuditEvent', search_params, hotel_code=f'{INTEGRATION_NAME}-adapter:', order_by='-timestamp', return_cursor=True)
    if satisfied_datastore_index:
        # Datastore perform the filtering
        return list(results)

    def filtered_results(element):
        if target_path_to_check not in element['path']:
            return False

        if hotel_code and element['hotel_code'] != hotel_code:
            return False

        if to_date and element['timestamp'] > to_date:
            return False

        return True

    results = filter(lambda x: filtered_results, results)
    return list(results)
