from paraty_commons_3.datastore.datastore_communicator import get_entity_by_key
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_key
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(hotel_code_provider=lambda f,a,k: a[1], entities='RoomType', only_thread_local_and_memory=True)
def get_room_info(room_key, hotel_code):
    key_object = alphanumeric_to_key(room_key)
    return get_entity_by_key(key_object, hotel_code=hotel_code)
