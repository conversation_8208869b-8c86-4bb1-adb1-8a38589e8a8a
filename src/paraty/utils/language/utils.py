from . import SPANISH, ENGLISH, GERMAN, PORTUGUESE, ITALIAN
import logging


translations_SPANISH = SPANISH.translations
translations_ENGLISH = ENGLISH.translations
translations_GERMAN = GERMAN.translations
translations_PORTUGUESE = PORTUGUESE.translations
translations_ITALIAN = ITALIAN.translations

logging.info("NOW LANGUAGES GENERAL DICTS ARE IMMUTABLES")


def get_translations_dict(language):
	language_dict = translations_SPANISH
	language = language.upper()
	if language == 'ENGLISH':
		language_dict = translations_ENGLISH
	elif language == 'PORTUGUESE':
		language_dict = translations_PORTUGUESE
	elif language == 'GERMAN':
		language_dict = translations_GERMAN
	elif language == 'ITALIAN':
		language_dict = translations_ITALIAN

	return language_dict


