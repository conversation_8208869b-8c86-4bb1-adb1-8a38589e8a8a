import json
import base64
import logging
from typing import Optional

from Cryptodome.Cipher import DES
from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item


def extract_extra_info_ccdata(reservation_json: dict, hotel: dict, data_base: dict, currency: Optional[str] = "EUR"):
	res = None

	cc_number = ''
	cc_company = ''
	cc_expired = ''
	cc_cvv = None
	cc_holder = None

	extra_info = reservation_json.get('extraInfo')
	if extra_info and isinstance(extra_info, str):
		extra_info = json.loads(extra_info)
		base64message = extra_info.get('cc_datas')
		send_cc = get_hotel_advance_config_item(hotel, "Save CC datas")
		if send_cc and base64message:

			res = {}
			logging.info("decrypting datas")
			passwordCc = get_hotel_advance_config_item(hotel, "Password Tarjetas")
			if passwordCc:
				passwordCc = passwordCc[0].get("value", "")

				message = base64.b64decode(base64message)
				obj = DES.new(passwordCc.encode('utf-8'), DES.MODE_ECB)
				decryptedData = obj.decrypt(message)

				infoCC = decryptedData.decode("utf-8").split('@@')

				if len(infoCC) > 0:
					cc_number = infoCC[0]

					if cc_number.strip() == "42":
						cc_number = "0000000000000042"

				if len(infoCC) > 1:
					cc_company = infoCC[1].upper()
					if cc_company in ["CA", "MA", "4B"]:
						cc_company = "MC"

					if cc_company not in ["AX ", "BC", "BL", "CB", "DN", "DS", "EC", "JC", "MC", "TP", "VI", "VISA"]:
						cc_company = "NULL"

				if len(infoCC) > 2:
					cc_expired = infoCC[2]
					ccExpiredInfo = cc_expired.split("/")

					# 08/19 -> 0819
					if ccExpiredInfo[0] and ccExpiredInfo[1]:
						cc_expired = "%02d%02d" % (int(ccExpiredInfo[0]), int(ccExpiredInfo[1]))

				if len(infoCC) > 3:
					cc_cvv = infoCC[3].strip()
					if len(cc_cvv) == 0:
						cc_cvv = None

				if len(infoCC) > 4:
					cc_holder = infoCC[4].strip()

			if not cc_holder or cc_holder.lower() == "none":
				cc_holder = reservation_json.get("name", "") + " " + reservation_json.get("lastName", "")

			if cc_number and cc_expired:
				res["cc_number"] = str(cc_number)
				res["cc_company"] = cc_company
				res["cc_expired"] = cc_expired
				res["cc_cvv"] = cc_cvv
				res["cc_holder"] = str(cc_holder)
				res["currency_code"] = currency

	if res:
		data_base['cc_datas'] = res

	return data_base
