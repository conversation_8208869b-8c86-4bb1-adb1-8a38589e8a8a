import html
import os
import sys
import traceback
import logging
import cgi

# from libs.postmark import PM<PERSON><PERSON>
from paraty_commons_3.libs.postmark.core import PMMail
from paraty import Config
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.mail.amazon import AmazonEmail

EMAILS_SUPPORT = "<EMAIL>"


app_name = Config.PROJECT

DEFAULT_TITLE = '[%s] Problem at integration Adapter - ' % app_name

def makeTraceback():
	"""
	returns a string with the full traceback
	"""
	excinfo = sys.exc_info()
	message = ('Application: %s\nVersion: %s\n\n%s\n\n'
		% (os.getenv('APPLICATION_ID'),
		os.getenv('CURRENT_VERSION_ID'),
		'<br/>'.join(traceback.format_exception(*excinfo)),))

	return message


def notify_error_by_email(subject, message):
	sendEmail(EMAILS_SUPPORT, DEFAULT_TITLE, "", message)


@timed_cache(key_builder=lambda x: "notify_exception_by_email %s" % x[1], minutes=5)
def notifyExceptionByEmail(myException, myRequest):

	message = makeTraceback()
	emailContent = "<html><body><B>Request: %s </B><br/><br/>%s</body><html/>" % (html.escape(str(myRequest))[:2000], str(message))
	sendEmail(EMAILS_SUPPORT, DEFAULT_TITLE, "", emailContent)


def sendEmail(address, title, contentText, contentHtml, replyTo=None, sender=None):
	try:
		logging.warning("Trying to send it using PMM....")
		if not sender:
			sender = app_name + " <<EMAIL>>"
		to = ""
		for myAddress in address.split(";"):
			to = to + myAddress + ","

		if Config.DEV:
			logging.warning("Not sending email in DEV mode")
			logging.warning("To: %s", to)
			logging.warning("Title: %s", title)
			return

		message = PMMail(api_key='70f0042b-fc70-43cf-a01c-86b783a31327',
						 sender=sender,
						 subject=title,
						 html_body=contentHtml,
						 text_body=contentText,
						 to=to,
						 bcc='<EMAIL>, <EMAIL>',
						 reply_to=replyTo)

		message.send()
		logging.warning("Sent!")
	except Exception as e:
		logging.warning("Error sending email using PMM to " + str(address))
		logging.warning(e)


def send_amazon_email(address, title, content, sender='<EMAIL>'):
	amazon_mail_controller = AmazonEmail()
	if sender:
		amazon_mail_controller.set_sender(sender)

	amazon_mail_controller.send(
		address,
		title,
		content,
		content,
		cc=[],
		bcc=[],
	)
