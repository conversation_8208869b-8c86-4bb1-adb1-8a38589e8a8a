import re

def encrypt_xml_sensitive_data(xml_content):
    xml_content = _mask_xml(xml_content)
    return xml_content


def _mask_xml(xml_content):
    # Pattern to match password attributes and values
    password_patterns = [
        # WSSE Password format
        r'(?i)<(?:wsse:)?Password[^>]*>(.*?)</(?:wsse:)?Password>',
        # Standard password elements
        r'(?i)<(password|passwd|pwd|auth|key|secret)>(.*?)</\1>',
        # Password attributes
        r'(?i)(password|passwd|pwd|auth|key|secret)[=:"]\s*([^"<>\s]+)',
        # Base64 encoded passwords (common in SOAP)
        r'(?i)<(?:wsse:)?BinarySecurityToken[^>]*>(.*?)</(?:wsse:)?BinarySecurityToken>',
    ]

    def mask_password(match):
        try:
            # For element matches (groups 1 and 2)
            if len(match.groups()) > 1 and match.group(2):
                return match.group(0).replace(match.group(2), '*****')
            # For attribute matches (group 2)
            elif len(match.groups()) > 1 and match.group(2):
                return match.group(0).replace(match.group(2), '*****')
            # For single group matches (like WSSE Password)
            elif match.group(1):
                return match.group(0).replace(match.group(1), '*****')
            # Fallback to masking the entire match
            return '*****'
        except IndexError:
            # If any group access fails, mask the entire match
            return '*****'

    masked_content = xml_content
    for pattern in password_patterns:
        masked_content = re.sub(pattern, mask_password, masked_content, flags=re.DOTALL)

    return masked_content