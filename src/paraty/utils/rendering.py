import os

from flask import render_template
from jinja2 import Template

__author__ = 'fmatheis'



def render_result(context, template_path):
	'''
	Renders the given template using the provided params (expected jinja2 template).
	'''
	if not context:
		context = {}

	return render_template(template_path, **context)


def render_base_integration_template(template_name, **kwargs):
	'''
	Renders the base template for the integration with the provided params.
	This works also in production, but for local development, the template is loaded from the local file system.
	'''
	target_templates_path = os.path.join(os.path.dirname(__file__), '../../templates')
	target_template = os.path.join(target_templates_path, template_name)
	template_element = Template(open(target_template).read())
	return render_template(template_element, content=template_name, **kwargs)