import logging
from google.cloud import ndb

import interface_to_implement
from paraty.utils.hotel_manager_utils import get_integration_configuration_by_key, \
	get_properties_at_integration_configuration, \
	USE_RATEMAP_FOR_BOOKING_RETRIEVAL, USE_BOARDMAP_FOR_BOOKING_RETRIEVAL, CACHE_MINUTES

from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_boards_of_hotel
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache


@timed_cache(hours=CACHE_MINUTES)
def get_real_rate_from_mapping(hotel, rate_key):
	real_key = rate_key
	hotel_code = hotel['applicationId']

	if get_integration_configuration_by_key(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), USE_RATEMAP_FOR_BOOKING_RETRIEVAL):
		rates_map = get_properties_at_integration_configuration(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "rateMap")

		if rates_map and rates_map.get(rate_key):

			logging.info("Converting rate: %s to %s becasue of rateMaps", rate_key, rates_map.get(rate_key))
			all_rates = {str(x['id']): x for x in get_rates_of_hotel(hotel, only_enabled=False, include_removed=True)}

			real_key = all_rates.get(rates_map.get(rate_key), {}).get('key')
			if not real_key:
				logging.error('Failure! The real rate to which the virtual rate is associated does not exist. Please check')

	return real_key


@timed_cache(hours=CACHE_MINUTES)
def get_real_board_from_mapping(hotel, board_key):
	real_key = board_key
	hotel_code = hotel['applicationId']

	if get_integration_configuration_by_key(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), USE_BOARDMAP_FOR_BOOKING_RETRIEVAL):
		boards_map = get_properties_at_integration_configuration(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "boardMap")

		if boards_map and boards_map.get(board_key):

			logging.info("Converting rate: %s to %s becasue of rateMaps", board_key, boards_map.get(board_key))
			all_boards = {str(x['id']): x for x in get_boards_of_hotel(hotel, include_removed=True)}

			real_key = all_boards.get(boards_map.get(board_key), {}).get('key')
			if not real_key:
				logging.error(
					'Failure! The real board to which the virtual board is associated does not exist. Please check!!')

	return real_key
