import datetime
import logging


import interface_to_implement
from paraty.integration.view_prices import ViewPricesService
from paraty.utils import date_utils, common_utils


def _get_start_and_end_dates(availability_items):

	start_date = None
	end_date = None

	for item in availability_items:

		#Initialize this with the first item
		if not start_date:
			start_date = item['day']
			end_date = item['day']
			continue

		if item['day'] < start_date:
			start_date = item['day']

		elif end_date < item['day']:
			end_date = item['day']

	return date_utils.string_to_date(start_date, format=interface_to_implement.AVAILABILITY_ITEM_DATE_FORMAT), date_utils.string_to_date(end_date, format=interface_to_implement.AVAILABILITY_ITEM_DATE_FORMAT)


def _check_field(field, item, related_items, already_verified, formatter=lambda x: x):

	result = ""
	is_latest = False

	if field in already_verified:
		return "", False

	else:

		item_has_field = item.get(field)

		#Check if this is the latest item for the property
		related_item_with_same_field = None
		value_is_what_is_expected = False
		if related_items and item_has_field:

			for current_related_item in related_items:
				if field in current_related_item:
					related_item_with_same_field = current_related_item
					value_is_what_is_expected = formatter(related_item_with_same_field[field]) == formatter(item[field])

		#There is nothing in the DB or the value is different
		if item_has_field and (not related_item_with_same_field or not value_is_what_is_expected):

			result = field
			is_latest = True
			already_verified[field] = True

		#Item has the field and there is nothing related
		elif item_has_field:
			is_latest = True
			already_verified[field] = True

	return result, is_latest

def _compare_float(f1, f2):

	return abs(float(f1) - float(f2)) <= float(0.1)

def _verify_item(db_availability_sorted, item, verified_items):
	key_to_check_price = "%s_%s_%s_%s_%s" % (item.get('day', ''), item.get('roomId', ''), item.get('boardId', ''), item.get('rateId', ''), item.get('capacity', ''))
	already_verified_price = key_to_check_price in verified_items
	already_verified = verified_items.get("%s_%s_%s_%s" % (item.get('day', ''), item.get('roomId', ''), item.get('boardId', ''), item.get('rateId', '')))

	#If it is the last modification for a given data (i.e. the most recent price change)
	min_stay_is_latest = False
	quantity_is_latest = False
	status_is_latest = False
	max_stay_is_latest = False
	release_is_latest = False
	price_is_latest = False


	if not already_verified:
		already_verified = {}
		verified_items["%s_%s_%s_%s" % (item['day'], item['roomId'], item['boardId'], item['rateId'])] = already_verified

	try:

		related_items = db_availability_sorted[item['day']].get(item.get('roomId'), {}).get(item.get('boardId'), {}).get(item.get('rateId'), {})

		#I.e. if we only have quantity
		if not item.get('rateId'):
			room_related_items = db_availability_sorted[item['day']][item['roomId']]
			related_items = [x for x in [list(x.values())[0][0] for x in room_related_items.values()] if x.get('quantity')]

		item_with_capacity = [x for x in related_items if x['capacity'] == item.get('capacity')]

		#Check price
		price_ok = ""
		if not already_verified_price:

			if item.get('price') and (not item_with_capacity or not _compare_float(item_with_capacity[0]['price'], item['price'])):
				price_ok = "price"
				price_is_latest = True
				verified_items["%s_%s_%s_%s_%s" % (item['day'], item['roomId'], item['boardId'], item['rateId'], item['capacity'])] = True
			elif item.get('price'):
				price_is_latest = True
				verified_items["%s_%s_%s_%s_%s" % (item['day'], item['roomId'], item['boardId'], item['rateId'], item['capacity'])] = True

		#Check min_stay
		min_stay_ok, min_stay_is_latest = _check_field('minimumStay', item, related_items, already_verified, formatter=lambda x: int(x))

		#Check max stay
		max_stay_ok, max_stay_is_latest = _check_field('maximumStay', item, related_items, already_verified, formatter=lambda x: int(x))

		#Check release
		release_ok, release_is_latest = _check_field('release', item, related_items, already_verified, formatter=lambda x: int(x))

		#Check quantity
		quantity_ok, quantity_is_latest = _check_field('quantity', item, related_items, already_verified, formatter=lambda x: int(x))

		#Check status
		status_ok, status_is_latest = _check_field('status', item, related_items, already_verified)

		everything_ok = "".join([price_ok, min_stay_ok, quantity_ok, status_ok, max_stay_ok, release_ok])
		something_is_latest = price_is_latest or min_stay_is_latest or quantity_is_latest or status_is_latest or max_stay_is_latest or release_is_latest

		return everything_ok, something_is_latest

	except Exception as e:
		logging.exception(e)
		return "", False


def validate_against_production(availability_items, hotel_code):
	min_date = min(availability_items, key=lambda k: k.get("day")).get("day")
	max_date = max(availability_items, key=lambda k: k.get("day")).get("day")

	min_date_splitted = [int(m) for m in min_date.split("-")]
	max_date_splitted = [int(m) for m in max_date.split("-")]

	min_date = datetime.date(min_date_splitted[0], min_date_splitted[1], min_date_splitted[2])
	max_date = datetime.date(max_date_splitted[0], max_date_splitted[1], max_date_splitted[2])

	# ViewPrices theoretically is going to give us what is available in the datastore, note that this is integration independent so it is safe
	view_prices_service = ViewPricesService(hotel_code, min_date, max_date)
	db_availability_items = view_prices_service.get_availability()

	logging.info("Availability items: %s", len(db_availability_items))

	db_availability_sorted = common_utils.from_array_to_grouped_dict(db_availability_items, ['day', 'roomId', 'boardId', 'rateId'])

	for item in availability_items:
		item_is_ko, is_latest = _verify_item(db_availability_sorted, item, {})

		if item_is_ko:
			logging.info("Found item NOT ok: %s", item_is_ko)
			item['ko'] = item_is_ko




