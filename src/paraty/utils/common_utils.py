from itertools import groupby


def from_array_to_grouped_dict(arr, arr_keys):
	results = {}
	for i in arr_keys:
		if not results:
			results = __group_by(arr, i)
		else:
			results = __group_by(results, i)

	return results


def __group_by(input, item):
	results = {}
	if isinstance(input, list):
		sorted_input = sorted(input, key=lambda x:x[item])
		for key, values_iter in groupby(sorted_input, key=lambda r: r[item]):
			results[key] = list(values_iter)
	else:
		for key, values in input.items():
			results[key] = __group_by(values, item)

	return results