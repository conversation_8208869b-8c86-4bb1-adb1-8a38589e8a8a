import base64
import datetime
import json
import logging

from paraty.constants.credit_cards import VISA_CODE, MASTERCARD_CODE, AMERICAN_EXPRESS_CODE, JAPAN_CREDIT_BUREAU_CODE, \
	DINERS_CLUB_CODE, CHINA_UNIONPAY_CODE, DIS<PERSON>VER_CODE
from paraty.constants.extra_info_properties import BILLING_DATA
from paraty.constants.integration_properties import SEND_BILLING_DATA
from paraty.constants.source_identifiers import RING2TRAV<PERSON>
from paraty.utils.agencies_utils import get_all_agencies_of_hotel
from paraty.utils.language.utils import get_translations_dict
from paraty.utils.language.SPANISH import translations as default_translations
from paraty.utils.mapping_utils import get_real_rate_from_mapping, get_real_board_from_mapping
from paraty.utils.hotel_manager_utils import ISO_DATE_FORMAT, PACKAGE, PACKAGE_SEPARATOR, CACHE_HOURS, \
	SEND_PACKAGE_AS_SERVICE, SHOW_ADDITIONAL_SERVICES_IN_COMMENTS, USE_ISO_FORMAT_COUNTRIES, \
	SEND_POSTAL_CODE_FROM_EXTRA_DATA, SEND_CITY_AS_PROVINCE, SEND_ADDITIONAL_SERVICES_BY_TYPE, \
	SEND_SPECIFIC_CLUB_INFO_IN_COMMENTS, TOP_PRIORITY_CLUB_INFO_IN_COMMENTS
from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, \
	get_web_page_properties, \
	get_hotel_advance_config_item, \
	get_rooms_of_hotel, get_boards_of_hotel, get_rates_of_hotel, get_promotions_of_hotel, get_hotel_web_config_item, \
	get_additional_services_of_hotel

from paraty_commons_3.datastore import datastore_communicator

import re

import unicodedata

import math

from paraty_commons_3.decorators.cache.timebased_cache import timed_cache

try:
	from Cryptodome.Cipher import DES
except Exception as e:
	logging.warning("Error loading Cipher, probably on mac testing locally")

from html_sanitizer import Sanitizer
import html.parser

from model.price_model import DISCOUNT_SUPLEMENT_CONFIG
from paraty.utils import date_utils
from paraty.utils import hotel_manager_utils as integration_hotel_manager_utils
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.hotel_manager import hotel_manager_utils


from paraty.utils.hotel_manager_utils import SHOW_CREDIT_CARD_BASIC_COMMENTS, SERVICE_DAYS_AS_QUANTITY, \
	SHOW_PAYMENT_ORDER_ID_IN_COMMENTS, NEVER_SENT_PAYMENT_AMOUNT

import interface_to_implement

forced_timezone = None
try:
	from pytz import timezone, utc
except Exception as e:
	logging.warning("IMPORT NATIVE PYTZ HAS FAILED COWARDLY: %s", e)
	forced_timezone = True

DEFAULT_TAX_DENOMINATOR = 1.10 #10% taxes
AGE_QUALIFYING_CODE_BABY = "BABY"

PROMOCODE = "Promocode"
SERVICIOS_ADICIONALES_INCLUIDOS = "Servicios Adicionales Incluidos"
PAQUETES_APLICADOS = "Paquetes aplicados: "
TEXT_TOTAL_RESERVA_SERVICIOS_INCLUIDOS = 'Total de la reserva con todos los servicios incluidos: {}'
TEXT_TOTAL_SERVICIOS_INCLUIDOS = 'Servicios incluidos: {}'
PAGADO_POR_TPV = "Pagado por TPV (payed by Gateway)"
PAGADO_POR_TPV_PT = "Pagamento por Gateway"
PENDIENTE_PAGO = "Pendiente de pago"
TEXT_CLUB_MEMBER = "Loyalty Club Member: "
TEXT_CLUB_MEMBER_PT = "ID de Utilizador "
SOURCE_RESERVATION = "Origen reserva: "
SOURCE_RESERVATION_PT = "Origem Reserva: "
BONO_GIFT = "Bono regalo: "
AMAZON_PAY = "Pagado por Amazon"

NOT_INCLUDE_CLUB_IN_COMMENTS = "not include club in comments"
SHOW_EXTRA_INFO_IN_COMMENTS = "show extra info in comments"
SEND_PROMOTIONS_IN_COMMENTS = 'send promotion in comments'
SUPPLEMENTS_INCLUDED_IN_PAYED = "supplements included in payed"
ADD_EXTRA_PAYED_FIRST_ROOM = "add extra payed first room"
INTEGRATE_CALLCENTER_COMMENTS = "integrate callcenter comments"
ONLY_SHOW_PACKAGE_NAME_IN_COMMENTS = "only send package name in comments"

##CCDATAS
PASSWORD_TARJETAS = "Password Tarjetas"
ALLOWED_CC = ["AX", "BC", "BL", "CB", "DN", "DS", "EC", "JC", "MC", "TP", "VI"]


def clean_html_tags(text):
	text = text.replace("&lt;", "<")
	text = text.replace("&gt;", ">")

	clean = re.compile('<.*?>')
	text = re.sub(clean, '', text)
	clean = re.compile('&.*?;')
	text = re.sub(clean, '', text)
	clean = re.compile('[\n]+')
	text = re.sub(clean, '\n', text)

	return text


@timed_cache(hours=CACHE_HOURS)
def get_supplements(hotel: dict) -> list[dict]:
	custom_language_management = get_hotel_advance_config_item(hotel, "Language Management")
	if custom_language_management:
		language = custom_language_management[0].get("value")
	else:
		language = "SPANISH"

	all_services = get_additional_services_of_hotel(hotel, language)
	return all_services


def _get_total_payed_amount_from_all_sources(extra_info_reservation):
	payed_amount = 0
	if extra_info_reservation.get("payed") and float(extra_info_reservation.get("payed")):
		payed_amount = float(extra_info_reservation.get("payed", "0"))
	if extra_info_reservation.get("payed_by_cobrador"):
		payed_amount += float(extra_info_reservation.get("payed_by_cobrador", "0"))

	if extra_info_reservation.get("payed_by_tpv_link"):
		payed_by_tpv_link = extra_info_reservation.get("payed_by_tpv_link", [])
		for payment in payed_by_tpv_link:
			payed_amount += float(payment.get("amount", "0") or 0)

	if extra_info_reservation.get("extra_payed_by_cobrador"):
		payed_amount += float(extra_info_reservation.get("extra_payed_by_cobrador", "0"))

	return payed_amount

def get_reservation_by_identifier(hotel, identifier):
	result = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('identifier',"=",identifier)], hotel_code=hotel['applicationId'])
	return result

def get_key_additional_service(hotel: dict, service_name: str):
	result = datastore_communicator.get_using_entity_and_params('Supplement', search_params=[('name',"=",service_name)], hotel_code=hotel['applicationId'])
	if result:
		return result[0].get('key')

	return None

def get_type_additional_service(hotel: dict, service_name: str):
	result = datastore_communicator.get_using_entity_and_params('Supplement', search_params=[('name',"=",service_name)], hotel_code=hotel['applicationId'])
	if result:
		return result[0].get('type')

	return None

def get_identifier_additional_service(hotel, service_key):
	'''
	Get the necessary identifier to sent in XML
	'''

	if not service_key:
		return "SERVICE"

	myIntegrationConfiguration = get_integration_configuration_of_hotel(hotel, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())
	# myIntegrationConfiguration = get_integration_configuration_of_hotel(hotel, "New Minder Site")

	if not myIntegrationConfiguration or len(myIntegrationConfiguration) == 0:
		logging.info("Not found integrationConfiguration for hotel: %s" % hotel['applicationId'])
		return False

	configurations = myIntegrationConfiguration[0].get('extraMap', [])

	# In case of only one we need to patch it
	if not type(configurations) == list:
		configurations = [configurations]

	myConfigs = {}
	for config in configurations:
		key, value = config.split(" @@ ")
		key = key.strip()
		myConfigs[key] = value

	return myConfigs.get(service_key, 'SERVICE')


def extract_data_to_push_reservation_from_json(reservation_json, hotel_code, specific_params = {}):
		source = {
			'code': specific_params.get("COMPANY_CODE"),
			'channel_type': specific_params.get("CHANNEL_TYPE"),
			'channel_primary': specific_params.get("CHANNEL_PRIMARY"),
			'company_name': specific_params.get("COMPANY_NAME"),
			'channel_name': specific_params.get("CHANNEL_NAME"),
		}

		hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
		translations = get_translations(hotel)

		hotel_name = hotel.get('name', "")

		language_aux = get_hotel_advance_config_item(hotel, "Language Management")
		if language_aux and len(language_aux) > 0:
			if language_aux[0].get("value") == 'PORTUGUESE':
				specific_params['SPECIAL_LENGUA_PORTUGUES'] = True

		creation_time_date = date_utils.string_to_date(reservation_json.get('timestamp'), format="%Y-%m-%d %H:%M:%S")

		integration_name = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()

		if specific_params.get('CHANGE_UTC_ZONE'):
			try:
				# #Change to UTC
				local = timezone('Europe/Madrid')
				naive = creation_time_date
				local_dt = local.localize(naive, is_dst=None)
				creation_time_date = local_dt.astimezone(utc)

				creation_time = creation_time_date.isoformat()
				modification_time = creation_time_date.isoformat().replace("+00:00", "")

			except Exception as e:
				'''
				EXCEPTION: AmbiguousTimeError

				Explanation: The reason for the error is almost the same but it happens when switching from summer to standard time.
				From 01:59 DST the clock shifts to 01:00 standard time, so we end with a duplicate time.

				Why has this happened(in our case)? Well we couldn't be sure how exactly this one got into our legacy data but the assumption
				is that at the moment when the record was saved the server has been in different timezone where this has been a valid time.

				Solution 1: This fix is quite simple, just add an hour if the exception occurs.

				extract from here:
				http://www.ilian.io/django-pytz-nonexistenttimeerror-and-ambiguoustimeerror/
				'''

				if not forced_timezone:
					local = timezone('Europe/Madrid')
					naive = creation_time_date + datetime.timedelta(hours=1)
					local_dt = local.localize(naive, is_dst=None)
					creation_time_date = local_dt.astimezone(utc)

				creation_time = creation_time_date.isoformat()
				modification_time = creation_time_date.isoformat().replace("+00:00", "")

		else:
			creation_time = creation_time_date.isoformat()
			modification_time = datetime.datetime.now().isoformat()


		extra_info = {}
		extra_info_text = reservation_json.get('extraInfo')
		if extra_info_text and isinstance(extra_info_text, str):
			extra_info = json.loads(extra_info_text)

		cancelled = reservation_json.get('cancelled')
		reservation_status = specific_params.get("RES_STATUS_NEW_RESERVATION")
		if cancelled == 'true' or (type(cancelled) == bool and cancelled):
			reservation_status = specific_params.get("RES_STATUS_CANCELLATION")

			creation_time_date = datetime.datetime.now()
			if reservation_json.get('cancellationTimestamp'):
				creation_time_date = date_utils.string_to_date(reservation_json.get('cancellationTimestamp'), format="%Y-%m-%d %H:%M:%S")

			# Change to UTC
			if (not forced_timezone) and specific_params.get('CHANGE_UTC_ZONE'):
				naive = creation_time_date
				local_dt = local.localize(naive, is_dst=None)
				creation_time_date = local_dt.astimezone(utc)

			modification_time = creation_time_date.isoformat().replace("+00:00", "")
		else:
		#Maybe is a modification from hotel Manager

			if specific_params.get("RES_STATUS_MODIFICATION"):
				#this adapter has modification implementation

				if extra_info.get('modification_timestamp') or reservation_json.get("modificationTimestamp"):
					reservation_status = specific_params.get("RES_STATUS_MODIFICATION")

		personalId = ""
		if extra_info.get('personalId', ''):
			personalId = extra_info.get('personalId', '')[:32]

		name = reservation_json.get('name', '')
		last_name = reservation_json.get('lastName', '')
		telephone = reservation_json.get('telephone')
		email = reservation_json.get('email', '').split(" ")[0]
		comments = reservation_json.get('comments', '')

		logging.info("comments: %s", comments)
		if not comments:
			comments = ""


		new_language = _get_value_at_integration_configuration(hotel, 'language constants in comments')

		if is_forced_pt(hotel):
			customer_language = 'PORTUGUESE'
		elif new_language:
			customer_language = new_language.upper()
		elif reservation_json.get('language'):
			customer_language = reservation_json.get('language')
		else:
			customer_language = "ENGLISH"

		reservation_supplements = reservation_json.get('additionalServices')
		show_additional_services_in_comments = _get_value_at_integration_configuration(hotel, SHOW_ADDITIONAL_SERVICES_IN_COMMENTS)

		# TODO: At python3 always is added to comments, but in python2 is controlled by advance config ¿?
		if reservation_supplements and (not show_additional_services_in_comments or show_additional_services_in_comments.lower() != "false"):
			comments += ".\n " + translations.get('T_ADDITIONAL_SERVICES_INCLUDED').upper() + ": " + reservation_supplements
			comments = translate_supplements(hotel, comments, reservation_supplements, customer_language, translations)

		show_extra_info_in_comments = _get_value_at_integration_configuration(hotel, SHOW_EXTRA_INFO_IN_COMMENTS)
		if show_extra_info_in_comments and extra_info.get(show_extra_info_in_comments):
			extra_info_in_comments = "\n" + show_extra_info_in_comments.upper() + ":" + extra_info.get(
				show_extra_info_in_comments) + ";"
			comments += extra_info_in_comments

		if extra_info.get("cancelled_by", "") == 'automatic SIBS':
			comments += "\ncancelled by multibanco"

		promotion = {}
		all_promotions = None
		if reservation_json.get('promotions'):
			promotion_key = reservation_json.get('promotions')
			all_promotions = get_promotions_of_hotel(hotel, customer_language)
			promotions = list(filter(lambda x: x.get('key') == promotion_key, all_promotions))
			promotion = promotions[0] if promotions else None

		# Requested by PORT + SIME
		send_promotion_in_comments = _has_property_at_integration_configuration(hotel, 'send promotions with promocode')
		if send_promotion_in_comments:
			if all_promotions:
				my_promocode = reservation_json.get('promocode', '')
				comments += ".\n " + PROMOCODE.upper() + ": " + my_promocode
				all_promotions_without_promocode = [x for x in all_promotions if not x.get('promocode')]
				promotion_names = _get_promotion_names(all_promotions_without_promocode, hotel, customer_language, reservation_json)
				for promotion_name in promotion_names:
					comments += ' %s' % promotion_name.replace(" ", "_").upper().replace("COPIA DE ", "").replace("COPIA_DE_", "")
				comments += ' @'
		else:
			if reservation_json.get('promocode'):
				comments += ".\n " + translations.get('T_PROMOCODE').upper() + ": " + reservation_json.get('promocode')

		price_base = float(reservation_json.get('price', '0'))
		price_suplement = float(reservation_json.get('priceSupplements', '0') or 0)

		if float(reservation_json.get('priceSupplements', '0')):
			comments += '\n ' + TEXT_TOTAL_RESERVA_SERVICIOS_INCLUIDOS.format(price_base + price_suplement)
			comments += '\n ' + TEXT_TOTAL_SERVICIOS_INCLUIDOS.format(price_suplement)

		package_reservation = {}

		show_additional_services_in_comments = integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, SHOW_ADDITIONAL_SERVICES_IN_COMMENTS)
		additional_services = _get_list_additional_service(reservation_json, specific_params, hotel)
		if float(reservation_json.get('priceSupplements', '0')) and (not show_additional_services_in_comments or show_additional_services_in_comments.lower() != "false"):
			comments += '\n ' + translations.get('T_TOTAL_RESERVATION_SERVICES_INCLUDED_TEXT').format(price_base + price_suplement)
			comments += '\n ' + translations.get('T_TOTAL_SERVICES_INCLUDED').format(price_suplement)

		if reservation_json.get('priceIncrease'):

			package_info = _get_package_full_info(reservation_json.get('priceIncrease'), hotel.get("applicationId"), language=customer_language)

			if package_info:
				package_name = package_info.get("package_name", "")
				package_description = package_info.get("package_description", "")
				clean_package_description = ""
				if package_description:
					clean_package_description = clean_html_tags(package_description)

				if _get_value_at_integration_configuration(hotel, ONLY_SHOW_PACKAGE_NAME_IN_COMMENTS).lower() == "true":
					comments += ".\n " + translations.get('T_APPLIED_PACKAGES').upper() + ": " + package_name
				else:
					comments += ".\n " + translations.get('T_APPLIED_PACKAGES').upper() + ": " + package_name + ". " + clean_package_description

				if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'],
																						integration_name,
																						SEND_PACKAGE_AS_SERVICE).lower() == "true":
					package_reservation = {
						"package_name": package_name,
						"package_description": clean_package_description
					}

		payed_amount = _get_total_payed_amount_from_all_sources(extra_info)
		show_tpv_in_comments = specific_params.get("SHOW_TPV_IN_COMMENTS")

		if payed_amount and not payed_amount == "0":
			currency = specific_params.get("CURRENCY_SYMBOL", "")
			if extra_info.get("currency_conversion_for_gateway"):
				currency = extra_info["currency_conversion_for_gateway"].get("original_currency", currency)

			comments += "\n " + translations.get('T_PAID_BY_TPV').upper() + ": " + str(payed_amount) + specific_params.get("CURRENCY_SYMBOL", "")

			if show_tpv_in_comments and extra_info.get("gateway_type"):
				comments += "\n TPV:" + extra_info.get("gateway_type").upper()

			if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, SHOW_PAYMENT_ORDER_ID_IN_COMMENTS):
				if extra_info.get("ds_MerchantIdentifier"):
					comments += " (" + "Payment order id" + ": " + str(extra_info.get("ds_MerchantIdentifier")) + ")"

		if specific_params.get("SOURCE_IN_COMMENTS", None):

			# This is request by Luna Hotels
			source_booking = reservation_json.get("source")
			source_identifier = "WEB"
			if source_booking:
				if source_booking == "Mobile":
					source_identifier = "MOBILE"
				if source_booking == "Callcenter":
					source_identifier = get_source_identifier(hotel, reservation_json)

				if source_booking == "Agency":
					source_identifier = "AGENCY"

				if specific_params.get("custom_booking_source_in_comments"):
					source_identifier = specific_params["custom_booking_source_in_comments"]

			if specific_params.get('SPECIAL_LENGUA_PORTUGUES'):
				comments += "\n" + SOURCE_RESERVATION_PT.upper() + source_identifier
			else:
				comments += "\n" + SOURCE_RESERVATION.upper() + source_identifier

		else:

			if not specific_params.get("HIDE_SOURCE_IN_COMMENTS", None):

				# Port hotel wants always payed and source toguether. not change order of this!
				source_booking = reservation_json.get("source")
				if source_booking:
					source_identifier = "[WEB]"
					if source_booking == "Callcenter":
						source_identifier = get_source_identifier(hotel, reservation_json)
					if source_booking == "Agency":
						source_identifier = "[AGENCY]"

					if specific_params.get("custom_booking_source_in_comments"):
						source_identifier = specific_params["custom_booking_source_in_comments"]

					comments += "\n" + translations.get('T_SOURCE').upper() + source_identifier

		club_member = extra_info.get("clubMember")
		if club_member  and add_club_info_incomments(hotel):
			comments += "\n " + translations.get('T_CLUB_MEMBER_TEXT').upper() + ": " + str(club_member)

		club_info_dict = {}
		# add club info

		if specific_params.get("SHOW_INFO_CLUB_FOR_NIGHTS") and extra_info.get("user_club_info"):
			try:
				if extra_info.get('clubMember_level'):
					club_info_dict["Nivel de Utilizador"] = extra_info.get('clubMember_level')
				club_info_dict["Noites Acumuladas"] = str(
					int(extra_info.get("user_club_info", {}).get("category_data", {}).get("accumulated_nights")))

			except Exception as e:
				club_info_dict["Noites Acumuladas"] = ""
		else:
			if club_member:
				club_info_dict["clubMember ID"] = extra_info.get('clubMember')
			if extra_info.get('clubMember_level'):
				club_info_dict["clubMember Level"] = extra_info.get('clubMember_level')
			if extra_info.get('clubMember_user_points_by_reservation'):
				club_info_dict["Puntos en esta reserva"] = extra_info.get('clubMember_user_points_by_reservation')
			if extra_info.get('clubMember_user_points'):
				club_info_dict["Puntos totales acumulados"] = extra_info.get('clubMember_user_points')

		send_specific_club_info_in_comments = integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, SEND_SPECIFIC_CLUB_INFO_IN_COMMENTS)
		if send_specific_club_info_in_comments:
			club_info_keys_list = send_specific_club_info_in_comments.split(";")
			for club_info_key in club_info_keys_list:
				club_info_dict = {
					club_info_key: extra_info.get(club_info_key)
				}

		top_priority_club_info_in_comments = integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, TOP_PRIORITY_CLUB_INFO_IN_COMMENTS)
		if top_priority_club_info_in_comments and top_priority_club_info_in_comments.lower() == "true":
			copy_comments = comments
			comments = ""
			for key_info, value_info in club_info_dict.items():
				comments += ("\n%s = %s" % (key_info, value_info))
			comments += ("\n%s" % (copy_comments))
		else:
			for key_info, value_info in club_info_dict.items():
				comments += ("\n%s = %s" % (key_info, value_info))

		bono_gift_used = extra_info.get("bono_gift_used")
		if bono_gift_used:
			comments += "\n %s %s" % (translations.get('T_BONO_GIFT'), bono_gift_used)

		integration_name = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()
		if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, SHOW_CREDIT_CARD_BASIC_COMMENTS):
			cc_datas = extract_extra_info_ccdata(reservation_json, hotel['applicationId'], hotel, {})
			if not cc_datas:
				cc_datas = {"cc_datas": extract_extra_info_tpv(reservation_json)}

			if cc_datas and cc_datas.get("cc_datas"):
				cc_datas = cc_datas.get("cc_datas")
				cc_type = cc_datas.get("cc_type")
				cc_company = cc_datas.get("cc_company")
				cc_holder = cc_datas.get("cc_holder")

				if not cc_holder:
					cc_holder = reservation_json['name'] + " " + reservation_json['lastName']

				comments += "\nCredit card info: %s | %s | %s" % (cc_type, cc_company, cc_holder)

		payment_gateway = extra_info.get("payment_gateway", "")
		if payment_gateway == "AMAZONPAY":
			comments += "\n %s " % (translations.get('T_AMAZON_PAY'))

		if extra_info.get("agency_id"):
			hotel_agency = get_hotel_advance_config_item(hotel, "remote login agencies")
			if hotel_agency:
				hotel_agency = hotel_manager_utils.get_hotel_by_application_id(hotel_agency[0].get("value", ""))

			else:
				hotel_agency = hotel

			all_agencies = get_all_agencies_of_hotel(hotel_agency['applicationId'])
			agency_used = [x for x in all_agencies if x.get("agency_hash") == extra_info.get("agency_hash")]
			if agency_used:
				agency_used = agency_used[0]
				agency_txt = u"\n\nDatos Agencia\nNombre agencia: %s\n" % extra_info.get("agency_name")
				agency_txt += u"Direccion: %s - %s - %s - %s - %s\n" % (
					agency_used.get("billing_address"), agency_used.get("city"), agency_used.get("postal_code"),
					agency_used.get("province"), agency_used.get("pais"))
				agency_txt += u"Precio total PVP: %s\n" % extra_info.get("agency_pvp_price")
				agency_txt += u"Porcentaje de comisión: %s%%\n" % extra_info.get("agency_commision")

				commision_agency = float(extra_info.get("agency_pvp_price", 0)) * (float(extra_info.get("agency_commision", 0)) / 100)
				payment_net = float(extra_info.get("agency_pvp_price", 0)) - commision_agency
				agency_txt += u"Total comisión agencia: %.2f\n" % commision_agency
				agency_txt += u"Total Neto a pagar: %.2f\n" % payment_net
				agency_txt += u"FIN Datos Agencia\n\n"

				comments += agency_txt

		# See bookingUtils.py at Hotel-webs ("%s,%s,%s,%s" % (personalDetails.address, personalDetails.postalCode, personalDetails.city, personalDetails.province)

		address = reservation_json.get('address', "")
		postal_code = ""
		city = ""
		province = ""

		if address:
			full_address = address.split(",")
			if len(full_address) > 2:
				address = full_address[0]
				postal_code = full_address[1]
				city = full_address[2]
			if len(full_address) > 3:
				province = full_address[3]
		else:
			# address hasn't to be Null never
			address = ""

		if extra_info.get("address_breakdown"):
			address_breakdown = extra_info.get("address_breakdown")
			if address_breakdown.get("address"):
				address = address_breakdown.get("address")

			if address_breakdown.get("city"):
				city = address_breakdown.get("city")
				if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'],
																						integration_name,
																						SEND_CITY_AS_PROVINCE):
					province = city

			if address_breakdown.get("postalCode"):
				postal_code = address_breakdown.get("postalCode")

		if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name,
																				SEND_POSTAL_CODE_FROM_EXTRA_DATA):
			if extra_info.get("extra_fields", {}).get("postal_code"):
				postal_code = extra_info.get("extra_fields", {}).get("postal_code")

		country = ""
		country_name = ""
		if reservation_json.get('country'):
			country = reservation_json.get('country', '').split(" ")[0]
			if not integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, USE_ISO_FORMAT_COUNTRIES):
				country_name = _get_country_name(country.lower())
			else:
				country_name = country
		else:
			country = reservation_json.get('geolocation', '').split(" ")[0]
			if not integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, USE_ISO_FORMAT_COUNTRIES):
				country_name = _get_country_name(country.lower())
			else:
				country_name = country

		if country_name:
			country_name = country_name.upper()

		start = reservation_json.get('startDate')
		end = reservation_json.get('endDate')

		num_days = (date_utils.string_to_date(end) - date_utils.string_to_date(start)).days
		duration = "P%sD" % num_days

		amount_after_taxes = float(reservation_json.get('price'))
		discount_supplements = True
		if (not integration_hotel_manager_utils.discount_supplements_in_total_reservation(hotel_code)) and (not specific_params.get(DISCOUNT_SUPLEMENT_CONFIG)):
			discount_supplements = False
			amount_after_taxes += float(reservation_json.get('priceSupplements', '0'))

		amount_before_taxes = amount_after_taxes / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)

		tax_amount = amount_after_taxes - amount_before_taxes
		res_identifier = reservation_json.get('identifier')

		if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, "fix shopping cart occupancy"):
			specific_params['fix_shopping_cart_occupancy'] = True

		room_stays, total_guest_count = _build_room_stays_json(reservation_json, amount_after_taxes, start, end, num_days, hotel,specific_params=specific_params)

		num_adults, num_children, num_babies = _count_persons(total_guest_count, specific_params)

		count = {'num_persons': num_adults + num_children + num_babies,
				 'num_adults': num_adults,
				 'num_children': num_children,
				 'num_babies': num_babies
				 }

		if not specific_params.get("NOT_MERGE_SAME_ROOMS"):
			room_stays = _merge_room_stays_if_same_room_type(room_stays, include_capacities=specific_params.get("INCLUDE_CAPACITIES", False), accumulate_persons=specific_params.get("ACCUMULATE_PERSONS", True))

		send_promotion_in_comments = _has_property_at_integration_configuration(hotel, SEND_PROMOTIONS_IN_COMMENTS)
		if send_promotion_in_comments:
			promotion_names = _get_promotion_names(all_promotions, hotel, customer_language, reservation_json, extra_info)
			for promotion_name in promotion_names:
				comments += u"\n%s" % promotion_name

		send_billing_data = _has_property_at_integration_configuration(hotel, SEND_BILLING_DATA)
		billing_data = {}
		if send_billing_data and extra_info.get(BILLING_DATA):
			billing_data = extra_info.get(BILLING_DATA)
			comments += "\nBilling name: %s %s" % (extra_info.get(BILLING_DATA).get("billing_first_name", ""), extra_info.get(BILLING_DATA, "").get("billing_last_name", ""))

		all_rates = get_rates_of_hotel(hotel, customer_language, include_removed=True)
		selectedRates = [x for x in all_rates if x.get('key') == reservation_json.get('rate')]

		rate_conditions = ""
		if selectedRates:
			rate = selectedRates[0]
			try:
				sanitizer = Sanitizer()
				description = rate.get('description')
				if not description:
					description = ""
				rate_conditions = sanitizer.sanitize(html.unescape(description))
			except Exception as e:
				logging.info("Imposible to sanitize_html IN: %s", rate.get('description'))
				rate_conditions = rate.get('description', '')

		if isinstance(rate_conditions, str):
			rate_conditions = str(rate_conditions)

		rate_conditions = unicodedata.normalize('NFKD', rate_conditions).encode('ASCII', 'ignore').lower()

		if reservation_json.get('birthday'):
			birthday = reservation_json.get('birthday')
		else:
			birthday = ""

		promocode = reservation_json.get('promocode')

		amount_before_taxes_formated = "%.2f" % amount_before_taxes
		amount_after_taxes_formated = "%.2f" % amount_after_taxes
		tax_amount_formated = "%.2f" % tax_amount
		total_sups = float(reservation_json.get("priceSupplements", "0"))

		send_payments = not integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'],
																								 integration_name,
																								 NEVER_SENT_PAYMENT_AMOUNT)

		if send_payments and payed_amount and not payed_amount == "0" and not float(amount_after_taxes_formated) == 0:

			if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, SUPPLEMENTS_INCLUDED_IN_PAYED):
				# Not adding price supplements
				difference_total_payed = float(payed_amount) / (float(amount_after_taxes_formated))
				for room in room_stays:
					room_amount_payed = float(room['room_amount']) * difference_total_payed
					room['room_amount_payed'] = "%.2f" % room_amount_payed
			else:
				sups_by_room = total_sups / len(room_stays)
				if not discount_supplements:
					# Supplements included in amount_after_taxes_formated
					difference_total_payed = float(payed_amount) / (float(amount_after_taxes_formated))
				else:
					# Supplements not included in amount_after_taxes_formated
					difference_total_payed = float(payed_amount) / (float(amount_after_taxes_formated) + total_sups)
				for room in room_stays:
					room_plus_sups = float(room['room_amount']) + sups_by_room
					room_amount_payed = room_plus_sups * difference_total_payed
					room['room_amount_payed'] = "%.2f" % room_amount_payed

			if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, ADD_EXTRA_PAYED_FIRST_ROOM):
				total_room_payed = sum(map(lambda m: float(m.get("room_amount_payed", "0.0")), room_stays))
				extra_payed = float(payed_amount) - total_room_payed
				room_stays[0]['room_amount_payed'] = "%.2f" % (float(room_stays[0]['room_amount_payed']) + extra_payed)

		include_current_payment_in_xml = True
		# This is for Noray because of they duplicate their identifier if we include all payments in modifications.
		never_send_payments_in_agencies_modifications = integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, "never sent payments in agencies modifications")
		if reservation_json.get("source") == "Agency" and (extra_info.get('modification_timestamp') or reservation_json.get("modificationTimestamp")) and never_send_payments_in_agencies_modifications.lower() == "true":
			include_current_payment_in_xml = False
			comments += ".\n " + translations.get('T_NEW_PAYMENT').upper()

		if specific_params.get("AMOUNTS_ONLY_INT"):
			amount_before_taxes_formated = amount_before_taxes_formated.replace(".","")
			amount_after_taxes_formated = amount_after_taxes_formated.replace(".","")
			tax_amount_formated = tax_amount_formated.replace(".","")

		if integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'],integration_name, INTEGRATE_CALLCENTER_COMMENTS):
			message = extra_info.get('comments_callseeker', '')
			if message:
				comments += "\n\r\n\r--------------\n\r\n\r"
				comments += message

			message = extra_info.get('comments_prebooking', '')
			if message:
				if not message.replace("Prereserva - Comentarios: ", "") in comments:
					comments += "\n\r\n\r--------------\n\r\n\r"
					comments += message

		if specific_params.get("SEND_PAYMENT_PENDING"):
			payed_amount = _get_total_payed_amount_from_all_sources(extra_info)
			if payed_amount and not str(payed_amount) == "0":
				total = max(float(amount_after_taxes) - float(payed_amount), 0)
				comments += "\n " + translations.get('T_PENDING_PAYMENT').upper() + ": " + str(total) + specific_params.get("CURRENCY_SYMBOL", "")
			else:
				comments += "\n " + translations.get('T_PENDING_PAYMENT').upper() + ": " + str(amount_after_taxes) + specific_params.get("CURRENCY_SYMBOL", "")

		guest = {
			'age_qualifying_code': "10", #Adult
			'primary_indicator': 'true',
			'profile_type': '1', #Customer profile
			'name': name,
			'last_name': last_name,
			'telephone': telephone,
			'phone_tech_type': '1', #voice
			'phone_formatted_ind': 'true', #Entire number is passed
			'phone_default_ind': 'true', #Is the primary phone for the profile
			'email': email,
			'email_type': '1', #Personal
			'email_default_ind': 'true', #This is the primary email for the profile
			'address_type': '1', #Home
			'address_line': address,
			'address_city': city,
			'postal_code': postal_code,
			'province': province,
			'country': country_name,
			'country_code': country,
			'birthday': birthday,
			'doc_id': personalId,
			'billing_data': billing_data
		}

		global_info = {
			'start': start,
			'end': end,
			'duration': duration,
			# 'credit_card': credit_card, #Lets ignore this for the moment
			'currency_code': specific_params.get("CURRENCY_CODE", "EUR"),
			'amount_before_taxes': amount_before_taxes_formated,
			'amount_after_taxes': amount_after_taxes_formated,
			'tax_amount': tax_amount_formated,
			'decimal_places': specific_params.get("DECIMAL_PLACES", 2),
			'res_identifier': res_identifier,
			'res_id_type': '14', #For reservation confirmation number
			'comments': comments,
			'tax_percentage': (float(specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)) -1) * 100,
			'total_guest_count': total_guest_count,
			'promotion': promotion,
			'rate_conditions': rate_conditions,
			'payed_amount': payed_amount,
			'additional_services': additional_services,
			'promocode': promocode,
			'comments_only_first_room': send_promotion_in_comments, # i.e. Port Hotels only wants the comments on the first room
			'include_current_payment_in_xml': include_current_payment_in_xml,
			'package_reservation': package_reservation
		}

		result = {
			'source': source,
			'creation_time': creation_time,
			'modification_time': modification_time,
			'reservation_status': reservation_status,
			'room_stays': room_stays,
			'guest': guest,
			'global_info': global_info,
			'hotel_code': hotel_code,
			'hotel_name': hotel_name,
			'count': count,
			'modificationTimestamp': reservation_json.get("modificationTimestamp")
		}

		return result


def _has_property_at_integration_configuration(hotel, property_name):
	integration_configurations = get_integration_configuration_of_hotel(hotel, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())
	if integration_configurations:
		integration_configuration = integration_configurations[0]
		exist_promotion_to_comments = list(filter(lambda x: str(x).lower() == str('%s @@ true' % property_name).lower(), integration_configuration.get('configurations', {})))
		return exist_promotion_to_comments
	return False


def _get_value_at_integration_configuration(hotel, property_name):
	integration_configurations = get_integration_configuration_of_hotel(hotel,interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name())
	if integration_configurations:
		integration_configuration = integration_configurations[0]
		for x in integration_configuration.get('configurations', {}):
			info = x.split(" @@ ")
			config_name = info[0]
			config_value = info[1]
			if config_name.lower() == property_name.lower():
				return config_value
	return ""


def _get_promotion_names(all_promotions, hotel, customer_language, reservation_json, extra_info=None):
	extra_info = extra_info or {}
	result = []

	if not all_promotions:
		all_promotions = get_promotions_of_hotel(hotel, customer_language)
	reservation_promotions = reservation_json.get('promotions') or ''
	promotions_keys = extra_info.get("real_promotions_keys", reservation_promotions.split(','))

	for key in promotions_keys:
		matching_promotions = [promo for promo in all_promotions if promo.get('key') == key]
		if matching_promotions:
			result.append(matching_promotions[0].get('name', ''))

	return result




def is_forced_pt(hotel):
	language_aux = get_hotel_advance_config_item(hotel, "Language Management")
	return True if language_aux and language_aux[0].get("value") == 'PORTUGUESE' else False


def get_translations(hotel: dict):
	if is_forced_pt(hotel):
		return get_translations_dict('PORTUGUESE')

	translations = get_translations_dict('SPANISH')
	new_language = _get_value_at_integration_configuration(hotel, 'language constants in comments')
	if new_language:
		translations = get_translations_dict(new_language)

	return translations


def _get_list_additional_service(reservation_json, specific_params, hotel):

	additional_services = []

	integration_name = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()
	service_days_as_quantity = integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, SERVICE_DAYS_AS_QUANTITY)
	send_additional_services_by_type = integration_hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], integration_name, SEND_ADDITIONAL_SERVICES_BY_TYPE)

	extra_info = {}
	extra_info_text = reservation_json.get('extraInfo')
	if extra_info_text and isinstance(extra_info_text, str):
		extra_info = json.loads(extra_info_text)


	strip_only_first_space = lambda x: x[1:] if x and x[0] == " " else x

	if extra_info.get("additional_services_keys"):

		additionalServicesTmp = extra_info.get('additional_services_keys').split(';')
		for service in additionalServicesTmp:
			pattern = re.compile("(.*) - name: (.*) - cantidad: ([0-9]*\.?[0-9]*) - dias: ([0-9]*\.?[0-9]*) - precio: ([0-9]*\.?[0-9]*)")
			straux = pattern.match(service)
			if straux:

				total_price = float(straux.group(5))
				quantity = int(straux.group(3))
				days = int(straux.group(4))

				if service_days_as_quantity:
					quantity = days
					logging.info("taking days as quantity: %s", quantity)

				additional_service = {}
				service_key = straux.group(1)
				service_key = service_key.strip()
				additional_service['name'] = straux.group(2)
				additional_service['quantity'] = quantity
				additional_service['days'] = days
				additional_service['start_date'] = reservation_json.get("startDate")
				star_date_time = datetime.datetime.strptime(reservation_json.get("startDate"), "%Y-%m-%d")
				end_date_time = star_date_time + datetime.timedelta(days=days)
				end_date = end_date_time.strftime(ISO_DATE_FORMAT)
				additional_service['end_date'] = end_date
				additional_service['price'] = total_price
				additional_service['amount_after_tax'] = float(total_price)

				amount_before_tax_service = float(total_price) / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)

				additional_service['amount_before_tax'] = "%.2f" % amount_before_tax_service
				additional_service['currency_code'] = specific_params.get("CURRENCY_CODE", "EUR")

				inventory_code = get_identifier_additional_service(hotel, service_key)
				if send_additional_services_by_type.lower() == "true":
					additional_service['type'] = get_type_additional_service(hotel, additional_service['name'])

				additional_service['inventory_code'] = inventory_code

				additional_services.append(additional_service)


	elif reservation_json.get('additionalServices'):

		additionalServicesTmp = reservation_json.get('additionalServices').split(';')
		for service in additionalServicesTmp:
			pattern = re.compile("(.*) - cantidad: ([0-9]*\.?[0-9]*) - dias: ([0-9]*\.?[0-9]*) - precio: ([0-9]*\.?[0-9]*)")
			straux = pattern.match(service)
			if straux:
				additional_service = {}
				additional_service['name'] = strip_only_first_space(straux.group(1))
				additional_service['quantity'] = int(straux.group(2))
				additional_service['days'] = int(straux.group(3))
				additional_service['price'] = float(straux.group(4))
				# additional_service['amount_after_tax'] = int(straux.group(3)) * float(straux.group(4))
				additional_service['amount_after_tax'] = int(float(straux.group(4)))

				amount_before_tax_service = int(float(straux.group(4))) / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)

				additional_service['amount_before_tax'] = "%.2f" % amount_before_tax_service
				additional_service['currency_code'] = specific_params.get("CURRENCY_CODE", "EUR")

				service_key = get_key_additional_service(hotel, additional_service['name'])
				inventory_code = get_identifier_additional_service(hotel, service_key)

				additional_service['inventory_code'] = inventory_code
				if send_additional_services_by_type.lower() == "true":
					additional_service['type'] = get_type_additional_service(hotel, additional_service['name'])

				additional_services.append(additional_service)

	return additional_services

def _get_package_full_info(package_key, hotel, language="SPANISH"):
	package_info = {}

	res_names = get_web_page_properties(hotel, language, "priceIncreaseName")
	res_descs = get_web_page_properties(hotel, language, "priceIncreaseDescription")

	for pack_res in res_names:
		if pack_res['entityKey'] == package_key:
			package_info["package_name"] = pack_res.get("value", "")
			break

	for pack_res in res_descs:
		if pack_res['entityKey'] == package_key:
			package_info["package_description"] = pack_res.get("value", "")
			break

	return package_info


def _get_country_name(country):
	country_map = {
		'es': 'Spain',
		'uk': 'United Kingdom',
		'pt': 'Portugal',
		'de': 'Germany',
		'ie': 'Ireland',
		'fr': 'France',
		'it': 'Italy'
	}

	return country_map.get(country, country)

def _get_international_board_code(board_name):
	# Check first in SPANISH
	if "media" in board_name:
		return "HB"
	if "completa" in board_name:
		return "FB"
	if "cena" in board_name:
		return "HB"
	if "gala" in board_name:
		return "HB"
	if "solo" in board_name:
		return "RO"
	if "desayuno" in board_name:
		return "BB"

	if "todo" in board_name:
		return "AI"

	#SECOND TRY ENGLISH
	meals = {
		'only': 'RO',
		'breakfast': 'BB',
		'half': 'HB',
		'full': 'FB',
		'inclusive': 'AI'
	}

	for current_board, board_code in meals.items():
		if current_board in board_name.lower():
			return board_code

	return ''

def _build_room_stays(reservation_xml, total_amount, start_date, end_date, num_days, hotel, specific_params={}):
	num_rooms = int(reservation_xml.find_element('numRooms').text)

	# Note that we are calculating an average
	daily_price = (float(total_amount) / float(num_days)) / float(num_rooms)

	room_price = float(total_amount) / float(num_rooms)
	room_amount = "%.2f" % room_price
	room_amount_before_taxes = "%.2f" % (room_price /specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))
	room_taxes = "%.2f" % (room_price - (room_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)))

	dates_range = [date_utils.date_to_string(x) for x in date_utils.get_dates_in_range(start_date, end_date)]

	all_rooms = {x['key']: x for x in get_rooms_of_hotel(hotel, language=specific_params.get("LANGUAGE", "ENGLISH"), include_removed=True)}
	all_boards = {x['key']: x for x in get_boards_of_hotel(hotel, language=specific_params.get("LANGUAGE", "ENGLISH"), include_removed=True)}

	rate_key = reservation_xml.find_element('rate').text
	rate_key = get_real_rate_from_mapping(hotel, rate_key)

	board_key = reservation_xml.find_element('regimen').text
	board_key = get_real_board_from_mapping(hotel, board_key)

	board_name = all_boards[board_key]['name'].lower()

	board_code = _get_international_board_code(board_name)

	meals = {
		'only': (False, False, False),
		'breakfast': (True, False, False),
		'half': (True, False, True),
		'full': (True, True, True),
		'inclusive': (True, True, True)
	}

	breakfast, lunch, dinner = (False, False, False)

	for current_board, meals_included in meals.items():
		if current_board in board_name:
			breakfast, lunch, dinner = meals_included
			break

	daily_amount = "%.2f" % daily_price
	amount_before_tax = "%.2f" % (daily_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))

	daily_rates = []
	for current_date in dates_range:
		effective_date = current_date
		expire_date = date_utils.date_to_string(date_utils.string_to_date(effective_date) + datetime.timedelta(days=1))

		daily_rate = {
			'effective_date': effective_date,
			'expire_date': expire_date,
			'amount_before_tax': amount_before_tax,
			'amount': daily_amount,
		}
		daily_rates.append(daily_rate)

	result = []

	total_adults = 0
	total_kids = 0


	for num_room in range(1, num_rooms + 1):

		room_key = reservation_xml.find_element('roomType' + str(num_room)).text

		adults = reservation_xml.find_element('adults' + str(num_room)).text
		total_adults += int(adults)

		guest_counts = [{'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_ADULT"), 'num_persons': int(adults)}]

		kids = reservation_xml.find_element('kids' + str(num_room)).text
		total_kids += int(kids)

		if kids != '0':
			guest_counts.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_CHILD"), 'num_persons': int(kids)})

		# note that we havent the babies in the reservation XML
		babies = reservation_xml.find_element('babies' + str(num_room)).text

		if babies != '0':
			guest_counts.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_BABIES"), 'num_persons': int(kids)})

		capacity = '%s-%s-%s' % (adults, kids, babies)

		rate_plan_code = '%s_%s' % (alphanumeric_to_id(rate_key), alphanumeric_to_id(board_key))

		current_room = all_rooms[room_key]

		room_stay = {
			'room_type_code': alphanumeric_to_id(room_key),
			'rate_type_code': alphanumeric_to_id(rate_key),
			'board_type_code': alphanumeric_to_id(board_key),
			'board_key': board_key,
			'number_of_units': '1', # Each room Stay will be for a different room
			'room_name': current_room.get('name', ""),
			'room_description': current_room.get('description', ""),
			'rate_plan_code': rate_plan_code,
			'breakfast': breakfast,
			'lunch': lunch,
			'dinner': dinner,
			'board_code': board_code,
			'daily_rates': daily_rates,
			'currency_code': specific_params.get("CURRENCY_CODE", "EUR"),
			'room_amount': room_amount,
			'decimal_places': 2,
			'room_amount_before_taxes': room_amount_before_taxes,
			'room_taxes': room_taxes,
			'guest_count': guest_counts,
			'capacity': capacity

		}
		result.append(room_stay)

	total_guest_count = [{'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_ADULT"), 'num_persons': total_adults}]
	total_guest_count.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_CHILD"), 'num_persons': total_kids})

	return result, total_guest_count


def _check_add_fix_round_daily_room_prices(room_amount, daily_rates, tax):
	count_price = 0
	for room_daily_info in daily_rates:
		count_price += float(room_daily_info.get("amount", 0))

	if count_price != float(room_amount):
		diff = float(room_amount) - count_price
		daily_rates[-1]["amount"] = "%.2f" % (float(daily_rates[-1].get("amount", 0)) + diff)
		daily_rates[-1]["amount_before_tax"] = "%.2f" % (float(daily_rates[-1].get("amount", 0)) / tax)


def round_up(n, decimals=0):
	multiplier = 10 ** decimals
	return math.ceil(n * multiplier) / multiplier


def _build_room_stays_json(reservation_json, total_amount, start_date, end_date, num_days, hotel, specific_params={}):
		logging.info("_build_room_stays_json total amount received: %s", total_amount)

		room_amount = 0
		room_amount_before_taxes = 0
		room_taxes = 0

		num_rooms = int(reservation_json.get('numRooms'))
		dates_range = [date_utils.date_to_string(x) for x in date_utils.get_dates_in_range(start_date, end_date)]

		shopping_cart_elements = None
		extra_info = reservation_json.get("extraInfo")
		prices_per_day = []
		force_discount_prices_shopping = False
		if extra_info:
			extra_info = json.loads(extra_info)
			prices_per_day = extra_info.get("prices_per_day")

			shopping_cart_absent_or_fix_required = not extra_info.get("shopping_cart") or (specific_params and specific_params.get("fix_shopping_cart_occupancy"))

			if shopping_cart_absent_or_fix_required and extra_info.get("shopping_cart_human_read"):	# something strange has happend. Let's convert shopping cart human reaf in normal shopping cart
				extra_info['shopping_cart'] = []
				for index, x in enumerate(extra_info['shopping_cart_human_read'].get("rooms", []), start=1):
					adults, kids, babies = x.get("occupancy", "").split("-")
					extra_info['shopping_cart'].append({
						"index_room": str(index),
						"price": str(x.get("price")),
						"rate_key": x.get("rate_key"),
						"regimen_key": x.get("board_key"),
						"room_key": x.get("room_key"),
						"adults": int(adults),
						"kids": int(kids),
						"babies": int(babies)
					})

			shopping_cart_elements = extra_info.get("shopping_cart")

		if shopping_cart_elements:
			reservation_json["numRooms"] = len(shopping_cart_elements)
			num_rooms = int(reservation_json.get('numRooms'))

		rooms_info_prices = {}
		calculated_total_amount = 0

		# Note that if we have changed dates or price it doesn't make sense anymore to send daily prices
		modified_price_or_dates = reservation_json.get('incidents') and ('Start Date changed' in reservation_json.get('incidents') or
																		 'End Date changed' in reservation_json.get('incidents') or
																		 'Total price changed' in reservation_json.get('incidents'))

		price_suplements = float(reservation_json.get('priceSupplements', '0'))

		inc_sup_by_day = 0
		inc_sup_by_room = 0
		inc_sup = 0

		if price_suplements and specific_params.get("ADD_SERVICES_AMOUNT_IN_DAILY_ROOMS"):
			logging.info("ADD_SERVICES_AMOUNT_IN_DAILY_ROOMS found in specific_params : %s", specific_params)

			inc_sup = float(price_suplements)
			inc_sup_by_day = float(price_suplements / num_rooms / len(dates_range))
			inc_sup_by_room = float(price_suplements / num_rooms )

			logging.info("_build_room_stays_json inc_sup: %s inc_sup_by_day: %s inc_sup_by_room: %s", inc_sup, inc_sup_by_day, inc_sup_by_room)

		if reservation_json.get("modificationTimestamp") and shopping_cart_elements:
			logging.info("ADVANCED MODIFICATION FOUND")

			for room_line in shopping_cart_elements:
				room_key = room_line.get("room_key")

				num_room = room_line.get("index_room")

				room_price = float(room_line.get("price")) + inc_sup_by_room
				room_amount = "%.2f" % room_price
				room_amount_before_taxes = "%.2f" % (room_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))
				room_taxes = "%.2f" % (room_price - (room_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)))

				# note that a modification hasn't daily prices!
				daily_rates = []
				for current_date in dates_range:
					effective_date = current_date
					expire_date = date_utils.date_to_string(date_utils.string_to_date(effective_date) + datetime.timedelta(days=1))

					# Note that we are calculating an average
					daily_price = (float(room_price) / float(num_days))

					daily_amount = "%.2f" % daily_price
					amount_before_tax = "%.2f" % (daily_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))

					calculated_total_amount += daily_price

					if specific_params.get("AMOUNTS_ONLY_INT"):
						daily_amount = daily_amount.replace(".","")
						amount_before_tax = amount_before_tax.replace(".","")

					daily_rate = {
								'effective_date': effective_date,
								'expire_date': expire_date,
								'amount_before_tax': amount_before_tax,
								'amount': daily_amount,
							}

					daily_rates.append(daily_rate)

				rooms_info_prices[room_key + str(num_room)] = {
					"daily_rates": daily_rates,
					"room_amount": room_amount,
					"room_amount_before_taxes": room_amount_before_taxes,
					"room_taxes": room_taxes
				}


		elif prices_per_day and not modified_price_or_dates:

			real_index_list_for_prices = _get_real_index_from_price_per_days(prices_per_day, num_rooms)
			current_range = range(1, num_rooms + 1)
			for num_room in current_range:
				try:
					index_room_for_get_price = real_index_list_for_prices[num_room - 1]
				except Exception as e:
					index_room_for_get_price = num_room

				if shopping_cart_elements and len(shopping_cart_elements) >= num_room:
					room_key = shopping_cart_elements[num_room -1].get("room_key")
				else:
					room_key = reservation_json.get('roomType' + str(num_room))

				room_key_dict_for_prices = str(index_room_for_get_price) + ": " + room_key
				room_daily_prices = prices_per_day.get(room_key_dict_for_prices)
				if room_daily_prices:

					room_price = float(room_daily_prices.get("total")) + inc_sup_by_room
					room_amount = "%.2f" % room_price
					room_amount_before_taxes = "%.2f" % (room_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))
					room_taxes = "%.2f" % (room_price - (room_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)))

					logging.info("_build_room_stays_json daily room price %s", room_amount)

					daily_rates = []
					for current_date in dates_range:

						effective_date = current_date
						expire_date = date_utils.date_to_string(date_utils.string_to_date(effective_date) + datetime.timedelta(days=1))

						date_extra_info_format = datetime.datetime.strptime(effective_date, '%Y-%m-%d').strftime('%d/%m/%Y')

						if len(room_daily_prices.get(date_extra_info_format, [])) > 2:
							daily_price = float(room_daily_prices.get(date_extra_info_format)[2]) + inc_sup_by_day

							logging.info("_build_room_stays_json daily_price for date %s : %s", date_extra_info_format, daily_price)

							daily_amount = "%.2f" % daily_price
							amount_before_tax = "%.2f" % (daily_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))

							calculated_total_amount += daily_price

							if specific_params.get("AMOUNTS_ONLY_INT"):
								daily_amount = daily_amount.replace(".", "")
								amount_before_tax = amount_before_tax.replace(".", "")

							daily_rate = {
								'effective_date': effective_date,
								'expire_date': expire_date,
								'amount_before_tax': amount_before_tax,
								'amount': daily_amount,
								'tax_amount': "%.2f" % (float(daily_amount) - float(amount_before_tax))
							}

							daily_rates.append(daily_rate)

					logging.info("_build_room_stays_json daily_rates %s ", daily_rates)

					if daily_rates:
						_check_add_fix_round_daily_room_prices(room_amount, daily_rates, specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))

						logging.info("_build_room_stays_json room_amount fixed %s and daily_rates fixed: %s", room_amount, daily_rates)

						rooms_info_prices[room_key + str(num_room)] = {
							"daily_rates": daily_rates,
							"room_amount": room_amount,
							"room_amount_before_taxes": room_amount_before_taxes,
							"room_taxes": room_taxes
						}

		if specific_params.get("FIX_TRUNCATE_PRICE"):
			total_amount_no_supplements = calculated_total_amount
		else:
			total_amount_no_supplements = float("%.0f" % (float(reservation_json.get("price", 0))))
			calculated_total_amount = float("%.0f" % (float(calculated_total_amount)))

		daily_rates_by_average = []
		room_amount_by_avarage = 0
		room_amount_before_taxes_by_avarage = 0
		room_taxes_by_avarage = 0

		# if something has failed, old way! (maybe this reservation doesn't have the daily process yet, or it is a wrong modification)
		if (not total_amount_no_supplements == (calculated_total_amount - inc_sup)) or (not len(rooms_info_prices) == num_rooms):

			logging.info('Imposible to extract daily prices. Daily prices found in extra info %s. Total reservation %s VS calculated_total_amount %s', rooms_info_prices, total_amount, calculated_total_amount - inc_sup)

			# Note that we are calculating an average (OLD WAY), and total amount has already added the suplements!
			daily_price = 0
			room_price = 0

			if float(total_amount) and float(num_days) and float(num_rooms):
				daily_price = ((float(total_amount) / float(num_days)) / float(num_rooms))
				room_price = (float(total_amount) / float(num_rooms))

			room_amount_by_avarage = "%.2f" % room_price
			room_amount_before_taxes_by_avarage = "%.2f" % (room_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))
			room_taxes_by_avarage = "%.2f" % (room_price - (room_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)))

			daily_amount = "%.2f" % daily_price
			amount_before_tax = "%.2f" % (daily_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))

			if specific_params.get("AMOUNTS_ONLY_INT"):
				daily_amount = daily_amount.replace(".", "")
				amount_before_tax = amount_before_tax.replace(".", "")

			daily_rates_by_average = []
			for current_date in dates_range:
				effective_date = current_date
				expire_date = date_utils.date_to_string(date_utils.string_to_date(effective_date) + datetime.timedelta(days=1))

				daily_rate = {
					'effective_date': effective_date,
					'expire_date': expire_date,
					'amount_before_tax': amount_before_tax,
					'amount': daily_amount,
				}
				daily_rates_by_average.append(daily_rate)

			_check_add_fix_round_daily_room_prices(room_amount_by_avarage, daily_rates_by_average, specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))

		all_rooms = {x['key']: x for x in get_rooms_of_hotel(hotel, language=specific_params.get("LANGUAGE", "ENGLISH"), include_removed=True)}
		all_boards = {x['key']: x for x in get_boards_of_hotel(hotel, language=specific_params.get("LANGUAGE", "ENGLISH"), include_removed=True)}
		all_rates = {x['key']: x for x in get_rates_of_hotel(hotel, language=specific_params.get("LANGUAGE", "ENGLISH"), include_removed=True)}

		rate_key = reservation_json.get('rate')
		if PACKAGE + PACKAGE_SEPARATOR in rate_key:
			rate_key = rate_key.split(PACKAGE_SEPARATOR)[2]
		rate_key = get_real_rate_from_mapping(hotel, rate_key)
		board_key = reservation_json.get('regimen')
		board_key = get_real_board_from_mapping(hotel, board_key)

		try:
			board_name = all_boards[board_key]['name'].lower()
		except:
			logging.warning("trying to get a deleted board: %s ", board_key)
			board_name = "deleted board"

		current_rate = all_rates.get(rate_key, {})

		board_code = _get_international_board_code(board_name)

		meals = {
			'only': (False, False, False),
			'half': (True, False, True),
			'full': (True, True, True),
			'inclusive': (True, True, True)
		}

		breakfast, lunch, dinner = (False, False, False)

		for current_board, meals_included in meals.items():
			if current_board in board_name:
				breakfast, lunch, dinner = meals_included
				break

		result = []

		total_adults = 0
		total_kids = 0
		total_babies = 0

		current_range = range(1, num_rooms + 1)
		real_index_list_for_prices = _get_real_index_from_price_per_days(prices_per_day, num_rooms)
		for num_room_range in current_range:

			num_room = num_room_range
			if shopping_cart_elements and len(shopping_cart_elements)>=num_room:
				#shopping cart way
				shopping_cart_e = shopping_cart_elements[num_room -1]

				room_key = shopping_cart_e.get("room_key")
				rate_key = shopping_cart_e.get("rate_key")
				if PACKAGE + PACKAGE_SEPARATOR in rate_key:
					rate_key = rate_key.split(PACKAGE_SEPARATOR)[2]
				board_key = shopping_cart_e.get("regimen_key")

				adults = int(shopping_cart_e.get("adults", 0))
				kids = int(shopping_cart_e.get("kids", 0))
				babies = int(shopping_cart_e.get("babies", 0))

				rate_key = get_real_rate_from_mapping(hotel, rate_key)
				board_key = get_real_board_from_mapping(hotel, board_key)

				board_name = all_boards[board_key]['name'].lower()
				index_room = int(shopping_cart_e.get("index_room", num_room))

			else:
				#normal way

				# note that this is not necessary with sopping cart because it is already sorted
				try:
					num_room = real_index_list_for_prices[num_room_range - 1]
				except Exception as e:
					num_room = num_room_range

				index_room = num_room
				room_key = reservation_json.get('roomType' + str(num_room))
				if not room_key:
					for index_room_prices_per_day, info in extra_info.get("prices_per_day").items():
						current_index_prices_per_day = int(index_room_prices_per_day.split(": ")[0])
						if num_room == current_index_prices_per_day:
							room_key = index_room_prices_per_day.split(": ")[1]

				adults = reservation_json.get('adults' + str(num_room))
				kids = reservation_json.get('kids' + str(num_room), 0)
				babies = reservation_json.get('babies' + str(num_room), 0)

			total_adults += int(adults)
			guest_counts = [{'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_ADULT", ""), 'num_persons': int(adults)}]

			all_guest = 0
			if not kids:
				# logging.warning("Reservation with identifier: %s, has kids with null value", reservation_json.get('identifier'))
				kids = '0'

			if not babies:
				babies = "0"

			total_kids += int(kids)

			if kids != '0':
				guest_counts.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_CHILD", ""), 'num_persons': int(kids)})

			if babies and babies != '0' and not specific_params.get("NEVER_INCLUDE_BABIES"):
				guest_counts.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_BABIES", 2), 'num_persons': int(babies)})

			rate_plan_code = '%s_%s' % (alphanumeric_to_id(rate_key), alphanumeric_to_id(board_key))

			current_room = all_rooms[room_key]

			total_babies += int(babies)

			capacity = '%s-%s-%s' % (adults, kids, babies)

			room_key = integration_hotel_manager_utils.get_real_room_from_mapping(hotel, room_key)

			if daily_rates_by_average:
				# if we haven't real daily prices separated by room, we do it y old way (avarage)
				daily_rates = daily_rates_by_average
				room_amount = room_amount_by_avarage
				room_amount_before_taxes = room_amount_before_taxes_by_avarage
				room_taxes = room_taxes_by_avarage

				logging.info("_build_room_stays_json daily_rates_by_average  room amount %s and daily_rates : %s", room_amount, daily_rates)

			else:
				daily_real_prices = rooms_info_prices.get(room_key + str(index_room), {})
				daily_rates = daily_real_prices.get("daily_rates", [])
				room_amount = daily_real_prices.get("room_amount", room_amount)
				room_amount_before_taxes = daily_real_prices.get("room_amount_before_taxes", room_amount_before_taxes)
				room_taxes = daily_real_prices.get("room_taxes", room_taxes)

				logging.info("_build_room_stays_json daily_real_prices  room amount %s and daily_rates : %s", room_amount, daily_rates)


			if force_discount_prices_shopping:
				discount_per_room = force_discount_prices_shopping / num_rooms
				discount_per_day = discount_per_room / num_days

				room_amount = "%.2f" % (float(room_amount) - discount_per_room)
				for x in daily_rates:
					x['amount'] = "%.2f" % (float(x['amount']) - discount_per_day)


			all_guest = int(adults) + int(kids) + int(babies)
			guest_rhp = []
			for i in range(1, all_guest + 1, 1):
				guest_rhp.append(i)

			room_stay = {
				'index_room': index_room,
				'room_type_code': alphanumeric_to_id(room_key),
				'rate_type_code': alphanumeric_to_id(rate_key),
				'board_type_code': alphanumeric_to_id(board_key),
				'board_key': board_key,
				'number_of_units': '1', #Each room Stay will be for a different room
				'room_name': current_room.get('name', ""),
				'room_description': current_room.get('description', ""),
				'rate_internal_name': current_rate.get("localName", ""),
				'rate_name': current_rate.get("name", ""),
				'rate_plan_code': rate_plan_code,
				'breakfast': breakfast,
				'lunch': lunch,
				'dinner': dinner,
				'board_code': board_code,
				'daily_rates': daily_rates,
				'currency_code': specific_params.get("CURRENCY_CODE", "EUR"),
				'room_amount': room_amount,
				'decimal_places': specific_params.get("DECIMAL_PLACES", 2),
				'room_amount_before_taxes': room_amount_before_taxes,
				'room_taxes': room_taxes,
				'guest_count': guest_counts,
				'capacity': capacity,
				"guest_rhp": guest_rhp
			}
			result.append(room_stay)

		# result Some channels as dingus neds like this
		result = sorted(result, key=lambda d: d['index_room'])

		if force_discount_prices_shopping:
			sum_total_room = sum([float(x.get("room_amount")) for x in result])
			if sum_total_room != total_amount:
				diff = total_amount - sum_total_room
				result[-1]["room_amount"] = "%.2f" % (float(result[-1]["room_amount"]) + diff)

			for room in result:
				sum_total_room = sum([float(x.get("amount")) for x in room.get("daily_rates")])
				if sum_total_room != float(room.get("room_amount")):
					diff = float(room.get("room_amount")) - sum_total_room
					room.get("daily_rates")[-1]["amount"] = "%.2f" % (float(room.get("daily_rates")[-1]["amount"]) + diff)

		total_guest_count = [{'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_ADULT"), 'num_persons': total_adults}]
		total_guest_count.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_CHILD"), 'num_persons': total_kids})
		total_guest_count.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_BABIES", AGE_QUALIFYING_CODE_BABY), 'num_persons': total_babies})

		return result, total_guest_count


def _get_real_index_from_price_per_days(prices_per_day, num_rooms):
	# Note that we can't just use the number of rooms as we might have cancelled one of the rooms
	try:

		if len(prices_per_day.items()) != num_rooms:
			return range(1, num_rooms + 1)

		current_range = []
		for key_room, price_room in prices_per_day.items():
			real_index = key_room.split(":")[0]
			current_range.append(int(real_index))
		current_range.sort()
	except Exception as e:
		logging.warning("imposible to get real room index from prices_per_day. Assuming normal range. Error: %s", e)
		current_range = range(1, num_rooms + 1)
	return current_range


def _count_persons(total_guest_count, specific_params):
	adults = 0
	kids = 0
	babies = 0

	for my_count in total_guest_count:
		if my_count.get('qualifying_code') == specific_params.get("AGE_QUALIFYING_CODE_ADULT"):
			adults += int(my_count.get('num_persons'))
		elif my_count.get('qualifying_code') == specific_params.get("AGE_QUALIFYING_CODE_CHILD"):
			kids += int(my_count.get('num_persons'))
		elif my_count.get('qualifying_code') == AGE_QUALIFYING_CODE_BABY:
			babies += int(my_count.get('num_persons'))

	return adults, kids, babies


def _build_room_stays_from_id(reservations_results, total_amount, start_date, end_date, num_days, hotel, specific_params={}):
	num_rooms = int(reservations_results.get('numRooms'))

	# Note that we are calculating an average
	daily_price = (float(total_amount) / float(num_days)) / float(num_rooms)

	room_price = float(total_amount) / float(num_rooms)
	room_amount = "%.2f" % room_price
	room_amount_before_taxes = "%.2f" % (room_price /specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))
	room_taxes = "%.2f" % (room_price - (room_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR)))

	dates_range = [date_utils.date_to_string(x) for x in date_utils.get_dates_in_range(start_date, end_date)]

	all_rooms = {x['key']: x for x in get_rooms_of_hotel(hotel, language=specific_params.get("LANGUAGE", "ENGLISH"), include_removed=True)}
	all_boards = {x['key']: x for x in get_boards_of_hotel(hotel, language=specific_params.get("LANGUAGE", "ENGLISH"), include_removed=True)}

	rate_key = reservations_results.get('rate')
	rate_key = get_real_rate_from_mapping(hotel, rate_key)

	board_key = reservations_results.get('regimen')
	board_key = get_real_board_from_mapping(hotel, board_key)

	board_name = all_boards[board_key]['name'].lower()

	board_code = _get_international_board_code(board_name)

	meals = {
		'only': (False, False, False),
		'breakfast': (True, False, False),
		'half': (True, False, True),
		'full': (True, True, True),
		'inclusive': (True, True, True)
	}

	breakfast, lunch, dinner = (False, False, False)

	for current_board, meals_included in meals.items():
		if current_board in board_name:
			breakfast, lunch, dinner = meals_included
			break

	daily_amount = "%.2f" % daily_price
	amount_before_tax = "%.2f" % (daily_price / specific_params.get("TAX_DENOMINATOR", DEFAULT_TAX_DENOMINATOR))

	daily_rates = []
	for current_date in dates_range:
		effective_date = current_date
		expire_date = date_utils.date_to_string(date_utils.string_to_date(effective_date) + datetime.timedelta(days=1))

		daily_rate = {
			'effective_date': effective_date,
			'expire_date': expire_date,
			'amount_before_tax': amount_before_tax,
			'amount': daily_amount,
		}
		daily_rates.append(daily_rate)


	result = []

	total_adults = 0
	total_kids = 0

	for num_room in range(1, num_rooms + 1):

		room_key = reservations_results.get('roomType' + str(num_room))

		adults = reservations_results.get('adults' + str(num_room))
		total_adults += int(adults)

		guest_counts = [{'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_ADULT"), 'num_persons': int(adults)}]

		kids = reservations_results.get('kids' + str(num_room))
		total_kids += int(kids)

		if kids != '0':
			guest_counts.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_CHILD"), 'num_persons': int(kids)})

		#note that we havent the babies in the reservation XML
		babies = reservations_results.get('babies' + str(num_room))
		if not babies:
			babies = '0'

		if babies != '0':
			guest_counts.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_BABIES", 2), 'num_persons': int(babies)})

		capacity = '%s-%s-%s' % (adults, kids, babies)

		rate_plan_code = '%s_%s' % (alphanumeric_to_id(rate_key), alphanumeric_to_id(board_key))

		current_room = all_rooms[room_key]

		all_guest = int(adults) + int(kids) + int(babies)
		guest_rhp = []
		for i in range(1, all_guest + 1, 1):
			guest_rhp.append(i)

		room_stay = {
			'room_type_code': alphanumeric_to_id(room_key),
			'rate_type_code': alphanumeric_to_id(rate_key),
			'board_type_code': alphanumeric_to_id(board_key),
			'board_key': board_key,
			'number_of_units': '1', # Each room Stay will be for a different room
			'room_name': current_room.get('name', ""),
			'room_description': current_room.get('description', ""),
			'rate_plan_code': rate_plan_code,
			'breakfast': breakfast,
			'lunch': lunch,
			'dinner': dinner,
			'board_code': board_code,
			'daily_rates': daily_rates,
			'currency_code': specific_params.get("CURRENCY_CODE", "EUR"),
			'room_amount': room_amount,
			'decimal_places': 2,
			'room_amount_before_taxes': room_amount_before_taxes,
			'room_taxes': room_taxes,
			'guest_count': guest_counts,
			'capacity': capacity,
			'guest_rhp': guest_rhp

		}
		result.append(room_stay)


	total_guest_count = [{'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_ADULT"), 'num_persons': total_adults}]
	total_guest_count.append({'qualifying_code': specific_params.get("AGE_QUALIFYING_CODE_CHILD"), 'num_persons': total_kids})

	return result, total_guest_count


def _merge_room_stays_if_same_room_type(room_stays, include_capacities=False, accumulate_persons=True):
	result = []

	temp = {}
	for room_stay in room_stays:

		if include_capacities:
			room_type_code = str(room_stay['room_type_code']) + "_" + room_stay['capacity']
		else:
			room_type_code = room_stay['room_type_code']

		if room_type_code not in temp:
			temp[room_type_code] = []

		temp[room_type_code].append(room_stay)

	for room_type_code, related in temp.items():
		related[0]['number_of_units'] = str(len(related))
		if accumulate_persons:
			for count in related[0]['guest_count']:
				count['num_persons'] = str(int(count['num_persons'])*len(related))

		result.append(related[0])

	return result


def _cc_are_throw_datatrans(reservation_json):

	extra_info = reservation_json.get('extraInfo')
	if extra_info and isinstance(extra_info, str):
		extra_info = json.loads(extra_info)

		if extra_info.get("datatransdata", ""):
			return True

		return False


def extract_extra_info_ccdata(reservation_json, hotel_code, hotel, data_base, currency="EUR", format_expired_date="%02d%02d"):
	res = None

	cc_number = ''
	cc_company = ''
	cc_expired = ''
	cc_cvv = None
	cc_holder = None

	extra_info = reservation_json.get('extraInfo')
	if extra_info and isinstance(extra_info, str):
		extra_info = json.loads(extra_info)
		base64message = extra_info.get('cc_datas')
		send_cc = get_hotel_advance_config_item(hotel, "Save CC datas")
		if send_cc and base64message:

			res = {}
			logging.info("decrypting datas")
			passwordCc = get_hotel_advance_config_item(hotel, PASSWORD_TARJETAS)
			if passwordCc:
				passwordCc = passwordCc[0].get("value", "")

				message = base64.b64decode(base64message)
				obj = DES.new(passwordCc.encode(), DES.MODE_ECB)
				decryptedData = obj.decrypt(message).decode('utf-8')

				infoCC = decryptedData.split('@@')

				if len(infoCC) > 0:
					cc_number = infoCC[0]

					if cc_number.strip() == "42":
						cc_number = "0000000000000042"

				if len(infoCC) > 1:
					cc_company = infoCC[1].upper()
					if cc_company in ["CA", "MA", "4B"]:
						cc_company = "MC"

					if cc_company not in ALLOWED_CC:
						cc_company = "NULL"

				if len(infoCC) > 2:
					cc_expired = infoCC[2]
					ccExpiredInfo = cc_expired.split("/")

					# 08/19 -> 0819
					if ccExpiredInfo[0] and ccExpiredInfo[1]:
						cc_expired = format_expired_date % (int(ccExpiredInfo[0]), int(ccExpiredInfo[1]))

				if len(infoCC) > 3:
					cc_cvv = infoCC[3].strip()
					if len(cc_cvv) == 0:
						cc_cvv = None

				if len(infoCC) > 4:
					cc_holder = infoCC[4].strip()

			if not cc_holder or cc_holder.lower() == "none":
				cc_holder = reservation_json.get("name", "") + " " + reservation_json.get("lastName", "")

			if get_hotel_advance_config_item(hotel, "Card holder from billing") and extra_info.get("billing_name"):
				cc_holder = extra_info['billing_name']

			if cc_number and cc_expired:
				res["cc_number"] = cc_number
				res["cc_company"] = cc_company
				res["cc_expired"] = cc_expired
				res["cc_cvv"] = cc_cvv
				res["cc_holder"] = cc_holder
				res["currency_code"] = currency

	if res:
		data_base['cc_datas'] = res

	return data_base


def add_club_info_incomments(hotel):
	# hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)

	integration_name = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()

	integration_configurations = get_integration_configuration_of_hotel(hotel, integration_name)

	if not integration_configurations:

		return True

	else:

		integration_configuration = integration_configurations[0].get('configurations', [])
		for config in integration_configuration:
			if "%s @@ true" % NOT_INCLUDE_CLUB_IN_COMMENTS.lower() ==  config.lower():
				return False

	return True

def extract_extra_info_tpv(reservation_json):
	res = {}
	extra_info = reservation_json.get('extraInfo')
	if extra_info and isinstance(extra_info, str):
		extra_info = json.loads(extra_info)

		if extra_info.get('cc_type','') or extra_info.get('cc_brand',''):
			res["cc_company"] = get_credit_card_brand(extra_info.get('cc_brand',0))
			res["cc_type"] = get_credit_card_type(extra_info.get('cc_type','D'))

	return res

def get_credit_card_type(type):
	credit_card_type = {
		'C': 1,
		'D': '',
	}

	return credit_card_type.get(type, type)


def get_credit_card_brand(brand):
	credit_card_brand = {
		"1": VISA_CODE,
		"2": MASTERCARD_CODE,
		"8": AMERICAN_EXPRESS_CODE,
		"9": JAPAN_CREDIT_BUREAU_CODE,
		"6": DINERS_CLUB_CODE,
		"22": CHINA_UNIONPAY_CODE,
		"7": DISCOVER_CODE,
	}

	return credit_card_brand.get(brand, brand)


def translate_supplements(hotel, comments, reservation_supplements, customer_language, translations):
	try:
		for supplement in reservation_supplements.split(';'):
			supplement_name = supplement.split(' - ')[0]
			supplement_name = supplement_name.lstrip()
			supplement_key = get_key_additional_service(hotel, supplement_name)
			translated_supplements = get_supplements(hotel)
			new_supplement_name = [x.get('name') for x in translated_supplements if supplement_key == x.get('key')]
			if new_supplement_name:
				comments = comments.replace(supplement_name, new_supplement_name[0])
		comments = comments.replace(default_translations.get('T_DAYS'), translations.get('T_DAYS'))
		comments = comments.replace(default_translations.get('T_QUANTITY'), translations.get('T_QUANTITY'))
		comments = comments.replace(default_translations.get('T_PRICE'), translations.get('T_PRICE'))

	except:
		pass
	return comments




def get_source_identifier(hotel, reservation_json):
	source_identifier = f"[{RING2TRAVEL}]"
	try:
		if len(reservation_json.get("identifier").split("-")) == 1:
			return source_identifier

		prefix = reservation_json.get("identifier").split("-")[0]
		prefix = prefix + "-"
		prefix_booking = get_hotel_web_config_item(hotel, "prefix booking")
		if len(prefix_booking) > 0:
			items_prefix = prefix_booking[0].get("configurations")
			for x in items_prefix:
				value_item = x.split("@@")
				if value_item[1].strip() == prefix:
					source_identifier = "[" + str(value_item[0].upper()) + "]"
					break
	except Exception as e:
		logging.warning("Error getting source identifier: %s", e)
		source_identifier = f"[{RING2TRAVEL}]"

	return source_identifier

def get_rooms_indexes(extra_info, num_rooms):
	indexes = []
	prices_per_day = extra_info.get('prices_per_day', [])
	try:
		for x in prices_per_day:
			indexes.append(int(x.split(':')[0]))
	except:
		return range(1, num_rooms + 1)
	return indexes
