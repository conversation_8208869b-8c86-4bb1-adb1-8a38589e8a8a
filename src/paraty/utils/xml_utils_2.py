__author__ = 'fmatheis'

import xml.etree.ElementTree as ET

from io import StringIO
from xml.etree import ElementTree


class XmlFinder():
    '''
    Based on bs4, it returns BS4 elements and datatypes.

    Copied from project 'siteminder'. Added find_elements method later on.
    TODO: share code for XmlFinder
    '''
    def __init__(self, xmlContent, from_encoding=None):

        it = ET.iterparse(StringIO(xmlContent))
        for _, el in it:
            if '}' in el.tag:
                el.tag = el.tag.split('}', 1)[1]  # strip all namespaces
        self.root = it.root



    def find_element(self, element_name):
        result = self.root.find(".//%s" % element_name)
        return result

    def find_elements(self, element_name):
        result = self.root.findall(".//%s" % element_name)
        return result

    def find_attribute(self, elementName, attributeName, conditions={}):
        return self.find_element(elementName, conditions).get(attributeName)

    def find_attribute2(self, elementName, attributeName):
        return self.find_element(elementName).attrib[attributeName]


def tag_to_xml(my_tag):
    return ElementTree.tostring(my_tag, encoding='utf8', method='xml').decode('utf-8')

def get_tag(elem, tag):
    tag_found = elem.find(tag)
    if not tag_found:
        raise Exception('Missing mandatory tag: %s' % tag)
    return tag_found
