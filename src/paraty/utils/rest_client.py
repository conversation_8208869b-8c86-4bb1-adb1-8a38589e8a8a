import json
from paraty_common.rest import rest_client

__author__ = 'fmatheis'

import re
import uuid

'''

Rest client to be used in conjuntion with https://code.google.com/p/appengine-rest-server/

Note that we use exactly the same syntaxis for queries


'''

REST_PASSWORD="Basic cGFibDA6bWF0aGUxcw=="

import libs.requests as requests

def get(url, entity, query="", authorization=REST_PASSWORD):
	return rest_client.get(url, entity, query=query, authorization=authorization)



def add_entity(rest_url, entity_type, entity, authorization=REST_PASSWORD):
	'''
	DEPRECATED, USE paraty_common instead
	'''

	url = '%s/%s' % (rest_url, entity_type)

	headers = {
	'Content-Type': 'application/json',
	'Authorization': authorization
	}

	payload = {
		'list': {
			entity_type: [entity.to_dict()]
		}
	}

	payload_json = json.dumps(payload)

	response = requests.post(url, headers=headers, data=payload_json)

	if response.status_code == 200:
		#print 'Entity added: %s' % str(entity)
		return response.content
	else:
		print('Entity could not be added: %s' % str(entity))
		return None





def update_entity(rest_url, entity_type, entity, authorization=REST_PASSWORD):

	'''
	DEPRECATED, USE paraty_common instead
	'''

	url = '%s/%s/%s' % (rest_url, entity_type, entity.key)

	headers = {
	'Content-Type': 'application/json',
	'Authorization': authorization
	}

	payload = {
		'list': {
			entity_type: [entity.to_dict()]
		}
	}

	payload_json = json.dumps(payload)

	response = requests.put(url, headers=headers, data=payload_json)

	if response.status_code == 200:
		#print 'Entity updated: %s' % str(entity)
		return response.content
	else:
		print('Entity could not be updated: %s' % str(entity))


if __name__ == "__main__":
	result = get("https://rest-dot-admin-hotel.appspot.com/rest/", "UsageLogEntry", "?feq_applicationId=5851669761884160&fgt_timestamp=2014-08-29 09:00&feq_action=UPDATE")
	print(result)