import io
import logging

from paraty.utils.pyftp import PyFTP


FTP_RESPONSE_CODE_STRING_OK = '226-File successfully transferred'
FTP_RESPONSE_CODE_OK = 226
FTP_RESPONSE_CODE_NOK = None


def store_string_as_file(file_str, remote_folder='/', remote_name='', config=None):
    """Store a string as a file into a remote ftp folder

    :param file_str: File to send as string
    :param remote_folder: Folder to use (absolute), default '/'
    :param remote_name: Name to give on the remote file system
    :param config: FTP configuration parameters
        i.e. config  = {
                            'hostname': '',
                            'username': '',
                            'password': '',
                            'port': 21
                        }
    """
    if not file_str:
        return

    remote_path = '{}/{}'.format(remote_folder.rstrip('/'), remote_name)
    memory_file = io.StringIO(file_str)

    try:
        client = PyFTP(config['hostname'], username=config['username'], password=config['password'], port=21)
        client.connect()
        if remote_folder and not client.exists(remote_folder):
            client.makedirs(remote_folder)
        response_code = client.ftp.storbinary('STOR %s' % remote_path, memory_file)
        client.close()
    except Exception as inst:
        logging.info(str(inst))
        raise Exception(str(inst))

    memory_file.close()
    return FTP_RESPONSE_CODE_OK if FTP_RESPONSE_CODE_STRING_OK in response_code else FTP_RESPONSE_CODE_NOK
