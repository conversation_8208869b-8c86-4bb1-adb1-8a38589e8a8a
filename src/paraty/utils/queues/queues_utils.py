import logging
import pickle
import uuid

from flask import request
from flask.views import MethodView

from paraty.config import Config
from paraty_commons_3 import queue_utils
from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache


@managers_cache(
    hotel_code_provider=lambda f,a,k: 'generic',
    ttl_seconds=5,
    background_refresh=False,
    only_thread_local_and_memory=True
)
def get_tasks_in_queue(queue_name):
    logging.info(f"Getting tasks in queue {queue_name}")
    return queue_utils.list_tasks(queue_name)

def defer(method, *args, **kwargs):
    available_kwargs_options = {
        "countdown", # Used to specify the time in seconds to wait before executing the task
        'queue_name', # Used to specify the queue where the task will be added
        'task_name' # Used to specify a custom name of the task
    }

    mapped_options = {x: kwargs.pop(x, None) for x in available_kwargs_options}
    kwargs = dict((x, kwargs[x]) for x in kwargs if x not in available_kwargs_options)

    try:
        logging.info(f"Deferring task {method.__name__}")
    except Exception as e:
        logging.info(f"Deferring task")

    if Config.DEV:
        method(*args, **kwargs)
        return

    serialized_method = pickle.dumps((method, args, kwargs))
    params = {
        'function_name': 'deferred-base-integration',
        'payload': serialized_method,
        'queue_name': mapped_options.get('queue_name') or 'default',
        'in_seconds': mapped_options.get('countdown', 5),
        'task_name': mapped_options.get('task_name') or 'task_%s' % str(uuid.uuid4()),
        'project_id': Config.PROJECT,
    }

    if Config.CLOUD_RUN_URL:
        params['queue_name'] = params['queue_name'] + '-cloud-run'
        params['custom_endpoint'] = Config.CLOUD_RUN_URL + '/execute_task'
        logging.info("Using custom endpoint for deferred task: %s" % params['custom_endpoint'])

    queue_utils.create_task(**params)


class QueuesSerializedHandler(MethodView):
    def post(self):
        logging.info("Deferred task received and processing")
        try:
            func, args, kwds = pickle.loads(request.data)
        except Exception as e:
            traceback_message = make_traceback()
            logging.error(traceback_message)
            logging.error(request.data)
            raise e

        func(*args, **kwds)
        logging.info("Deferred task processed")
        return 'ok'

    def get(self):
        return ''
