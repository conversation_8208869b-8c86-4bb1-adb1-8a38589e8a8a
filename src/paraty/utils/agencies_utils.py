from google.cloud.datastore import Entity

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache


@timed_cache(hours=24) # Kept the same cache as the original in python2
def get_all_agencies_of_hotel(hotel_code: str) -> list[Entity]:
    return get_using_entity_and_params("Agency", hotel_code=hotel_code)