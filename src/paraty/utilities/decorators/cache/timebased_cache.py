from datetime import datetime, timedelta
from copy import deepcopy
from threading import RLock

'''
Based on solution proposed at http://www.willmcgugan.com/blog/tech/2007/10/14/timed-caching-decorator/

NOTE that this method is synchronized per function by default
'''


results = []

def clear_cache():
	for result in results:
		result.clear()


def timed_cache(seconds=0, minutes=0, hours=0, days=0, key_builder=None, synchronize_calls=True):
	time_delta = timedelta(seconds=seconds,
						   minutes=minutes,
						   hours=hours,
						   days=days)

	def decorate(f):

		f._lock = RLock()
		f._updates = {}
		f._results = {}
		results.append(f._results)

		def do_cache(*args, **kwargs):

			lock = f._lock
			if synchronize_calls:
				lock.acquire()

			try:
				if key_builder:
					key = key_builder(args)
				else:
					key = f.__name__ + str((args, tuple(sorted(kwargs.items(), key=lambda i: i[0]))))

				updates = f._updates
				results = f._results

				t = datetime.now()
				updated = updates.get(key, t)

				if key not in results or t - updated > time_delta:
					# Calculate
					updates[key] = t
					result = f(*args, **kwargs)
					results[key] = deepcopy(result)
					return result

				else:
					# Cache
					return deepcopy(results[key])

			finally:
				if synchronize_calls:
					lock.release()

		return do_cache

	return decorate
