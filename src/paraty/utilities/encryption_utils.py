import hashlib

import unicodedata
from Cryptodome.Cipher import DES,AES

from urllib.parse import urlparse, parse_qs, urlencode
import base64
import os


import json

ENCRYPTION_KEY = b'j1Bj26rde#%@F$#@'

def hash_to_md5(my_string):
	m = hashlib.md5(my_string.encode())
	return m.hexdigest()


def encryptMessage(message, password):

	#We are expecting unicodes, not strings (just in case)
	if isinstance(message, bytes):
		message = message.decode('utf8')

	message = unicodedata.normalize('NFKD', message).encode('ASCII', 'ignore').decode('utf8')

	obj=DES.new(password, DES.MODE_ECB)

	#Note that the algorithm requires the message to be multiple of 8 characters
	if len(message)%8 > 0:
		missingChars = 8 - len(message)%8
		message = "%s%s" % (message,  (''.join(missingChars*[' '])))

	return obj.encrypt(message)

def decryptMessage(message, password):
	obj=DES.new(password, DES.MODE_ECB)
	return obj.decrypt(message)


def build_encrypted_url(url):
	if "from_manager2" not in url:
		parsed_url = urlparse(url)
		query_params = parse_qs(parsed_url.query)
		data = {}
		for key, values in query_params.items():
			data[key] = values[0]
		encrypted_data = encrypt_data(data)
		if parsed_url.netloc:
			domain = '{}://{}'.format(parsed_url.scheme, parsed_url.netloc)
			path = parsed_url.path
			encrypted_url = '{}{}?{}'.format(domain, path, urlencode({"data": encrypted_data}))
		else:
			path = parsed_url.path
			encrypted_url = '{}?{}'.format(path, urlencode({"data": encrypted_data}))
		return encrypted_url
	return url

def encrypt_data(data):
	iv = os.urandom(16)
	encryptor = AES.new(ENCRYPTION_KEY, AES.MODE_CFB, iv)
	encrypted_data = encryptor.encrypt(json.dumps(data).encode("utf-8"))
	return base64.urlsafe_b64encode(iv + encrypted_data).decode("utf-8")

def decrypt_data(data):
	data_dict = None
	try:
		payload = base64.urlsafe_b64decode(data)
		iv = iv = payload[:16]
		payload = payload[16:]
		encryptor = AES.new(ENCRYPTION_KEY, AES.MODE_CFB, iv)
		decrypted_data = encryptor.decrypt(payload).decode("utf-8")
		data_dict = json.loads(decrypted_data)
	except Exception as e:
		return data
	return data_dict


if __name__ == '__main__':
	result = hash_to_md5("test")
	assert '098f6bcd4621d373cade4e832627b4f6' == result, 'Whaaat!'
