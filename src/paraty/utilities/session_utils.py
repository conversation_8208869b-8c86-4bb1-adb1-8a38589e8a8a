import json
import threading
import uuid

import datetime

from flask import request, g, session
from flask_login import UserMixin

#720 hours for the moment
from paraty.communications import datastore_communicator

SESSION_EXPIRATION_TIME = 60 * 60 * 720



class UserSession(UserMixin):

	def __init__(self, login_response, session_id=str(uuid.uuid4())):
		self.content = login_response
		self.session_id = session_id


	def set_value(self, key, value):
		self.content[key] = value
		session[key] = value

		#We are not going to create a different session, we just use the one in hotel Manager
		# save_session_to_datastore()


	def get_value(self, key):
		if key in self.content:
			return self.content.get(key)

		if key in session:
			return session[key]


	def get_id(self):
		return self.session_id

	def __eq__(self, other):
		"""Overrides the default implementation"""
		if isinstance(other, UserSession):
			return self.content == other.content and self.session_id == other.session_id



def set_current_session(current_session):
	g.current_session = current_session


def get_current_session():
	if 'current_session' in g:
		return g.current_session
	return None


def get_session_from_datastore(session_id):

	entity_from_datastore = datastore_communicator.get_entity("AdminUserSession", session_id, hotel_code='admin-hotel')

	if not entity_from_datastore:
		return

	session_from_datastore = _entity_to_class(entity_from_datastore)
	set_current_session(session_from_datastore)
	return get_current_session()


def _entity_to_class(entity):

	return UserSession(dict(entity), session_id=entity.key.name)