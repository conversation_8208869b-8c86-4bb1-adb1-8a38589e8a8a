import os
import json
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache


@timed_cache(days=5)
def get_json_file():
	file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "currencies.json")
	with open(file_path, "r") as fp:
		return json.load(fp)


def get_currency_symbol(currency):
	return get_json_file().get(currency, "€")

if __name__ == "__main__":
	print(get_currency_symbol("USD"))