{"172": "Deneg<PERSON>, no repetir.", "173": "<PERSON><PERSON><PERSON>, no repetir sin actualizar datos de tarjeta.", "174": "<PERSON><PERSON><PERSON>, no repetir antes de 72 horas.", "9064": "Número de posiciones de la tarjeta incorrecto", "9078": "Tipo de operación no permitida para esa tarjeta", "9093": "Tarjeta no existente", "9094": "<PERSON><PERSON>zo servidores internacionales", "9104": "Comercio con 'titular seguro' y titular sin clave de compra segura", "9218": "El comercio no permite op. seguras por entrada /operaciones", "9253": "Tarjeta no cumple el check-digit", "9256": "El comercio no puede realizar preautorizaciones", "9257": "Esta tarjeta no permite operativa de preautorizaciones", "9261": "Operación detenida por superar el control de restricciones en la entrada al SIS", "9912": "Emisor no disponible", "9915": "A petición del usuario se ha cancelado el pago", "9997": "Se está procesando otra transacción en SIS con la misma tarjeta", "9998": "Operación en proceso de solicitud de datos de tarjeta", "9999": "Operación que ha sido redirigida al emisor a autenticar", "0195": "Requiere autenticación SCA", "0900": "Transacción autorizada para devoluciones y confirmaciones", "0400": "Transacción autorizada para anulaciones", "0101": "Tarjeta caducada", "0102": "Tarjeta en excepción transitoria o bajo sospecha de fraude", "0106": "Intentos de PIN excedidos", "0125": "Tarjeta no efectiva", "0129": "<PERSON>ó<PERSON> de seguridad (CVV2/CVC2) incorrecto", "0180": "Tarjeta ajena al servicio", "0184": "Error en la autenticación del titular", "0190": "Denegación del emisor sin especificar motivo", "0191": "<PERSON><PERSON> de caducidad err<PERSON>", "0202": "Tarjeta en excepción transitoria o bajo sospecha de fraude con retirada de tarjeta", "0904": "Comercio no registrado en FUC", "0909": "<PERSON><PERSON><PERSON> de siste<PERSON>", "0913": "Pedido repetido", "0944": "Sesión Incorrecta", "0950": "Operación de devolución no permitida", "0912": "Emisor no disponible", "8102": "Operación que ha sido redirigida al emisor a autenticar EMV3DS V1.0.2 (para H2H)", "8210": "Operación que ha sido redirigida al emisor a autenticar EMV3DS V2.1.0 (para H2H)", "8220": "Operación que ha sido redirigida al emisor a autenticar EMV3DS V2.2.0 (para H2H)", "9001": "<PERSON><PERSON><PERSON>", "9002": "<PERSON><PERSON><PERSON> gen<PERSON><PERSON>", "9003": "<PERSON><PERSON><PERSON> gen<PERSON><PERSON>", "9004": "<PERSON><PERSON><PERSON> gen<PERSON><PERSON>", "9005": "<PERSON><PERSON><PERSON> gen<PERSON><PERSON>", "9006": "<PERSON><PERSON><PERSON> gen<PERSON><PERSON>", "9007": "El mensaje de petición no es correcto, debe revisar el formato", "9008": "falta Ds_Merchant_MerchantCode", "9009": "Error de formato en Ds_Merchant_MerchantCode", "9010": "Error falta Ds_Merchant_Terminal", "9011": "Error de formato en Ds_Merchant_Terminal", "9012": "<PERSON><PERSON><PERSON> gen<PERSON><PERSON>", "9013": "<PERSON><PERSON><PERSON> gen<PERSON><PERSON>", "9014": "Error de formato en Ds_Merchant_Order", "9015": "Error falta <PERSON>_Merchant_Currency", "9016": "Error de formato en Ds_Merchant_Currency", "9018": "Falta <PERSON>s_Merchant_Amount", "9019": "Error de formato en Ds_Merchant_Amount", "9020": "Falta Ds_Merchant_MerchantSignature", "9290": "9290 <PERSON><PERSON><PERSON> gen<PERSON><PERSON><PERSON>. Hable con su entidad bancaria", "9291": "9291 <PERSON><PERSON><PERSON> gen<PERSON><PERSON><PERSON>. Hable con su entidad bancaria", "9292": "9292 <PERSON><PERSON><PERSON> gen<PERSON><PERSON><PERSON>. Hable con su entidad bancaria", "9293": "9293 <PERSON><PERSON><PERSON> gen<PERSON><PERSON><PERSON>. Hable con su entidad bancaria", "SIS0431": "Error del objeto JSON que se envía codificado en el parámetro Ds_MerchantParameters", "SIS0051": "Error número de pedido repetido", "SIS0334": "Bloqueo por control de Seguridad. Hable con su entidad bancaria", "SIS0057": "Que revise el importe que quiere devolver (supera el permitido)", "SIS0042": "Error en el cálculo de la firma", "SIS0617": "Error del parámetro DS_MERCHANT_EXCEP_SCA es de tipo MIT y no vienen datos de COF o de pago por referencia", "SIS0883": "No tiene el servicio contratado", "0290": "SIS0290 E<PERSON><PERSON> gen<PERSON><PERSON>. Hable con su entidad bancaria", "0291": "SIS0291 <PERSON><PERSON><PERSON> gen<PERSON><PERSON>. Hable con su entidad bancaria", "0292": "SIS0292 <PERSON><PERSON><PERSON> gen<PERSON><PERSON>. Hable con su entidad bancaria", "0293": "SIS0293 <PERSON><PERSON><PERSON> gen<PERSON><PERSON>. Hable con su entidad bancaria"}