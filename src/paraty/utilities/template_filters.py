from paraty import app
from flask import request
from urllib3.util.url import Url



@app.template_filter()
def fixurl(url):
    scheme = "https"
    if app.config.get("DEV"):
        scheme = "http"
    if request:
        base_url = request.host
    elif app.config.get("DEV"):
        base_url = ""
    else:
        base_url = "https://payment-seeker.appspot.com"

    return Url(scheme=scheme, host=base_url, path=url).url


@app.template_filter()
def money_string_format(amount):
    return "%.2f" % amount
