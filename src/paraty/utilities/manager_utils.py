import requests

from paraty import Config
from paraty.communications import datastore_communicator
from paraty.utilities.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.common_data.common_data_provider import get_pictures_for_entity, CACHE_HOURS
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.logging.my_gae_logging import logging


@timed_cache(hours=24)
def get_all_hotels():
	all_hotels = list(datastore_communicator.get_using_entity_and_params('HotelApplication', [], hotel_code='admin-hotel'))
	result = {x['applicationId']: x for x in all_hotels if x['enabled']}
	return result



def get_hotel_by_application_id(hotel_code):
	return get_all_hotels().get(hotel_code)


def _get_project_id_and_namespace(hotel):
	namespace = None
	project_id = hotel['applicationId']
	if 'multi' in hotel['url']:
		project_id = hotel['url'][17:]
		project_id = project_id[:project_id.find('.appspot')]
		namespace = hotel['applicationId']
	return project_id, namespace

@timed_cache(hours=24, key_builder=lambda x: 'booking_domain_%s' % x[0])
def get_booking_domain(hotel_code):

	booking_domain = list(datastore_communicator.get_using_entity_and_params("ConfigurationProperty", search_params=[('mainKey', '=', 'Dominio booking')], hotel_code=hotel_code))
	if booking_domain:
		return booking_domain[0].get('value')
	return ""


def set_url_to_https(url_to_change):
	if url_to_change:
		return url_to_change.replace('http:', 'https:')
	else:
		return None

@timed_cache(hours=24, key_builder=lambda x: 'logotype_hotel%s' % x[0])
def get_hotel_logotypes(hotel_code, filter=""):

	partial_result = []
	pictures = list(datastore_communicator.get_using_entity_and_params("Picture", search_params=[('mainKey', '=', "logoPicture")], hotel_code=hotel_code))
	if pictures and len(pictures) > 0:
		for picture in pictures:
			if picture.get("enabled"):
				if picture.get("priorityInWeb"):
					priority = picture.get("priorityInWeb")
				else:
					priority = ''
				partial_result.append({'servingUrl': set_url_to_https(picture.get("servingUrl")), 'priority': priority, 'name': picture.get("name")})

	partial_result.sort(key=lambda p: p['priority'])

	result = [item['servingUrl'] for item in partial_result if not filter or (item.get('name') and filter in item.get('name'))]

	return result




def flush_entity_cache(hotel_code, entity_name):

	flush_url = '%s/integrations?action=flush_entity&hotel_code=%s&entity_name=%s' % (Config.MANAGER_URL, hotel_code, entity_name)
	requests.get(flush_url)


@timed_cache(hours=24, key_builder=lambda x: 'get_configuration_property_value%s_%s' % (x[0], x[1]))
def get_configuration_property_value(hotel_code, config_name):

	configuration_property = list(datastore_communicator.get_using_entity_and_params("ConfigurationProperty", search_params=[('mainKey', '=', config_name)], hotel_code=hotel_code))
	if configuration_property:
		return configuration_property[0].get('value')
	return ""




def get_hotel_project_and_namespace(hotel_code):

	current_hotel = get_all_hotels().get(hotel_code)

	if not current_hotel:
		return None, None

	return _get_project_id_and_namespace(current_hotel)




def get_hotel_codes_from_ids(list_of_ids):

	all_hotels = get_all_hotels()

	hotel_id_map = {x.id:x['applicationId'] for x in all_hotels.values()}

	result = []
	for current_id in list_of_ids:
		result.append(hotel_id_map.get(current_id))

	return result


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='WebSection', ttl_seconds=3600*CACHE_HOURS)
def get_picture_from_section(hotel_code, picture_name, section, language, get_url=False):
	result = ''
	my_entity_key = datastore_communicator.build_key("WebSection", int(section.key.id), hotel_code)
	if my_entity_key:
		my_key = id_to_entity_key(hotel_code, my_entity_key)
		logging.info(f"Getting picture {picture_name} from section {my_key}")
		pictures = get_pictures_for_entity(hotel_code, my_key, language, include_extra_props=True)
		if pictures:
			picture = next((pic for pic in pictures if pic.get('title') == picture_name), None)
			if picture:
				result = picture.get('servingUrl') if get_url else picture
	return result
