import os
import sys
import traceback
import logging
import cgi
import ssl

from paraty.libs.postmark.core import PMMail
from paraty.pages.cobrador.cobrador_constants import BACKEND_GOOGLE_GROUP, PARATY_EPAYMENT_SUPERVISORS
from paraty.utilities.decorators.cache.timebased_cache import timed_cache
from paraty import Config

EMAILS_SUPPORT = "<EMAIL>"

app_name = 'Paraty Tech'

DEFAULT_TITLE = '[%s] Problem at integration Adapter - ' % app_name


def makeTraceback():
	"""
	returns a string with the full traceback
	"""
	excinfo = sys.exc_info()
	message = ('Application: %s\nVersion: %s\n\n%s\n\n'
		% (os.getenv('APPLICATION_ID'),
		os.getenv('CURRENT_VERSION_ID'),
		'<br/>'.join(traceback.format_exception(*excinfo)),))

	return message


def notify_error_by_email(subject, message):
	try:
		sendEmail(EMAILS_SUPPORT, subject, "", message)
	except:
		sendEmail_backup(EMAILS_SUPPORT, DEFAULT_TITLE, "", message)

@timed_cache(key_builder=lambda x: "notify_exception_by_email %s" % x[1], minutes=5)
def notifyExceptionByEmail(myException, myRequest):

	message = makeTraceback()
	emailContent = "<html><body><B>Request: %s </B><br/><br/>%s</body><html/>" % (cgi.escape(str(myRequest))[:2000], str(message))
	try:
		logging.warning("Unexpected Exception" + str(myException))
		sendEmail(EMAILS_SUPPORT, DEFAULT_TITLE, "", emailContent)
	except:
		sendEmail_backup(EMAILS_SUPPORT, DEFAULT_TITLE, "", emailContent)


def sendEmail(address, title, contentText, contentHtml, sender=None, backup_sender="<EMAIL>"):
	sendEmail_backup(address, title, contentText, contentHtml, sender=sender, backup_sender=backup_sender)

@timed_cache(key_builder=lambda x: "send_email_cache_%s" % x[1], minutes=5)
def sendEmailCache(content_html, title, email_to, minutes=2):
	my_trace = makeTraceback()
	sendEmail_backup(f"{email_to};<EMAIL>", title, "", "<p>%s</p><p>%s</p>" % (content_html, my_trace))

def notifyMappingSeekerMissed(hotel, city, site):
	try :
		logging.warning("[Mapping Seeker] Mapping not found in inventory for hotel %s in %s and site %s " % (hotel, city, site))
		emailContent = "<html><body>Mapping not found in inventory for hotel <b>%s</b> in <b>%s</b> and site <b>%s</b> <br/></body><html/>" % (hotel, city, site)
		sendEmail("<EMAIL>", '[Mapping Seeker] Missing hotel in inventory', "", emailContent)
	except:
		pass




def sendEmail_backup(address, title, contentText, contentHtml, replyTo=None, sender=None, backup_sender="<EMAIL>"):
	try:
		logging.warning("Trying to send it using PMM....")
		if not sender:
			sender = app_name + " <%s>" % backup_sender
		to = ""
		for myAddress in address.split(";"):
			to = to + myAddress + ","
		bcc = f'<EMAIL>'
		if not Config.DEV:
			bcc += f', {PARATY_EPAYMENT_SUPERVISORS}'
		message = PMMail(api_key='70f0042b-fc70-43cf-a01c-86b783a31327',
						 sender=sender,
						 subject=title,
						 html_body=contentHtml,
						 text_body=contentText,
						 to=to,
						 bcc=bcc,
						 reply_to=replyTo)

		message.send()
		logging.warning("Sent!")
	except Exception as e:
		logging.warning("Error sending email using PMM to " + str(address))
		logging.warning(e)



def sendEmail_localdev(address, title, contentText, contentHtml, replyTo=None, sender=None, backup_sender="<EMAIL>"):
	try:
		logging.warning("Trying to send it using PMM....")
		if not sender:
			sender = app_name + " <%s>" % backup_sender
		to = ""
		for myAddress in address.split(";"):
			to = to + myAddress + ","
		
		# Configure SSL context with system certificates
		import certifi
		ssl_context = ssl.create_default_context(cafile=certifi.where())
		ssl_context.check_hostname = True
		ssl_context.verify_mode = ssl.CERT_REQUIRED
		
		message = PMMail(api_key='70f0042b-fc70-43cf-a01c-86b783a31327',
						 sender=sender,
						 subject=title,
						 html_body=contentHtml,
						 text_body=contentText,
						 to=to,
						 reply_to=replyTo,
						 ssl_context=ssl_context)

		message.send()
		logging.warning("Sent!")
	except Exception as e:
		logging.warning("Error sending email using PMM to " + str(address))
		logging.warning(e)
		# If SSL verification fails, try without verification (not recommended for production)
		try:
			import urllib3
			urllib3.disable_warnings()
			
			ssl_context = ssl.create_default_context()
			ssl_context.check_hostname = False
			ssl_context.verify_mode = ssl.CERT_NONE
			
			message = PMMail(api_key='70f0042b-fc70-43cf-a01c-86b783a31327',
							 sender=sender,
							 subject=title,
							 html_body=contentHtml,
							 text_body=contentText,
							 to=to,
							 reply_to=replyTo,
							 ssl_context=ssl_context)
			
			message.send()
			logging.warning("Sent with SSL verification disabled!")
		except Exception as e2:
			logging.warning("Failed to send email even with SSL verification disabled: " + str(e2))
			# Try one last time with requests
			try:
				import requests
				from requests.packages.urllib3.exceptions import InsecureRequestWarning
				requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
				
				message = PMMail(api_key='70f0042b-fc70-43cf-a01c-86b783a31327',
								 sender=sender,
								 subject=title,
								 html_body=contentHtml,
								 text_body=contentText,
								 to=to,
								 reply_to=replyTo)
				
				message.send()
				logging.warning("Sent using requests with SSL verification disabled!")
			except Exception as e3:
				logging.warning("All attempts to send email failed. Last error: " + str(e3))


def build_email_sender_from_hotel_webs_config(email_sender_config, backup_sender="<EMAIL>"):
	if "-" in email_sender_config:
		if email_sender_config.count("-") > 1:
			reply_email = email_sender_config.split("-", (email_sender_config.count("-") - 1))
		else:
			reply_email = email_sender_config.split("-")
		email_sender = u" %s <%s>" % (reply_email[0], reply_email[1])
	else:
		email_sender = u" %s <%s>" % (email_sender_config, backup_sender)

	return email_sender

def build_new_payment_email_content(translations, custom_section_translations):
	if custom_section_translations and custom_section_translations.get("T_new_payment_ok_info_payment"):
		info_payment = custom_section_translations.get("T_new_payment_ok_info_payment")
	else:
		info_payment = translations.get("T_PAYMENT_OK_CUSTOMER_EMAIL")

	if custom_section_translations and custom_section_translations.get("T_new_payment_email_body"):
		body_message = custom_section_translations.get("T_new_payment_email_body")
	else:
		body_message = translations.get("T_NEW_PAYMENT_EMAIL_BODY")

	if custom_section_translations and custom_section_translations.get("T_new_payment_subject_email"):
		subject_email = custom_section_translations.get("T_new_payment_subject_email")
	else:
		subject_email = translations.get("T_TITLE_EMAIL_PAYMENT")

	if custom_section_translations and custom_section_translations.get("T_new_payment_title_in_body"):
		title_in_body = custom_section_translations.get("T_new_payment_title_in_body")
	else:
		title_in_body = translations.get("T_PAYMENT_DONE_OK")
	return info_payment, body_message, subject_email, title_in_body