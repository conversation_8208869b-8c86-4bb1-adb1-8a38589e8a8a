import datetime
import json
import logging

from paraty.communications import datastore_communicator
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


def get_config_property_value(hotel_code, main_key):

	properties = list(datastore_communicator.get_using_entity_and_params('ConfigurationProperty', [('mainKey', '=', main_key)], hotel_code=hotel_code))
	if not properties:
		return None

	return properties[0].get('value', None)



def get_inner_url(hotel_code):
    hotel = get_hotel_by_application_id(hotel_code)
    hotel_url = hotel['url']
    if not 'multi' in hotel_url:
        inner_url = "https://" + hotel['applicationId'] + ".appspot.com"
    else:

        host = hotel_url.replace("rest-", hotel['applicationId'] + "-").replace("/multi/" + hotel['applicationId'], "")
        namespace = hotel['applicationId']
        # application_id = host.replace("https://", "").replace(".appspot.com", "").replace("-dot-", "").replace(hotel['applicationId'], "")
        application_id = host.replace("https://", "").split("-dot-")[-1].replace(".appspot.com", "")

        inner_url = 'https://%s-dot-%s.appspot.com' % (namespace, application_id)
    return inner_url


def generate_booking1_direct_search_by_reservation(hotel_code, reservation):
    booking_domain = get_configuration_property_value(hotel_code, 'Dominio asociado')
    search_domain = get_configuration_property_value(hotel_code, 'Dominio booking')
    if search_domain:
        booking_domain = search_domain

    start_datetime = datetime.datetime.strptime(reservation.get('startDate'), '%Y-%m-%d')
    end_datetime = datetime.datetime.strptime(reservation.get('endDate'), '%Y-%m-%d')

    start_date = start_datetime.strftime('%d/%m/%Y')
    end_date = end_datetime.strftime('%d/%m/%Y')

    num_rooms = reservation.get('numRooms', 1)

    target_url = f'{booking_domain}/booking1?startDate={start_date}&endDate={end_date}&namespace={hotel_code}&numRooms={num_rooms}'

    extra_info = json.loads(reservation.get('extraInfo', '{}'))
    shopping_cart = process_shopping_cart(extra_info)

    if shopping_cart:
        target_url = append_cart_info_to_url(target_url, shopping_cart)
    else:
        target_url = append_room_info_to_url(target_url, reservation)

    return target_url


def parse_reservation_dates(reservation):
    start_datetime = datetime.datetime.strptime(reservation.get('startDate'), '%Y-%m-%d')
    end_datetime = datetime.datetime.strptime(reservation.get('endDate'), '%Y-%m-%d')
    return start_datetime.strftime('%d/%m/%Y'), end_datetime.strftime('%d/%m/%Y')


def process_shopping_cart(extra_info):
    shopping_cart = []
    try:
        if "shopping_cart_human_read" in extra_info:
            shopping_cart = parse_human_readable_cart(extra_info.get("shopping_cart_human_read", {}).get('rooms', []))
        else:
            shopping_cart = parse_original_cart(extra_info.get("original_rooms", {}))
    except Exception as e:
        logging.error(f"Error processing shopping cart: {e}")
    return shopping_cart


def parse_human_readable_cart(rooms):
    return [{
        "adults": int(room.get('occupancy', '0-0-0').split("-")[0]),
        "kids": int(room.get('occupancy', '0-0-0').split("-")[1]),
        "babies": int(room.get('occupancy', '0-0-0').split("-")[2])
    } for room in rooms]


def parse_original_cart(shopping_cart_data):
    return [{
        "adults": room.get('adults', 0),
        "kids": room.get('kids', 0),
        "babies": room.get('babies', 0)
    } for room in shopping_cart_data.values()]


def append_cart_info_to_url(target_url, shopping_cart):
    for index, room in enumerate(shopping_cart, start=1):
        target_url += f"&adultsRoom{index}={room.get('adults', 0)}&kidsRoom{index}={room.get('kids', 0)}&babiesRoom{index}={room.get('babies', 0)}"
    return target_url


def append_room_info_to_url(target_url, reservation):
    for i in range(3):
        target_url += f"&adultsRoom{i + 1}={reservation.get(f'adults{i + 1}', 0)}"
        target_url += f"&kidsRoom{i + 1}={reservation.get(f'kids{i + 1}', 0)}"
        target_url += f"&babiesRoom{i + 1}={reservation.get(f'babies{i + 1}', 0)}"
    return target_url