#Note, this needs to be synchronized with com.paraty.common.services.shared.configuration.Languages
from copy import deepcopy

from paraty.utilities.languages.translations import webDictionary
from paraty_commons_3.common_data.common_data_provider import CACHE_HOURS, get_hotel_advance_config_value, \
	_set_language_web_page_properties, get_rates_of_hotel
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty.utilities.languages import language_utils

SPANISH = 'SPANISH'
ENGLISH = 'ENGLISH'
GERMAN = 'GERMAN'
PORTUGUESE = 'PORTUGUESE'
ITALIAN = 'ITALIAN'
FRENCH = 'FRENCH'
DUTCH = 'DUTCH'
RUSSIAN = 'RUSSIAN'
CATALAN = 'CATALAN'
FINNISH = 'FINNISH'
POLISH = 'POLISH'


LANGUAGE_TITLES = {
	SPANISH:u"Español",
	ENGLISH:u"English",
	GERMAN:u"Deutsch",
	PORTUGUESE: u"Português",
	ITALIAN: u"Italiano",
	FRENCH: u"Français",
	DUTCH: u"Nederlands",
	RUSSIAN: u"Russian",
	POLISH: u"Polski",
	CATALAN: u"Català",
	FINNISH: u"Suomi",
}

LANGUAGE_CODES = {
			SPANISH: 'es',
			ENGLISH: 'en',
			GERMAN: 'de',
			PORTUGUESE: 'pt',
			ITALIAN: 'it',
	}


def get_language_code(language):
	return LANGUAGE_CODES.get(language, 'en')


def get_language_in_manager_based_on_locale(locale):

	if not locale:
		return SPANISH
	else:
		dictionary = {
			'es': SPANISH,
			'en': ENGLISH,
			'de': GERMAN,
			'pt': PORTUGUESE,
			'it': ITALIAN,
			'es_ES': SPANISH,
			"en_EN": ENGLISH,
			"pt_PT": PORTUGUESE,
			'it_IT': ITALIAN,
			'de_DE': GERMAN,
		}
		return dictionary.get(locale, SPANISH)


def get_all_languages():
	return SPANISH, ENGLISH, GERMAN, PORTUGUESE, ITALIAN


def get_language_title(languageCode):
	return LANGUAGE_TITLES.get(languageCode, "")


def get_web_dictionary(language, hotel=None):
	'''
	Returns the general dictionary for the given language
	'''

	#If not defined we set spanish
	if not language:
		language = SPANISH

	if language == 'default':
		language = SPANISH

	try:
		dict_name = 'translations_' + language.upper()
		result = getattr(webDictionary, dict_name)

	except:
		dict_name = 'translations_' + SPANISH
		result = getattr(webDictionary, dict_name)

	if hotel:
		temp_result = deepcopy(result)
		translations_to_replace = deepcopy(get_translations_from_section(hotel, language))
		temp_result._dict.update(translations_to_replace)
		return temp_result

	return result


def _get_translation_section(hotel, section_name, language, set_languages=True):

	result_entities = get_using_entity_and_params('WebSection', search_params=[('name', '=', section_name)], hotel_code=hotel['applicationId'])
	if not result_entities:
		return None

	result_entities[0]['spanish_name'] = result_entities[0]['name']
	if set_languages:
		_set_language_web_page_properties(hotel['applicationId'], result_entities[0], language, [])

	return result_entities[0]

@timed_cache(hours=CACHE_HOURS)
def get_translations_from_section(hotel, language):

	translation_config = get_hotel_advance_config_value(hotel["applicationId"], "Texto a reemplazar")
	if not translation_config:
		return {}

	translations = _get_translation_section(hotel, translation_config, language)
	if not translations:
		return {}

	translations_to_replace = {key.replace("PMTSEEKER_", ""): translations[key] for key in translations if key.startswith("PMTSEEKER_")}
	return translations_to_replace

def build_custom_text_b3_by_key(translations, translations_section, extra_data, reservation_amount, amount_to_pay, hotel):
	custom_text = {}
	custom_amount = {reservation_amount: amount_to_pay}
	room_key = next(iter(extra_data.get("prices_per_day", {}))).split("@@")[0]
	rate_identifier = extra_data.get("room_info", {})[0].get("rate_identifier")
	rates_hotel = get_rates_of_hotel(hotel)
	current_rate = list(filter(lambda x: x.get("localName") == rate_identifier, rates_hotel))
	rate_key = ""
	if current_rate:
		rate_key = current_rate[0].get("key")
	currency_value = translations.get("T_currency_value")
	if room_key or rate_key:
		key_to_use = "T_%s_%s" % (room_key, rate_key)
		current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace("@@amount@@", amount_to_pay).replace("@@currency@@", extra_data.get("currency"))
		final_translate = translations_section.get("T_" + key_to_use)
		if not final_translate:
			key_to_use = "T_" + room_key
			final_translate = translations_section.get(key_to_use)
			if not final_translate:
				key_to_use = "T_" + rate_key
				final_translate = translations_section.get(key_to_use)
		if final_translate:
			final_translate = final_translate.replace("@@amount@@", amount_to_pay).replace("@@currency@@", extra_data.get("currency")).replace("@@currency_value@@", current_currency_value)
			custom_text[reservation_amount] = final_translate
	return custom_text, custom_amount