from . import SPANISH, ENGLISH, GERMAN, PORTUGUESE, ITALIAN, FRENCH
import logging

#AFTER ADDING A NEW LANGUAGE, EXECUTE UNIT TEST TO MAKE SURE WE ARENT MISSING ANY KEY
from ...data_structures.advanced_dictionaries import ImmutableDict

translations_SPANISH = ImmutableDict(SPANISH.translations)
translations_ENGLISH = ImmutableDict(ENGLISH.translations)
translations_GERMAN = ImmutableDict(GERMAN.translations)
translations_PORTUGUESE = ImmutableDict(PORTUGUESE.translations)
translations_ITALIAN = ImmutableDict(ITALIAN.translations)
translations_FRENCH = ImmutableDict(FRENCH.translations)

logging.info("NOW LANGUAGES GENERAL DICTS ARE INMUTABLES")



