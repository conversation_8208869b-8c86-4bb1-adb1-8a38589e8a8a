from flask import render_template

from paraty.utilities import session_utils
from paraty.utilities.languages import language_utils

def build_template(template_path, context, force_language=None, hotel=None):

	try:
		if force_language:
			language = force_language
		else:
			language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language, hotel)
	except:
		translations = language_utils.get_web_dictionary(False)

	translations = dict(translations)
	translations.update(context)
	context = translations

	return render_template(template_path, **context)


def build_iframe_page(content, css_list=None, jsLib_list=None, js_list=None):
	'''
	Pages to be displayed inside an iframe (embebed on another webpage)
	'''

	page_params = {
		'content': content,
		'title': '',
		'language': 'en'
	}

	if css_list:
		page_params['css_list'] = css_list
	if jsLib_list:
		page_params['jsLib_list'] = jsLib_list
	if js_list:
		page_params['js_list'] = js_list

	return build_template('common/iframe_template.html', page_params)