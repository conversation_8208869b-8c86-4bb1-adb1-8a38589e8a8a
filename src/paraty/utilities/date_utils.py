from datetime import datetime, timedelta, date
from paraty.utilities.manager_utils import get_configuration_property_value
import pytz

from paraty_commons_3.language_utils import LANGUAGE_CODES

forced_timezone = None
try:
	from pytz import timezone
except Exception as e:
	forced_timezone = True


def get_dates_in_range(startDate, endDate, include_last=False, days=1, format='%Y-%m-%d'):
	d = datetime.strptime(startDate, format)
	end_date = datetime.strptime(endDate, format)
	delta = timedelta(days=days)
	ranges = []
	while d < end_date:
		ranges.append(d)
		d += delta

	if include_last:
		ranges.append(d)

	return ranges

def get_dates_until_date(date_init, num_days):
	dates = []
	counter = 0
	while counter < num_days:
		d = date_init - timedelta(days=counter)
		dates.append(d)
		counter += 1
	return dates

def get_date_from_today(offset):
	today = date.today()
	return today + timedelta(days=offset)

def substract_days(date, num):
	return date - timedelta(days=num)

def add_days(date, num):
	return date + timedelta(days=num)

def get_current_date():
	return date.today()

def get_yesterday_date():
	today = date.today()
	return today - timedelta(days=1)

def string_to_date(date, format="%Y-%m-%d"):
	return datetime.strptime(date, format)

def break_date_range(start, end, max_length, format="%Y-%m-%d"):
	'''
	Break a date range into intervals

	i.e. 2010/10/01, 2010/12/01, 20
	-->
	[('2016-10-01', '2016-10-31'), ('2016-10-31', '2016-11-30'), ('2016-11-30', '2016-12-01')]

	'''
	start = datetime.strptime(start,format)
	end = datetime.strptime(end, format)
	num_intervals = ((end - start) / max_length).days + 1

	resulting_list = []

	for i in range(num_intervals):
		resulting_list.append((start + timedelta(days=max_length) * i).strftime(format))
	resulting_list.append(end.strftime(format))
	result = []
	for index, element in enumerate(resulting_list[:-1]):
		result.append((element, resulting_list[index + 1]))
	return result

def date_to_string(date, format="%Y-%m-%d"):

	if not date:
		return None

	return date.strftime(format)

def datetime_to_string(date, format="%d/%m/%Y %H:%M:%S"):
	return date.strftime(format)

def get_timestamp(format="%Y-%m-%d %H:%M:%S"):

	if forced_timezone:
		return datetime_to_string(datetime.now(), format=format)

	else:
		return datetime_to_string(datetime.now(timezone('Europe/Madrid')), format=format)

def get_specific_timestamp(hotel_code, config_name):
	default_tz = 'Europe/Madrid'
	time_zone = default_tz
	specific_timezone = get_configuration_property_value(hotel_code, config_name)
	if specific_timezone:
		time_zone = specific_timezone

	my_tz = timezone(time_zone)
	if not my_tz:
		my_tz = timezone(default_tz)

	geolocalized_now = datetime.now(my_tz)
	utc_now = datetime.now(tz=pytz.UTC)
	return geolocalized_now.strftime("%Y-%m-%d %H:%M:%S")


def format_date_to_string(date_str, language, format="%Y-%m-%d"):
	from babel.dates import format_date
	date_obj = datetime.strptime(date_str, format)
	language_code = LANGUAGE_CODES.get(language, 'es')
	formatted_date = format_date(date_obj, format="full", locale=language_code)
	return formatted_date


if __name__ == "__main__":
	print(break_date_range("2016-10-01", "2016-12-01", 30))