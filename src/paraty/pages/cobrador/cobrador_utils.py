import unicodedata

import base64
import copy
import hashlib
import hmac
import json
# import logging

from datetime import date
import datetime
import re
import uuid
from flask import request

import requests
from Cryptodome.Cipher import AES
from flask import request as r
from Cryptodome.Cipher import DES3
from pytz import timezone


from paraty import config, Config
from paraty.pages.cobrador.cobrador_constants import COBRADOR_QUEUE, INTEGRATION_CONFIG_MAP_SEPARATOR, \
	COBRADOR_SERVER_PATH, \
	LIMITS_BY_CURRENCIES, ALL_PAYMENT_METHODS, METADATA_URL, SEPARATOR_INFO_ERROR_ORDER, EMAIL_SENDER_CONFIG, \
	MESSAGE_KEY, \
	USE_ENCRYPTED_PAY_LINKS, PAYMENT_TYPE_LINK, PAYMENT_TYPE_IN_WEB, NAMESPACE, REAL_PAYMENTS, \
	GATEWAY_ERROR_CODE_RETURNED, COBRADOR_FAILOVER_SERVER_PATH, USE_ALTERNATIVE_PAYMENT_SEEKER, GATEWAY_WITH_BIZUM, \
	RATE_MAP_SEPARATOR, MANUAL_PAYMENT_TYPES, USER, PASSWORD, GENERIC_IMAGE_EMAIL, BACKEND_GOOGLE_GROUP, REAL_REFUNDS, \
	FORCE_PAYMENT_LINK_GATEWAY_BY_CURRENCY

from paraty.utilities.currencies import get_currency_symbol
from paraty.utilities.date_utils import format_date_to_string
from paraty.utilities.email_utils import notify_error_by_email, build_email_sender_from_hotel_webs_config, sendEmail
from paraty.utilities.languages import language_utils
from paraty_commons_3 import queue_utils
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore

from paraty_commons_3.datastore.datastore_utils import id_to_entity_key
from paraty_commons_3.decorators.cache.managers_cache.cache_entry_refresher import refresh_entity_timestamps
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty.utilities.languages.language_utils import get_language_in_manager_based_on_locale, get_language_code, \
	get_web_dictionary, get_all_languages, SPANISH
from paraty.utilities.manager_utils import get_configuration_property_value, get_hotel_logotypes, \
	get_picture_from_section
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, \
	get_rooms_of_hotel, get_rates_of_hotel, \
	get_boards_of_hotel, get_multirates_of_hotel, get_conditions_of_hotel, get_hotel_advance_config_item, \
	get_additional_services_of_hotel, get_web_section
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id, get_internal_url
from paraty_commons_3.redis.redis_communicator import build_redis_client_hotel
from paraty_commons_3.utils import proxy_utils
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.date_utils import date_to_string
from urllib.parse import urlencode, quote
from paraty_commons_3.logging.my_gae_logging import logging

VERSION_CSS = "1.1"

IGNORE_EXCEPTIONS = ["no such customer"]
# Ignore some exceptions from stripe


def gateways_format_price(amount):
	price = str('%.2f' % float(amount)).split('.')
	price_euros = price[0]
	if len(price) > 1:
		price_cents = str(price[1])
		while len(price_cents) < 2:
			price_cents += "0"
	else:
		price_cents = "00"

	amount_formated = str(price_euros) + price_cents[0:2]  # Only two digits

	return amount_formated


def encrypt_message_DES3(message, password):
	iv = b'\0\0\0\0\0\0\0\0'
	des3 = DES3.new(password, DES3.MODE_CBC, iv)
	return des3.encrypt(message.encode())


def get_integration_name(hotel_code):
	integration_name = get_configuration_property_value(hotel_code, 'Use Payment Gateway Special Cobrador')
	if not integration_name:
		integration_name = get_configuration_property_value(hotel_code, 'Use Payment Gateway')

	if r.values.get("force_gateway"):
		integration_name = r.values['force_gateway']
		# Truncate force_gateway value to prevent issues with long strings
		integration_name = truncate_string_by_bytes(integration_name)

	if ";" in integration_name:
		integration_name = integration_name.split(";")[0]

	logging.info("Looking for integration_name ('Use Payment Gateway'): %s", integration_name)

	return integration_name


def get_payment_gateway_configuration(gateway_type, hotel_code):
	'''
	TODO: FOR THE MOMENT We continue taken config form XML-> config
	Gets the payment provider configuration
	'''

	hotel_info = get_hotel_by_application_id(hotel_code)
	integration_configurations = get_integration_configuration_of_hotel(hotel_info, gateway_type)
	gateway_configuration = {}
	if integration_configurations:
		logging.info("integration_configuration found!")

		integration_configuration = integration_configurations[0].get('configurations', [])
		for config in integration_configuration:
			key_and_value = config.split(INTEGRATION_CONFIG_MAP_SEPARATOR)
			gateway_configuration[key_and_value[0]] = key_and_value[1]

	return gateway_configuration


def add_sid_to_url(url, sid):
	if "?" in url:
		return url + "&sid=" + sid
	else:
		return url + "?sid=" + sid


def add_parameters_and_namespace_to_url(url, parameters, hotel_code, multitenancy=False):
	separator = "?" if "?" not in url else "&"
	for key, value in parameters.items():
		url += f"{separator}{key}={value}"
		separator = "&"
	if NAMESPACE not in url and multitenancy:
		url += "&namespace=" + hotel_code
	return url


def _get_project_id_and_namespace(hotel):
	namespace = None
	project_id = hotel['applicationId']
	if 'multi' in hotel['url']:
		project_id = hotel['url'][17:]
		project_id = project_id[:project_id.find('.appspot')]
		namespace = hotel['applicationId']
	return project_id, namespace


def extract_params_from_request(request):
	try:
		body = request.get_json()
	except:
		body = {}

	if not body:
		try:
			payload = request.data
			if isinstance(request.data, bytes):
				payload = payload.decode().replace("'", '"').replace("False", "false").replace("True", "true").replace("None", "null")
				body = json.loads(payload)
		except:
			body = {}

	if not isinstance(body, dict):
		body = {}

	if not body:
		body = dict(request.form)

	body.update(dict(request.args))
	return body

def extract_paylink_data(body):
	paylink_data = {}
	paylink_data["name"] = body.get("paylink_customer_name","")
	paylink_data["phone_number"] = body.get("paylink_customer_phone_number","")
	paylink_data["address"] = body.get("paylink_customer_billingAddress","")
	paylink_data["city"] = body.get("paylink_customer_city","")
	paylink_data["postal_code"] = body.get("paylink_customer_customer_postal_code","")
	paylink_data["amount"] = body.get("amount")
	paylink_data["currency"] = body.get("currency")
	paylink_data["identifier"] = body.get("payment_order_id")
	paylink_data["email"] = body.get("paylink_customer_email")
	return paylink_data


# try:
# 	body = request.get_json()
# except:
# 	body = None
# #try get params by POST
# if not body:
# 	body = dict(request.form)
# 	if not body:
# 		#try get params by GET
# 		body = dict(request.args)
# return body


def build_common_iframe_html(hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data={}, custom_url=None, extra_urlparams=""):
	language_code = get_language_code(extra_data.get("language", "es"))
	destination_id = extra_data.get("destination_id")
	get_params = "?hotel_code=%s&sid=%s&payment_order_id=%s&amount=%s&language=%s&currency=%s%s" % (
	hotel_code, sid, payment_order_id, amount, language_code, extra_data.get('currency', ''), extra_urlparams)

	if gateway_type:
		get_params += "&force_gateway=%s" % gateway_type

	gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

	try:
		secret_key_sha256 = gateway_configuration.get("secret_key_sha256")
		security_str = encrypt_amount_with_security(amount, payment_order_id, secret_key_sha256, sid)

		get_params += "&security_str=%s" % security_str
	except Exception as e:
		logging.error("build_common_iframe_html - >Imposible to add security_str hotel_code: %s: %s",
					  hotel_code, str(e))



	ok_url = add_sid_to_url(gateway_configuration["url_ok"], sid)

	if custom_url is not None:
		url = custom_url + get_params
	else:
		use_alternative_payment_seeker = get_hotel_advance_config_item(get_hotel_by_application_id(hotel_code), 'Use alternative payment seeker')
		url = COBRADOR_SERVER_PATH
		if use_alternative_payment_seeker:
			url = use_alternative_payment_seeker[0].get('value')
		url = url + "/forms/cobrador/get_common_paraty_gateway_form" + get_params
		# url = 'http://127.0.0.1:8080' + "/forms/cobrador/get_common_paraty_gateway_form" + get_params

	params = {"src_form": url,
			  "destination_id": destination_id,
			  "ok_url": ok_url}

	form_template = "pages/cobrador/gateways/common_paraty_iframe.html"

	language = get_language_in_manager_based_on_locale(language_code)
	html_form_gateway = build_template(form_template, params, force_language=language)

	return html_form_gateway


def build_inner_common_paraty_form(hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
	gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

	ok_url = add_sid_to_url(gateway_configuration["url_ok"], sid)
	today = datetime.datetime.now()
	actualYear = today.strftime("%Y")
	actualMonth = today.strftime("%m")
	credit_cards_years = []
	for i in range(15):
		YearToAppend = int(actualYear) + i
		credit_cards_years.append(str(YearToAppend)[-2:])

	date_card_validator_year = actualYear[-2:]
	date_card_validator_month = int(actualMonth) + 1

	css_list = ['/static/css/libs/fontawesome5.css', '/static/css/libs/jquery_confirm/jquery-confirm.min.css',
				'/static/css/pages/cobrador/cobrador_common_form.css?v=%s' % VERSION_CSS]
	jsLib_list = ['/static/js/libs/jQuery.min.js', '/static/js/libs/jquery_confirm/jquery-confirm.min.js',
				  '/static/js/libs/jquery_confirm/jquery-confirm.min.js']


	try:
		secret_key_sha256 = gateway_configuration.get("secret_key_sha256", "")
		# semilla DES3
		security_str = encrypt_amount_with_security(amount, payment_order_id, secret_key_sha256, sid)

	except Exception as e:
		logging.error("build_inner_common_paraty_form >Imposible to generate security_str hotel_code: %s: %s", hotel_code, str(e))
		security_str = ""

	use_alternative_payment_seeker = get_hotel_advance_config_item(get_hotel_by_application_id(hotel_code),
	                                                           'Use alternative payment seeker')
	url = COBRADOR_SERVER_PATH
	if use_alternative_payment_seeker:
		url = use_alternative_payment_seeker[0].get('value')

	hide_billingZipCode = True if gateway_configuration.get("hide_billingZipCode") else False
	send_email_to_tpv = True if gateway_configuration.get("send_email_to_tpv") else False
	currency = extra_data.get('currency', 'EUR')

	params = {
		"hotel_code": hotel_code,
		"post_url": url + "/forms/cobrador/do_payment_by_post?force_gateway=" + gateway_type,
		"payment_order_id": payment_order_id,
		"amount": amount,
		"security_str": security_str,
		"ok_url": ok_url,
		"sid": sid,
		"credit_cards_years": credit_cards_years,
		'date_card_validator_year': date_card_validator_year,
		'date_card_validator_month': date_card_validator_month,
		"add_doc_headers": extra_data.get("add_doc_headers"),
		"jsLib_list": jsLib_list,
		"css_list": css_list,
		"hide_billingZipCode": hide_billingZipCode,
		"send_email_to_tpv": send_email_to_tpv,
		"currency": currency
	}

	# paraty is in charge to ask credit card. Then CC will be sent to gateway (resortcom in this case)
	form_template = "pages/cobrador/gateways/common_paraty_form.html"

	if add_button:
		params["add_button"] = True

	language_code = extra_data.get("language", "es")
	language = get_language_in_manager_based_on_locale(language_code)
	html_form_gateway = build_template(form_template, params, force_language=language)

	return html_form_gateway


def encrypt_amount_with_security(amount, payment_order_id, secret_key_sha256, sid):
	secret_key_sha256_b64 = base64.b64decode(secret_key_sha256)
	amount_key_for_signature = encrypt_message_DES3(payment_order_id, secret_key_sha256_b64)

	str_amount_to_encrypt = str("%s@@%s" % (amount, sid))
	str_amount_to_encrypt = str_amount_to_encrypt.encode("utf-8")
	str_amount_to_encrypt = base64.b64encode(str_amount_to_encrypt)

	security_str = hmac.new(amount_key_for_signature, str_amount_to_encrypt, digestmod=hashlib.sha256).digest()
	security_str = base64.b64encode(security_str)
	security_str = security_str.decode("utf8")
	return security_str



def post_body(payment_url, body_request, user, password, headers):
	# TODO: take this use_proxy from configuration

	response_json = None
	use_proxy = True
	post_directly = True
	if use_proxy:
		post_directly = False
		try:
			logging.info("POSTING to: %s", payment_url)
			resortcom_response = proxy_utils.post_using_generic_proxy_tls_1_2(payment_url, body_request, headers, timeout=120)
			result_resort_com = resortcom_response.json()
			logging.info("result_resort_com by proxy: %s", result_resort_com)
		except:

			logging.warning("Error trying to post VIA Proxy")
			logging.info("Trying to post directly")
			post_directly = True

	if post_directly:
		logging.info("Posting directly")
		response = requests.post(payment_url, data=body_request, headers=headers, auth=(user, password))
		response_json = response.json()

	return response_json


def _get_payments_by_link(reservation, translations={}, order=None, hotel_code=None):
	# get payment made by tpv links
	link_web_payment = []
	extra_info = reservation.get("extraInfo", "{}")
	if extra_info:
		extra_info = json.loads(extra_info)
		if extra_info.get("payed_by_tpv_link"):
			for payment_by_link in extra_info.get("payed_by_tpv_link"):
				if (not order) or order == payment_by_link.get("order"):
					data = {
						"timestamp": payment_by_link.get("timestamp"),
						"order": payment_by_link.get("order"),
						"amount": float(payment_by_link.get("amount")),
						"user": translations.get("T_LINK", "enlace"),
						"reservation_identifier": reservation.get("identifier"),
						"type": "Cobro por link al cliente", "comments": "",
						"payed_by_tpv_link_index": extra_info.get("payed_by_tpv_link").index(payment_by_link),
						"deleted": payment_by_link.get("deleted", False)
					}

					if payment_by_link.get("datatransdata") and hotel_code:
						hotel_url= get_internal_url(hotel_code)
						url_params = {
							"action": "decrypt",
							"data": quote(payment_by_link.get("datatransdata"), safe='')
						}

						data["datatrans"] = "%s/utils?%s" % (hotel_url, urlencode(url_params))

					link_web_payment.append(data)
	return link_web_payment


def audit_response(hotel_code, gateway_type, payment_order_id, web_sid, response_txt, type_audit="Process payment", payload="{}"):
	try:
		# none if not a modification
		id_audit = None
		now = date_to_string(datetime.datetime.now(), format="%Y-%m-%d %H:%M:%S")

		audit_response_dict = {
			"hotel_code": hotel_code,
			"gateway_type": gateway_type,
			"payment_order_id": payment_order_id,
			"web_sid": web_sid,
			"response": response_txt,
			"timestamp": now,
			"type": type_audit,
			"payload": payload
		}

		audit_id = datastore_communicator.save_to_datastore("AuditResponse", id_audit, audit_response_dict, hotel_code=hotel_code)

		try:
			try:
				info = json.dumps(audit_response_dict)

			except Exception as e:
				audit_response_dict["response"] = audit_response_dict["response"].decode("utf-8")
				info = json.dumps(audit_response_dict)
			data_task = {
				"hotel_code": hotel_code,
				"info": info,
				"save_audit": True
			}
			task_uuid = str(uuid.uuid4())
			queue_utils.create_task("save_response_process", json.dumps(data_task), queue_name="save-info-payments",
									task_name=f"save_audit_{task_uuid}_{hotel_code}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}", in_seconds=30)
		except Exception as e:
			logging.warning(audit_response_dict)
			logging.error(f"Error creating task for save paymen in save reservation reservation info: {e}")


	except Exception as e:
		logging.warning("Error auditing response for %s payment_order_id: %s", gateway_type, payment_order_id)
		logging.warning("Error auditing: %s", e)


def date_range_list(start_date, end_date):
	# Return list of datetime.date objects between start_date and end_date (inclusive).
	date_list = []
	curr_date = start_date
	while curr_date <= end_date:
		date_list.append(curr_date)
		curr_date += datetime.timedelta(days=1)
	return date_list


def limit_amount_excedeed(hotel_code, currency, num_nights, amount, room_info=[], sid=""):
	try:
		limit_amount = LIMITS_BY_CURRENCIES.get(currency.upper())

		num_nights_ok = num_nights and float(num_nights)
		amount_ok = amount and float(amount)
		num_rooms = 1
		if room_info:
			num_rooms = len(room_info) or 1


		if num_nights_ok and amount_ok and limit_amount:
			if (float(amount) / float(num_nights) / float(num_rooms)) > float(limit_amount):
				message_error = "Hotel: %s Sid: %s, Amount: %s, currency: %s, Num Nights: %s. <br> LIMITS: %s" % (
				hotel_code, sid, amount, currency, num_nights, LIMITS_BY_CURRENCIES)
				notify_error_by_email("[MEGA URGENT] LIMIT AMOUNT EXCEEDED IN PAYMENT SEEKERS!", message_error)
				return True
	except Exception as e:
		message_error = "Hotel: %s Sid: %s, Amount: %s, currency: %s, Num Nights: %s. <br> LIMITS: %s" % (
		hotel_code, sid, amount, currency, num_nights, LIMITS_BY_CURRENCIES)
		logging.error("Exception in limit_amount_excedeed. ASSUMING FILTER IS OK!!  %s %s", message_error, e)
		notify_error_by_email("[URGENT] limit_amount_excedeed EXCEPTION",
							  "Exception in limit_amount_excedeed. ASSUMING FILTER IS OK!!: %s  %s" % (message_error, e))

	return False


def get_limit_error_message(language_code):
	return build_template('pages/cobrador/limit_amount_error.html', {}, force_language=language_code)
	# language = get_language_in_manager_based_on_locale(language_code)
	# try:
	# 	translations = get_web_dictionary(language)
	# except:
	# 	translations = get_web_dictionary(False)
	#
	# return translations.get("T_LIMIT_ERROR")


def extract_payments_time_control(config):
	time_payments_control = config.get("time payments control") if config.get("time payments control") else 2
	if time_payments_control != "off":
		try:
			time_payments_control = int(time_payments_control)
			time_payments_control = time_payments_control * 60
		except:
			logging.warning("Bad configuration! Please check the config 'time payments control'")
			time_payments_control = 180

	return time_payments_control


def get_all_gateways(hotel_code):
	configurations = datastore_communicator.get_using_entity_and_params('IntegrationConfiguration', search_params=[], hotel_code=hotel_code)
	results = []
	for configuration in configurations:
		for payment_method in ALL_PAYMENT_METHODS:
			config_name = configuration.get("name", "")
			if payment_method in config_name and not config_name.startswith("_") and not "RANGE_PERCENT" in config_name:
				results.append(config_name)

	if get_config_property_value(hotel_code, GATEWAY_WITH_BIZUM):
		results.append("BIZUM")

	return list(set(results))


@timed_cache(hours=2)
def get_config_property_value(hotel_code, config):
	return get_config_property_value_without_cache(hotel_code, config)


def get_config_property_value_without_cache(hotel_code, config):
	configurations = datastore_communicator.get_using_entity_and_params('ConfigurationProperty', search_params=[("mainKey", "=", config)],
																		hotel_code=hotel_code)
	if configurations:
		return configurations[0].get("value")

	return None


def send_paymentlink_email_to_customer(hotel_code, request_params, user_name):
	logging.info("send_paymentlink_email_to_customer")
	logging.info("sending payment link with parameters: %s" % json.dumps(request_params))

	configuration_payment_seeker = get_config_payment_seeker_configuration(hotel_code)
	hotel = get_hotel_by_application_id(hotel_code)
	identifier = request_params.get("identifier")
	email_title = ''
	amount = request_params.get("amount", "").replace(",", ".").replace('%', '')
	type_link = request_params.get("type_link", "")
	comments = request_params.get("comment_email", "").replace("\n", "<br>")
	unique_usage_link = True if request_params.get("unique_usage") == "on" else False
	force_payed_link = True if request_params.get("allow_sending") == "on" else False
	show_currency_conversion = configuration_payment_seeker.get("Show currency conversion")

	expire_hours = request_params.get("expire_hours", "")

	link_sent = "ERROR"
	if not identifier:
		return link_sent

	reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))
	if reservations:

		reservation = reservations[0]

		try:
			language = reservation.get("language")
		except:
			language = "SPANISH"

		if not language:
			language = "SPANISH"

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

		translations = language_utils.get_web_dictionary(language, hotel=hotel)
		num_nights = calculate_total_nights(reservation.get('startDate'), reservation.get('endDate'), format="%Y-%m-%d")
		total_price = float(reservation.get("price", "0")) + float(reservation.get("priceSupplements", "0"))
		customer_email = reservation.get("email")

		email_sender_config = get_configuration_property_value(hotel_code, EMAIL_SENDER_CONFIG)
		email_sender = build_email_sender_from_hotel_webs_config(email_sender_config,
																 backup_sender="<EMAIL>")

		amount_text = "0"
		if type_link == "price":
			amount_text = amount
		if type_link == "full":
			amount_text = get_total_pending_amount_to_pay(reservation)
		if type_link == "percent":
			amount_text = get_percent_pending_amount_to_pay(reservation, amount)
			amount = amount_text

		amount_text = float("{:.2f}".format(float(amount_text)))
		default_currency = get_configuration_property_value(hotel_code, "Base Price Currency") or "EUR"
		currency = get_currency_symbol(extra_info.get("currency", default_currency))

		if not type_link == "token":

			text = translations.get("T_NEW_LINK_EMAIL_PAYMENT").replace("@@amount@@", str(amount_text)).replace(
				"@@currency@@", currency)

			if extra_info.get("status_reservation") == "pending":
				text_aux = translations.get("T_NEW_LINK_EMAIL_PAYMENT").replace("@@amount@@", str(amount_text)).replace(
					"@@currency@@", currency)
				text = "".join([translations.get("T_TEXTO_ADDITIONAL_NEW_LINK_1"), translations.get("T_TEXTO_ADDITIONAL_NEW_LINK_2"), text_aux])
		else:
			text = translations.get("T_NEW_LINK_EMAIL_TOKEN")
			if extra_info.get("status_reservation") == "pending":
				text = "".join([translations.get("T_TEXTO_ADDITIONAL_NEW_LINK_1"), translations.get("T_NEW_LINK_EMAIL_PAYMENT")]).replace("@@amount@@", str(amount_text)).replace(
					"@@currency@@", currency)

		if request_params.get("built_link"):
			link = request_params["built_link"]
		else:
			link = build_new_link(reservation, type_link, amount, hotel_code, unique_usage_link=unique_usage_link,force_payed_link= force_payed_link)

		header_image = "https://cdn2.paraty.es/test-backend3/images/6b5712725331c2e=s600"

		logo_type = ""
		filter_logo = "survey"
		if configuration_payment_seeker and configuration_payment_seeker.get("logo_name"):
			filter_logo = configuration_payment_seeker.get("logo_name")
		logos = get_hotel_logotypes(hotel_code,filter_logo)
		if logos:
			logo_type = logos[0]

		if expire_hours:

			try:
				# check if this expire hour is bigger than arrival client
				current_datetime = datetime.datetime.now()

				parsed_start_date = datetime.datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
				# Add 72 hours to the current date and time
				today_plus_hours = current_datetime + datetime.timedelta(hours=float(expire_hours))

				if today_plus_hours > parsed_start_date:
					end_day_of_start_date = datetime.datetime.strptime(reservation.get("startDate") + " 23:59:59", "%Y-%m-%d %H:%M:%S")

					# Calculate the time difference
					time_difference = end_day_of_start_date - current_datetime

					# Calculate the hours between the two datetime objects
					hours_difference = int(time_difference.total_seconds() / 3600)

					expire_hours = hours_difference
			except:
				logging.warning("Imposible to calculate expire_hours in payment link email %s", identifier)
		not_show_generic_image = configuration_payment_seeker.get("not show generic image")
		generic_image_path = GENERIC_IMAGE_EMAIL
		if configuration_payment_seeker and configuration_payment_seeker.get("payment link customer", "")=="V2":
			template_dict = {
				'telephone': configuration_payment_seeker.get("telephone"),
				'email': configuration_payment_seeker.get("email"),
				'greeting_text': translations.get('T_greetings').replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo")),
				'main_text': translations.get('T_safe_payment').replace('@@HOTEL@@',  configuration_payment_seeker.get("name hotel")),
				'hi_customer': '%s %s %s,' % (translations.get('T_HOLA'), reservation.get('name'), reservation.get('lastName')),
				'charge_amount': translations.get('T_charge').replace('@@AMOUNT@@', str(amount_text)).replace('@@CURRENCY@@', currency),
				'expire_hours': translations.get('T_will_expire').replace('@@expire_hours@@', str(expire_hours)),
				'color1': configuration_payment_seeker.get("color link html", '#967E2C'),
				'link': link,
				"logo_type": logo_type,
				"not_show_generic_image": not_show_generic_image,
				"generic_image_path": generic_image_path,
			}

			html_content = build_template('pages/cobrador/emails/email_new_payment_link_customer_v2.html', template_dict,
										  force_language=language)
		elif configuration_payment_seeker and (configuration_payment_seeker.get("payment link customer", "")=="V4" or configuration_payment_seeker.get("payment link customer", "")=="V5" or configuration_payment_seeker.get("payment link customer", "") == "daypass"):
			hotel = get_hotel_by_application_id(hotel_code)
			translations_section = language_utils._get_translation_section(hotel,"_email_link_payed", language,True)
			prestay_image = get_picture_from_section(hotel_code, 'prestay_image', translations_section, language, get_url=True)
			pending_amount_to_pay = get_total_pending_amount_to_pay(reservation)
			additional_services = get_additional_services(hotel, extra_info.get('additional_services_keys', None), language)
			location = get_picture_from_section(hotel_code, 'location', translations_section, language, False)

			if translations_section.get('T_NEW_LINK_EMAIL_TITLE'):
				email_title = translations_section.get('T_NEW_LINK_EMAIL_TITLE').replace('@@identifier@@', reservation.get('identifier'))
			second_text = translations_section.get('T_SECOND_TEXT_EMAIL', '')
			second_text = second_text.replace('@@hours@@', str(configuration_payment_seeker.get("expire_hours_link_email", 2)))
			farewell_message = translations_section.get('T_FAREWELL', '')
			template_dict = {
				'logo_type': logo_type,
				'hotelname': configuration_payment_seeker.get("name hotel"),
				'main_text': translations_section.get('T_email_text_v3', translations.get("T_MAIN_TEXT_EMAIL_LINK")).replace('@@name@@', reservation.get('name')).replace('@@surname@@',reservation.get('lastName')),
				'entrydate': reservation.get("startDate"),
				'departuredate': reservation.get("endDate"),
				'identifier': reservation.get("identifier"),
				'total_pending': pending_amount_to_pay,
				'currency': currency,
				'payment_link': link,
				'color1': configuration_payment_seeker.get("color link html", '#002E54'),
				'phone': configuration_payment_seeker.get("telephone"),
				'whatsapp': configuration_payment_seeker.get("whatsapp",""),
				'email': configuration_payment_seeker.get("email"),
				'greeting_text': translations_section.get('T_greetings', translations.get("T_greetings")).replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo")),
				"one_step_to_finish": translations_section.get('T_one_step_to_finish'),
				'expire_hours': translations.get('T_will_expire').replace('@@expire_hours@@', str(expire_hours)),
				'comments': comments,
				'user': user_name,
				'num_nights': num_nights,
				'total_price': total_price,
				'second_text': second_text,
				'prestay_image': prestay_image,
				'legal_text': translations_section.get('T_LEGAL_TEXT'),
				'additional_services': additional_services,
				'until_hour': translations_section.get('T_UNTIL_HOUR'),
				'from_hour': translations_section.get('T_FROM_HOUR'),
				'location': location,
				'farewell_message': farewell_message,
			}

			template_dict['rooms'] = get_rooms_info(reservation, extra_info, hotel, language)

			if extra_info.get("status_reservation") == "pending" and translations_section.get('T_email_text_v3_status_pending'):
				template_dict["main_text"] = translations_section.get('T_email_text_v3_status_pending').replace('@@name@@', reservation.get('name')).replace('@@surname@@',reservation.get('lastName'))

			if type_link != "token" and amount_text > 0:
				template_dict["amount_to_pay"] = amount_text

			if extra_info.get("visual_currency") and extra_info.get("visual_currency_conversion_rate") and show_currency_conversion:
				conversion_rate = extra_info.get("visual_currency_conversion_rate")
				conversion_currency = extra_info.get("visual_currency")
				template_dict["currency_conversion"] = conversion_currency
				currency_conversion_amount = "%.2f" % (float(amount) / conversion_rate)
				template_dict["currency_conversion_amount"] = currency_conversion_amount
				pending_amount_currency_conversion_amount = "%.2f" % (float(pending_amount_to_pay) / conversion_rate)
				template_dict["pending_amount_currency_conversion_amount"] = pending_amount_currency_conversion_amount


			try:
				email_version = "V4"
				if configuration_payment_seeker.get("payment link customer", "") == 'V5':
					template_dict['departuredate'] = format_date_to_string(reservation.get("endDate"), language)
					template_dict['entrydate'] = format_date_to_string(reservation.get("startDate"), language)
					email_version = "V5"

				html_template = f"pages/cobrador/emails/email_new_payment_link_customer_{email_version}.html"
				if configuration_payment_seeker.get("payment link customer", "") == "daypass":
					html_template = build_template_link_payment_daypass(hotel_code, html_template, reservation,
																		template_dict)

				html_content = build_template(html_template, template_dict, force_language=language)

			except Exception as e:
				logging.info(e)
		# elif configuration_payment_seeker and configuration_payment_seeker.get("payment link customer", "")=="V3":
		# 	template_dict = {
		# 		'telephone': configuration_payment_seeker.get("telephone"),
		# 		'email': configuration_payment_seeker.get("email"),
		# 		'greeting_text': translations.get('T_greetings').replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo")),
		# 		'main_text': translations.get('T_safe_payment').replace('@@HOTEL@@',  configuration_payment_seeker.get("name hotel")),
		# 		'hi_customer': '%s %s %s,' % (translations.get('T_HOLA'), reservation.get('name'), reservation.get('lastName')),
		# 		'charge_amount': translations.get('T_charge').replace('@@AMOUNT@@', str(amount_text)).replace('@@CURRENCY@@', currency),
		# 		'expire_hours': translations.get('T_will_expire').replace('@@expire_hours@@', str(expire_hours)),
		# 		'color1': configuration_payment_seeker.get("color link html", '#967E2C'),
		# 		'link': link,
		# 		"logo_type": logo_type,
		# 		"lenguage": language,
		# 		'pay_reservation':  translations.get('T_PAY_RESERVATION'),
		# 		'link_will_expire':translations.get('T_link_will_expire').replace('@@expire_hours@@', str(expire_hours)),
		# 		'thanks_prebook_hotel':translations.get('T_thanks_prebook_hotel').replace('@@HOTEL@@',  configuration_payment_seeker.get("name hotel"))
		#
		# 	}
		#
		# 	html_content = build_template('pages/cobrador/emails/email_new_payment_link_customer_v3.html', template_dict,
		# 								  force_language=language)
		else:

			template_dict = {
				"text": text,
				"link": link,
				"comments": comments,
				"logo_type": logo_type,
				"identifier": reservation.get("identifier"),
				"header_image": header_image,
				"hi_customer": translations.get("T_NEW_LINK_EMAIL_BODY").replace("@@cliente@@", reservation.get(
					"name") + " " + reservation.get("lastName")),
				"expire_hours": expire_hours
			}

			html_content = build_template('pages/cobrador/emails/email_new_payment_link_customer.html', template_dict,
										  force_language=language)


		title = email_title if email_title else translations.get("T_NEW_LINK_EMAIL_TITLE") + " " + reservation.get("identifier")

		email_hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
		if email_hotel_name:
			title += " " + email_hotel_name


		sendEmail(customer_email, title, "", html_content, sender=email_sender, backup_sender="<EMAIL>")
		logging.info("email sent correctly to %s", customer_email)

		link_email_hotel = get_configuration_property_value(hotel_code, "Payment link copy hotel")
		if link_email_hotel:
			sendEmail(link_email_hotel, title, "", html_content, sender=email_sender, backup_sender="<EMAIL>")

		# save a fake payment, for the historic
		error = None
		order = identifier
		if "ERROR" in order:
			info_error = order.split(SEPARATOR_INFO_ERROR_ORDER)
			if len(info_error) > 1:
				order = info_error[1]
				error = info_error[0]
			else:
				error = order

		payment_type = PAYMENT_TYPE_LINK
		today_ts = datetime.datetime.now()
		today_ts_txt = date_to_string(today_ts, format="%Y-%m-%d %H:%M:%S")

		payment_params = {
			"order": order,
			"error": error,
			"comments": comments,
			"type": payment_type,
			"user": user_name,
			"reservation_identifier": identifier,
			"amount": float(amount_text),
			"timestamp": today_ts_txt,
		}
		payment_id = save_secure_payment_reservation(payment_params, hotel_code)


		extra_info['link_payment_sended'] = "ok"
		extra_info["payment_link_send_date"] = get_hotel_datetime(hotel_code).strftime("%Y-%m-%d %H:%M:%S")
		reservation["extraInfo"] = json.dumps(extra_info)
		logging.info("[%s][UPDATE] Saving link of payment in reservation, hotel_code: %s", identifier, hotel_code)
		reservation_id = int(reservation.key.id)
		datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)

		link_sent = "OK"

	else:
		logging.info("reservation %s not found" % identifier)

	return link_sent


def build_template_link_payment_daypass(hotel_code, html_template, reservation, template_dict):
	reservation_daypass = json.loads(reservation["extraInfo"])["shopping_cart"]
	template_dict["daypass"] = reservation_daypass
	if get_configuration_property_value(hotel_code, "Only one hotel"):
		hotel_code_daypass = reservation_daypass[0].get("hotel_code")
		template_dict["hotelname"] = get_configuration_property_value(hotel_code_daypass, "Nombre del hotel")
	html_template = "pages/cobrador/emails/email_new_payment_link_customer_daypass.html"
	return html_template


def get_total_pending_amount_to_pay(reservation):
	total_payed = 0
	total_pending = 0
	extra_info = reservation.get("extraInfo", "{}")
	if extra_info:

		extra_info = json.loads(extra_info)
		if extra_info.get("payed"):
			total_payed = float(extra_info.get("payed"))

		if extra_info.get("payed_by_cobrador"):
			total_payed += float(extra_info.get("payed_by_cobrador"))

		if extra_info.get("payed_by_tpv_link"):
			for payment_by_link in extra_info["payed_by_tpv_link"]:
				if payment_by_link.get('datatransdata'):
					continue
				total_payed += float(payment_by_link.get("amount"))

		total_price = float(reservation.get("price", "0")) + float(reservation.get("priceSupplements", "0"))
		total_pending = total_price - total_payed
		if total_pending < 0:
			total_pending = 0

	return total_pending


def get_percent_pending_amount_to_pay(reservation, percent):
	total_pending = get_total_pending_amount_to_pay(reservation)
	total_to_be_paid = float(percent) * float(total_pending) / 100
	return total_to_be_paid


def get_reservation_currency(reservation):
	extra_info = reservation.get("extraInfo", "{}")
	if extra_info:
		extra_info = json.loads(extra_info)
		if extra_info.get("currency"):
			return extra_info.get("currency")

	return "EUR"


def build_new_link(reservation, type_link, amount, hotel_code, unique_usage_link=True,force_payed_link= False):
	'''
        amount_for_link could be:
        price = full
        price = token
        price = xxXX
    '''

	total_pending = get_total_pending_amount_to_pay(reservation)

	if type_link == "full":
		amount = str(total_pending)

	if type_link == "token":
		amount_for_link = "token"
	elif type_link == "pending":
		amount_for_link = "pending"
	elif type_link == "datatrans":
		amount_for_link = "datatrans"
	else:

		# convert to SERMEPA FORMAT
		amount_for_link = gateways_format_price(amount)
		logging.info("Price in semerpa format: %s" % amount_for_link)

	booking_domain = get_configuration_property_value(hotel_code, 'Dominio asociado')
	search_domain = get_configuration_property_value(hotel_code, 'Dominio booking')
	if search_domain:
		booking_domain = search_domain

	# if get_config_property_value_without_cache(hotel_code, USE_ENCRYPTED_PAY_LINKS):

	data = {
		"identifier": reservation.get("identifier"),
		"price": amount_for_link,
		"link_creation_ts": get_hotel_datetime(hotel_code).strftime("%Y-%m-%d %H:%M:%S")

	}

	currency = get_reservation_currency(reservation)
	logging.info("Reservation currency for payment link: %s" % currency)

	force_gateway_by_currency = get_configuration_property_value(hotel_code, FORCE_PAYMENT_LINK_GATEWAY_BY_CURRENCY)
	if force_gateway_by_currency:
		force_gateway_by_currency = force_gateway_by_currency.strip(";").split(";")

		for option in force_gateway_by_currency:
			gateway, gateway_currency = option.split(":")

			if gateway_currency.lower() == currency.lower():
				logging.info("Forcing payment method: %s" % gateway)
				data["force_gateway_by_link"] = gateway
				break

	if unique_usage_link:
		data["unique_usage_link"] =str(uuid.uuid4())
	if force_payed_link:
		data["force_payed_link"] = force_payed_link

	encryptor = AES.new(MESSAGE_KEY, AES.MODE_CFB)
	encrypted_data = encryptor.encrypt(json.dumps(data).encode("utf-8"))
	encrypted_data = base64.urlsafe_b64encode(encryptor.iv + encrypted_data).decode("utf-8")

	request_params = {
		"namespace": hotel_code,
		"data": encrypted_data
	}
	# else:
	# 	request_params = {
	# 		"identifier": reservation.get("identifier"),
	# 		"price": amount_for_link,
	# 		"namespace": hotel_code,
	# 		"link_creation_ts": get_hotel_datetime(hotel_code).strftime("%Y-%m-%d %H:%M:%S")
	# 	}
	# 	if unique_usage_link:
	# 		request_params["unique_usage_link"] =str(uuid.uuid4())
	# 	if force_payed_link:
	# 		request_params["force_payed_link"] = force_payed_link

	custom_payment_link = get_configuration_property_value(hotel_code, 'Use custom payment link')
	if custom_payment_link:
		link = "%s?%s" % (custom_payment_link, urlencode(request_params))
	else:
		link = booking_domain + "/booking3_tpv?" + urlencode(request_params)

	force_gateway_link = get_configuration_property_value(hotel_code, 'Gateway force by link cobrador')
	if force_gateway_link:
		link += "&force_gateway_by_link=%s" % force_gateway_link

	return link


@timed_cache(hours=24)
def get_config_payment_seeker_configuration(hotel_code):
	hotel_info = get_hotel_by_application_id(hotel_code)
	integration_name = "PAYMENT SEEKER"

	integration_configurations = get_integration_configuration_of_hotel(hotel_info, integration_name)
	gateway_configuration = {}
	if integration_configurations:

		integration_configuration = integration_configurations[0].get('configurations', [])
		for config in integration_configuration:
			key_and_value = config.split(INTEGRATION_CONFIG_MAP_SEPARATOR)
			gateway_configuration[key_and_value[0]] = key_and_value[1]

	return gateway_configuration


def get_amount_to_pay(total_amount, percent=None, total_reservation_nights=None, num_nights_to_pay=None, prices_per_day=None):
	reservation_amount = total_amount
	total_reservation_nights = total_reservation_nights
	num_nigths_to_pay = num_nights_to_pay
	percent_to_pay = percent
	final_amount_to_pay = reservation_amount
	if num_nigths_to_pay:
		if not prices_per_day:
			final_amount_to_pay = float((float(reservation_amount) / int(total_reservation_nights)) * int(num_nigths_to_pay))

		else:
			final_amount_to_pay = 0
			for room in prices_per_day:
				prices = prices_per_day.get(room)
				final_amount_to_pay += sum(prices[:int(num_nigths_to_pay)])

			final_amount_to_pay = min([final_amount_to_pay, float(reservation_amount)])

	if percent_to_pay:
		final_amount_to_pay = float(percent_to_pay) * float(reservation_amount) / 100

	return float("{:.2f}".format(float(final_amount_to_pay)))


def audit_error_web_payment(hotel_code, identifier, amount, error_message, order_id=None):
	all_valid_languages = get_all_languages()
	hotel_languages = get_config_property_value(hotel_code, "Hotel Manager Languages")
	language = all_valid_languages[0]
	if hotel_languages:
		hotel_languages = hotel_languages.split("-")
		for hotel_language in hotel_languages:
			if hotel_language in all_valid_languages:
				language = hotel_language

	if not order_id:
		order_id = identifier

	if not order_id:
		order_id = "ERROR"

	if not error_message:
		error_message = "ERROR IN WEB: %s" % order_id


	web_dictionary = get_web_dictionary(language)

	payment_params = {
		"order": order_id,
		"error": error_message,
		"comments": "",
		"type": PAYMENT_TYPE_IN_WEB,
		"user": web_dictionary.get("T_WEB_USER"),
		"reservation_identifier": identifier,
		"amount": amount,
		"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
		"extra_info": {}
	}

	save_secure_payment_reservation(payment_params, hotel_code)


def get_hotel_datetime(hotel_code):
	default_tz = 'Europe/Madrid'

	time_zone = default_tz

	specific_timezone = get_config_property_value(hotel_code, "Specific timezone")
	if specific_timezone:
		time_zone = specific_timezone

	my_tz = timezone(time_zone)
	if not my_tz:
		my_tz = timezone(default_tz)

	geolocalized_now = datetime.datetime.now(my_tz)

	timezone_adjust = get_config_property_value(hotel_code, "Timezone adjust")

	if timezone_adjust:
		res = re.match("([+-])(\d+)", timezone_adjust)
		if res:
			symbol = res.group(1)
			amount = int(res.group(2))

			if symbol == "+":
				geolocalized_now += datetime.timedelta(hours=amount)
			else:
				geolocalized_now -= datetime.timedelta(hours=amount)

	return geolocalized_now


def get_room_name(all_room,list_rooms):
	names_rooms = []
	for index in list_rooms:
		room = list_rooms.get(index)
		names_rooms.append(all_room.get(str(room.get("id"))))

	return names_rooms



def get_reservation_amount_without_commition(reservation):
	reservation_aux = copy.deepcopy(reservation)
	try:
		agency_info = reservation.get("agency_info")
		commission = reservation.get("agency_info").get("commision")
		neto = (reservation.get("agency_info").get("type") =="neto")
		agency_enable_net_payment = (agency_info and agency_info.get("enable_net_payment") and str(agency_info.get("enable_net_payment")).lower()!="false")
		if commission and neto and agency_enable_net_payment:
			commission = float(commission)
			price = float(reservation.get("price"))
			commition_agency = (price * commission) / 100
			reservation["price"] =round(price - commition_agency, 2)
			prices_days = reservation.get("price_days")
			for price_day in prices_days:
				result_prices = []
				for item in prices_days.get(price_day):
					commition_agency = (float(item) * commission) / 100
					result_prices.append(round(float(item)-commition_agency,2))
				reservation["price_days"][price_day] = result_prices

		return reservation

	except Exception as e:
		return reservation_aux


def get_all_payment_reservation_list(identifier, hotel_code, only_real_amounts_in_reservation):
	filter_params = [('reservation_identifier', '=', identifier)]
	payment_reservation_list = get_all_payments_info_list(filter_params, hotel_code,
															   only_real_payments=only_real_amounts_in_reservation)

	payment_reservation_json_list = []
	for payment in payment_reservation_list:

		if (not only_real_amounts_in_reservation) or is_real_payment_in_reservation(payment) and (not payment.get("error") and float(payment.get("amount", 0)) > 0):

			current_payment_json = {
				"order": payment.get("order", identifier),
				"amount": payment.get("amount"),
				"timestamp": payment.get("timestamp"),
				"type": payment.get("type")
			}
			payment_reservation_json_list.append(current_payment_json)

	return payment_reservation_json_list



def is_real_payment_in_reservation(payment):
	if payment.get("type", "") == "extra":
		#extras payments have not to be integrated or taken in account by default
		return False
	if PAYMENT_TYPE_LINK == payment.get("type", ""):
		# Links are not real payments!!
		return False

	return True



def calculate_cancel_policy_for_period(reservation,hotel_code):
	try:
		hotel = get_hotel_by_application_id(hotel_code)
		rates = get_rates_of_hotel(hotel, "", only_enabled=True) + get_multirates_of_hotel(hotel, "", only_enabled=True)
		current_rate = {x.get("key"): x for x in rates}
		rate_key = reservation.get("rate", "")

		if rate_key.startswith("PACKAGE_"):
			if len(rate_key.split("_@_")) > 2:
				rate_key = rate_key.split("_@_")[2]

		current_rate = current_rate.get(rate_key)

		if not current_rate:
			return ""

		star_date = reservation.get("startDate")
		end_date = reservation.get("endDate")
		condition_rates = get_conditions_of_hotel(hotel)
		condition_rates = {x.get("key"): x for x in condition_rates}
		condition_rates_now = condition_rates.get(current_rate.get("rateCondition"))
		for condition_period in condition_rates_now.get('cancellationPeriods', ''):
			star_date_period = condition_period.split(";")[0]
			end_date_period = condition_period.split(";")[1]
			rate_conditions = condition_period.split(";")[2]
			if star_date_period >= star_date and star_date <= end_date_period:
				continue


			current_rate_period = condition_rates.get(rate_conditions)
			cancellation_policy_period = current_rate_period.get('cancellationPolicy', '')
			return cancellation_policy_period

		return ""
	except Exception as e:
		return ""


def convert_to_numeric_identifier(identifier):
	new_identifier = "".join([i for i in str(identifier) if i.isnumeric()])
	if len(new_identifier) >= 8:
		return new_identifier
	return identifier


def translate_language_code(language):

	lang_codes_dict = {"FRENCH": "fra", "GERMAN": "ger", "ENGLISH": "eng", "SPANISH": "spa", "PORTUGUESE": "pt", "ITALIAN": "ita"}

	return lang_codes_dict.get(language, "pt")


def normalice_txt_for_xml(udata):

	valid_ascii = """! # $ % & ' * + - . / 0 1 2 3 4 5 6 7 8 9 = ?
@ A B C D E F G H I J K L M N O P Q R S T U V W X Y Z ^ _
` a b c d e f g h i j k l m n o p q r s t u v w x y z { | } ~"""
	return ''.join(x for x in unicodedata.normalize('NFKD', udata) if (x in valid_ascii) or (x in ["0","1","2","3","4","5","6","7","8","9", " ", "@", "-", "_", "."]))


def get_rooms_info(reservation, extra_info, hotel, language):
	all_rooms = get_rooms_of_hotel(hotel, language)
	all_rooms = {x.get("key"): x.get("name") for x in all_rooms}
	all_rates = get_rates_of_hotel(hotel, language)
	all_rates = {x.get("key"): x.get("name") for x in all_rates}
	all_board = get_boards_of_hotel(hotel, language)
	all_board = {x.get("key"): x.get("name") for x in all_board}
	rooms_number = int(reservation.get('numRooms'))
	shopping_cart = extra_info.get('shopping_cart', [])
	rooms_info = []
	get_rooms_from_entity = False
	if shopping_cart:
		for room in shopping_cart:
			ages = get_rooms_ages(extra_info, room.get('index_room'))

			room_info = {
				'index_room': room.get('index_room'),
				'adults': room.get('adults', 0),
				'kids': room.get('kids', 0),
				'babies': room.get('babies', 0),
				'rate': all_rates.get(room.get('rate_key')),
				'board': all_board.get(room.get('regimen_key')),
				'name': all_rooms.get(room.get('room_key')),
				'kids_ages': ages
			}
			rooms_info.append(room_info)
		if not rooms_info:
			get_rooms_from_entity = True
	elif not shopping_cart or get_rooms_from_entity:
		for index_room in range(1, rooms_number + 1):
			ages = get_rooms_ages(extra_info, index_room)
			room_info = {
				'index_room': index_room,
				'adults': reservation.get(f'adults{index_room}', 0),
				'kids': reservation.get(f'kids{index_room}', 0),
				'babies': reservation.get(f'babies{index_room}', 0),
				'rate': all_rates.get(reservation.get('rate')),
				'board': all_board.get(reservation.get('regimen')),
				'name': all_rooms.get(reservation.get(f'roomType{index_room}')),
				'kids_ages': ages
			}
			rooms_info.append(room_info)
	return rooms_info


def get_rooms_ages(extra_info, index_room, ):
	all_ages = extra_info.get('agesByRoom', {}).get(f'kidsAges{index_room}', {})
	return all_ages.get('kids', []) + all_ages.get('babies', [])


def is_real_payment(payment):

	order = payment.get("order", "")
	if isinstance(order, str):
		order = order.upper()

	if payment and (not payment.get("error")) and ( "ERROR" not in order) and payment.get("type") in REAL_PAYMENTS:
		return True

	return False

def add_web_payments_to_total_payed(reservation, hotel_code, total_payed):
	payments_in_reservation = list(datastore_communicator.get_using_entity_and_params('PaymentsReservation', [
		('reservation_identifier', '=', reservation.get('identifier'))], hotel_code=hotel_code))

	if payments_in_reservation:
		for payment in payments_in_reservation:
			if is_real_payment(payment):
				total_payed += float(payment.get("amount"))
	return total_payed


#-------------------------------------------------------------------------------------------
#OWN PARATY FORMS GATEWAYS (payment BY API)

def encryptMessageDES3_by_API(message, password):
	iv = b"\0\0\0\0\0\0\0\0"
	des3 = DES3.new(password, DES3.MODE_CBC, iv)
	message = des3.encrypt(message.encode())
	return message

def send_cobrador_response_to_client_server(gateway_configuration, sid, amount, payment_order_id, extra_params={}):
	# hote-webs -> cobrador_merchant_handler.py -> post -> process_response_from_cobrador

	payment_order_id_str = str(payment_order_id)
	logging.info("[BY API]send_cobrador_response_to_client_server. sid: %s payment_order_id: %s amount: %s", sid, payment_order_id_str, amount)

	merchant_url = gateway_configuration.get("merchant_url")
	merchant_url = add_sid_to_url(merchant_url, sid)

	# extraemos el secret de del xml-> config
	secret_key_sha256 = gateway_configuration.get("secret_key_sha256") or gateway_configuration.get("inner_secret_key")

	# semilla DES3
	secret_key_sha256_b64 = base64.b64decode(secret_key_sha256)
	key_for_signature = encrypt_message_DES3(payment_order_id_str, secret_key_sha256_b64)

	# Parametros a enviar encriptados
	cobrador_parameters = {"payment_order_id": payment_order_id_str,
						   "amount": amount,
						   "response_code": "OK",
						   "payment_by_api_info": extra_params #maybe you want to encrypt extra_data to get it in process_response
						   }


	cobrador_parameters_SHA256 = json.dumps(cobrador_parameters)
	cobrador_parameters_SHA256 = cobrador_parameters_SHA256.encode("utf-8")
	cobrador_parameters_SHA256 = base64.b64encode(cobrador_parameters_SHA256)

	encrypted_parameters = hmac.new(key_for_signature, cobrador_parameters_SHA256, digestmod=hashlib.sha256).digest()
	cobrador_signature = base64.b64encode(encrypted_parameters)

	# Y se devuelve a hotel webs, la respuesta encriptda y la firma
	payload = {"cobrador_parameters": cobrador_parameters_SHA256,
			   "cobrador_signature": cobrador_signature}

	logging.info("send_cobrador_response_to_hotel_webs. Sending merchant_url: %s, payload: %s", merchant_url, payload)
	response = requests.post(merchant_url, data=payload)
	if response.status_code != 200:
		logging.error("[BY API]COBRADOR: Payment has not be sent to Client Server! response.status_code: %s", response.status_code)
		return False
	logging.info("[BY API]COBRADOR: Payment has been sent to Cliente Server CORRECTLY response.status_code: %s", response.status_code)
	return True


def decrypt_paraty_own_response_from_api(hotel_code, gateway_type, cobrador_parameters, cobrador_signature, just_return_decrypted_data=False):

	gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

	secret_key_sha256 = gateway_configuration.get("secret_key_sha256") or gateway_configuration.get('inner_secret_key')
	secret_key_sha256_b64 = base64.b64decode(secret_key_sha256)

	# descodificamos b64 y cargamos en json
	decodecParameters = base64.urlsafe_b64decode(str(cobrador_parameters))
	cobrador_parameters_json = json.loads(decodecParameters)
	if just_return_decrypted_data:
		return cobrador_parameters_json
	# sacamos el identificador y el amount
	payment_order_id = cobrador_parameters_json.get("payment_order_id")
	amount = cobrador_parameters_json.get("amount")

	logging.info("[BY API] Real amount paid received from cobrador: %s", amount)
	response_code = cobrador_parameters_json.get("response_code")
	logging.info("[BY API] Cobrador. cobrador_parameters_json received: %s", cobrador_parameters_json)

	# generamos nuestra firma a partir del cobrador_parameters
	# semilla DES3
	key_for_signature = encryptMessageDES3_by_API(payment_order_id, secret_key_sha256_b64)

	#TODO: posibly we have to do this??: cobrador_parameters.strip().encode()
	signature = hmac.new(key_for_signature, cobrador_parameters.strip().encode(), hashlib.sha256).digest()
	# signature_generated = base64.urlsafe_b64encode(signature)
	signature_generated = base64.b64encode(signature).decode()

	# y comparamos!
	if (not signature_generated.replace('+', ' ') == cobrador_signature.replace('+', ' ')) or response_code != "OK":
		logging.warning("Cobrador: Bad response. Signatures are not equals or response is not OK. response_code: %s",
						response_code)
		return {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"GATEWAY_ORDER_ID": payment_order_id,
			"GATEWAY_PAID_AMOUNT": amount,
			"GATEWAY_EXTRA_INFO": {
				"ds_MerchantIdentifier": payment_order_id
			}
		}
	logging.info("[BY API] Cobrador: Good response. Signatures are equals!")


	return {
		"CODE": "OK",
		"GATEWAY_ORDER_ID": payment_order_id,
		"GATEWAY_PAID_AMOUNT": amount,
		"GATEWAY_EXTRA_INFO": {
			"payment_by_api_info": cobrador_parameters_json.get("payment_by_api_info", {}),
			"ds_MerchantIdentifier": payment_order_id
		}
	}


def get_reservations_total_price(extra_data):
	room_info = extra_data.get("room_info", [])
	total_price = 0.0
	for room in room_info:
		total_price += room.get("amount", 0)
	return total_price


def is_test_hotel(hotel_code):
	return "test" in hotel_code


def get_ultimate_cobrador_path(hotel_info, fail_over=False):
	retr

	if fail_over:
		logging.info("Returning fail_over url")
		return COBRADOR_FAILOVER_SERVER_PATH

	url_to_use = COBRADOR_SERVER_PATH

	use_external_payment_seeker = get_hotel_advance_config_item(hotel_info, USE_ALTERNATIVE_PAYMENT_SEEKER)
	if use_external_payment_seeker:
		url_to_use = use_external_payment_seeker[0].get('value', '')

	return url_to_use


def get_reservation_metadata(hotel_code, identifier):
	hotel_code = requests.args.get('hotel_code')
	identifier = requests.args.get('identifier')

	data = {
		"hotel_code": hotel_code,
		"identifier": identifier
	}

	response = requests.post(METADATA_URL, data=data)
	return response.json()


def get_request_body():
	logging.info("Headers received in get_request_body: %s", request.headers)

	result = request.get_data()
	if result:
		logging.info("request data obtained correctly from request.get_data(): %s", result)
		return result

	result = request.data
	if result:
		logging.info("request data obtained correctly from request.data: %s", result)
		return result

	result = ("&".join(["%s=%s" % (x, y) for x, y in request.form.items()])).encode("utf-8")
	if result:
		logging.info("request data obtained correctly from  request.form.items(): %s", result)
		return result


def hotel_has_avalon(hotel_code):
	search_params = [('downloadBooking', '=', True), ('name', '=', 'avalon')]
	all_integration_configuration = get_all_integration_configuration_processed(hotel_code, search_params=search_params)

	return True if all_integration_configuration.get('url') else False


def get_manual_payment_types(hotel_code):
	search_params = [('name', '=', MANUAL_PAYMENT_TYPES)]
	return get_all_integration_configuration_processed(hotel_code, search_params=search_params)


def get_all_integration_configuration_processed(hotel_code, search_params=[]):
	all_integration_configuration = list(datastore_communicator.get_using_entity_and_params('IntegrationConfiguration', search_params, hotel_code=hotel_code))

	if not isinstance(all_integration_configuration, list):
		all_integration_configuration = [all_integration_configuration]

	myConfigs = {}
	for entity in all_integration_configuration:
		for config in entity.get('configurations', []):
			key, value = config.split(RATE_MAP_SEPARATOR)
			myConfigs[key] = value
	return myConfigs


def fix_payment_order_id(payment_order_id):
	excess_chars = len(payment_order_id) - 8
	if excess_chars > 0:
		payment_order_id = payment_order_id[excess_chars:]
	return payment_order_id


def is_test_rate(body):
	return any("test" in room.get("rate_name", "").lower() or "test" in room.get("rate_identifier", "").lower() 
		for room in body.get("room_info", []))


def cancel_reservation_in_manager(id, hotel_code):
	reservation_key = id_to_entity_key(hotel_code, id)
	requestString = f"/book?applicationId={hotel_code}&reservationKey={reservation_key}&forceCancellation=true"
	manager_url = "https://admin-hotel.appspot.com"
	response = requests.delete(manager_url + requestString, data={'identifier': reservation_key}, auth=(USER, PASSWORD))
	status_response = response.status_code
	logging.info("Response to cancellation from manager: %s" % status_response)
	return status_response == 200


def ignore_error_email(error_returned):
	if not error_returned:
		return False
	for exception in IGNORE_EXCEPTIONS:
		if exception in error_returned.lower():
			return True
	return False


def retry_process_response(hotel_code, gateway_type_used):
	from paraty.pages.cobrador.cobrador import get_integration_cobrador_name
	if not hotel_code:
		return False
	integration_name = get_integration_cobrador_name(hotel_code)
	for gateway in integration_name.split(';'):
		if gateway.lower() != gateway_type_used:
			return gateway
	return False



# def avoid_exception(error, hotel_code):
# 	avoid_exceptions = list(datastore_communicator.get_using_entity_and_params('AvoidException', search_params=[], hotel_code='payment-seeker:'))
# 	for exception in avoid_exceptions:
# 		exception_name = exception.get("ExceptionName")
# 		hotels_to_avoid = exception.get("HotelsToAvoid")
# 		if exception_name and exception_name in str(error):
# 			if hotels_to_avoid and hotels_to_avoid != hotel_code:
# 				return False
# 			else:
# 				logging.info(f"Avoiding exception: {error} due to rule {exception_name} in entity AvoidException")
# 				return True
# 		return False


def calculate_total_nights(start_date, end_date, format="%d/%m/%Y"):
	import datetime
	start_date = datetime.datetime.strptime(start_date, format)
	end_date = datetime.datetime.strptime(end_date, format)
	number_of_nights = (end_date - start_date).days
	return number_of_nights


def get_additional_services(hotel, additional_services, language):
	supplements = get_additional_services_name_price_key(additional_services)
	if supplements:
		services = get_additional_services_of_hotel(hotel, language)
		if services:
			for supplement in supplements:
				for service in services:
					if supplement.get('key') == service.get('key'):
						supplement['name'] = service.get('name', supplement['name'])
						break
	return supplements


def get_reservations_currency(hotel_code, extra_info):
	default_currency = get_configuration_property_value(hotel_code, "Base Price Currency") or "EUR"
	if get_configuration_property_value(hotel_code, "special conversion for gateway"):
		return get_currency_symbol(extra_info.get("currency", default_currency)), get_currency_symbol(extra_info.get('currency_conversion_for_gateway', {}).get('new_currency') or default_currency)
	return get_currency_symbol(extra_info.get("currency", default_currency)), get_currency_symbol(extra_info.get("currency", default_currency))


def get_additional_services_name_price_key(additional_services):
	result = []
	if additional_services:
		try:
			services = additional_services.split(";")
			for service in services:
				if service.strip():
					slices = service.split(" - ")

					key = slices[0].strip()
					name = None
					price = 0.0

					for slice in slices:
						if slice.startswith("name:"):
							name = slice.split(":")[1].strip()
						elif slice.startswith("price:"):
							price = float(slice.split(":")[1].strip())

					if name:
						result.append({"key": key, "name": name, "price": price})
		except Exception as e:
			logging.error(f"Error processing additional service for email: {e}")
	return result


def get_all_languages_configured(hotel_code, configuration):
	booking_3_gateways_text = json.loads(configuration.get('booking_3_gateways_text', '{}'))
	languages_configured = get_hotel_advance_config_item(hotel_code, "Hotel Manager Languages") or get_hotel_advance_config_item(hotel_code, "language")
	if languages_configured:
		languages_configured_list = languages_configured[0].get('value').split('-')
		return {x:booking_3_gateways_text.get(x, '') for x in languages_configured_list}
	return {}


def get_booking_3_gateways_text(body):
	booking_3_gateway_text = {
		key.replace("_booking_3_gateway_text", ""): value
		for key, value in body.items()
		if key.endswith("_booking_3_gateway_text")
	}
	return json.dumps(booking_3_gateway_text)


def complete_payment(params):
	amount = float(params.get("amount", 0))/100
	reservations_total_price = params.get("reservations_total_price")
	logging.info(f"Amount to be paid: {amount} - Reservations total price: {reservations_total_price}")
	return True if abs(amount - reservations_total_price) <= 1 else False


def get_lock_key(hotel_code, function_name=""):
	date_ = date.today()
	if function_name:
		return f"{hotel_code}_{function_name}_{date_.year}_{date_.month}_{date_.day}"
	else:
		return f"{hotel_code}_{date_.year}_{date_.month}_{date_.day}"


def execution_locked(hotel_code, lock_key):
	r = build_redis_client_hotel(hotel_code)
	if r.exists(lock_key):
		return True
	else:
		return False


def lock_execution(hotel_code, lock_key):
	r = build_redis_client_hotel(hotel_code)
	r.set(lock_key, "locked", ex=3600, nx=True)
	return True


def unlock_execution(hotel_code, lock_key):
	r = build_redis_client_hotel(hotel_code)
	r.delete(lock_key)
	return True

def send_email_locked_execution(function, hotel_code, lock_key):
	subject = f"{function} {hotel_code} is locked"
	logging.error(f"{function} {hotel_code} is locked")
	message = f"{function} {hotel_code} is locked by {lock_key}"
	sendEmail(BACKEND_GOOGLE_GROUP, subject, "", message)

def process_pending_refund(hotel_code, payment_params):
	if payment_params.get("extra_params", {}).get("create_task"):
		checkout_id = payment_params.get("extra_params").get("checkout_id")
		order_id = payment_params.get("order")
		identifier = payment_params.get("reservation_identifier")
		payload = {
			"merchant_url": f"https://payment-seeker.appspot.com/refund/webhook?hotel_code={hotel_code}&identifier={identifier}&checkout_id={checkout_id}&order_id={order_id}&force_gateway=W2M"
		}

		data = {
			"task_id": str(uuid.uuid4()),
			"data": base64.urlsafe_b64encode(json.dumps(payload).encode("utf-8")).decode("utf-8")
		}

		try:
			queue_utils.create_task('execute_fallback_task', json.dumps(data),
									queue_name=COBRADOR_QUEUE,
									task_name=f'fallback_task__{order_id}_{hotel_code}',
									in_seconds=600)
		except Exception as e:
			logging.warning(
				f"Error controlled. Payment already differed: {order_id}_{hotel_code}")
			logging.warning("%s" % e)

def send_payment_confirmation_emails(hotel_code, identifier, comment_email, send_customer_email, send_hotel_email, send_modification_email,
									 payed_in_this_transaction, order, manual_payment, is_refund, avoid_sending_payment, translations,
									 reservation=None):
	from paraty.pages.cobrador.cobrador import send_modification_emails, send_payment_confirmation_email_to_customer, send_reservation_to_adapter
	if not reservation:
		reservation = datastore_communicator.get_using_entity_and_params("Reservation", search_params=[("identifier", "=", identifier)], hotel_code=hotel_code)
		if reservation:
			reservation = reservation[0]
	if send_customer_email:
		logging.info("[%s]Sending email to customer", order)
		send_payment_confirmation_email_to_customer(hotel_code, reservation, comment_email,
													payed_in_this_transaction, manual_payment=manual_payment, addressee="customer", is_refund=is_refund, payment_Id=order)

	if send_hotel_email:
		logging.info("[%s]Sending email to hotel", order)
		send_payment_confirmation_email_to_customer(hotel_code, reservation, comment_email,
													payed_in_this_transaction, manual_payment=manual_payment, addressee="hotel", is_refund=is_refund, payment_Id=order)

	if send_modification_email:
		logging.info("[%s]Sending modification email", order)
		send_modification_emails(hotel_code, reservation)

	try:
		# Note that this is necesary to prestige
		send_reservation_to_adapter(hotel_code, reservation.get("identifier"),
									payed_in_this_transaction=payed_in_this_transaction, payment_order_id=order,
									avoid_sending_payment=avoid_sending_payment)
	except Exception as e:
		logging.error("Imposible to send transaction to adapter: %s, %s" %(reservation.get("identifier"), hotel_code))
		logging.error("ERROR: %s", e)
		response = {
			"status":"OK",
			"message": translations.get("T_RESERVATION_NOT_SENDED_TO_ADAPTER")
		}
		return response
	# Just a flag
	return False
		

def save_secure_payment_reservation(payment_params, hotel_code, id_payment=None):

	#if id_payment, its a modification.... Normaly internal comments in a payment


	order = payment_params.get("order")
	kind_payment = "PaymentsReservation"
	if not is_real_payment(payment_params):
		kind_payment = "PaymentsReservationExtraInfo"

	if payment_params.get("real_kind") and id_payment:
		kind_payment = payment_params.get("real_kind")

	if kind_payment == "PaymentsReservation":
		#if real, check if duplicated.

		is_refund = payment_params.get("type", "") in REAL_REFUNDS

		if (not id_payment) and not is_refund:
			#dont check if its a modification os a comment
			payments_possible_repeated = list(
				datastore_communicator.get_using_entity_and_params('PaymentsReservation', [("order", "=", order)],
																   hotel_code=hotel_code))

			if payments_possible_repeated:

				false_positive = False
				for payment_repeated in payments_possible_repeated:
					if not is_real_payment(payment_repeated):
						false_positive = True
					else:
						false_positive = False
						break

				if not false_positive:
					subject = "TEST no worries [MEGA URGENT] POSSIBLE REPEATED PAYMENT CHECK URGENT:  %s" % hotel_code
					message_error = "We save the payment, BUT check urgent this ORDER %s in this hotel %s" % (order, hotel_code)
					logging.error("%s %s", subject, message_error)
					notify_error_by_email(subject, message_error)

	payment_params['order'] = truncate_string_by_bytes(payment_params.get('order'))
	payment_params['error'] = truncate_string_by_bytes(payment_params.get('error'))

	payment_id = datastore_communicator.save_to_datastore(kind_payment, id_payment, payment_params,
														  hotel_code=hotel_code)


	return payment_id

def get_all_payments_info_list(filter_params, hotel_code, only_real_payments=True):


	payments_list_by_cobrador = list(
		datastore_communicator.get_using_entity_and_params('PaymentsReservation', filter_params,
														   hotel_code=hotel_code))

	for payment in payments_list_by_cobrador:
		payment["real_kind"] = "PaymentsReservation"

	if not only_real_payments:
		payments_list_extra_info = list(
			datastore_communicator.get_using_entity_and_params('PaymentsReservationExtraInfo', filter_params,
														   hotel_code=hotel_code))

		for payment in payments_list_extra_info:
			payment["real_kind"] = "PaymentsReservationExtraInfo"

	else:
		payments_list_extra_info = []

	combined_payments = payments_list_by_cobrador + payments_list_extra_info
	combined_payments.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

	return combined_payments

def truncate_string_by_bytes(string):
	if string and isinstance(string, str):
		while len(string.encode('utf-8')) > 1500:
			string = string[:-1]
	return string


def save_reservation_and_flush_cache(reservation, hotel_code, reservation_id=None):
	reservation_updated_id = None
	try:
		reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation,
																		  hotel_code=hotel_code)
		refresh_entity_timestamps('Reservation', hotel_code)
	except Exception as e:
		logging.error("error saving reservation in %s", hotel_code)
		logging.error(e)

	return reservation_updated_id


def filter_reservation_metadata_by_hotel_code(reservation_metadata, hotel_code):
	hotel_code_filter = hotel_code.split('-')[0]
	reservation_metadata_filtered = [x for x in reservation_metadata if hotel_code_filter in x.get("hotel_code")]
	return reservation_metadata_filtered if reservation_metadata_filtered else reservation_metadata

def check_cc_expire_date(start_date, cc_expire_date):
	try:
		start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d")

		cc_month, cc_year = cc_expire_date.split('/')

		cc_year_full = int("20" + cc_year)  # Convertir YY a 20YY
		cc_month_int = int(cc_month)

		# Get the last day oh the expiration month
		if cc_month_int == 12:
			last_day = 31
		else:
			# We calculate the last day of the month by obtaining the first day of the following month and subtracting 1 day.
			next_month = datetime.datetime(cc_year_full, cc_month_int + 1, 1)
			last_day_date = next_month - datetime.timedelta(days=1)
			last_day = last_day_date.day

		cc_expire_date_obj = datetime.datetime(cc_year_full, cc_month_int, last_day, 23, 59, 59)

		if cc_expire_date_obj < start_date_obj:
			return True
		return False
	except Exception as e:
		logging.error(f"Error al comparar fechas de expiración: {e}")
		return False

def send_cc_expire_date_notification(hotel_code, session_from_hotel, payment_order_id, cc_expire_date):
	hotel = get_hotel_by_application_id(hotel_code)
	payment_seeker_config = get_config_payment_seeker_configuration(hotel_code)
	language = session_from_hotel.get("reservation-dict", {}).get("langauge", SPANISH)
	customer_name = session_from_hotel.get("reservation-dict", {}).get("name")
	translations = language_utils.get_web_dictionary(language)

	# Obtain the first day of the expiration month
	try:
		cc_month, cc_year = cc_expire_date.split('/')
		cc_year_full = int("20" + cc_year)  # Convertir YY a 20YY
		cc_month_int = int(cc_month)

		# Create date for the first day of the expiration month
		first_day_of_expiry_month = datetime.datetime(cc_year_full, cc_month_int, 1)

		# Format date as string (if necessary)
		first_day_formatted = first_day_of_expiry_month.strftime("%Y-%m-%d")

		logging.info(f"First day of expiration month: {first_day_formatted}")
	except Exception as e:
		logging.error(f"Error in calculating the first day of the expiration month: {e}")
		first_day_formatted = ""

	#TODO: descomentar la siguiente linea
	# customer_email = session_from_hotel.get("reservation-dict", {}).get("email")
	customer_email = "<EMAIL>"

	logo_type = ""
	filter_logo = "survey"
	if payment_seeker_config and payment_seeker_config.get("logo_name"):
		filter_logo = payment_seeker_config.get("logo_name")
	logos = get_hotel_logotypes(hotel_code, filter_logo)
	if logos:
		logo_type = logos[0]

	custom_section_translations = get_web_section(hotel, payment_seeker_config.get("notifications email custom text section"), language, set_languages=True)
	if custom_section_translations and custom_section_translations.get("T_MESSAGE_CC_EXPIRE_DATE"):
		content_message = custom_section_translations["T_MESSAGE_CC_EXPIRE_DATE"]
	else:
		content_message = translations.get("T_MESSAGE_CC_EXPIRE_DATE")

	content_message = content_message.replace("@@CHARGE_EXPIRE_DATE@@", first_day_formatted)

	if custom_section_translations and custom_section_translations.get("T_TITLE_CC_EXPIRE_DATE"):
		content_title = custom_section_translations["T_TITLE_CC_EXPIRE_DATE"]
	else:
		content_title = translations.get("T_TITLE_CC_EXPIRE_DATE")

	if customer_name:
		content_title = customer_name + " " + content_title

	if custom_section_translations and custom_section_translations.get("T_CUSTOMER_SUPPORT_TEXT"):
		customer_support_text = custom_section_translations.get("T_CUSTOMER_SUPPORT_TEXT")
	else:
		customer_support_text = translations.get("T_costumer_support_v2")

	hotel_name = payment_seeker_config.get("name hotel corpo")
	if not hotel_name:
		hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
	greeting_text = translations.get("T_greetings").replace('@@HOTEL@@', hotel_name)


	context = {
		"logo_type": logo_type,
		"phone": payment_seeker_config.get("telephone", ""),
		"email": payment_seeker_config.get("email", ""),
		"cc_expire_message": content_message,
		"cc_expire_title": content_title,
		"customer_support_text": customer_support_text,
		"greeting_text": greeting_text,
		"expiry_date": cc_expire_date,
		"first_day_of_expiry": first_day_formatted
	}

	subject_email = translations.get("T_SUBJECT_CC_EXPIRE_DATE")
	if custom_section_translations and custom_section_translations.get("T_SUBJECT_CC_EXPIRE_DATE"):
		subject_email = custom_section_translations.get("T_SUBJECT_CC_EXPIRE_DATE")

	context.update(translations)
	html_content = build_template('pages/cobrador/emails/email_cc_expire_date_notification.html', context, force_language="SPANISH")

	title = subject_email + " " + payment_order_id

	email_sender_config = get_configuration_property_value(hotel_code, EMAIL_SENDER_CONFIG)
	email_sender = build_email_sender_from_hotel_webs_config(email_sender_config, backup_sender="<EMAIL>")

	sendEmail(customer_email, title, "", html_content, sender=email_sender,backup_sender="<EMAIL>")

def scheduled_payments_simulator(hotel_code, reservation, rates_map=None, rooms_map=None):
	"""
	Simulates future scheduled payments for a reservation as the check-in date approaches.
	"""
	from paraty.pages.cobrador.cobrador import get_rules_by_type
	from paraty.pages.cobrador.cobrador import get_rules_to_apply_for_reservation
	from paraty.pages.cobrador.cobrador import _get_amount_to_be_payed
	from paraty.pages.cobrador.cobrador import filter_rules_acumulate_rules
	import copy

	logging.info("[SIMULATOR] Starting payment simulation for reservation %s in hotel %s",
				 reservation.get("identifier"), hotel_code)

	# Get programmatically payment rules
	all_rules = get_rules_by_type(hotel_code, "programmatically")

	# Create a map of rules by ID for quick access
	rules_map = {str(rule.id): rule for rule in all_rules}

	# If maps are not provided, we get them
	if rates_map is None or rooms_map is None:
		hotel = get_hotel_by_application_id(hotel_code)

		if rates_map is None:
			all_rates = get_rates_of_hotel(hotel, include_removed=True)
			rates_map = {x.get("key"): x for x in all_rates}

		if rooms_map is None:
			all_rooms = get_rooms_of_hotel(hotel, include_removed=True)
			rooms_map = {x.get("key"): x for x in all_rooms}

	# Reservation start date
	start_date = datetime.datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")

	# Current real date
	real_current_datetime = get_hotel_datetime(hotel_code).replace(tzinfo=None)
	real_current_datetime = real_current_datetime.replace(hour=0, minute=0, second=0, microsecond=0)

	# Create a copy of the reservation to simulate payments
	simulation_reservation = copy.deepcopy(reservation)

	# Get total price and set initial state
	try:
		sim_extra_info = json.loads(simulation_reservation.get("extraInfo", "{}"))
	except:
		sim_extra_info = {}

	# Initialize tracking lists
	executed_rules = sim_extra_info.get("programmatically_payment_cobrador_ids", []) or []
	excluded_rules = sim_extra_info.get("exclude_programmatically_payment_cobrador_ids", []) or []
	future_payments = []

	# Sort rules by days before check-in (from highest to lowest)
	sorted_rules = sorted(all_rules, key=lambda r: int(r.get("conditions_days", 0)), reverse=True)

	# Find the rule with the most days in advance
	max_days_before = 0
	for rule in sorted_rules:
		if str(rule.id) not in excluded_rules:
			days_before = int(rule.get("conditions_days", 0))
			if days_before > 0 and days_before > max_days_before:
				max_days_before = days_before

	# Simulate time progression from the earliest date to the check-in date
	earliest_date = start_date - datetime.timedelta(days=max_days_before)
	if earliest_date < real_current_datetime:
		earliest_date = real_current_datetime

	# Simulate day by day from the earliest date to the check-in date
	simulated_date = earliest_date
	while simulated_date < start_date:
		# For each simulated day, check which rules would apply
		rules_applied_today = []  # Rules applied on this day

		for rule in sorted_rules:
			rule_id = str(rule.id)

			# Skip rules already executed or excluded
			if rule_id in executed_rules or rule_id in excluded_rules:
				continue

			# Calculate the execution date of the rule
			conditions_days = int(rule.get("conditions_days", 0))
			execution_date = start_date - datetime.timedelta(days=conditions_days)

			# If the execution date matches the current simulated date
			if execution_date.date() == simulated_date.date():
				# Check if the rule applies to this reservation
				# Important: Pass all rules and then filter only the current one
				all_applicable_rules = get_rules_to_apply_for_reservation(simulation_reservation, all_rules, rates_map, rooms_map, hotel_code, skip_conditions_days=True)

				# Filter to keep only the current rule
				filtered_rules = [r for r in all_applicable_rules if str(r.id) == rule_id]

				if filtered_rules:
					# Calculate the amount to be paid
					amount = _get_amount_to_be_payed(simulation_reservation, rule, hotel_code, ignore_applied_rule=True)

					if amount and amount > 0:
						# Get reservation currency
						currency = get_reservation_currency(simulation_reservation)

						future_payments.append({
							'amount': float(amount),
							'date': execution_date.strftime("%Y-%m-%d"),
							'rule_id': rule_id,
							'rule_description': rule.get("description", ""),
							'conditions_days': conditions_days,
							'currency': currency
						})

						# Update the simulation to reflect this payment
						if sim_extra_info.get("programmatically_payment_cobrador_ids"):
							sim_extra_info["programmatically_payment_cobrador_ids"].append(rule_id)
						else:
							sim_extra_info["programmatically_payment_cobrador_ids"] = [rule_id]

						# Update the total paid in the simulation
						if sim_extra_info.get("payed"):
							sim_extra_info["payed"] = float(sim_extra_info["payed"]) + amount
						else:
							sim_extra_info["payed"] = amount

						# Update the simulated reservation
						simulation_reservation["extraInfo"] = json.dumps(sim_extra_info)

						# Mark the rule as executed
						executed_rules.append(rule_id)
						rules_applied_today.append(rule_id)

		# After processing all rules for the day, look for accumulated rules
		# Use filter_rules_acumulate_rules directly to find accumulated rules
		if rules_applied_today:
			# Update the simulated reservation to include rules applied today
			acumulate_rules = [r for r in all_rules if str(r.id) not in executed_rules and str(r.id) not in excluded_rules]

			# Apply the accumulated rules filter directly
			acumulate_rules = filter_rules_acumulate_rules(acumulate_rules, simulation_reservation)

			# Process the accumulated rules found
			for acumulate_rule in acumulate_rules:
				acumulate_rule_id = str(acumulate_rule.id)

				# Check if the accumulated rule applies to this reservation
				filtered_acumulate_rules = get_rules_to_apply_for_reservation(
					simulation_reservation, [acumulate_rule], rates_map, rooms_map, hotel_code, skip_conditions_days=True
				)

				if filtered_acumulate_rules:
					# Calculate the amount to pay for the accumulated rule
					acumulate_amount = _get_amount_to_be_payed(
						simulation_reservation, acumulate_rule, hotel_code, ignore_applied_rule=True
					)

					if acumulate_amount and acumulate_amount > 0:
						# Get reservation currency
						currency = get_reservation_currency(simulation_reservation)

						# Add the future payment for the accumulated rule
						future_payments.append({
							'amount': float(acumulate_amount),
							'date': simulated_date.strftime("%Y-%m-%d"),  # Current simulated date
							'rule_id': acumulate_rule_id,
							'rule_description': acumulate_rule.get("description", ""),
							'conditions_days': 0,  # Accumulated rules don't have their own condition days
							'currency': currency,
							'is_accumulated': True  # Mark as accumulated rule
						})

						# Update the simulation to reflect this accumulated payment
						sim_extra_info["programmatically_payment_cobrador_ids"].append(acumulate_rule_id)
						sim_extra_info["payed"] = float(sim_extra_info["payed"]) + acumulate_amount

						# Update the simulated reservation
						simulation_reservation["extraInfo"] = json.dumps(sim_extra_info)

						# Mark the accumulated rule as executed
						executed_rules.append(acumulate_rule_id)

		# Move to the next day
		simulated_date += datetime.timedelta(days=1)

	# Sort by execution date
	future_payments.sort(key=lambda x: x['date'])

	logging.info("[SIMULATOR] Found %d future payments for reservation %s",
				 len(future_payments), reservation.get("identifier"))

	return future_payments


def initiate_cobrador_response(gateway_name="COBRADOR"):
	return {
		'CODE': GATEWAY_ERROR_CODE_RETURNED,
		'PAYMENT_GATEWAY_NAME': gateway_name,
		'no_redirect': True,
		'GATEWAY_EXTRA_INFO': {},
		'payment_gateway': gateway_name
	}

def get_pending_refunds_by_order_id(hotel_code, order_id):
	payment = list(datastore_communicator.get_using_entity_and_params('PaymentsReservationExtraInfo',
		[('order', '=', order_id)], hotel_code=hotel_code))
	# pending_refunds = [x for x in payment if x.get('type') == 'failed_devolution']
	pending_refunds = [x for x in payment if x.get('type') == 'refund pending']
	if not pending_refunds:
		return []
	pending_refunds.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
	return pending_refunds


def change_refund_status(hotel_code, identifier, order_id, status):
	CONFIRMED_REFUND_COMMENT = "Refund confirmed"
	FAILED_REFUND_COMMENT = "Refund failed"
	# status can be 'devolution' or 'failed_devolution'
	pending_refunds = get_pending_refunds_by_order_id(hotel_code, order_id)

	if pending_refunds:
		payment = pending_refunds[0]
		payment_id = payment.id
		payment['type'] = status
		payment['comments'] = CONFIRMED_REFUND_COMMENT if status == 'devolution' else FAILED_REFUND_COMMENT

		if status == 'failed_devolution':
			# save the fail devolution
			datastore_communicator.save_to_datastore("PaymentsReservationExtraInfo", payment_id, payment,
			                                         hotel_code=hotel_code)

		if status == 'devolution':

			# REMOVE pending devolution when we had the devolution confirmation
			datastore_communicator.delete_entity("PaymentsReservationExtraInfo", payment_id, hotel_code=hotel_code)
			# add the devolution confirmation
			save_secure_payment_reservation(payment, hotel_code)

			reservation = list(datastore_communicator.get_using_entity_and_params('Reservation',
			                                                                      [('identifier', '=', identifier)],
			                                                                      hotel_code=hotel_code))

			if reservation:
				reservation = reservation[0]
				extra_info = json.loads(reservation.get("extraInfo", "{}"))
				payed_by_cobrador = extra_info.get('payed_by_cobrador', 0) or 0
				new_payment_amount = + payment.get('amount', 0)
				if payed_by_cobrador:
					extra_info['payed_by_cobrador'] = payed_by_cobrador + payment.get('amount', 0)
				else:
					extra_info['payed_by_cobrador'] = payment.get('amount', 0)

				reservation["extraInfo"] = json.dumps(extra_info)
				reservation_id = int(reservation.key.id)
				datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation,
				                                         hotel_code=hotel_code)

		return True

	return False


def save_pending_refund(hotel_code, refund_response, order_to_refund, identifier, gateway):
	payload = {
		"order_to_refund": order_to_refund,
		"gateway": gateway,

	}

	properties = {
		"identifier": identifier,
		"external_identifier": refund_response.get('paymentPspReference'),
		# pspReference is the same for both payment and refund
		"hotel_code": hotel_code,
		"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
		"refunded": False,
		"extraInfo": json.dumps(payload)
	}


	save_to_datastore("PendingRefunds", None, properties, hotel_code="payment-seeker:")

def get_pending_refunds(search_params):
	return list(datastore_communicator.get_using_entity_and_params('PendingRefunds', search_params, hotel_code="payment-seeker:"))