import json
import logging
from datetime import datetime, timedelta

from paraty.pages.cobrador.cobrador_utils import get_all_gateways, get_integration_name, \
	get_config_property_value_without_cache

from paraty.pages.cobrador.cobrador_constants import USE_PAYMENT_GATEWAY_BY_COBRADOR

from paraty_commons_3.language_utils import LANGUAGE_CODES


def get_payment_gateways_to_use(hotel_code, rules_to_apply, has_bizum=False):
	gateways = []
	rules_applied = ""
	rules_to_apply = sorted(rules_to_apply, key=lambda l: l.get("restriction_priority"),reverse=True)
	#ONLY RETURN DIFFERENT GATEWAYS BUT WITH SAME CONDITIONS
	best_amount = rules_to_apply[0].get("amount")
	best_type_amount = rules_to_apply[0].get("type_amount")
	best_type_rule = rules_to_apply[0].get("type_rule")

	has_optional_tokenizator = True if list(filter(lambda x: x.get('type_rule') == "create_token" and x.get("optional_payment"), rules_to_apply)) else False
	for rule_to_apply in rules_to_apply:

		is_same_amount = best_amount == rule_to_apply.get("amount") and best_type_amount == rule_to_apply.get("type_amount") \
				and best_type_rule == rule_to_apply.get("type_rule") and  rule_to_apply.get("type_rule") != "create_token"

		#we have to inform Hotel Webs that this rate has to be tokenized (amount 0)
		is_tokenizator = rule_to_apply.get("type_rule") == "create_token"
		is_optional_tokenizator = rule_to_apply.get("optional_payment")
		if is_same_amount or is_tokenizator or has_optional_tokenizator or has_bizum:

			gateways_configured_in_rule = rule_to_apply.get("gateways", "")
			is_optional_tokenizator = True if rule_to_apply.get("type_rule") == "create_token" and rule_to_apply.get("optional_payment") else False
			if not gateways_configured_in_rule:
				gateways_configured_in_rule = get_integration_name(hotel_code)

			gateways_from_rule = gateways_configured_in_rule.split("||")
			if "@@all@@" in gateways_from_rule:
				gateways_from_rule = get_all_gateways(hotel_code)
			gateways_from_rule = [i for i in gateways_from_rule if i]
			if gateways and is_tokenizator and not is_optional_tokenizator:
				continue
			for gateway in gateways_from_rule:
				if gateway not in [g.get("gateway_name") for g in gateways] or is_tokenizator:

					new_gateway = {
						"gateway_name": gateway
					}
					if is_tokenizator and not has_optional_tokenizator:
						new_gateway["specific_amount"] = 0
					if is_optional_tokenizator:
						new_gateway["is_optional"] = rule_to_apply.get("optional_payment")
					gateways.append(new_gateway)
					rules_applied += rule_to_apply.get("description", "Rule unkonwn") + ";"
	return gateways, rules_applied.strip(";"), has_optional_tokenizator

def get_response_amount_to_pay(rules_to_apply_filtered, reservation, all_rooms_map, hotel_code, has_bizum, has_optional_tokenizator):
	from paraty.pages.cobrador.cobrador import calculate_payment_by_type

	rule_to_apply = rules_to_apply_filtered[0]  # always the BEST (more restrictive) is first
	rules_applied = rule_to_apply.get("description")
	amount_to_pay = calculate_payment_by_type(rule_to_apply, reservation, all_rooms_map)
	gateways_to_use = []
	if get_config_property_value_without_cache(hotel_code, USE_PAYMENT_GATEWAY_BY_COBRADOR) or has_bizum:
		gateways_to_use, rules_applied, has_optional_tokenizator = get_payment_gateways_to_use(hotel_code,
		                                                                                       rules_to_apply_filtered,
		                                                                                       has_bizum)

	response_amount_to_pay = {
		"amount": float("%.2f" % amount_to_pay),
		"rule_applied": rules_applied,
		"gateways": gateways_to_use
	}

	if has_optional_tokenizator:
		response_amount_to_pay["early_payment"] = True

	# if rule_to_apply.get("optional_payment"):
	#    response_amount_to_pay["optional_payment"] = True

	if rule_to_apply["type_rule"] == "early_payment":
		response_amount_to_pay['original_price'] = reservation['price']
		response_amount_to_pay['discount'] = rule_to_apply['early_payment_percent']
		response_amount_to_pay['early_payment'] = True

	elif rule_to_apply.get("type_amount") == "num_days" and rule_to_apply.get("amount"):
		response_amount_to_pay['num_days'] = int(rule_to_apply['amount'])

	if rule_to_apply.get("fake_tokenizator"):
		response_amount_to_pay['automatic_refund'] = True

	if rule_to_apply.get("force_tokenizator"):
		response_amount_to_pay['force_tokenizator'] = True

	return response_amount_to_pay


def get_payment_multigateways_to_use(hotel_code, rules_to_apply, reservation, all_room_map):
    from paraty.pages.cobrador.cobrador import calculate_payment_by_type

    result = {
        "amount": 0,
        "gateways": [],
        "rule_applied": ""
    }

    rules_to_apply = sorted(rules_to_apply, key=lambda l: l.get("restriction_priority"), reverse=True)

    for rule in rules_to_apply:
        amount_to_pay = calculate_payment_by_type(rule, reservation, all_room_map)
        
        gateways_configured = rule.get("gateways", "")
        if not gateways_configured:
            gateways_configured = get_integration_name(hotel_code)
            
        gateway_list = gateways_configured.split("||")
        if "@@all@@" in gateway_list:
            gateway_list = get_all_gateways(hotel_code)
        gateway_list = [g for g in gateway_list if g]

        result["amount"] = max(result["amount"], amount_to_pay)
        result["rule_applied"] = rule.get("description", "Rule unknown")
        
        for gateway in gateway_list:
            found_gateway = next((g for g in result["gateways"] if g["gateway_name"] == gateway), None)
            if found_gateway:
                found_gateway["multiple_gateways"].append({
                    gateway: {
                        "amount": amount_to_pay,
                        "rule_applied": rule.get("description", "Rule unknown")
                    }
                })
            else:
                result["gateways"].append({
                    "gateway_name": gateway,
                    "multiple_gateways": [{
                        gateway: {
                            "amount": amount_to_pay,
                            "rule_applied": rule.get("description", "Rule unknown"),
                            "text": json.loads(rule.get('booking_3_gateways_text', '{}')).get(reservation.get("language", "SPANISH"))
                        }
                    }]
                })

    merged_gateways = []
    for gateway in result["gateways"]:
        found_merged_gateway = next((g for g in merged_gateways if g["gateway_name"] == gateway["gateway_name"]), None)
        if found_merged_gateway:
            found_merged_gateway["multiple_gateways"].extend(gateway["multiple_gateways"])
        else:
            merged_gateways.append(gateway)

    result["gateways"] = [{
        "gateway_name": ";".join([g["gateway_name"] for g in merged_gateways]),
        "multiple_gateways": [mg for g in merged_gateways for mg in g["multiple_gateways"]]
    }]

    return result


def filter_rules_by_country(all_rules, reservation):

	if reservation.get("country"):
		result = []
		country = reservation.get("country")
		for rule in all_rules:
			if rule.get("country", ""):
				valid_countries = rule.get("country", "").split("||")
				valid_countries = [i for i in valid_countries if i]
				if valid_countries:
					if rule.get("negative_country", False):
						if country.upper() not in valid_countries:
							result.append(rule)
					else:
						if country.upper() in valid_countries:
							result.append(rule)
				else:
					result.append(rule)
			else:
				result.append(rule)
		return result
	else:
		return all_rules

def filter_rules_by_language(all_rules, reservation):

	if reservation.get("language"):
		result = []
		language = LANGUAGE_CODES.get(reservation.get("language", "").upper())
		if not language:
			return all_rules

		for rule in all_rules:

			language_rule = rule.get("language", "")
			if language_rule == '[]':
				language_rule = ""

			if language_rule:
				valid_language = language_rule.split("||")
				valid_language = [i for i in valid_language if i]
				if valid_language:
					if rule.get("negative_language", False):
						if language not in valid_language:
							result.append(rule)
					else:
						if language in valid_language:
							result.append(rule)
				else:
					result.append(rule)
			else:
				result.append(rule)
		return result
	else:
		return all_rules

def filter_rules_by_user_type(all_rules, reservation):
	result = []
	callcenter = False
	if reservation.get("callcenter") and reservation.get("callcenter") != "None":
		callcenter = True
	agency = False
	if reservation.get("agency_info") and reservation.get("agency_info") != "None":
		agency = True

	if (not callcenter or not agency) and reservation.get("extraInfo"):
		try:
			extra_info = json.loads(reservation.get("extraInfo"))
		except json.JSONDecodeError:
			extra_info = {}

		agency = bool(extra_info.get("agency_hash")) if not agency else agency
		callcenter = bool(reservation.get("agent")) if not callcenter else callcenter

	web_user = not callcenter and not agency

	for rule in all_rules:
		if rule.get("user_type", ""):
			valid_users = rule.get("user_type", "").split("||")
			valid_users = [i for i in valid_users if i]
			if valid_users:

				rule['restriction_priority'] += 2

				if rule.get("negative_user_type", False):
					if agency and "agency" not in valid_users:
						result.append(rule)
					elif callcenter and "callcenter" not in valid_users:
						result.append(rule)
					elif web_user and "web_user" not in valid_users:
						result.append(rule)
				else:
					if "all" in valid_users:
						result.append(rule)
					elif agency and "agency" in valid_users:
						result.append(rule)
					elif callcenter and "callcenter" in valid_users:
						result.append(rule)
					elif web_user and "web_user" in valid_users:
						result.append(rule)
			else:
				result.append(rule)
		else:
			result.append(rule)
	return result


def filter_rules_by_cc_expiration(all_rules, reservation):
	# Filter rules that have cc_expire_date_payment enabled
	cc_expire_rules = [rule for rule in all_rules if rule.get("cc_expire_date_payment") == "on"]

	# If there are no rules with cc_expire_date_payment, we return all rules
	if not cc_expire_rules:
		return all_rules

	# Extract reservation information
	extra_info = reservation.get("extraInfo", "{}")
	if isinstance(extra_info, str):
		try:
			extra_info = json.loads(extra_info)
		except:
			logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
			return all_rules

	cc_expire_date = extra_info.get("cc_expire_date")
	start_date = reservation.get("startDate")

	# If there is no expiration date or entry date, we return all the rules
	if not cc_expire_date or not start_date or not extra_info.get("notification_cc_expired_sent"):
		return all_rules

	try:
		start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")

		cc_month, cc_year = cc_expire_date.split('/')
		cc_month_int = int(cc_month)
		cc_year_full = int("20" + cc_year)  # Convertir YY a 20YY

		if cc_month_int == 12:
			last_day = 31
		else:
			# We calculate the last day of the month by obtaining the first day of the following month and subtracting 1 day.
			next_month = datetime(cc_year_full, cc_month_int + 1, 1)
			last_day_date = next_month - timedelta(days=1)
			last_day = last_day_date.day

		# Create full expiration date (last day of the month at 23:59:59)
		cc_expire_date_obj = datetime(cc_year_full, cc_month_int, last_day, 23, 59, 59)

		current_date = datetime.now()

		# Check if the card expires before the entry date AND we are in the same month of expiration.
		if (cc_expire_date_obj < start_date_obj and current_date.month == cc_month_int and current_date.year == cc_year_full):

			logging.info("The card expires (%s) before the entry date (%s) and we are in the same month of expiration (%s/%s)",cc_expire_date_obj, start_date_obj, current_date.month, current_date.year)

			# If there are multiple rules with cc_expire_date_payment, we select the one that charges the highest amount
			if len(cc_expire_rules) > 1:
				from paraty.pages.cobrador.cobrador import calculate_payment_by_type

				all_rooms_map = {}

				# Calculate the amount for each rule and select the one that charges the most
				max_amount = -1
				selected_rule = None

				for rule in cc_expire_rules:
					try:
						amount = calculate_payment_by_type(rule, reservation, all_rooms_map)
						if amount > max_amount:
							max_amount = amount
							selected_rule = rule
					except Exception as e:
						logging.error("Error calculating the amount for the rule %s: %s", rule.get('id'), e)

				if selected_rule:
					logging.info("Filtered rule '%s' with amount %s (maximum between expiration rules)",selected_rule.get('description'), max_amount)
					return [selected_rule]

			return [cc_expire_rules[0]]
	except Exception as e:
		logging.error("Error when comparing expiration dates: %s", e)

	return all_rules