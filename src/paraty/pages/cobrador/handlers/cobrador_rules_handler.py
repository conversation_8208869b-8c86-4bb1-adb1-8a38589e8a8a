from datetime import datetime

from flask import request, redirect

from paraty import app
from paraty.authentication.login_utils import login_from_session<PERSON><PERSON>, login_using_referrer, check_permission_to_manager
from paraty.config import get_manager_2_url_bis
from paraty.pages.cobrador.cobrador import VERSION_CSS, VERSION_JS, get_rules_by_type
from paraty.pages.cobrador.cobrador_constants import USE_PAYMENT_GATEWAY_BY_COBRADOR, GATEWAY_WITH_BIZUM
from paraty.pages.cobrador.cobrador_utils import get_config_property_value_without_cache, get_all_gateways, \
	get_config_property_value, get_all_languages_configured, get_booking_3_gateways_text
from paraty.utilities import session_utils
from paraty.utilities.languages.language_utils import get_web_dictionary
from paraty.utilities.templates.templates_processor import build_template, build_iframe_page

from paraty_commons_3 import language_utils
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_rooms_of_hotel, \
	get_additional_services_of_hotel, get_hotel_advance_config_item
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, get_entity, \
	save_to_datastore, delete_entity
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.logging.my_gae_logging import logging

EXTRA_CHECK_BY_GATEWAY = {
	"SIBS2.0 COBRADOR": {
		"force_token": True
	}
}

@app.route("/pages/show_payments_rules_history", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_history():
	current_sess = session_utils.get_current_session()
	hotel_code = current_sess.get_value('hotel_code')
	session_key = current_sess.get_value("session_id")
	request_params = request.values

	if not session_utils.get_current_session().get_value("language"):
		locale_language = request_params.get("language", 'default')
		language = language_utils.get_language_in_manager_based_on_locale(locale_language)

	else:
		language = session_utils.get_current_session().get_value("language")
		locale_language = language_utils.get_language_code(language)

	session_utils.get_current_session().set_value("language", language)

	logging.info("Cobrador home for hotel_code: %s", hotel_code)
	content_page = _get_content_history_tab(hotel_code)

	context = {
		'hotel_code': hotel_code,
		'session_key': session_key,
		'active_rules_history_tag': 'active',
		'content_page': content_page,
		'is_backend': check_permission_to_manager("backend"),
		'no_cobrador_rules': check_permission_to_manager("no_cobrador_rules"),
		'no_cobrador_payments': check_permission_to_manager("no_cobrador_payments"),
		'locale_language': locale_language,
		'language': language_utils.get_language_in_manager_based_on_locale(locale_language)
	}

	content = build_template('pages/cobrador/cobrador_base.html', context)
	css_list = [
		'/static/css/libs/fontawesome5.css',
		'/static/css/libs/jquery_confirm/jquery-confirm.min.css',
		'/static/css/pages/cobrador/jquery.json-viewer.css',
		f'/static/css/pages/cobrador/cobrador.css?v={VERSION_CSS}',
	]

	jsLib_list = ['/static/js/libs/jQuery.min.js',
				  '/static/js/libs/jquery_confirm/jquery-confirm.min.js']

	js_list = [f'/static/js/pages/cobrador/cobrador.js?v={VERSION_JS}']
	return build_iframe_page(content, css_list, jsLib_list, js_list)

@app.route("/pages/cobrador", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_home():
	current_sess = session_utils.get_current_session()
	hotel_code = current_sess.get_value('hotel_code')
	session_key = current_sess.get_value("session_id")
	request_params = request.values

	if not session_utils.get_current_session().get_value("language"):
		locale_language = request_params.get("language", 'default')
		language = language_utils.get_language_in_manager_based_on_locale(locale_language)

	else:
		language = session_utils.get_current_session().get_value("language")
		locale_language = language_utils.get_language_code(language)

	session_utils.get_current_session().set_value("language", language)

	logging.info("Cobrador home for hotel_code: %s", hotel_code)
	content_page = _get_content_rules_tab(hotel_code)

	context = {
		'hotel_code': hotel_code,
		'session_key': session_key,
		'active_payments_rules_tag': 'active',
		'content_page': content_page,
		'is_backend': check_permission_to_manager("backend"),
		'no_cobrador_rules': check_permission_to_manager("no_cobrador_rules"),
		'no_cobrador_payments': check_permission_to_manager("no_cobrador_payments"),
		'locale_language': locale_language,
		'language': language_utils.get_language_in_manager_based_on_locale(locale_language)
	}

	content = build_template('pages/cobrador/cobrador_base.html', context)
	css_list = [
		'/static/css/libs/fontawesome5.css',
		'/static/css/libs/jquery_confirm/jquery-confirm.min.css',
		f'/static/css/pages/cobrador/cobrador.css?v={VERSION_CSS}',
	]
	jsLib_list = ['/static/js/libs/jQuery.min.js',
				  '/static/js/libs/jquery_confirm/jquery-confirm.min.js',
				  '/static/js/libs/jquery_confirm/jquery-confirm.min.js']

	js_list = [f'/static/js/pages/cobrador/cobrador.js?v={VERSION_JS}']
	return build_iframe_page(content, css_list, jsLib_list, js_list)


@app.route("/pages/cobrador/form_configuration", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_form_new_config():
	current_sess = session_utils.get_current_session()
	hotel_code = current_sess.get_value('hotel_code')
	session_key = current_sess.get_value("session_id")
	request_params = request.values
	language_by_session = current_sess.get_value("language")

	if current_sess:
		locale_language = language_utils.get_language_code(language_by_session)
		
	else:
		locale_language = request_params.get("language", 'default')

	payment_gateway_by_cobrador = get_config_property_value_without_cache(hotel_code, USE_PAYMENT_GATEWAY_BY_COBRADOR)
	has_bizum = get_config_property_value(hotel_code, GATEWAY_WITH_BIZUM)

	if not payment_gateway_by_cobrador and has_bizum:
		payment_gateway_by_cobrador = True

	configuration_id = ""
	configuration = {}
	body = dict(request.args)
	rates_saved = []
	rooms_saved = []
	gateways = []
	gateways_cc = []
	country = []
	language_select = ""
	user_type = []
	acumulate_rules_saved = []
	excluded_programmatically_rules = []

	if body.get("configuration_id"):
		configuration_id = body.get("configuration_id")
		configuration = get_entity('PaymentConfiguration', int(configuration_id), hotel_code=hotel_code)
		if configuration.get("rates"):
			for saved_rate in configuration.get("rates", "").split("||"):
				rates_saved.append(saved_rate)

		if configuration.get("rooms"):
			for saved_rooms in configuration.get("rooms", "").split("||"):
				rooms_saved.append(saved_rooms)

		acumulate_rules_saved = configuration.get("acumulate_rules",[])

		if configuration.get("gateways", ""):
			gateways = configuration.get("gateways", "").split("||")

		if configuration.get("gateways_cc", ""):
			gateways_cc = configuration.get("gateways_cc", "").split("||")

		if configuration.get("country"):
			country = configuration.get("country", "").split("||")

		if configuration.get("language"):
			language_select = configuration.get("language", "").split("||")

		if configuration.get("user_type"):
			user_type = configuration.get("user_type", "").split("||")

		if configuration.get("excluded_programmatically_rules"):
			excluded_programmatically_rules = configuration.get("excluded_programmatically_rules", "").split("||")

		if not configuration.get("increase_in_priority") and not configuration.get("increase_in_priority") == 0:
			configuration["increase_in_priority"] = 0
	try:
		language = session_utils.get_current_session().get_value("language")
		translations = get_web_dictionary(language)
	except:
		translations = get_web_dictionary(False)

	rates = list(get_using_entity_and_params('Rate', [('enabled', '=', True)], hotel_code=hotel_code))
	for rate in rates:
		rate["id"] = rate.key.id
		if str(rate.get("id")) in rates_saved:
			rate["selected"] = "true"

	rooms = list(get_using_entity_and_params('RoomType', [], hotel_code=hotel_code))
	for room in rooms:
		room["id"] = room.key.id
		if str(room.get("id")) in rooms_saved:
			room["selected"] = "true"
	if not configuration or not configuration.get("conditions_days", ""):
		configuration["conditions_days"] = "100000"

	rules = get_rules_by_type(hotel_code, "programmatically")
	for rule in rules:
		if str(rule.id) in acumulate_rules_saved:
			rule["selected"] = "true"
		if str(rule.id) in excluded_programmatically_rules:
			rule["exclude"] = "true"
	config_conditions_days = []
	if configuration.get("start_date"):
		new_conditions = {}
		new_conditions["start_date"] = configuration.get("start_date")
		new_conditions["end_date"] = configuration.get("end_date")
		new_conditions["1day_inrange"] = configuration.get("1day_inrange")
		new_conditions["payed_day_range"] = configuration.get("payed_day_range")
		new_conditions["name_date_start"] = "start_date1"
		new_conditions["name_date_end"] = "end_date1"
		new_conditions["name_1dayinrange"] = "1dayinrange1"
		new_conditions["name_payed_day_range"] = "payed_day_range1"
		config_conditions_days.append(new_conditions)

	if configuration.get("config_conditions_days"):
		for i, condition in enumerate(configuration.get("config_conditions_days")):
			new_conditions={}
			new_conditions["start_date"] = condition.get("start_date")
			new_conditions["end_date"] = condition.get("end_date")
			new_conditions["1day_inrange"] = condition.get("1day_inrange")
			new_conditions["payed_day_range"] = condition.get("payed_day_range")
			new_conditions["name_date_start"] = "start_date" + str(i+1)
			new_conditions["name_date_end"] = "end_date" + str(i+1)
			new_conditions["name_1dayinrange"] = "1dayinrange" + str(i+1)
			new_conditions["name_payed_day_range"] ="payed_day_range" + str(i+1)
			config_conditions_days.append(new_conditions)

	context = {
		'configuration': configuration,
		'configuration_id': configuration_id,
		'hotel_code': hotel_code,
		'rates': rates,
		'rooms': rooms,
		"rules": rules,
		"country": country,
		"language": language_select,
		"user_type": user_type,
		'session_key': session_key,
		'all_rate_selected': "@@all@@" in rates_saved,
		'all_rooms_selected': "@@all@@" in rooms_saved or not rooms_saved,
		'all_gateways': get_all_gateways(hotel_code),
		'gateways': gateways,
		'gateways_cc': gateways_cc,
		'config_conditions_days': config_conditions_days,
		'payment_gateway_by_cobrador': payment_gateway_by_cobrador,
		'all_acumulate_rules':  "@@all@@" in acumulate_rules_saved or not acumulate_rules_saved,
		"back_button": {"href": "/pages/cobrador?back=true&sessionKey=%s&language=%s" % (session_key, locale_language),
						"label": translations.get("T_VOLVER")},
		'locale_language': locale_language,
		'calendar_language': language_utils.get_language_in_manager_based_on_locale(locale_language),
		'all_languages': get_all_languages_configured(get_hotel_by_application_id(hotel_code), configuration),
		'custom_booking_3_text': get_hotel_advance_config_item(get_hotel_by_application_id(hotel_code), "custom booking3 text for tpv")
	}

	for x in context["all_gateways"]:
		if EXTRA_CHECK_BY_GATEWAY.get(x):
			context.update(EXTRA_CHECK_BY_GATEWAY[x])

	content_page = build_template('pages/cobrador/configurations_form.html', context)

	context = {
		'hotel_code': hotel_code,
		'session_key': session_key,
		'active_payments_rules_tag': 'active',
		'content_page': content_page,
		'is_backend': check_permission_to_manager("backend"),
		'no_cobrador_rules': check_permission_to_manager("no_cobrador_rules"),
		'no_cobrador_payments': check_permission_to_manager("no_cobrador_payments"),
		'locale_language': locale_language,
		'calendar_language': language_utils.get_language_in_manager_based_on_locale(locale_language)
	}

	content = build_template('pages/cobrador/cobrador_base.html', context)
	css_list = ['/static/css/libs/fontawesome5.css',
				'/static/css/libs/jquery_confirm/jquery-confirm.min.css',

				'/static/css/libs/select2.min.css',
				'/static/css/pages/cobrador/cobrador.css?v=%s' % VERSION_CSS]
	jsLib_list = ['/static/js/libs/jQuery.min.js',
				  '/static/js/libs/jQuery-ui.min.js',
				  '/static/js/libs/datepicker/datepicker-es.js',
				  '/static/js/libs/select2.min.js',
				  '/static/js/libs/jquery_confirm/jquery-confirm.min.js']
	js_list = ['/static/js/pages/cobrador/cobrador.js?v=%s' % VERSION_JS]
	return build_iframe_page(content, css_list, jsLib_list, js_list)


@app.route("/pages/cobrador/save_configuration", methods=['POST'])
@login_from_sessionKey
@login_using_referrer
def save_payment_rule():
	current_sess = session_utils.get_current_session()
	hotel_code = current_sess.get_value('hotel_code')
	session_key = current_sess.get_value("session_id")

	body = dict(request.form)

	if body['configuration_id']:
		action_type = 'modify'
	else:
		action_type = 'create'

	changes = body.get('changes', '')

	logging.info("save_new_config hotel_code %s: %s", hotel_code, body)
	if body.get("optional_tokenizador"):
		# body["type_rule"] = "early_payment"
		body["optional_payment"] = True

	rates = ""
	policies = ""
	words = ""

	if body.get("type_rate_filter") == "by_rates":
		rates = request.form.getlist('rates')
		rates = '||'.join([str(elem) for elem in rates])
	if body.get("type_rate_filter") == "by_policies":
		policies = body.get("rate_policies")
	if body.get("type_rate_filter") == "by_words":
		words = body.get("rate_words", "rate_words").replace(" ", ";").replace(",", ";")

	if body.get("type_rate_filter") == "by_rates":
		rates = request.form.getlist('rates')
		rates = '||'.join([str(elem) for elem in rates])

	rooms = request.form.getlist('rooms')
	rooms = '||'.join([str(elem) for elem in rooms])

	gateways = request.form.getlist("use_gateway")
	gateways = "||".join(gateways)

	country = request.form.getlist("country")
	country = "||".join(country)
	negative_country = body.get("negative_country") == "on"
	language = ""
	negative_language= body.get("negative_language") == "on"
	if body.get("language"):
		language = "||".join(request.form.getlist("language"))

	user_type = request.form.getlist("user_type")
	user_type = "||".join(user_type)
	negative_user_type = body.get("negative_user_type") == "on"

	excluded_programmatically_rules = request.form.getlist("excluded_programmatically_rules")
	excluded_programmatically_rules = "||".join(excluded_programmatically_rules)

	days_limit = body.get("conditions_days_limit")
	if body.get("limitdates1day", "off") == "on" and not body.get("conditions_days_limit"):
		days_limit = "0"

	if body.get("cumulative"):
		acumulate_rules = request.form.getlist('acumulate_rules')
	else:
		acumulate_rules = ""

	if 'all' in acumulate_rules:
		rules = [str(x.id) for x in get_rules_by_type(hotel_code, "programmatically")]
		acumulate_rules = rules

	amount = body.get("amount", "")
	amount = amount.replace("%", "")
	amount = amount.strip()

	if body.get("increase_in_priority"):
		increase_in_priority = int(body.get("increase_in_priority"))
	else:
		increase_in_priority = 0

	config_conditions_days = []
	if body.get("add_dates_range") == "on":
		if body.get("start_date"):
			conditions = {
				"start_date": body.get("start_date"),
				"end_date": body.get("end_date"),
				"1day_inrange": body.get("1dayinrange", ""),
				"payed_day_range": body.get("payed_day_range", "")
			}

			config_conditions_days.append(conditions)

		contador_conditions = sum(1 for clave in body if clave.startswith('start_date'))
		for i in range(1, contador_conditions + 1, 1):
			if body.get("start_date" + str(i)):
				conditions = {
					"start_date": body.get("start_date" + str(i)),
					"end_date": body.get("end_date" + str(i)),
					"1day_inrange": body.get("1dayinrange" + str(i), ""),
					"payed_day_range": body.get("payed_day_range" + str(i), "")
				}

				config_conditions_days.append(conditions)

	additional_days_reminder_counter = sum(1 for key in body if key.startswith('additional_days_reminder'))
	additional_days_before_payment_reminder = []
	for i in range(1, additional_days_reminder_counter + 1, 1):
		if body.get("additional_days_reminder" + str(i)):
			additional_days_before_payment_reminder.append(body["additional_days_reminder" + str(i)])


	configuration_params = {"status": body.get("status"),
							"description": body.get("description"),
							"type_rule": body.get("type_rule"),
							"start_date": body.get("start_date"),
							"end_date": body.get("end_date"),
							"1day_inrange": body.get("1dayinrange", ""),
							"conditions_days": body.get("conditions_days"),
							"type_amount": body.get("type_amount"),
							"amount": amount,
							"send_customer_email": body.get("send_customer_email"),
							"send_payment_link_if_error": body.get("send_payment_link_if_error"),
							"days_before_payment_reminder": body.get("days_before_payment_reminder"),
							"payment_reminder": body.get("payment_reminder"),
							"send_modification_email":body.get("send_modification_email"),
							"rooms": rooms,
							"gateways": gateways,
							"country": country,
							"negative_country": negative_country,
							"language": language,
							"negative_language": negative_language,
							"user_type": user_type,
							"negative_user_type": negative_user_type,
							"rates": rates,
							"rate_words": words,
							"rate_policies": policies,
							"number_day": body.get("number_day"),
							"optional_payment": body.get("optional_payment"),
							"limit_dates": body.get("limitdates1day", "off"),
							"num_limit_dates": days_limit,
							"payed_day_range": body.get("paydayrange", ""),
							"acumulate_rules": acumulate_rules,
							"include_supplements": True if body.get("include_supplements", "") else False,
							"fake_tokenizator": True if body.get("fake_tokenizator", "") else False,
							"type_fixed_amount": body.get("type_fixed_amount", ""),
							"use_pay_link": body.get("use_pay_link", ""),
							"config_conditions_days": config_conditions_days,
							"add_dates_range": body.get("add_dates_range"),
							"number_days_stay_reservation":body.get("number_days_stay_reservation"),
							"number_days_stay_max_reservation":body.get("number_days_stay_max_reservation"),
							"minimum_reservation_amount": body.get("minimum_reservation_amount"),
							"send_customer_error_email": body.get("send_customer_error_email"),
							"excluded_programmatically_rules": excluded_programmatically_rules,
							"increase_in_priority": increase_in_priority,
							"cc_expire_date_payment": body.get("cc_expire_date_payment"),
							"additional_days_before_payment_reminder": additional_days_before_payment_reminder
							}

	if body.get("early_payment_percent") and body.get("type_rule") == "early_payment":
		if "%" in body['early_payment_percent']:
			body['early_payment_percent'] = body['early_payment_percent'].replace("%", "")

		configuration_params['early_payment_percent'] = float(body['early_payment_percent'])

	else:
		configuration_params['early_payment_perrcent'] = ""

	if body.get("type_amount") == "supplement":
		configuration_params['amount'] = 0.0

	configuration_id = None
	if body.get("configuration_id"):
		# updating!
		configuration_id = int(body.get("configuration_id"))
	if body.get('send_payment_link_if_error') == "on":
		configuration_params['email_if_fail'] = body.get('email_if_error')
		configuration_params['phone_if_fail'] = body.get('phone_if_error')
		configuration_params['hours_if_fail'] = body.get('hours_if_error')

	if body.get('send_error_payment_channel') == "on":
		configuration_params['send_error_payment_channel'] = True

	else:
		configuration_params['send_error_payment_channel'] = False

	if body.get('force_tokenizator') == "on":
		configuration_params['force_tokenizator'] = True

	else:
		configuration_params['force_tokenizator'] = False

	configuration_params["average_price_day"] = body.get('average_price_day') == "on"

	configuration_params["booking_3_gateways_text"] = get_booking_3_gateways_text(body)

	if body.get("use_extra_gateway_cc") == "on" and request.form.getlist("use_gateway_cc"):
		configuration_params["gateways_cc"] = "||".join(request.form.getlist("use_gateway_cc"))
	else:
		configuration_params["gateways_cc"] = []

	configuration_id = save_to_datastore("PaymentConfiguration", configuration_id,
																configuration_params, hotel_code=hotel_code)
	if changes != '{}':
		save_payment_rule_history(configuration_id, body.get("description"), action_type, changes, hotel_code)

	return redirect(get_manager_2_url_bis() + "/pages/cobrador?configuration_id=%s&sessionKey=%s" % (configuration_id, session_key))


def save_payment_rule_history(id, rule, action_type, action_details, hotel_code):

	if action_type == 'create':
		action_details = '{"status": {"before": "-", "after": "off"}}'
	elif action_type == 'trash':
		action_details = '{"status": {"before": "off", "after": "del"}}'
	elif action_type == 'delete':
		action_details = '{"status": {"before": "del", "after": "-"}}'
	elif action_type == 'recovery':
		action_details = '{"status": {"before": "del", "after": "off"}}'

	history_params = {
		"user": session_utils.get_current_session().get_value('user_name'),
		"payment_rule_id": id,
		"payment_rule_description": rule,
		"action_details": action_details,
		"action_type": action_type,
		"action_date": datetime.now()
	}
	configuration_id = save_to_datastore("PaymentRulesHistory", None, history_params, hotel_code=hotel_code, exclude_from_indexes=())

@app.route("/pages/cobrador/update_status_rule", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def update_status_rule():
	hotel_code = session_utils.get_current_session().get_value('hotel_code')

	body = dict(request.args)
	configuration_id = int(body.get("configuration_id"))
	configuration = get_entity('PaymentConfiguration', configuration_id, hotel_code=hotel_code)
	configuration["status"] = body.get("status")
	configuration_id = save_to_datastore("PaymentConfiguration", configuration_id, configuration,
																hotel_code=hotel_code)
	if configuration_id:
		old_status = 'off' if configuration["status"] == 'on' else 'on'
		new_value = '{"status": {"before": "' + old_status + '", "after": "' + configuration["status"] +'"}}'
		save_payment_rule_history(configuration_id, body.get("description"), 'modify', new_value, hotel_code)
	logging.info("updating status. configuration_id: %s , status: %s", configuration_id, body.get("status"))
	return "OK"


@app.route("/pages/cobrador/remove_rule", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def remove_rule():
	hotel_code = session_utils.get_current_session().get_value('hotel_code')

	body = dict(request.args)
	configuration_id = int(body.get("configuration_id"))

	delete_entity("PaymentConfiguration", configuration_id, hotel_code=hotel_code)

	if configuration_id:
		save_payment_rule_history(configuration_id, body.get("rule"), 'delete', '', hotel_code)

	logging.info("Remove configuration_id: %s", configuration_id)
	return "OK"

@app.route("/pages/cobrador/trash_rule", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def trash_rule():
	hotel_code = session_utils.get_current_session().get_value('hotel_code')
	body = dict(request.args)
	configuration_id = int(body.get("configuration_id"))
	configuration = get_entity('PaymentConfiguration', configuration_id, hotel_code=hotel_code)
	configuration["status"] = 'del'
	configuration_id = save_to_datastore("PaymentConfiguration", configuration_id, configuration, hotel_code=hotel_code)

	if configuration_id:
		save_payment_rule_history(configuration_id, body.get("rule"), 'trash', '', hotel_code)
		return "OK"
	else:
		return "KO"

@app.route("/pages/cobrador/recover_rule", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def recover_rule():
	hotel_code = session_utils.get_current_session().get_value('hotel_code')
	body = dict(request.args)
	configuration_id = int(body.get("configuration_id"))
	configuration = get_entity('PaymentConfiguration', configuration_id, hotel_code=hotel_code)
	configuration["status"] = 'off'
	configuration_id = save_to_datastore("PaymentConfiguration", configuration_id, configuration, hotel_code=hotel_code)
	data = {
		'response': "OK",
	}
	if configuration_id:
		data['rule'] =  body.get("rule")
		save_payment_rule_history(configuration_id, body.get("rule"), 'recovery', '', hotel_code)
	else:
		data['response'] = "KO"
	return data
def _get_content_rules_tab(hotel_code):
	logging.info("_get_content_rules_tab: %s", hotel_code)

	request_params = request.values

	locale_language = request_params.get("language", 'default')

	body = dict(request.args)
	configuration_id = body.get('configuration_id', "")

	table_list_html = get_configurations_html_table_list(hotel_code, 'actual_rules')
	deleted_list_html = get_configurations_html_table_list(hotel_code, 'deleted_rules')
	context = {
		'session_key': session_utils.get_current_session().get_value('session_id'),
		'configuration_id': configuration_id,
		'table_list_html': table_list_html,
		'deleted_table_list_html': deleted_list_html,
		'locale_language': locale_language
	}

	return build_template('pages/cobrador/rules_tab.html', context)


def _get_content_history_tab(hotel_code):
	logging.info("_get_content_rules_tab: %s", hotel_code)

	request_params = request.values

	locale_language = request_params.get("language", 'default')

	body = dict(request.args)
	configuration_id = body.get('configuration_id', "")

	table_list_html = get_history_html_table_list(hotel_code)
	context = {
		'configuration_id': configuration_id,
		"table_list_html": table_list_html,
		'locale_language': locale_language
	}

	return build_template('pages/cobrador/history_tab.html', context)

def get_configurations_html_table_list(hotel_code, mode):
	'''
		mode = 'actual_rules' or 'deleted_rules'
	'''
	payments_configurations = list(get_using_entity_and_params('PaymentConfiguration', [], hotel_code=hotel_code))

	if mode == 'actual_rules':
		payments_configurations = [item for item in payments_configurations if item['status'] != 'del']
	else:
		payments_configurations = [item for item in payments_configurations if item['status'] == 'del']

	all_configurations = []

	try:
		language = session_utils.get_current_session().get_value("language")
		translations = get_web_dictionary(language)
	except:
		translations = get_web_dictionary(False)
		language = "SPANISH"

	hotel = get_hotel_by_application_id(hotel_code)
	all_rates = get_rates_of_hotel(hotel, language=language, include_removed=True)
	all_rooms = get_rooms_of_hotel(hotel, language=language, include_removed=True)

	rates_map = {x['id']: x for x in all_rates}
	rooms_map = {x['id']: x for x in all_rooms}

	for config in payments_configurations:

		rates_text = ""

		if config.get("rates"):
			if "@@all@@" not in config.get("rates"):
				all_rates_id = config.get("rates").split("||")
				for rate_id in all_rates_id:
					if rate_id.isnumeric():
						rate_name = str(rates_map.get(int(rate_id), {}).get("localName", rate_id))
						name = rates_map.get(int(rate_id), {}).get("name", rate_id)
						if name:
							rate_name += " - " + name
						rates_text += rate_name + "<br>"
					else:
						rates_text += rate_id + "<br>"
			else:
				rates_text = translations.get("T_ALLS") + translations.get("T_RATES") + "<br>"
		if config.get("rooms"):
			if "@@all@@" not in config.get("rooms"):
				all_rooms_id = config.get("rooms").split("||")
				for room_id in all_rooms_id:
					if room_id.isnumeric():
						name = str(rooms_map.get(int(room_id), {}).get("name", room_id))
						rates_text += name + "<br>"
					else:
						rates_text += room_id + "<br>"
			else:
				rates_text += translations.get("T_ALLS") + translations.get("T_ROOMS") + "<br>"

		if config.get("rate_words"):
			rates_text = config.get("rate_words")

		if config.get("rate_policies") and config.get("rate_policies") == "nr":
			rates_text = translations.get("T_PAYMENT_NR_POLICY")
		if config.get("rate_policies") and config.get("rate_policies") == "pvp":
			rates_text = translations.get("T_PAYMENT_FLEX_POLICY")

		range_date_text=""
		if not config.get("config_conditions_days") and config.get("start_date"):
			range_date_text = config.get("start_date") + " - " + config.get("end_date")
		elif  config.get("config_conditions_days"):
			for item in  config.get("config_conditions_days"):
				range_date_text += item.get("start_date") + " - " + item.get("end_date") + "<br>"
		else:
			range_date_text = translations.get("T_ALL")


		type_amount_text = ""
		if config.get("type_amount", "") == "num_days":
			if config.get("amount") and int(config.get("amount", 0)) == 1:
				type_amount_text = translations.get("T_DAY")
			else:
				type_amount_text = translations.get("T_DAYS")
		elif config.get("type_amount", "") == "percentage":
			type_amount_text = "%"

		elif config.get("type_amount", "") == "supplement":
			config['amount'] = translations.get("T_PAY_ONLY_SUPPLEMENTS")


		type_rule_text = ""
		if config.get("type_rule") == "in_web":
			type_rule_text = translations.get("T_PAYMENT_IN_WEB")
		elif config.get("type_rule") == "programmatically":
			type_rule_text = translations.get("T_PAYMENT_PROGRAMMATICALLY")
		elif config.get("type_rule") == "early_payment":
			type_rule_text = "Early Payment"
		elif config.get("type_rule") == "credit_card_conditional":
			type_rule_text = translations.get("T_PAYMENT_CREDIT_CART_CONDITIONAL")
		elif config.get("type_rule") == "flight_hotel":
			type_rule_text = "Vuelo + Hotel"
		if config.get("type_rule") == "create_token":
			type_rule_text = translations.get("T_CREATE_TOKEN")

		conditions_days = config.get("conditions_days")
		if int(config.get("conditions_days", "0")) >= 999:
			conditions_days = translations.get("T_ALWAYS")

		config_dict = {"id": config.key.id,
					   "description": config.get("description", ""),
					   "status": config.get("status"),
					   "type_rule": config.get("type_rule"),
					   "type_rule_text": type_rule_text,
					   "rates": rates_text,
					   "range_date_text": range_date_text,
					   "conditions_days": conditions_days,
					   "amount": config.get("amount"),
					   "type_amount_text": type_amount_text,
					   "limit_dates": config.get("limit_dates", "off"),
					   "num_limit_dates": config.get("num_limit_dates", ""),
					   "include_supplements": config.get("include_supplements"),
					   "paydayrange": config.get("payed_day_range", "")}

		if config.get("gateways"):
			config_dict['description'] += " - " + " || ".join(config['gateways'].split("||"))

		all_configurations.append(config_dict)

	type_rule_order = {
		"in_web": 1,
		"flight_hotel": 2,
		"create_token": 3,
		"early_payment": 4,
		"programmatically": 5
	}

	all_configurations = sorted(all_configurations, key=lambda k: (type_rule_order.get(k['type_rule'], 999), k.get("type_amount")))
	all_configurations_sorted = {}

	if all_configurations:
		for rule in all_configurations:
			if all_configurations_sorted.get(rule.get("type_rule")):
				all_configurations_sorted[rule.get('type_rule')].append(rule)

			else:
				all_configurations_sorted[rule.get('type_rule')] = [rule]

	context = {"all_configurations": all_configurations_sorted}
	context['session_key'] = session_utils.get_current_session().get_value('session_id')

	if mode == 'actual_rules':
		return build_template('pages/cobrador/configurations_table_list.html', context)
	else:
		return build_template('pages/cobrador/deleted_rules_table_list.html', context)

def get_history_html_table_list(hotel_code):

	payments_history = list(get_using_entity_and_params('PaymentRulesHistory', [], hotel_code=hotel_code, order_by="-action_date"))
	payments_history = [
		{
			**dict(registro),
			'action_date': registro['action_date'].strftime("%Y-%m-%d %H:%M")
		}
		for registro in payments_history
	]
	context = {"all_configurations": payments_history}
	return build_template('pages/cobrador/history_payment_rules_table_list.html', context)