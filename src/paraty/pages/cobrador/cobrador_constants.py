COBRADOR_SERVER_PATH = "https://payment-seeker.appspot.com"
USE_ALTERNATIVE_PAYMENT_SEEKER = "Use alternative payment seeker"
COBRADOR_FAILOVER_SERVER_PATH = "https://failover-dot-payment-seeker.appspot.com"
BACKEND_GOOGLE_GROUP = "<EMAIL>"
PARATY_EPAYMENT_SUPERVISORS = "<EMAIL>,<EMAIL>,<EMAIL>"
USER = 'paco'
PASSWORD = 'paco'

#COBRADOR_SERVER_PATH = "http://*************:8899"

METADATA_URL = "https://payment-seeker.appspot.com/pages/cobrador/get_reservation_metadata"

EMAIL_SENDER_CONFIG = 'Email sender'
EMAIL_DENIED_CARD = "Email tarjeta rechazada"
CREDIT_CARD_BY_TOKEN_CONFIG = "credit cards by token"
COBRADOR_AUTOMATIC_PAYMENT = "Cobrador automatic payment"
USE_PAYMENT_GATEWAY_BY_COBRADOR = "Use multiple payment gateway by cobrador"
COBRADOR_AUTOMATIC_BILLING_ONCE_A_DAY= "Cobrador automatic billing once a day"
AUTOMATIC_RFESERVATION_CANCELATION = "Cancel reservations with uncomplete payments"
USE_ENCRYPTED_PAY_LINKS = "Use encrypted pay links"
GATEWAY_WITH_BIZUM = "Gateway with Bizum"
FORCE_PAYMENT_LINK_GATEWAY_BY_CURRENCY = "force gateway by currency in payment links"
CONVERT_HTML_PDF_URL = "https://pdf-handlers-nv3f7s7soa-ew.a.run.app/html_to_pdf"

# Web Configurations
PREFIX_BOOKING = "prefix booking"
#if populated, cobrador will check automatically for do payments programmatically
COBRADOR_CONFIG = "cobrador"
COBRADOR_QUEUE = 'cobrador-payment'

INTEGRATION_CONFIG_MAP_SEPARATOR = " @@ "

SEPARATOR_INFO_ERROR_ORDER = "||"

GATEWAY_ERROR_RETURNED = "ERROR"
GATEWAY_SUCESS_RETURNED = "OK"
GATEWAY_ERROR_CODE_RETURNED = "KO"
GATEWAY_PENDING_RETURNED = "PENDING"

SID_FROM_COBRADOR = "from_cobrador"
HOUR_NOT_PAYMENT = 14
FIRST_HOUR_IN_THE_MORNING = 6

#TIPOS DE PAGOS DESDE EL PEP!!!
PAYMENT_TYPE_GIFT_CARD = "gift card"
PAYMENT_TYPE_LINK = "Envío de link al cliente"
PAYMENT_TYPE_PROGRAMATICALLY = "programado"
PAYMENT_TYPE_IN_WEB = "in_web"
PAYMENT_TYPE_EARLY_PAYMENT = "early_payment"
PAYMENT_TYPE_CREATE_TOKEN = "create_token"
PAYMENT_TYPE_FLIGHT = "flight_hotel"
PAYMENT_TYPE_REFUND = "devolution"
PAYMENT_TYPE_REFUND_EXTRA = "extra devolution"
PAYMENT_TYPE_REFUND_AUTOMATIC = "automatic devolution"
PAYMENT_TYPE_REFUND_PENDING = "refund pending"
PAYMENT_TYPE_REFUND_FAILED = "failed_devolution"
PAYMENT_TYPE_MANUAL_EXTRA = "manual_extra" #Pago externo, que se notifica manualmente en el PeP
PAYMENT_TYPE_MANUAL = "manual" #pago real realizado  desde el PeP de forma manual
PAYMENT_TYPE_EXTRA = "extra" #pago real realizado  desde el PeP de forma manual, pero extra a la reserva (para saltarse el limite del total de la reserva)


REAL_PAYMENTS = [PAYMENT_TYPE_IN_WEB, PAYMENT_TYPE_REFUND, PAYMENT_TYPE_REFUND_AUTOMATIC, PAYMENT_TYPE_REFUND_EXTRA,
				 PAYMENT_TYPE_PROGRAMATICALLY, PAYMENT_TYPE_MANUAL_EXTRA, PAYMENT_TYPE_MANUAL, PAYMENT_TYPE_EXTRA]


REAL_REFUNDS = [PAYMENT_TYPE_REFUND, PAYMENT_TYPE_REFUND_AUTOMATIC, PAYMENT_TYPE_REFUND_EXTRA]

SEND_REDUNICRE_RESPONSE_TO_HOTEL = "send redunicre response to hotel"
EMAIL_BOOKING = "Email reservas"

PAYLINKS_COBRADOR_INTEGRATION_NAME = "PEP_PAYLINKS COBRADOR"

NAMESPACE = "namespace"

CUSTOM_DOMAIN = 'Dominio booking'
KEY_DOMAIN = 'Dominio asociado'


PAYCOMET = "PAYCOMET"
TRUST = "TRUST"
REDUNICRE = "REDUNICRE"
W2M = "W2M"
ADYEN = "ADYEN"

MULTIBANCO = "MULTIBANCO"
MBWAY = "MBWAY"

SERMEPA_CC_TOKEN_ID = "sermepa_cc_token"
SERMEPA_CC_EXPIREDATE = "sermepa_cc_expiredate"
SERMEPA_BRAND_CC = "sermepa_brand_cc"
SERMEPA_TYPE_CC = "sermepa_type_cc"
SERMEPA_ORDER_RECEIVED = "sermepa_order_received"
GATEWAY_PAID_AMOUNT = "GATEWAY_PAID_AMOUNT"
SERMEPA_COF_TXNID = "sermepa_cof_txnid"
LINK_TPV_PAYMENT_ORDER = "link_tpv_payment_order"

GATEWAY_ERROR_DICT = {"ERROR": GATEWAY_ERROR_RETURNED, "CODE": GATEWAY_ERROR_CODE_RETURNED}

CONTENT_TYPE_XML = "text/xml"
CONTENT_TYPE_TEXT = "application/text"



MESSAGE_KEY = b")RF@aTfUjXn2r4u9"

LIMITS_BY_CURRENCIES = {
	"EUR": 5000*3,
	"USD": 5000*3,
	"GBP": 2750*3,
	"MXN": 80000*2,
	"DOP": 220000*3,
	"COP": 20360913*3
}

ALL_PAYMENT_METHODS = [
	"SERMEPA",
	"RESORTCOM",
	"EVO",
	"SIBS2.0",
	"SIBS",
	"EPAYCO",
	"STRIPE",
	"PAYBYRD",
	"ADDON_PAYMENTS",
	"WORLDLINE",
	"DATATRANS_TPV",
	"PLACETOPAY",
	"BANORTE",
	"PAYPAL_V2",
	"PAYPAL",
	"NEXI",
	"REDUNICRE",
	"PLACETOPAY",
	"PAYJP",
	"UNIVERSALPAY",
	"BOOK_AND_PAY",
	"SEQURA",
	"PAY_U",
	"PAYLANDS",
	"AMAZONPAY",
	"REDSYS",
	"PAYME",
	"CECA",
	"PAYCOMET",
	"AZUL",
	"SECURE",
	"PEP_PAYLINKS",
	"OPENPAY",
	"SANTANDER",
	"AFFIRM",
	"PAY-U",
	"TRUST",
	"W2M",
	"SCALAPAY",
	"ADYEN"
]

REDIS_HOST = "************"
REDIS_PASSWORD = "nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ"
REDIS_PORT = 6666

MULTIBANCO_PAYMENT_DATA = "Multibanco Payment Data"

RATE_MAP_SEPARATOR = " @@ "
MANUAL_PAYMENT_TYPES = "MANUAL PAYMENT TYPES"

VERSIONS_EMAIL_PAYMENT = ["V2", "V4", "V5"]
GENERIC_IMAGE_EMAIL = '/static/images/cobrador/transaction-main-image.png'
GENERIC_ERROR_PAYMENT_IMAGE_EMAIL = '/static/images/cobrador/generic-error-transaction-image.png'
GENERIC_SUCCESSFUL_PAYMENT_IMAGE_EMAIL = '/static/images/cobrador/generic-successful-transaction-image.png'

