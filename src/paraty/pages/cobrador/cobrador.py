import cProfile
import copy
import inspect
import json
import logging
from datetime import datetime, timedelta, date
from random import randint
from copy import deepcopy
from urllib.parse import urlparse, parse_qs, urlencode
import traceback
import requests
import base64
import time


from paraty import app
from paraty.authentication.login_utils import login_from_session<PERSON><PERSON>, login_using_referrer, check_permission_to_manager
from paraty.config import get_manager_2_url_bis, Config
from paraty.pages.cobrador.gateways.adyen import AdyenController
from paraty.pages.cobrador.gateways.banorte import BanorteController
from paraty.pages.cobrador.gateways.evo_sessions import EvoSessionsControler
from paraty.pages.cobrador.gateways.w2m import W2MController
from paraty.pages.cobrador.gateways.wompi import WompiController
from paraty.pages.cobrador.gateways.scalapay import ScalapayController
from paraty.pages.cobrador.gateways.worldline_eu2 import WorldLineEu2GatewayController
from paraty.utilities.date_utils import get_specific_timestamp
from paraty.utilities.email_utils import sendEmail_localdev, build_new_payment_email_content, sendEmailCache
from paraty.pages.cobrador.cobrador_constants import EMAIL_SENDER_CONFIG, \
	COBRADOR_QUEUE, INTEGRATION_CONFIG_MAP_SEPARATOR, COBRADOR_AUTOMATIC_PAYMENT, PAYMENT_TYPE_REFUND_FAILED, PAYMENT_TYPE_REFUND_PENDING, SEPARATOR_INFO_ERROR_ORDER, \
	COBRADOR_AUTOMATIC_BILLING_ONCE_A_DAY, \
	HOUR_NOT_PAYMENT, FIRST_HOUR_IN_THE_MORNING, USE_PAYMENT_GATEWAY_BY_COBRADOR, AUTOMATIC_RFESERVATION_CANCELATION, \
	PAYMENT_TYPE_LINK, PAYMENT_TYPE_PROGRAMATICALLY, PAYMENT_TYPE_IN_WEB, PAYMENT_TYPE_REFUND, PAYMENT_TYPE_GIFT_CARD, \
	PAYMENT_TYPE_REFUND_EXTRA, PREFIX_BOOKING, PAYMENT_TYPE_REFUND_AUTOMATIC, PAYMENT_TYPE_EARLY_PAYMENT, \
	PAYMENT_TYPE_CREATE_TOKEN, GATEWAY_WITH_BIZUM, VERSIONS_EMAIL_PAYMENT, GENERIC_IMAGE_EMAIL, PAYMENT_TYPE_FLIGHT, \
	CONVERT_HTML_PDF_URL, GENERIC_ERROR_PAYMENT_IMAGE_EMAIL, GENERIC_SUCCESSFUL_PAYMENT_IMAGE_EMAIL, \
	BACKEND_GOOGLE_GROUP
from paraty.pages.cobrador.cobrador_payment_rules_utiles import get_payment_gateways_to_use, filter_rules_by_country, \
	filter_rules_by_user_type, filter_rules_by_language, get_payment_multigateways_to_use, get_response_amount_to_pay, filter_rules_by_cc_expiration
from paraty.pages.cobrador.cobrador_utils import gateways_format_price, _get_payments_by_link, date_range_list, \
	extract_payments_time_control, get_config_property_value_without_cache, process_pending_refund, \
	send_payment_confirmation_emails, send_paymentlink_email_to_customer, \
	build_new_link, get_config_property_value, get_integration_name, get_payment_gateway_configuration, \
	get_config_payment_seeker_configuration, get_reservation_amount_without_commition, get_hotel_datetime, \
	get_percent_pending_amount_to_pay, get_all_payment_reservation_list, calculate_cancel_policy_for_period, \
	convert_to_numeric_identifier, extract_params_from_request, get_room_name, is_real_payment_in_reservation, \
	add_web_payments_to_total_payed, hotel_has_avalon, get_manual_payment_types, get_reservations_currency, \
	execution_locked, get_lock_key, lock_execution, unlock_execution, send_email_locked_execution, \
	save_secure_payment_reservation, get_all_payments_info_list, truncate_string_by_bytes, \
	save_reservation_and_flush_cache
from paraty.pages.cobrador.gateways.addons import AddonsController
from paraty.pages.cobrador.gateways.addons2 import Addons2GatewayController
from paraty.pages.cobrador.gateways.ceca import CecaController
from paraty.pages.cobrador.gateways.openpay import OpenpayController
from paraty.pages.cobrador.gateways.paybyrd import PaybyrdController
from paraty.pages.cobrador.gateways.paylands import PaylandsController
from paraty.pages.cobrador.gateways.resortcom import ResortcomControler
from paraty.pages.cobrador.gateways.santander import SantanderController

from paraty.pages.cobrador.gateways.sermepa import SermepaControler
from paraty.pages.cobrador.gateways.sibs import SibsController
from paraty.pages.cobrador.gateways.sibs2 import SIBS2FormControler, SIBS2Controller
from paraty.pages.cobrador.gateways.stripe import StripeController
from paraty.pages.cobrador.gateways.worldline import WorldLineGatewayController
from paraty.pages.cobrador.gateways.worldline_eu import WorldLineEuGatewayController
from paraty.utilities.currencies import get_currency_symbol
from paraty.utilities.encryption_utils import build_encrypted_url
from paraty.utilities.hotel_utils import generate_booking1_direct_search_by_reservation
from paraty.utilities.languages import language_utils
from paraty.utilities.languages.language_utils import SPANISH
from paraty.utilities.languages.translations.ENGLISH import translations
from paraty.utilities.manager_utils import get_configuration_property_value, get_hotel_logotypes, \
	get_picture_from_section
from paraty.utilities.session_utils import UserSession
from paraty.utilities.templates.templates_processor import build_iframe_page, build_template
from paraty.utilities import session_utils

from paraty.pages.cobrador.gateways.trust import TrustController

from paraty.pages.cobrador.gateways.payu_insite import PayuInsiteController

from paraty.pages.cobrador.gateways.evo import EvoControler
# from paraty.pages.cobrador.gateways.affirm import AffirmController
from paraty_commons_3 import queue_utils
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, \
	get_integration_configuration_of_hotel, get_rooms_of_hotel, get_hotel_web_config_item, get_boards_of_hotel, \
	get_web_section, ISO_DATETIME_FORMAT
from paraty_commons_3.concurrency.concurrency_utils import execute_in_parallel
from paraty_commons_3.datastore import datastore_communicator
from flask import request, redirect, make_response, jsonify
from paraty.utilities.email_utils import sendEmail, build_email_sender_from_hotel_webs_config
from paraty_commons_3.datastore.datastore_communicator import _get_datastore_client, chunks
from paraty_commons_3.datastore.datastore_utils import get_inner_url
from paraty_commons_3.date_utils import date_to_string
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.decorators.retry import retry
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id, get_all_valid_hotels
from paraty_commons_3.logging.my_gae_logging import logging as log


from werkzeug.datastructures import ImmutableMultiDict, CombinedMultiDict

DEFAULT_SEARCH_NUM_DAYS = 90
VERSION_CSS = "1.45"
VERSION_JS = "2.07"


def get_audit_response_with_retry(hotel_code, search_params=[], return_cursor=False, order_by=None):
	"""
	Get AuditResponse data with automatic retry using 'payment-seeker:' hotel_code if no data found.

	Args:
		hotel_code (str): The hotel code to search for
		search_params (list): Search parameters for the query
		return_cursor (bool): Whether to return cursor
		order_by (str): Order by parameter

	Returns:
		Query results from datastore
	"""
	# First attempt with the provided hotel_code
	audits_info_map = datastore_communicator.get_using_entity_and_params(
		"AuditResponse",
		hotel_code=hotel_code,
		search_params=search_params,
		return_cursor=return_cursor,
		order_by=order_by
	)

	# If no data found with current hotel_code, try with "payment-seeker:"
	if not audits_info_map:
		logging.info("No audits found with hotel_code %s, trying with 'payment-seeker:'", hotel_code)
		audits_info_map = datastore_communicator.get_using_entity_and_params(
			"AuditResponse",
			hotel_code="payment-seeker:",
			search_params=search_params,
			return_cursor=return_cursor,
			order_by=order_by
		)

	return audits_info_map

# ZONE PAYMENTS

@app.route("/pages/cobrador/reservations", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_reservations():
	current_sess = session_utils.get_current_session()
	hotel_code = current_sess.get_value('hotel_code')
	session_key = current_sess.get_value("session_id")
	request_params = request.values

	locale_language = request_params.get("language", 'default')
	language = language_utils.get_language_in_manager_based_on_locale(locale_language)
	#session_utils.get_current_session().set_value("language", language)

	content_page = _get_content_reservations_tag(hotel_code, session_key)

	context = {
		'hotel_code': hotel_code,
		'session_key': session_key,
		'active_payments_zone_tag': 'active',
		'content_page': content_page,
		'is_backend': check_permission_to_manager("backend"),
		'no_cobrador_rules': check_permission_to_manager("no_cobrador_rules"),
		'no_cobrador_payments': check_permission_to_manager("no_cobrador_payments"),
		'calendar_language': language_utils.get_language_in_manager_based_on_locale(locale_language),
		'locale_language': locale_language
	}

	content = build_template('pages/cobrador/cobrador_base.html', context)
	css_list = ['/static/css/libs/fontawesome5.css', '/static/css/libs/jquery_confirm/jquery-confirm.min.css',
				'/static/css/pages/cobrador/cobrador.css?v=%s' % VERSION_CSS]
	jsLib_list = ['/static/js/libs/jQuery.min.js', '/static/js/libs/jQuery-ui.min.js',
				  '/static/js/libs/datepicker/datepicker-es.js',
				  '/static/js/libs/jquery_confirm/jquery-confirm.min.js']
	js_list = ['/static/js/pages/cobrador/cobrador.js?v=%s' % VERSION_JS]
	return build_iframe_page(content, css_list, jsLib_list, js_list)


def _get_content_reservations_tag(hotel_code, session_key):
	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	request_params = request.values
	locale_language = request_params.get("language", 'default')

	reservations_list = get_reservation_html_table_list(hotel_code)

	payment_seeker_configuration = get_config_payment_seeker_configuration(hotel_code)
	remote_hotels = []
	remote_hotels_dict = {}
	if payment_seeker_configuration and payment_seeker_configuration.get("remote_hotels"):
		remote_hotels = payment_seeker_configuration["remote_hotels"].split(";")
		for remote_hotel_code in remote_hotels:
			current_remote_hotel = get_hotel_by_application_id(remote_hotel_code)
			remote_hotels_dict[remote_hotel_code] = current_remote_hotel.get("name")
	context = {
		'hotel_code': hotel_code,
		'session_key': session_key,
		"table_list_html": reservations_list,
		"back_button": {"href": "/pages/cobrador?back=true&sessionKey=%s&language=%s" % (session_key, locale_language),
						"label": translations.get("T_VOLVER") },
		"remote_hotels": remote_hotels_dict
	}

	content = build_template('pages/cobrador/reservation_search_form.html', context)
	return content


@app.route("/pages/cobrador/get_reservations", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def filter_reservations_list():
	hotel_code = session_utils.get_current_session().get_value('hotel_code')
	logging.info("filter_reservations_list for hotelcode: %s", hotel_code)

	#WARNING!! If you run the project in local enviroment please dont't parse 'request.args' or 'request.form' to Dict.!!!
	#Next lines must be changed. ONLY IN LOCAL FOR ALL ENDPOINTS YOU NEED!!
	body = dict(request.args)


	filter_params = []
	filter_by_payment_reservation_date = []
	filter_by_names = ""
	filter_by_start_date = ""

	filter_by_end_date = ""
	filter_by_prices = ""
	filter_by_prices_payment = ""
	filter_by_remote_hotel = ""
	filter_by_type_payed = ""

	# Be careful, because of index, we can only search by TS or identifier :( so lets only use TS
	filter_book_date =None
	if body.get("book_date_initial") and body.get("book_date_finish"):
		book_date = datetime.strptime(body.get("book_date_initial"), '%d/%m/%Y').strftime('%Y-%m-%d')
		filter_book_date = book_date
		filter_params.append(("timestamp", ">=", book_date + " 00:00:00"))
		book_date = datetime.strptime(body.get("book_date_finish"), '%d/%m/%Y').strftime('%Y-%m-%d')
		filter_book_date += " -- " + book_date
		filter_params.append(("timestamp", "<=", book_date + " 23:59:59"))


	if body.get("start_date_initial") and body.get("start_date_finish"):
		start_date_initial = datetime.strptime(body.get("start_date_initial"), '%d/%m/%Y').strftime('%Y-%m-%d')
		start_date_finish = datetime.strptime(body.get("start_date_finish"), '%d/%m/%Y').strftime('%Y-%m-%d')
		filter_by_start_date = True

	if body.get("end_date_initial") and  body.get("end_date_finish"):
		end_date_initial = datetime.strptime(body.get("end_date_initial"), '%d/%m/%Y').strftime('%Y-%m-%d')
		filter_by_end_date = end_date_initial
		end_date_finish = datetime.strptime(body.get("end_date_finish"), '%d/%m/%Y').strftime('%Y-%m-%d')

	if body.get("name_surname"):
		filter_by_names = body.get("name_surname")
	if body.get("prices_reservations"):
		filter_by_prices = float(body.get("prices_reservations", 0).replace(",", "."))
	if body.get("amount_payment"):
		filter_by_prices_payment = float(body.get("amount_payment", 0).replace(",", "."))
	if body.get("remote_hotels"):
		filter_by_remote_hotel = body.get("remote_hotels").split(";")
	if body.get("type_payed"):
		filter_by_type_payed = body.get("type_payed")

	if body.get("payment_reservation_date_initial") and body.get("payment_reservation_date_finish"):
		payment_reservation_date_initial = datetime.strptime(body.get("payment_reservation_date_initial"), '%d/%m/%Y')
		reservation_date_initial = payment_reservation_date_initial - timedelta(days=0)
		filter_by_payment_reservation_date.append(("timestamp", ">=", payment_reservation_date_initial.strftime('%Y-%m-%d') + " 00:00:00"))
		filter_params.append(("timestamp", ">=", reservation_date_initial.strftime('%Y-%m-%d') + " 00:00:00"))
		payment_reservation_date_finish = datetime.strptime(body.get("payment_reservation_date_finish"), '%d/%m/%Y').strftime('%Y-%m-%d')
		filter_by_payment_reservation_date.append(("timestamp", "<=", payment_reservation_date_finish + " 23:59:59"))
		filter_params.append(("timestamp", "<=", payment_reservation_date_finish + " 23:59:59"))

	extra_filters = {}

	if filter_by_names:
		extra_filters["filter_by_names"] = filter_by_names
		filter_params.append(("name", "=", filter_by_names))
	if filter_by_start_date:
		extra_filters["filter_by_start_date_initial"] = start_date_initial
		extra_filters["filter_by_start_date_finish"] = start_date_finish
		filter_params.append(("startDate", ">=", start_date_initial))
		filter_params.append(("startDate", "<=", start_date_finish))
	if filter_by_end_date:
		extra_filters["filter_by_end_date_initial"] = end_date_initial
		extra_filters["filter_by_end_date_finish"] = end_date_finish
		filter_params.append(("endDate", ">=", end_date_initial))
		filter_params.append(("endDate", "<=", end_date_finish))
	if body.get("has_token") and body['has_token'] == "true":
		extra_filters['has_token'] = True
	if body.get("has_error") and body['has_error'] == "true":
		extra_filters['has_error'] = True
	if body.get("payment_locator"):
		extra_filters['payment_locator'] = body.get("payment_locator")
	if filter_by_payment_reservation_date:
		extra_filters['payment_reservation_date'] = filter_by_payment_reservation_date
	if filter_by_prices:
		extra_filters['filter_by_prices'] = filter_by_prices
	if filter_by_prices_payment:
		extra_filters['filter_by_prices_payment'] = filter_by_prices_payment
	if filter_by_remote_hotel:
		extra_filters['filter_by_remote_hotels'] = filter_by_remote_hotel
	if filter_by_type_payed:
		extra_filters['filter_by_type_payed'] = filter_by_type_payed
	if filter_book_date:
		extra_filters['filter_book_date'] = filter_book_date
	if body.get("identifier"):
		filter_params = [("identifier", "=", body.get("identifier"))]



	return get_reservation_html_table_list(hotel_code, filter_params=filter_params, extra_filters=extra_filters)

@app.route("/pages/cobrador/build_pdf_list", methods=['POST', 'GET'])
@login_from_sessionKey
@login_using_referrer
def build_pdf_list():
	"""
		It takes from POST:
			- html_code: This is a html data in a string
			- pdf_type: If this value is 'details' the list it´s a detail list of the transactions; otherwise it is a booking list.
		* We use CONVERT_HTML_PDF_URL of builds-tools-2 to convert from txt to pdf
	"""
	if request.method == "GET":
		return "Method not allowed", 200
	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	if request.values.get("pdf_type", "") == 'details':
		table_html = '<H1 style="text-align:center">' + translations.get("T_PAYMENTS_TABLE_TITLE") +  '</H1><HR>' + request.values.get("content-pdf-to-build-details")
	else:
		table_html = '<H1 style="text-align:center">' + translations.get("T_BOOKING_LIST") +'</H1><HR>' + request.values.get("content-pdf-to-build")

	payload = {"html_code": str(table_html).replace('\n', '').replace('"', "\'")}
	response = requests.post(CONVERT_HTML_PDF_URL, data=payload)
	response_flask = make_response(response.content)
	response_flask.headers['Content-Type'] = 'application/pdf'

	if response.status_code == 200:
		return response_flask
	else:
		logging.error(f"Error converting HTML to PDF: {response.status_code} - {response.text}")
		return {"ok": False, "error": "Failed creating PDF"}, 500

@app.route("/pages/cobrador/build_excel", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def build_excel_with_payments():
	hotel_code = session_utils.get_current_session().get_value('hotel_code')
	body = dict(request.args)
	identifier = body.get("identifier", "")
	all_identifiers = body.get("all_identifiers", "")
	# book_date = body.get("book_date", "")
	# startDate = body.get("startDate", "")
	# endDate = body.get("endDate", "")

	logging.info("Getting excel of payments for %s", hotel_code)
	# if startDate:
	# 	startDate = startDate.split("/")
	# 	startDate = "%s-%s-%s" % (startDate[2], startDate[1], startDate[0])
	# if endDate:
	# 	endDate = endDate.split("/")
	# 	endDate = "%s-%s-%s" % (endDate[2], endDate[1], endDate[0])

	extra_params = []
	if identifier:
		extra_params.append(('identifier', '=', identifier))
	# elif book_date:
	# 	book_date = book_date.split("/")
	# 	book_date = "%s-%s-%s" % (book_date[2], book_date[1], book_date[0])
	# 	start_timestamp = book_date + " 00:00:00"
	# 	end_timestamp = book_date + " 23:59:59"
	# 	extra_params.append(('timestamp', '>=', start_timestamp))
	# 	extra_params.append(('timestamp', '<=', end_timestamp))
	# elif startDate:
	# 	extra_params.append(('startDate', '>=', startDate))
	# elif endDate:
	# 	extra_params.append(('endDate', '<=', endDate))

	if extra_params:
		logging.info("We have found filters for excel!!!")
	list_params = []
	remote_hotels = [hotel_code]
	for remote_hotel in remote_hotels:
		remote_hotel = remote_hotel.strip()
		if all_identifiers:
			for x in all_identifiers.split(";"):
				list_params.append([remote_hotel, [('identifier', '=', x)], True])
		else:
			list_params.append([remote_hotel, extra_params, True])

	pararel_reservations = execute_in_parallel(_get_remote_reservations, list_params)
	# if pararel_reservations and not identifier:
	# 	filtered_pararel_reservations = []
		# if startDate and endDate:
		# 	startDate = datetime.strptime(startDate, '%Y-%m-%d').date()
		# 	endDate = datetime.strptime(endDate, '%Y-%m-%d').date()
		# 	filtered_pararel_reservations = [x for x in pararel_reservations[0] if
		# 									 datetime.strptime(x.get('startDate'), '%Y-%m-%d').date() >= startDate and datetime.strptime(x.get('endDate'),
		# 																																 '%Y-%m-%d').date() <= endDate]
		# elif startDate and not endDate:
		# 	filtered_pararel_reservations = [x for x in pararel_reservations[0] if x.get('startDate') == startDate]
		# elif endDate and not startDate:
		# 	filtered_pararel_reservations = [x for x in pararel_reservations[0] if x.get('endDate') == endDate]

		# if filtered_pararel_reservations:
		# 	logging.info("Reservations have been filtered by date")
		# 	pararel_reservations = [filtered_pararel_reservations]

	reservations = []
	for reservation_list in pararel_reservations:
		for reservation in reservation_list:
			reservation["total"] = float("{:.2f}".format(reservation["total"]))
			reservation["total_payed"] = float("{:.2f}".format(reservation["total_payed"]))

			reservations.append(reservation)

	reservations = sorted(reservations, key=lambda l: l.get("timestamp"), reverse=True)
	params_excel = {
		"excel_tabs": {
			"Payments": {
				"headers": ["Estado", "Localizador", "Fecha Reserva", "Cliente", "Entrada", "Salida", "Precio",
							"Cobrado"],
				"rows": [[x.get("status"), x.get("identifier"), x.get("timestamp"),
						  "%s %s" % (x.get("name"), x.get("surname")), x.get("startDate"), x.get("endDate"),
						  x.get("total"), x.get("total_payed")] for x in reservations]
			}
		}
	}
	response = requests.post("https://hotel-tools.appspot.com/external/build_excel", data=json.dumps(params_excel))
	response = make_response(response.content)
	response.headers["Content-Type"] = "text/xls"
	response.headers["Content-Disposition"] = "attachment; filename=payments.xls"
	return response


@app.route('/pages/cobrador/next_reservations', methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def load_next_reservations():
	current_sess = session_utils.get_current_session()
	hotel_code = current_sess.get_value('hotel_code')
	page = request.args.get("page", "")

	start_date_ts = (datetime.today() - timedelta(days=10*int(page)+1)).strftime("%Y-%m-%d")
	end_date_ts = (datetime.today() - timedelta(days=10*int(page)+10)).strftime("%Y-%m-%d")

	filter_params = [("timestamp", "<=", start_date_ts + " 23:59:59"), ("timestamp", ">=", end_date_ts + " 00:00:00")]


	next_reservations= _get_remote_reservations(hotel_code, filter_params)

	return jsonify(next_reservations=next_reservations)


def get_reservation_html_table_list(hotel_code, filter_params=[], extra_filters={}):
	current_sess = session_utils.get_current_session()
	hotel_code = current_sess.get_value('hotel_code')
	session_key = current_sess.get_value("session_id")
	logging.info("getting reservations for hotelcode: %s", hotel_code)

	remote_hotels_payment = get_configuration_property_value(hotel_code, "Remote hotels payment seeker")
	if remote_hotels_payment:
		extra_filters["filter_by_remote_hotels"] = remote_hotels_payment.split(";")
		remote_hotels = extra_filters["filter_by_remote_hotels"]
	else:
		remote_hotels = [hotel_code]
		if extra_filters.get("filter_by_remote_hotels"):
			remote_hotels.extend(extra_filters["filter_by_remote_hotels"])

	total_reservations = 0

	total_price_dict = {}
	total_payed_dict = {}

	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	# TODO take remote hotels from CobradorCOnfiguration in example
	'''agency_config = get_configuration_agency(hotel_code)
	if agency_config and agency_config.get("remote hotels"):
		remote_hotels = agency_config.get("remote hotels").split(",")'''

	has_extra_params = False
	if extra_filters:
		has_extra_params = True

	list_params = []
	for remote_hotel in remote_hotels:
		remote_hotel = remote_hotel.strip()
		list_params.append([remote_hotel, filter_params, has_extra_params])


	default_search = False
	filter_text = ""
	if (not filter_params) and (not extra_filters):
		filter_text = translations.get("T_INITIAL_SEARCH_FILTER")
		default_search = True
	else:

		if extra_filters.get("filter_book_date"):
			field_name = translations.get("T_BOOK_DATE")
			filter_text += translations.get("T_USED_SEARCH_FILTER").replace("@@field@@", field_name).replace(
				"@@value@@", extra_filters.get("filter_book_date")) + "<br>"

		if extra_filters.get("filter_by_names"):
			field_name = "%s/%s" % (translations.get("T_NAME"), translations.get("T_SURNAME"))
			filter_text += translations.get("T_USED_SEARCH_FILTER").replace("@@field@@", field_name).replace(
				"@@value@@", extra_filters.get("filter_by_names")) + "<br>"
		if extra_filters.get("filter_by_start_date_initial"):
			value_date = str(extra_filters.get("filter_by_start_date_initial")) + "--" + str(extra_filters.get("filter_by_start_date_finish"))
			filter_text += translations.get("T_USED_SEARCH_FILTER").replace("@@field@@",
																			translations.get("T_ENTRY_DATE")).replace(
				"@@value@@",value_date) + "<br>"
		if extra_filters.get("filter_by_end_date_initial"):
			value_date = str( extra_filters.get("filter_by_end_date_initial")) + "--" + str(extra_filters.get("filter_by_end_date_finish"))
			filter_text += translations.get("T_USED_SEARCH_FILTER").replace("@@field@@", translations.get(
				"T_DEPARTURE_DATE")).replace("@@value@@", value_date) + "<br>"
		if extra_filters.get("has_token"):
			filter_text += "Token<br>"
		if extra_filters.get("has_error"):
			filter_text += "Error<br>"
		if extra_filters.get("payment_reservation_date"):
			value_date = str(extra_filters.get("payment_reservation_date")[0][2].split(" ")[0]) + " -- " + str( extra_filters.get("payment_reservation_date")[1][2].split(" ")[0])
			filter_text += translations.get("T_USED_SEARCH_FILTER").replace("@@field@@",
																			translations.get("T_PAYMENT_RESERVATION_DATE")).replace("@@value@@",value_date) + "<br>"
		if extra_filters.get("filter_by_prices"):
			field_name = "%s" % (translations.get("T_TOTAL_AMOUNT"))
			filter_text += translations.get("T_USED_SEARCH_FILTER").replace("@@field@@", field_name).replace(
				"@@value@@", str(extra_filters.get("filter_by_prices"))) + "<br>"

		if extra_filters.get("filter_by_prices_payment"):
			field_name = "%s" % (translations.get("T_TOTAL_AMOUNT_PAYMENT"))
			filter_text += translations.get("T_USED_SEARCH_FILTER").replace("@@field@@", field_name).replace(
				"@@value@@", str(extra_filters.get("filter_by_prices_payment"))) + "<br>"

		if extra_filters.get("filter_by_remote_hotels"):
			field_name = "%s" % (translations.get("T_TOTAL_AMOUNT_PAYMENT"))
			filter_text += translations.get("T_USED_SEARCH_FILTER").replace("@@field@@", field_name).replace(
				"@@value@@", str(extra_filters.get("filter_by_remote_hotels"))) + "<br>"

	remote_payment_found = False

	payments_list_by_cobrador = []
	payment_reservation_date = extra_filters.get("payment_reservation_date")
	apply_successful_payments_by_date = False
	if payment_reservation_date:
		filter_params = payment_reservation_date
		payments_list_by_cobrador = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=False)
		if extra_filters.get("has_error"):
			filtered_payments_list_by_cobrador = list([x for x in payments_list_by_cobrador if x.get('order') and "ERROR" in x.get('order')])
		else:
			filtered_payments_list_by_cobrador = list([x for x in payments_list_by_cobrador if x.get('order') and "ERROR" not in x.get('order')])
		if filtered_payments_list_by_cobrador:
			included_transaction = []
			payments_list_by_cobrador = list()
			for transaction in filtered_payments_list_by_cobrador:
				if not transaction.get("reservation_identifier") in included_transaction:
					included_transaction.append(transaction.get("reservation_identifier"))
					payments_list_by_cobrador.append(transaction)
			apply_successful_payments_by_date = True


	if remote_hotels_payment and not filter_params:
		pararel_reservations = {}
	else:
		if len(list_params) == 1:
			pararel_reservations = [_get_remote_reservations(list_params[0][0], list_params[0][1], list_params[0][2], payments_list_by_cobrador)]
		else:
			pararel_reservations = execute_in_parallel(_get_remote_reservations, list_params)


	posible_initial_order_id = None
	reservations = []
	apply_payment_locator = False
	filter_payment_locator = extra_filters.get("payment_locator")
	apply_payments_with_error = False
	filter_by_type_payed = extra_filters.get("filter_by_type_payed")

	if filter_payment_locator:
		filter_params = [('order', '=', filter_payment_locator)]
		if remote_hotels:
			remote_list_params = []
			logging.info("Searching in remote holtes: %s", remote_hotels)
			for current_hotel in remote_hotels:
				remote_list_params.append([current_hotel, [('order','=', filter_payment_locator)], True])
			remote_payments_list_by_cobrador = execute_in_parallel(_get_remote_paymentsResevation, remote_list_params)
			payments_list_by_cobrador = [payment_list[0] for payment_list in remote_payments_list_by_cobrador if payment_list]
		else:
			payments_list_by_cobrador = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=False)
		if len(payments_list_by_cobrador) > 0:
			apply_payment_locator = True
		else:
			# maybe the filter_payment_locator is not a payment in Cobrador, but a initial oayment in hotel_webs, I mean locator == identifier
			posible_initial_order_id = filter_payment_locator

	# if payment_reservation_date:
	# 	filter_params = payment_reservation_date
	# 	payments_list_by_cobrador = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=False)
	# 	if extra_filters.get("has_error"):
	# 		filtered_payments_list_by_cobrador = list([x for x in payments_list_by_cobrador if x.get('order') and "ERROR" in x.get('order')])
	# 	else:
	# 		filtered_payments_list_by_cobrador = list([x for x in payments_list_by_cobrador if x.get('order') and "ERROR" not in x.get('order')])
	# 	if filtered_payments_list_by_cobrador:
	# 		included_transaction = []
	# 		payments_list_by_cobrador = list()
	# 		for transaction in filtered_payments_list_by_cobrador:
	# 			if not transaction.get("reservation_identifier") in filtered_payments_list_by_cobrador:
	# 				included_transaction.append(transaction.get("filtered_payments_list_by_cobrador"))
	# 				payments_list_by_cobrador.append(transaction)
	# 		apply_successful_payments_by_date = True

	if extra_filters.get("has_error"):
		if not payments_list_by_cobrador:
			payments_list_by_cobrador = get_all_payments_info_list([], hotel_code, only_real_payments=False)

		filtered_payments_list_by_cobrador = list([x for x in payments_list_by_cobrador if x.get('order') and "ERROR" in x.get('order')])
		apply_payments_with_error = True

	for reservation_list in pararel_reservations:
		for reservation in reservation_list:
			try:
				extra_info = reservation.get("extraInfo", "{}")
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Error decodig extra info")
				extra_info = {}
			apply_identifier = (not extra_filters.get("filter_by_identifier")) or reservation.get(
				"identifier").lower() == extra_filters.get("filter_by_identifier").lower()
			apply_names = (not extra_filters.get("filter_by_names")) or filter_names_apply(
				extra_filters.get("filter_by_names"), reservation)
			# apply_start_date=True
			apply_start_date = ((not extra_filters.get("filter_by_start_date_finish") and not extra_filters.get("filter_by_start_date_initial")) or (reservation.get("startDate") >= extra_filters.get("filter_by_start_date_initial") and reservation.get("startDate") <= extra_filters.get("filter_by_start_date_finish")))
			apply_reservation_date_finish = (not extra_filters.get("payment_reservation_date_finish") or (reservation.get("timestamp") >= extra_filters.get("payment_reservation_date_finish")))
			apply_end_date = ((not extra_filters.get("filter_by_end_date_finish") and not extra_filters.get("filter_by_end_date_initial")) or (reservation.get("endDate") >= extra_filters.get("filter_by_end_date_initial") and reservation.get("endDate") <= extra_filters.get("filter_by_end_date_finish")))

			# if (not apply_start_date and not apply_end_date) or (extra_filters.get("filter_by_start_date_initial") and extra_filters.get("filter_by_end_date_initial")):
			# 	if reservation.get("startDate") >= extra_filters.get("filter_by_start_date") and reservation.get("endDate") <= extra_filters.get("filter_by_end_date"):
			# 		apply_start_date = True
			# 		apply_end_date = True
			apply_has_token = not extra_filters.get("has_token") or (
					extra_filters.get("has_token") and reservation.get("has_token"))
			apply_by_prices = not extra_filters.get("filter_by_prices") or float(reservation.get("price"))  == extra_filters.get("filter_by_prices")
			apply_by_prices_payment = not extra_filters.get("filter_by_prices_payment") or float("{:.2f}".format(reservation.get("total_payed", 0))) == extra_filters.get(
				"filter_by_prices_payment")

			add_paid_to_payments_list_by_cobrador = get_add_paid_to_payments_list_by_cobrador(reservation, payment_reservation_date, extra_info)

			if posible_initial_order_id:
				apply_identifier = (reservation.get("identifier").lower() == posible_initial_order_id)

				if not apply_identifier:
					apply_identifier = (extra_info.get("payment_from_modification", [{}])[0]).get("payment_id") == posible_initial_order_id

			if (not apply_identifier) and apply_payment_locator:
				apply_identifier = (reservation.get("identifier").lower() == payments_list_by_cobrador[0].get('reservation_identifier').lower())
				if not apply_identifier:
					continue

			if apply_successful_payments_by_date:
				payments_list_by_cobrador_copy = payments_list_by_cobrador
				if add_paid_to_payments_list_by_cobrador:
					payments_list_by_cobrador_copy.append(add_paid_to_payments_list_by_cobrador)
				apply_reservation_payment_date = (reservation.get(
					"identifier") in [transaction.get("reservation_identifier") for transaction in payments_list_by_cobrador_copy])
				if not apply_reservation_payment_date:
					continue

			elif payment_reservation_date and not (reservation["timestamp"] >= payment_reservation_date[0][2] and reservation["timestamp"] <= payment_reservation_date[1][2]):
				continue

			if apply_payments_with_error:
				current_payments_reservation_list = list(filter(lambda x: reservation.get("identifier") in x.get("reservation_identifier"),
							filtered_payments_list_by_cobrador))
				if len(current_payments_reservation_list) == 0:
					continue

			if apply_payment_locator:
				apply_reservation_payment_locator = (reservation.get("identifier").lower() == payments_list_by_cobrador[0].get('reservation_identifier').lower())
				if not apply_reservation_payment_locator:
					continue
			if filter_payment_locator and not apply_payment_locator and (not apply_identifier):

				tpv_link = extra_info.get("payed_by_tpv_link")
				apply_identifier = False
				if tpv_link:
					for tpv in tpv_link:
						if tpv.get("order") == filter_payment_locator:
							apply_identifier = True
							break
				if not apply_identifier:
					continue
			apply_type_payed = True
			if filter_by_type_payed:
				if filter_by_type_payed =="all_payments":
					apply_type_payed = (reservation.get("total",0) == reservation.get("total_payed", 0))
				elif filter_by_type_payed=="parcial_payments":
					apply_type_payed = (reservation.get("total", 0) > reservation.get("total_payed", 0) and reservation.get("total_payed", 0) >0)
				elif filter_by_type_payed=="no_payments":
					apply_type_payed = (reservation.get("total_payed", 0) ==0)

			if apply_identifier and apply_names and apply_reservation_date_finish and apply_start_date and apply_end_date and apply_has_token and apply_by_prices and apply_by_prices_payment and apply_type_payed:
				total_reservations += 1
				price_currency, paid_currency = get_reservations_currency(hotel_code, extra_info)
				if total_price_dict.get(price_currency):
					total_price_dict[price_currency] += reservation.get("total", 0)
				else:
					total_price_dict[price_currency] = reservation.get("total", 0)

				if total_payed_dict.get(paid_currency):
					total_payed_dict[paid_currency] += reservation.get("total_payed", 0)
				else:
					total_payed_dict[paid_currency] = reservation.get("total_payed", 0)

				total_payed = reservation.get("total_payed", 0)
				total_price = reservation.get("total", 0)

				if extra_info.get("bono_gift_used"):
					gift_card =  extra_info.get("original_price_before_discount", 0) - extra_info.get("final_discounted_price", 0)

					if gift_card > (total_price-total_payed):
						total_payed = total_price
					else:
						total_payed = total_payed + gift_card

				reservation["price_currency"] = price_currency
				reservation["total"] = float("{:.2f}".format(total_price))
				reservation["total_payed"] = float("{:.2f}".format(total_payed))
				reservation["total_extra_payed"] = float("{:.2f}".format(reservation.get("total_extra_payed", 0)))
				reservation["currency"] = paid_currency
				reservations.append(reservation)

	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	config = get_use_payment_gateway_configuration(hotel_code)
	refund_not_allowed = config.get('refund_not_allowed')

	total_price = " + ".join(["%.2f %s" % (total_price_dict[i], i) for i in total_price_dict])
	total_payed = " + ".join(["%.2f %s" % (total_payed_dict[i], i) for i in total_payed_dict])

	request_params = request.values
	locale_language = request_params.get("language", 'default')
	force_language = language_utils.get_language_in_manager_based_on_locale(locale_language)

	context = {
		'hotel_code': hotel_code,
		'session_key': session_key,
		'has_remote_hotels': len(remote_hotels) > 1,
		'filter_text': filter_text,
		'default_search': default_search,
		'reservations': reservations,
		"summary": {"total_price": total_price, "total_reservations": total_reservations,
					"total_payed": total_payed},
		"back_button": {"href": "/pages/cobrador?back=true&sessionKey=%s&language=%s" % (session_key, locale_language),
						"label": translations.get("T_VOLVER")},
		'hidden_refund': refund_not_allowed,
		'total_price_dict': total_price_dict,
		'total_payed_dict': total_payed_dict,
		'total_reservations': total_reservations,
		'locale_language': locale_language
	}

	content = build_template('pages/cobrador/reservations_table_list.html', context, force_language)

	css_list = []
	jsLib_list = []
	js_list = []
	return build_iframe_page(content, css_list, jsLib_list, js_list)


def filter_names_apply(filter_by_names, reservation):
	all_names = filter_by_names.split(" ")
	for name in all_names:
		if name.lower() in reservation.get("name").lower():
			return True
		if name.lower() in reservation.get("lastName").lower():
			return True

	return False


def get_add_paid_to_payments_list_by_cobrador(reservation, dates_filter, extra_info):
	if extra_info.get('payed') and int(float(extra_info.get('payed', '0.0'))) > 0 and check_dates(reservation.get('timestamp'), dates_filter):
		return {'reservation_identifier': reservation.get('identifier')}
	payed_by_tpv_link = extra_info.get("payed_by_tpv_link", None)
	if payed_by_tpv_link:
		for link_payment in payed_by_tpv_link:
			if not link_payment.get("amount"):
				continue
			link_payment_amount = float(link_payment.get("amount", 0))
			if (link_payment_amount and link_payment_amount > 0) and check_dates(link_payment.get('timestamp'), dates_filter):
				return {'reservation_identifier': reservation.get('identifier')}


def check_dates(timestamp, dates_filter):
	# Temporal patch for oasis extras
	if len(timestamp) > 19:
		timestamp = timestamp[:19]
	timestamp = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
	if dates_filter:
		for filtro in dates_filter:
			key, operator, date = filtro
			valor_datetime = datetime.strptime(date, '%Y-%m-%d %H:%M:%S')
			if operator == '>=' and not (timestamp >= valor_datetime):
				return False
			elif operator == '<=' and not (timestamp <= valor_datetime):
				return False
			elif operator == '>' and not (timestamp > valor_datetime):
				return False
			elif operator == '<' and not (timestamp < valor_datetime):
				return False
		return True
	return False


def _get_remote_reservations(remote_hotel, filter_params=[], has_extra_params=False, reservations_by_payments=[]):

	if (not filter_params) and not has_extra_params:
		start_date_ts = datetime.today() - timedelta(days=10)
		search_start_date = start_date_ts.strftime("%Y-%m-%d")
		filter_params = [('timestamp', '>=', search_start_date)]

	if reservations_by_payments:
		reservations_by_payments = [x.get("reservation_identifier") for x in reservations_by_payments]

	reservations_list = []

	hotel_info = get_hotel_by_application_id(remote_hotel)

	interface_controller = get_interface_controller(remote_hotel)

	field_sets = list(set([i[0] for i in filter_params]))

	search_params = {}

	for field in field_sets:
		for filter_param in filter_params:
			if field == filter_param[0]:
				if not search_params.get(field):
					search_params[field] = []
				search_params[field].append(filter_param)

	search_params = list(search_params.values())

	searches = []
	is_only_timestamp = len(filter_params) == 2 and filter_params[0][0] == "timestamp" and filter_params[1][0] == "timestamp"
	if search_params and not is_only_timestamp:
		for search in search_params:
			try:
				searches.append(list(datastore_communicator.get_using_entity_and_params('Reservation', search, hotel_code=remote_hotel, keys_only=True)))
			except Exception as e:
				logging.warning("invalid reservation list filter: %s" % str(e))

		reservation_keys = []

		for search in searches:
			for reservation_key in search:
				if all([reservation_key in i for i in searches]):
					reservation_keys.append(reservation_key.key)

		client = _get_datastore_client(hotel_code=remote_hotel)
		my_chunks = chunks(reservation_keys, 500)
		reservations = []
		for x in my_chunks:
			reservations_chunk = list(client.get_multi(x))
			reservations.extend(reservations_chunk)

	else:
		reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', filter_params, hotel_code=remote_hotel))

	#logging.info("[Remote] reservations found: %s", len(reservations))
	reservations_identifiers = [reservation.get("identifier") for reservation in reservations]
	for x in reservations_by_payments:
		if not x in reservations_identifiers:
			extra_reservation = datastore_communicator.get_using_entity_and_params('Reservation', [("identifier", "=", x)], hotel_code=remote_hotel)
			if extra_reservation:
				reservations.append(extra_reservation[0])

	for reservation in reservations:
		extra_info = reservation.get("extraInfo", "{}")
		total_payed = 0
		total_extra_payed = 0
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}
			try:
				reservation["total"] = float(reservation.get("price", "0"))

				if reservation.get("priceSupplements"):
					reservation["total"] += float(reservation['priceSupplements'])

				if get_config_property_value(remote_hotel, "include accommodation tax in pep"):
					try:
						reservation["total"] += float(extra_info.get("price_info").get("taxes").get("accommodation").get("value"))
					except:
						logging.warning(f"Error getting accommodation tax for reservation: {reservation.get('identifier')}")

				reservation["hotel"] = hotel_info.get("name")
				reservation["hotel_code"] = hotel_info.get("applicationId")

				if reservation.get("cancelled"):
					reservation["status"] = "ko"
				elif extra_info.get("status_reservation") == "pending":
					reservation["status"] = "pending"
				else:
					reservation["status"] = "ok"

				has_a_payment = True
				if extra_info.get("payed") or extra_info.get("payed_by_cobrador") or extra_info.get("payed_by_tpv_link") or extra_info.get(
						"link_payment_sended"):
					total_payed = float(extra_info.get("payed", 0))


				history_payed = extra_info.get("modifications", {}).get("changes", {}).get("extraInfo", {}).get("payed", {})
				if history_payed and not history_payed.get("old_value") is None and history_payed.get("new_value") is None:
					total_payed = 0
				if interface_controller:
					if extra_info.get("payment_gateway"):
						gateway_type = extra_info.get("payment_gateway")
						interface_controller = get_interface_to_implement(gateway_type)
					reservation["has_token"] = interface_controller.reservation_has_token(reservation)
				reservation["has_refund"] = has_a_payment
				reservation["has_historic"] = has_a_payment

				if extra_info.get("payed_by_cobrador"):
					total_payed += float(extra_info.get("payed_by_cobrador"))

				if extra_info.get("extra_payed_by_cobrador"):
					total_extra_payed += total_extra_payed + float(extra_info.get("extra_payed_by_cobrador"))

				if extra_info.get("payed_by_tpv_link"):
					for payment_by_link in extra_info["payed_by_tpv_link"]:
						total_payed += float(payment_by_link.get("amount") or 0)

				#NO USAR: relentiza todo y puede duplicar el total paid ya que payed_by_cobrador lo debe llevar ya!
				#total_payed = add_web_payments_to_total_payed(reservation, remote_hotel, total_payed)

				default_currency = get_configuration_property_value(remote_hotel, "Base Price Currency") or "EUR"
				currency = get_currency_symbol(extra_info.get("currency", default_currency))
				reservation["currency"] = currency

				reservation["total_payed"] = total_payed
				reservation["total_extra_payed"] = total_extra_payed
				if not total_payed:
					reservation["pay_status"] = "ko"
				elif total_payed >= float("{:.2f}".format(reservation["total"])):
					reservation["pay_status"] = "ok"
				else:
					reservation["pay_status"] = "oko"

				reservations_list.append(reservation)
			except Exception as e:
				log.info("Error en la reserva %s, no ha podido mostrarse.", reservation['identifier'])
	reservations_list.sort(key=lambda x: x['timestamp'], reverse=True)
	return reservations_list



def _get_remote_paymentsResevation(remote_hotel, filter_params, extra_filters):
	payment_reservation = []

	try:
		payment_reservation = get_all_payments_info_list(filter_params, remote_hotel, only_real_payments=False)
		if payment_reservation:
			return payment_reservation
	except Exception as e:
		logging.warning("Payment not found in %s", filter_params)
		return payment_reservation




@app.route("/pages/cobrador/payment_form", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_payment_form():
	current_sess = session_utils.get_current_session()
	session_key = current_sess.get_value("session_id")
	body = dict(request.args)
	hotel_code = body.get("hotel_code", current_sess.get_value('hotel_code'))
	logging.info("Payment form for hotel_code: %s", hotel_code)
	identifier = body.get("identifier")
	if body.get("hotel_code"):
		hotel_code = body.get("hotel_code")

	return build_payments_section_html(hotel_code, identifier, session_key)


@app.route("/pages/cobrador/manual_payment", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def manual_payment():
	current_sess = session_utils.get_current_session()

	session_key = current_sess.get_value("session_id")

	body = dict(request.args)
	hotel_code = body.get("hotel_code", current_sess.get_value('hotel_code'))
	logging.info("[Manual] payment form for hotel_code: %s", hotel_code)
	identifier = body.get("identifier")

	return build_payments_section_html(hotel_code, identifier, session_key, type_payment='manual')


@app.route("/pages/cobrador/refund_list", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_transactions_to_refund():
	# return show_payments_historic()
	current_sess = session_utils.get_current_session()
	session_key = current_sess.get_value("session_id")

	body = dict(request.args)
	identifier = body.get("identifier")
	payment_id = body.get("payment_id")
	error = body.get("error")
	hotel_code = body.get("hotel_code", current_sess.get_value('hotel_code'))

	return build_payments_section_html(hotel_code, identifier, session_key, hide_form=True, payment_id=payment_id, error=error,
									   show_transactions_to_refund=True)


@app.route("/pages/cobrador/refund_form", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_refund_form():
	current_sess = session_utils.get_current_session()
	session_key = current_sess.get_value("session_id")


	body = dict(request.args)

	hotel_code = body.get("hotel_code",current_sess.get_value('hotel_code'))
	logging.info("Refund form for hotel_code: %s", hotel_code)

	identifier = body.get("identifier")
	order = body.get("order")

	return build_payments_section_html(hotel_code, identifier, session_key, is_refund=order)


@app.route("/pages/cobrador/payments_historic", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_payments_historic():
	current_sess = session_utils.get_current_session()

	try:
		session_key = current_sess.get_value("session_id")
		language = current_sess.get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	body = dict(request.args)

	identifier = body.get("identifier")
	payment_id = body.get("payment_id")
	error = body.get("error")
	hotel_code = body.get("hotel_code", current_sess.get_value('hotel_code'))
	logging.info("Historic of payments for hotel_code: %s", hotel_code)

	if error:
		response = {
			"status": "KO",
			"message": translations.get("T_MESSAGE_RESET_FAILED")}
		return response
	return build_payments_section_html(hotel_code, identifier, session_key, hide_form=True, payment_id=payment_id,
									   error=error)


def build_payments_section_html(hotel_code, identifier, session_key, hide_form=False, payment_id=None, error=None, is_refund=False,
								show_transactions_to_refund=False, type_payment=None):
	reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))
	if reservations:
		reservation = reservations[0]

		try:
			language = session_utils.get_current_session().get_value("language")
			translations = language_utils.get_web_dictionary(language)
		except:
			translations = language_utils.get_web_dictionary(False)

		try:
			extra_info = json.loads(reservation.get("extraInfo", "{}"))
		except:
			extra_info = {}

		default_currency = get_configuration_property_value(hotel_code, "Base Price Currency") or "EUR"
		currency = get_currency_symbol(extra_info.get("currency", default_currency))

		price_currency, paid_currency = get_reservations_currency(hotel_code, extra_info)

		info_summary = get_infor_reservation_summary(hotel_code, session_key, reservation)
		max_permited_amount = info_summary.get("total_pending")
		if is_refund:
			max_permited_amount = info_summary.get("total_payed")

		default_value_to_refund = ""
		order = ""
		transaction_sumary = ""
		payment_type = ""
		if is_refund:

			order = is_refund
			# get the max amount to refund
			filter_params = [('order', '=', order)]

			payments_list_by_cobrador = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=False)

			payments_list_by_cobrador = list(filter(lambda x: type(x.get('amount')) != str, payments_list_by_cobrador))

			if len(payments_list_by_cobrador) > 0:
				payment_type = payments_list_by_cobrador[0].get("type", "")
				if payments_list_by_cobrador[0].get("type") == PAYMENT_TYPE_LINK or payments_list_by_cobrador[0].get("amount") < 0:
					payments_list_by_cobrador[0]["amount"] = float(info_summary.get("total_payed").split(" ")[0])

			payments_list_by_link = _get_payments_by_link(reservation, translations={}, order=order, hotel_code=hotel_code)
			all_payments_list = payments_list_by_cobrador + payments_list_by_link

			identifier_has_prefix = False
			if get_hotel_web_config_item(hotel_code, PREFIX_BOOKING):
				new_identifier = convert_to_numeric_identifier(identifier)
				if new_identifier == order:
					identifier_has_prefix = True


			if all_payments_list:
				# (excluding devolutions)
				all_payments_list = list(filter(lambda x: x.get('amount') > 0, all_payments_list))
				if all_payments_list:
					default_value_to_refund = all_payments_list[0].get("amount")
					max_permited_amount = default_value_to_refund
			elif (order == identifier) or identifier_has_prefix:
				# maybe is an initial payment
				payed = float(extra_info.get("payed", "0"))
				if payed > 0:
					default_value_to_refund = payed
					max_permited_amount = default_value_to_refund

			transaction_sumary = get_payments_html_table_list(hotel_code, reservation, session_key,
															  show_transactions_to_refund=show_transactions_to_refund,
															  only_this_transaction=order, price_currency=price_currency, paid_currency=paid_currency)

		reservation_summary_html = get_resevation_summary_html(hotel_code, session_key, reservation)

		payments_list = ""
		if not is_refund:
			payments_list = get_payments_html_table_list(hotel_code, reservation, session_key,
														 show_transactions_to_refund=show_transactions_to_refund, price_currency=price_currency, paid_currency=paid_currency)

		request_params = request.values

		locale_language = request_params.get("language", 'default')
		rules = get_rules_by_type(hotel_code, "programmatically")
		if extra_info.get("exclude_programmatically_payment_cobrador_ids"):
			for reservation_rule in  extra_info.get("exclude_programmatically_payment_cobrador_ids"):
				for rule in rules:
					if str(rule.key.id) == str(reservation_rule):
						rule["selected"]="true"

		context = {
			'identifier': identifier,
			'payment_id': payment_id,
			'hide_form': hide_form,
			'currency': currency,
			'price_currency': price_currency,
			'paid_currency': paid_currency,
			'reservation_summary_html': reservation_summary_html,
			'hotel_code': hotel_code,
			'show_transactions_to_refund': show_transactions_to_refund,
			"order": order,
			"payment_type": payment_type,
			"transaction_sumary": transaction_sumary,
			'session_key': session_key,
			"table_list_html": payments_list,
			"error": error,
			"max_permited_amount": max_permited_amount,
			"default_value_to_refund": default_value_to_refund,
			"back_button": {"href": "/pages/cobrador/reservations?sessionKey=%s&language=%s" % (session_key, locale_language),
							"label": translations.get("T_VOLVER")},
			"rules": rules,
			"locale_language": locale_language,
			"has_avalon": hotel_has_avalon(hotel_code),
			"manual_payment_types": get_manual_payment_types(hotel_code)
		}

		if is_refund or show_transactions_to_refund:
			if request.args.get("is_extra"):
				context['is_extra_payment'] = True

			integration_name = get_integration_name(hotel_code)
			integration_config = get_payment_gateway_configuration(integration_name, hotel_code)
			if integration_config and integration_config.get("full_refund_mandatory"):
				context["full_refund_mandatory"] = True
			content = build_template('pages/cobrador/refund_form.html', context)
		elif type_payment:
			content = build_template('pages/cobrador/manual_payment.html', context)
		else:
			content = build_template('pages/cobrador/payment_form.html', context)

		return content

	return "ERROR"


def get_payments_html_table_list(hotel_code, reservation, session_key, show_transactions_to_refund=False,
								 only_this_transaction=None, price_currency="€", paid_currency="€"):

	current_sess = session_utils.get_current_session()

	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)

	except:
		translations = language_utils.get_web_dictionary(False)

	identifier = reservation.get("identifier")

	interface_cobrador = get_interface_controller(hotel_code)

	request_params = request.values

	locale_language = request_params.get("language", 'default')

	back_button = {"href": "/pages/cobrador/reservations?sessionKey=%s&language=%s" % (session_key, locale_language),
				   "label": translations.get("T_VOLVER")}


	filter_params = [('reservation_identifier', '=', identifier)]
	if only_this_transaction:
		filter_params.append(('order', '=', only_this_transaction))
		back_button = ""  # hide back button

	payments_list_by_cobrador = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=False)

	[payments_list_by_cobrador.update({"hotel_code": hotel_code}) for payments_list_by_cobrador in payments_list_by_cobrador]

	logging.info("payments found by cobrador: %s", len(payments_list_by_cobrador))
	# add posible first payment made by client directly in web
	initial_web_payment = []
	if reservation:
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)

				#Only for test (if we add manually a bank payment)
				if app.config['DEV']:
					order = extra_info.get('paymentOrderId', 0)
				else:
					order = interface_cobrador.get_initial_order_id_from_extra_info(extra_info)

				if extra_info.get("bono_gift_used"):  # There is a gift card and we add it
					gift_card = extra_info.get("original_price_before_discount", 0) - extra_info.get(
						"final_discounted_price", 0)

					if gift_card > 0:
						initial_web_payment.append({"timestamp": reservation.get("timestamp"), "order": order,
													"amount": gift_card,
													"user": translations.get("T_WEB_USER"),
													"reservation_identifier": identifier,
													"type": PAYMENT_TYPE_GIFT_CARD, "comments": ""
													})

				if extra_info.get("payed"):

					if extra_info.get("payment_from_modification"):
						order = extra_info['payment_from_modification'][0].get("payment_id")

					if not order:
						# normaly, first operation is the same as identifier (at least in sermepa)
						order = identifier
						if get_hotel_web_config_item(hotel_code, PREFIX_BOOKING):
							order = convert_to_numeric_identifier(order)

					history_payed = extra_info.get("modifications", {}).get("changes", {}).get("extraInfo", {}).get("payed", {})
					include_payment = True
					if history_payed and not history_payed.get("old_value") is None and history_payed.get("new_value") is None:
						include_payment = False

					if include_payment:
						initial_web_payment.append({"timestamp": reservation.get("timestamp"), "order": order,
													"amount": float(extra_info.get("payed")),
													"user": translations.get("T_WEB_USER"),
													"reservation_identifier": identifier,
													"type": PAYMENT_TYPE_IN_WEB, "comments": ""
													})

				# get payment made by tpv links
				if extra_info.get("payed_by_tpv_link"):
					initial_web_payment += _get_payments_by_link(reservation, translations=translations, hotel_code=hotel_code)

			except:
				logging.warning("Extra Info bad formed for: %s. Or not controlled found IMPOSSIBLE TO get initial_web_payment!",
							  reservation.get("identifier"))

	if show_transactions_to_refund or only_this_transaction:
		# only show transactions that could be refunded
		payments_list = []
		for initial_web in initial_web_payment:
			if initial_web_payment and initial_web.get("amount") > 0:
				payments_list = initial_web_payment
				break
			else:
				payments_list = []

		for payment in payments_list_by_cobrador:

			payment_error = payment.get("error", "")
			if not payment_error:
				payment_error = ""

			if not "error" in payment_error.lower():
				if payment.get("amount") == "":
					payment["amount"] = 0
				payments_list.append(payment)
	else:
		payments_list = initial_web_payment + payments_list_by_cobrador

	rules_all = get_rules_all(hotel_code)
	rules = {x.id: x for x in rules_all}
	index = 0
	total_payed = 0

	PAYMENT_TYPE_MAP = {
		"programmatically": "T_PAYMENT_PROGRAMMATICALLY",
		"credit_card_conditional": "T_PAYMENT_CREDIT_CART_CONDITIONAL",
		"create_token": "T_CREATE_TOKEN",
		"extra": "T_PAYMENT_EXTRA",
		"manual": "T_PAYMENT_MANUAL",
		"manual_extra": "T_PAYMENT_MANUAL_EXTRA",
		PAYMENT_TYPE_IN_WEB: "T_PAYMENT_IN_WEB",
		PAYMENT_TYPE_GIFT_CARD: "T_PAYMENT_GIFT_CARD",
		PAYMENT_TYPE_REFUND: "T_REFUND",
		PAYMENT_TYPE_REFUND_EXTRA: "T_REFUND_EXTRA",
		PAYMENT_TYPE_REFUND_PENDING: "T_REFUND_PENDING",
		PAYMENT_TYPE_REFUND_FAILED: "T_REFUND_FAILED"
	}

	for payment in payments_list:
		payment["index"] = index
		index += 1

		if not payment.get("error") and payment.get("type") != "Envío de link al cliente":
			total_payed += float(payment.get("amount", 0) or 0)

		payment_type = payment.get("type", "").lower()
		payment["type_txt"] = translations.get(PAYMENT_TYPE_MAP.get(payment_type, "")) or payment.get("type", "")

		if payment_type in ["manual", "manual_extra"]:
			amount = float(payment.get("amount", 0) or 0)
			has_exclusion = payment.get('excluded_rules')
			if amount == 0 and has_exclusion:
				if payment_type == "manual_extra" and not payment.get("deleted_amount"):
					payment["type_txt"] = translations.get("T_PAYMENT_EXCLUSION")
				elif payment_type == "manual":
					payment["type_txt"] = translations.get("T_PAYMENT_EXCLUSION")

		if payment.get("error"):

			# translate error
			try:
				error_code = payment["error"].split(": ")[1]
				payment["error_info"] = interface_cobrador.translate_error(error_code)
				payment["error_code"] = error_code
			except Exception as e:
				log.info("Not implemented translate error to this gateway")
				payment["error_info"] = payment["error"]

			payment["color_class_transaction"] = "pay_status_color_ko"
		elif float(payment.get("amount", 0) or 0) < 0:
			payment["color_class_transaction"] = "pay_status_color_oko"

		if payment.get("rule_id"):
			payment["rules_descripcion"] = f'{rules.get(int(payment.get("rule_id")), {}).get("description")} ( {payment.get("rule_id")} )'
			if not rules.get(int(payment.get("rule_id")), {}).get("description"):
				payment["rules_descripcion"] = f'( {payment.get("rule_id")} )'

	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	payments_list = sorted(payments_list, key=lambda k: k['timestamp'])
	context = {
		'price_currency': price_currency,
		'paid_currency': paid_currency,
		'hotel_code': hotel_code,
		'session_key': session_key,
		'payments_list': payments_list,
		"user": current_sess.get_value('user_name'),
		'show_transactions_to_refund': show_transactions_to_refund,
		"summary": {"total_payments": len(payments_list), "total_payed": float("{:.2f}".format(total_payed)), "total_extra_payed": float("{:.2f}".format(total_payed))},
		"back_button": back_button
	}

	content = build_template('pages/cobrador/payments_table_list.html', context)
	css_list = []
	jsLib_list = []
	js_list = []
	return content


@app.route("/pages/cobrador/do_payment", methods=['POST'])
@login_from_sessionKey
@login_using_referrer
def do_payment_in_reservation():
	current_sess = session_utils.get_current_session()
	hotel_code = current_sess.get_value('hotel_code')
	session_key = current_sess.get_value("session_id")
	body = dict(request.form)
	if not body and Config.DEV:
		body = {x.split("=")[0]: x.split("=")[1] for x in request.data.decode("utf-8").split("&")}
	hotel_code = body.get("hotel_code",current_sess.get_value('hotel_code'))
	identifier = body.get("identifier")
	logging.info("[%s] New payment in hotel_code %s" %(identifier,hotel_code))
	try:
		language = current_sess.get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)
	today_ts = datetime.now()
	today_ts_txt = date_to_string(today_ts, format="%Y-%m-%d %H:%M:%S")
	excluded_rules = request.form.getlist('rules')

	if 'all' in excluded_rules:
		rules = [str(x.id) for x in get_rules_by_type(hotel_code, "programmatically")]
		excluded_rules = rules
	elif excluded_rules:
		excluded_rules = excluded_rules[0].split("||")



	interface_to_implement = get_interface_controller(hotel_code)
	config = interface_to_implement.get_configuration(hotel_code)
	time_payments_control = extract_payments_time_control(config)

	if not app.config.get("DEV") and exit_payment_in_reservation_in_short_period(today_ts_txt, identifier, hotel_code, time_payments_control):
		logging.warning("[%s] there is a payment in a short period of time  hotel_code %s" %(identifier, hotel_code))

		response = {
			"status": "KO",
			"message": translations.get("T_PAYMENT_ERROR_TIME")}
		return response


	extra_payment = body.get("extra_payment", "") == "on"

	logging.info("[%s] Payload payment: %s" %(identifier, body))

	payed_in_this_transaction = float(body.get("amount", "").replace(",", "."))

	reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))
	if reservations:
		reservation = reservations[0]
		order = execute_payment_in_gateway(hotel_code, reservation, payed_in_this_transaction, body)

		payment_type = "manual"
		if extra_payment:
			payment_type = "extra"

		extra_info = {}
		if isinstance(order, dict):
			# extra_info = {"pasref": order.get('pasref'), "authcode": order.get('authcode')}
			extra_info = copy.deepcopy(order)
			if order.get("order_id"):
				order = order.get("order_id")

		if isinstance(order, str):
			order = order.upper()

		error = None
		if "ERROR" in order:
			info_error = order.split(SEPARATOR_INFO_ERROR_ORDER)
			if len(info_error) > 1:
				order = info_error[1]
				error = info_error[0]
			else:
				error = order

			# be sure ALWAYS SEND AN ERROR!!!!!
			if not error:
				error = "UNKNOWN ERROR"

		payment_params = {
			"order": order,
			"error": error,
			"comments": body.get("comments", "").replace("\n", "<br>"),
			"type": payment_type,
			"user": current_sess.get_value('user_name'),
			"reservation_identifier": identifier,
			"amount": payed_in_this_transaction,
			"timestamp": today_ts_txt,
			"extra_info": extra_info,
			"excluded_rules": excluded_rules
		}


		send_customer_email = body.get("customer_email", "") == "on"
		send_hotel_email = body.get("hotel_email", "") == "on"
		comment_email = body.get("comment_email", "")

		send_modification_email = body.get("send_modification_email", "") == "on"

		response = _save_and_send_payments_confirmations(hotel_code, reservation, payment_params,payed_in_this_transaction, extra_payment,send_customer_email,send_modification_email, comment_email, manual_payment=True, send_hotel_email=send_hotel_email)







		return response


def _save_and_send_payments_confirmations(hotel_code, reservation, payment_params, payed_in_this_transaction,
										  extra_payment, send_customer_email, send_modification_email, comment_email, avoid_sending_payment=False, manual_payment=False, is_refund=False, send_hotel_email=True):

	current_sess = session_utils.get_current_session()
	try:
		language = current_sess.get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	save_payment_reservation(hotel_code, payment_params)

	order = payment_params.get("order")
	error = payment_params.get("error")

	if not "ERROR" in order and not error:
		is_a_pending_refund = payment_params.get("extra_params", {}).get("refund_pending")

		# update reservation and send email
		programmatically_payment = None
		if payment_params.get("type") == PAYMENT_TYPE_PROGRAMATICALLY:
			programmatically_payment = payment_params.get("rule_id")

		reservation_updated_id, total_payed_by_cobrador, total_extra_payed_by_cobrador = save_payment_info_in_reservation(hotel_code, reservation,
																payed_in_this_transaction, payment_params.get("order"),
																programmatically_payment=programmatically_payment,
																excluded_rules=payment_params.get("excluded_rules"), extra_payment=extra_payment, is_a_refund=is_refund, is_a_pending_refund=is_a_pending_refund)

		if comment_email:
			comment_email = comment_email.replace("\n", "<br>")

		if is_a_pending_refund:
			process_pending_refund(hotel_code, payment_params)
		else:
			response = send_payment_confirmation_emails(hotel_code, reservation.get('identifier'), comment_email, send_customer_email, send_hotel_email, send_modification_email,
											payed_in_this_transaction, order, manual_payment, is_refund, avoid_sending_payment, translations, reservation=reservation)
			if response:
				return response

		response = {
			"status": "OK",
			"message": translations.get("T_successful_charge") if not is_refund else translations.get("T_successful_refund"),
			"new_total_payed_by_cobrador": total_payed_by_cobrador,
			"new_total_extra_payed_by_cobrador": total_extra_payed_by_cobrador
		}

	else:
		response = {
			"status": "KO",
			"message": translations.get("T_unsuccessful_charge") if not is_refund else translations.get("T_unsuccessful_refund")
		}
	return response

@retry(Exception, tries=4, delay=3)
def save_payment_reservation(hotel_code, payment_params):
	save_secure_payment_reservation(payment_params, hotel_code)
	return True

@app.route("/pages/cobrador/do_refund", methods=['POST'])
@login_from_sessionKey
@login_using_referrer
def do_refund_in_reservation():
	try:
		current_sess = session_utils.get_current_session()

		session_key = current_sess.get_value("session_id")

		body = dict(request.form)
		if not body and Config.DEV:
			body = {x.split("=")[0]: x.split("=")[1] for x in request.data.decode("utf-8").split("&")}
		hotel_code = body.get("hotel_code", current_sess.get_value('hotel_code'))
		identifier = body.get("identifier")
		logging.info("[%s] New refund in hotel_code %s" % (identifier, hotel_code))

		excluded_rules = request.form.getlist('rules')
		if 'all' in excluded_rules:
			rules = [str(x.id) for x in get_rules_by_type(hotel_code, "programmatically")]
			excluded_rules = rules
		elif excluded_rules:
			excluded_rules = excluded_rules[0].split("||")

		response = process_refund(hotel_code, body, identifier,current_sess = current_sess, excluded_rules = excluded_rules)
		if response.get("status") == "KO":
			logging.warning("Wrong external refund %s  %s in reservation %s" % (
			hotel_code, body.get("order"), body.get("identifier")))
			response["message"] = "An error has occurred in the refund"
		return response

	except Exception as e:
		logging.error("ERROR during the refund %s in reservation %s" % (body.get("order"), body.get("identifier")))
		logging.error("ERROR %s", e)
		return redirect(
			get_manager_2_url_bis() + "/pages/cobrador/payments_historic?identifier=%s&error=payment&sessionKey=%s" % (
				identifier, session_key))


@app.route("/pages/cobrador/do_refund_external", methods=['GET'])
def do_refund_external_in_reservation():
	try:
		body = dict(request.form)
		if not body:
			body = extract_params_from_request(request)

		hotel_code = body.get('hotel_code')

		identifier = body.get("identifier")
		if not identifier:
			identifier = body.get("order")
		logging.info("[%s] New external refund in hotel_code %s" % (identifier, hotel_code))

		response = process_refund(hotel_code, body, identifier)
		if response.get("status") == "KO":
			logging.info("Successful external refund %s  %s in reservation %s" % (hotel_code, body.get("order"), body.get("identifier")))
			response["message"] = "An error has occurred in the external refund"
		return response


	except Exception as e:
		logging.error("ERROR during the EXTERNAL refund %s  %s in reservation %s" % (hotel_code, body.get("order"), body.get("identifier")))
		logging.error("ERROR %s", e)
		return make_response("Internal server error", 500)


@retry(Exception, tries=4, delay=3)
def save_payment_info_in_reservation(hotel_code, reservation, payed_in_this_transaction, order, is_a_refund=False,
									 programmatically_payment=None, excluded_rules=None, extra_payment=False, is_a_pending_refund=False):
	if is_a_pending_refund:
		payed_in_this_transaction = 0

	extra_info = reservation.get("extraInfo", "{}")
	if extra_info:
		try:
			extra_info = json.loads(extra_info)
		except:
			logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
						  reservation.get("identifier"))

			return ""

		reservation_id = int(reservation.key.id)
		accumulated_payed = float(extra_info.get("payed_by_cobrador", "0"))
		accumulated_extra_payed = float(extra_info.get("extra_payed_by_cobrador", "0"))
		if extra_payment:
			already_extra_payed = float(extra_info.get("extra_payed_by_cobrador", "0"))
			accumulated_extra_payed = already_extra_payed + payed_in_this_transaction

			logging.info("TPV OK: Already extra_payed_by_cobrador: %s, payed in this transaction : %s accumulated_payed: %s",
						 already_extra_payed, payed_in_this_transaction, accumulated_extra_payed)

			extra_info["extra_payed_by_cobrador"] = accumulated_extra_payed
		else:
			already_payed = float(extra_info.get("payed_by_cobrador", "0"))
			accumulated_payed = already_payed + payed_in_this_transaction

			logging.info("TPV OK: Already payed_by_cobrador: %s, payed in this transaction : %s accumulated_payed: %s",
						 already_payed, payed_in_this_transaction, accumulated_payed)

			extra_info["payed_by_cobrador"] = accumulated_payed
		if excluded_rules:
			extra_info["exclude_programmatically_payment_cobrador_ids"] = excluded_rules
		if programmatically_payment:
			if extra_info.get("programmatically_payment_cobrador_ids"):
				extra_info['programmatically_payment_cobrador_ids'].append(str(programmatically_payment))

			else:
				extra_info['programmatically_payment_cobrador_ids'] = [str(programmatically_payment)]

		if not is_a_refund:
			extra_info["last_merchant_order_used"] = order


		#we have to change modificationTimestamp becasue it has to be intergrated!
		today_ts = datetime.now()
		modification_timestamp = date_to_string(today_ts, format="%Y-%m-%d %H:%M:%S")
		extra_info["lastPaymentTimestamp"] = modification_timestamp
		reservation["modificationTimestamp"] = modification_timestamp

		#If a pending reservation has a real payment, it's normal to convert the reservstion to confirm
		if extra_info.get("status_reservation") and extra_info.get("status_reservation") == "pending" and not is_a_refund:
			extra_info["status_reservation"] = "confirmed"

		reservation["extraInfo"] = json.dumps(extra_info)

		reservation_updated_id = save_reservation_and_flush_cache(reservation, hotel_code, reservation_id=reservation_id)

		logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
					 reservation_id)

		return reservation_updated_id, accumulated_payed, accumulated_extra_payed


def get_infor_reservation_summary(hotel_code, session_key, reservation):
	total_price = 0
	total_payed = 0
	total_extra_payed = 0
	total_pending = 0
	price_currency, paid_currency = "€", "€"
	if reservation:
		total_price = float(reservation.get("price", "0")) + float(reservation.get("priceSupplements", "0"))

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
				if get_configuration_property_value(hotel_code, "include accommodation tax in pep"):
					try:
						total_price += float(extra_info.get("price_info").get("taxes").get("accommodation").get("value"))
					except:
						logging.warning(f"Error getting accommodation tax for reservation: {reservation.get('identifier')}")
				if extra_info.get("payed"):
					history_payed = extra_info.get("modifications", {}).get("changes", {}).get("extraInfo", {}).get("payed", {})
					include_payment = True
					if history_payed and history_payed.get("old_value") is None and history_payed.get("new_value") is None:
						include_payment = False

					if include_payment:
						total_payed = float(extra_info.get("payed"))

				if extra_info.get("payed_by_cobrador"):
					total_payed += float(extra_info.get("payed_by_cobrador"))

				if extra_info.get("extra_payed_by_cobrador"):
					total_extra_payed += float(extra_info.get("extra_payed_by_cobrador"))

				if extra_info.get("bono_gift_used"):  # It has gift card
					gift_card = extra_info.get("original_price_before_discount", 0) - extra_info.get("final_discounted_price", 0)

					if gift_card > 0:
						total_payed += gift_card

				if extra_info.get("payed_by_tpv_link"):
					for payment_by_link in extra_info["payed_by_tpv_link"]:
						if payment_by_link.get('datatransdata'):
							continue
						total_payed += float(payment_by_link.get("amount"))
				# NO USAR: relentiza todo y puede duplicar el total paid ya que payed_by_cobrador lo debe llevar ya!
				#total_payed = add_web_payments_to_total_payed(reservation, hotel_code, total_payed)

				price_currency, paid_currency = get_reservations_currency(hotel_code, extra_info)

			except:
				logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO get extra Info!",
							  reservation.get("identifier"))
				return redirect(
					get_manager_2_url_bis() + "/pages/cobrador/payments_historic?error=true&sessionKey=%s" % session_key)

			total_pending = total_price - total_payed
			if total_pending < 0:
				total_pending = 0

		context = {
			"identifier": reservation.get("identifier"),
			'customer': reservation.get("name") + " " + reservation.get("lastName"),
			"email": reservation.get("email"),
			"total_price": "{:.2f} {}".format(total_price, price_currency),
			"total_payed": "{:.2f} {}".format(total_payed, paid_currency),
			"total_extra_payed": "{:.2f} {}".format(total_extra_payed, paid_currency),
			"total_pending": "{:.2f} {}".format(total_pending, paid_currency),
		}

		return context

	return {}


def get_resevation_summary_html(hotel_code, session_key, reservation):
	context = get_infor_reservation_summary(hotel_code, session_key, reservation)

	content = build_template('pages/cobrador/reservation_summary.html', context)
	css_list = []
	jsLib_list = []
	js_list = []
	return content


@retry(Exception, tries=4, delay=3)
def send_payment_confirmation_email_to_customer(hotel_code, reservation, comments, amount, manual_payment=False, addressee="", is_refund=False, payment_Id="0"):
	try:
		language_hotel = session_utils.get_current_session().get_value("language")
		translations_hotel = language_utils.get_web_dictionary(language_hotel)
	except:
		translations_hotel = language_utils.get_web_dictionary(False)
		language_hotel = "SPANISH"

	try:
		language_client = reservation.get("language")
		translations_client = language_utils.get_web_dictionary(language_client)


	except:
		language_client = "SPANISH"
		translations_client = language_utils.get_web_dictionary(False)

	html_content_client, title_client = email_to_customer(hotel_code, reservation, comments, amount, translations_client, language_client, manual_payment=manual_payment, is_refund=is_refund, paymentId=payment_Id)
	html_content_hotels, title_hotel = email_to_customer(hotel_code, reservation, comments, amount, translations_hotel, language_hotel, manual_payment=manual_payment, is_refund=is_refund, paymentId=payment_Id)
	logging.info("[EMAIL] Sending payment confirmation to customer!")

	emails = reservation.get("email")
	special_email = get_special_hotel_email(hotel_code)

	email_sender_config = get_configuration_property_value(hotel_code, EMAIL_SENDER_CONFIG)
	email_sender = build_email_sender_from_hotel_webs_config(email_sender_config, backup_sender="<EMAIL>")

	if addressee == "customer":
		sendEmail(emails, title_client, "", html_content_client, sender=email_sender, backup_sender="<EMAIL>")
	if addressee == "hotel":
		sendEmail(special_email, title_hotel, "", html_content_hotels, sender=email_sender, backup_sender="<EMAIL>")
	logging.info("email sent correctly to %s", emails)


def send_error_payment_email_to_the_hotel(hotel_code, reservation, amount, res_payment, transaction_type="payment"):
	logging.info("[EMAIL] Sending ERROR of payment to hotel!")
	configuration_payment_seeker = get_config_payment_seeker_configuration(hotel_code)
	try:
		if session_utils.get_current_session():
			language = session_utils.get_current_session().get_value("language")
		else:
			language = reservation.get("language")
			if not language:
				language = "SPANISH"
		if configuration_payment_seeker.get("language_management_for_error_email_payment", "").lower() == "true":
			language = get_configuration_property_value(hotel_code, "Language Management")
		hotel = get_hotel_by_application_id(hotel_code)
		translations = language_utils.get_web_dictionary(language, hotel=hotel)
	except:
		hotel = get_hotel_by_application_id(hotel_code)
		translations = language_utils.get_web_dictionary(False)
		language = reservation.get("language")
		if not language:
			language = "SPANISH"

	custom_section_translations = get_web_section(hotel, configuration_payment_seeker.get("notifications email custom text section"), language, set_languages=True)

	email_sender_config = get_configuration_property_value(hotel_code, EMAIL_SENDER_CONFIG)
	email_sender = build_email_sender_from_hotel_webs_config(email_sender_config, backup_sender="<EMAIL>")

	special_email = get_special_hotel_email(hotel_code)
	exclude_email_notify_error = get_excluded_email_to_notify_error(hotel_code)

	if exclude_email_notify_error:
		exclude_email_notify_error = exclude_email_notify_error.split(";")
		special_email = ";".join([email for email in special_email.split(";") if email not in exclude_email_notify_error])

	logo_type = ""
	filter_logo = "survey"
	if configuration_payment_seeker and configuration_payment_seeker.get("logo_name"):
		filter_logo = configuration_payment_seeker.get("logo_name")
	logos = get_hotel_logotypes(hotel_code, filter_logo)
	if logos:
		logo_type = logos[0]

	header_image = GENERIC_ERROR_PAYMENT_IMAGE_EMAIL
	if configuration_payment_seeker.get("error_transaction_header_image"):
		header_image = "/static/images/cobrador/%s" % configuration_payment_seeker.get("error_transaction_header_image")
	if transaction_type == "payment":
		title = translations.get("T_TITLE_EMAIL_PAYMENT_ERROR") + reservation.get("identifier")
		info_error = "%s %s" % (translations.get("T_PAYMENT_ERROR"), translations.get("T_MORE_ERROR_DETAILS"))
	else:
		title = translations.get("T_TITLE_EMAIL_REFUND_ERROR") + reservation.get("identifier")
		info_error = translations.get("T_REFUND_ERROR")

	template_dict = {"title": title,
					 "body": translations.get("T_HOLA"),
					 "info_payment": info_error + " " + translations.get(
						 "T_PAYMENT_AMOUNT") + ": " + str(amount),
					 "comment": res_payment,
					 "header_image": header_image,
					 "logo_type": logo_type
					 }

	html_content = build_template('pages/cobrador/emails/email_payment_error_hotel.html', template_dict,
								  force_language=language)

	subject_email = translations.get("T_TITLE_EMAIL_PAYMENT_ERROR")
	if custom_section_translations and custom_section_translations.get("T_error_new_payment_email_subject"):
		subject_email = custom_section_translations.get("T_error_new_payment_email_subject")

	title = subject_email + " " + reservation.get("identifier")

	email_hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
	if email_hotel_name:
		title += " " + email_hotel_name

	sendEmail(special_email, title, "", html_content, sender=email_sender, backup_sender="<EMAIL>")
	logging.info("email sent correctly to %s", special_email)


def send_error_payment_email_to_the_customer(hotel_code, reservation, amount, res_payment, transaction_type="payment",body=None):
	logging.info("[EMAIL] Sending ERROR of payment to customer!")
	configuration_payment_seeker = get_config_payment_seeker_configuration(hotel_code)
	try:
		if session_utils.get_current_session():
			language = session_utils.get_current_session().get_value("language")
		else:
			language = reservation.get("language")
			if not language:
				language = "SPANISH"
		if configuration_payment_seeker.get("language_management_for_error_email_payment", "").lower() == "true":
			language = get_configuration_property_value(hotel_code, "Language Management")
		hotel = get_hotel_by_application_id(hotel_code)
		translations = language_utils.get_web_dictionary(language, hotel=hotel)
	except:
		hotel = get_hotel_by_application_id(hotel_code)
		translations = language_utils.get_web_dictionary(False)
		language = reservation.get("language")
		if not language:
			language = "SPANISH"

	custom_section_translations = get_web_section(hotel, configuration_payment_seeker.get("notifications email custom text section"), language, set_languages=True)
	email_sender_config = get_configuration_property_value(hotel_code, EMAIL_SENDER_CONFIG)
	email_sender = build_email_sender_from_hotel_webs_config(email_sender_config,backup_sender="<EMAIL>")

	logo_type = ""
	filter_logo = "survey"
	if configuration_payment_seeker and configuration_payment_seeker.get("logo_name"):
		filter_logo = configuration_payment_seeker.get("logo_name")
	logos = get_hotel_logotypes(hotel_code, filter_logo)
	if logos:
		logo_type = logos[0]
	if transaction_type == "payment":
		subject_email = translations.get("T_TITLE_EMAIL_PAYMENT_ERROR") + reservation.get("identifier")

		if custom_section_translations and custom_section_translations.get("T_error_new_payment_email_subject"):
			subject_email = custom_section_translations.get("T_error_new_payment_email_subject") + reservation.get("identifier")
		info_error = translations.get("T_PAYMENT_ERROR")
	else:
		subject_email = translations.get("T_TITLE_EMAIL_REFUND_ERROR") + reservation.get("identifier")
		info_error = translations.get("T_REFUND_ERROR")

	email_hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
	if email_hotel_name:
		subject_email += " " + email_hotel_name

	special_email = reservation.get("email")
	body_message = translations.get("T_NEW_PAYMENT_EMAIL_BODY")
	comments = body.get("comment_email")
	if not comments:
		comments = translations.get("T_NO_COMMENTS")

	if transaction_type == "payment":
		info_payment = info_error + " " + translations.get("T_PAYMENT_AMOUNT") + ": " + str(amount)
	else:
		info_payment = info_error + " " + translations.get("T_REFUND_AMOUNT") + ": " + str(amount)

	not_show_generic_image = configuration_payment_seeker.get("not show generic image")
	generic_image_path = GENERIC_ERROR_PAYMENT_IMAGE_EMAIL
	if configuration_payment_seeker.get("error_transaction_header_image"):
		generic_image_path = "/static/images/cobrador/%s" % configuration_payment_seeker.get("error_transaction_header_image")
	template_dict = {
		'telephone': configuration_payment_seeker.get("telephone"),
		'email': configuration_payment_seeker.get("email"),
		'greeting_text': translations.get('T_greetings').replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo", hotel_code)),
		'main_text': body_message.replace("@@cliente@@",reservation.get("name") + " " + reservation.get("lastName")),'hi_customer': '%s %s %s,' % (
		translations.get('T_HOLA'), reservation.get('name'), reservation.get('lastName')),
		'color1': configuration_payment_seeker.get("color link html", '#967E2C'),
		"logo_type": logo_type,
		"info_payment": info_payment,
		"comment": comments,
		"not_show_generic_image": not_show_generic_image,
		"generic_image_path": generic_image_path
	}

	html_content = build_template('pages/cobrador/emails/email_payment_error_customer.html',template_dict,force_language=language)
	sendEmail(special_email, subject_email, "", html_content, sender=email_sender, backup_sender="<EMAIL>")
	logging.info("email sent correctly to %s", special_email)

def get_special_hotel_email(hotel_code):
	interface_to_implement = get_interface_controller(hotel_code)
	config = interface_to_implement.get_configuration(hotel_code)
	reservation_emails = get_configuration_property_value(hotel_code, 'Email reservas')
	special_hotel_email = config["special_email"] if config.get("special_email") else reservation_emails
	return special_hotel_email

def get_excluded_email_to_notify_error(hotel_code):
	interface_to_implement = get_interface_controller(hotel_code)
	config = interface_to_implement.get_configuration(hotel_code)
	excluded_emails = config["exclude_email_notify_error"] if config.get("exclude_email_notify_error") else []
	return excluded_emails


def execute_payment_in_gateway(hotel_code, reservation, amount, body):
	logging.info("[EXECUTE PAYMENT] Exceuting payment in gateway! hotel_code: %s", hotel_code)

	payment_types = get_integration_cobrador_name(hotel_code)
	if payment_types:
		for x in payment_types.split(";"):
			gateway_configuration = get_payment_gateway_configuration(x, hotel_code)
			if "ONLY_CARD" in x:
					continue
			if not gateway_configuration:
				sendEmailCache(f"No gateway configuration for {x} in {hotel_code}", f"ERROR GATEWAY CONFIG PROGRAMATICALLY {x} - {hotel_code}", "<EMAIL>")
	else:
		sendEmailCache(f"No gateway configuration: {hotel_code}", f"ERROR GATEWAY CONFIG PROGRAMATICALLY - {hotel_code}", "<EMAIL>")

	interface_to_implement = get_interface_controller_programation(hotel_code, reservation)

	if interface_to_implement:
		try:
			try:
				func_data = inspect.signature(interface_to_implement.execute_payment_in_gateway)
				arguments_list = list(func_data.parameters.values())
				arguments_list = [i.name for i in arguments_list]
			except Exception as e:
				logging.info("error checking execute payment")
				arguments_list = []

			if "extra_data" in arguments_list:
				res_payment = interface_to_implement.execute_payment_in_gateway(hotel_code, reservation, amount, extra_data=body)
			else:
				res_payment = interface_to_implement.execute_payment_in_gateway(hotel_code, reservation, amount)

			logging.info("[RESPONSE] PAYMENT FROM GATEWAY: %s", res_payment)
		except Exception as e:
			logging.critical("[%s] Critical error in payment: %s" %(reservation.get("identifier"),e))
			logging.critical("[%s] Critical error in payment Traceback: %s" % (reservation.get("identifier"), traceback.format_exc()))
			res_payment = "ERROR"

		if "ERROR" in res_payment:
			# TODO: traducir el error para el email
			res_payment = interface_to_implement.translate_error(res_payment)
			send_error_payment_email_to_the_hotel(hotel_code, reservation, amount, res_payment)

			customer_email = True if body.get("send_customer_error_email") == "on" else False
			if customer_email:
				send_error_payment_email_to_the_customer(hotel_code, reservation, amount, res_payment, body=body)
			logging.warning("[%s] Error!!! It cannot be posible to execute the payment!!!", reservation.get("identifier"))
			if "ERROR" not in res_payment:
				# 	IMPORTANTE NUNCA QUITAR ESTO, SIEMPRE QUE ES UN ERROR, DEBE DEVOLVER "ERROR
				res_payment = "ERROR||" + res_payment

		else:
			logging.info("SUCCESSFUL PAYMENT! The payment has been executed in reservation %s", reservation.get("identifier","") )
		return res_payment

	return "ERROR"


def force_gateway_in_request(payment_type):
	original_args = dict(request.args)
	original_values = dict(request.values)

	original_args["force_gateway"] = payment_type
	original_values["force_gateway"] = payment_type

	request.args = ImmutableMultiDict(mapping=original_args)
	request.values = CombinedMultiDict(dicts=[original_values])


def execute_refund_in_gateway(hotel_code, body, reservation, amount, order_to_refund="", payment=None):
	logging.info("[EXECUTE REFUND] Executing refund in gateway! hotel_code: %s", hotel_code)

	interface_to_implement = None
	extra_info = json.loads(reservation.get("extraInfo"))
	payment_type_used = extra_info.get("payment_gateway_used")
	if payment_type_used:
		interface_to_implement = get_interface_to_implement(payment_type_used)
		if interface_to_implement:
			force_gateway_in_request(payment_type_used)

			logging.info("execute_refund_in_gateway payment_type_used in reservation: %s", payment_type_used)


	if not interface_to_implement:

		interface_to_implement = get_interface_controller(hotel_code)
		integration_name = extra_info.get("payment_gateway", extra_info.get('payment_gateway_name'))
		if integration_name:
			interface_to_implement = get_interface_to_implement(integration_name)

	if interface_to_implement:
		try:
			if payment:
				res_payment = interface_to_implement.execute_refund_in_gateway(hotel_code, reservation, amount,
																			   order_to_refund, payment=payment)
			else:
				res_payment = interface_to_implement.execute_refund_in_gateway(hotel_code, reservation, amount,
																			   order_to_refund)
		except Exception as e:
			logging.critical("[%s] Critical error in refund: %s" %(reservation.get("identifier"),e))
			res_payment = "ERROR"

		logging.info("[REFUND RESPONSE]: %s", res_payment)
		extra_params = {}
		if isinstance(res_payment, dict):
			extra_params = res_payment.get("extra_params")
			res_payment = res_payment.get("order_id")
		if "ERROR" in res_payment:
			# TODO: traducir el error para el email
			res_payment_email = interface_to_implement.translate_error(res_payment)
			send_error_payment_email_to_the_hotel(hotel_code, reservation, amount, res_payment_email, transaction_type="refund")

			customer_email = True if body.get("send_customer_error_email") == "on" else False
			if customer_email:
				send_error_payment_email_to_the_customer(hotel_code, reservation, amount, res_payment_email, transaction_type="refund", body=body)
		if extra_params.get('refund_pending'):
			logging.info("[PENDING REFUND] The refund is pending to be executed in the gateway")
			return {
				"order_id": res_payment,
				"extra_params": extra_params
			}

		return res_payment

	logging.error("[ERROR]: Missing configuration of cobrador!!!")
	return "ERROR||Please set a configuration for cobrador"


def process_refund(hotel_code, body, identifier, current_sess=None, excluded_rules = None):
	order_to_refund = body.get("order")

	filter_params = [('order', '=', order_to_refund)]
	payment = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=True)

	payed_in_this_transaction = -1 * float(body.get("amount", "").replace(",", "."))
	amount_to_be_refund = -1 * payed_in_this_transaction

	if payment:
		payment = payment[0]

	reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))

	# today_ts = datetime.now()
	today_ts_txt = get_specific_timestamp(hotel_code, 'Specific timezone')
	if reservations:
		reservation = reservations[0]

		extra_info = {}
		if reservation.get("extra_info"):
			extra_info = json.loads(reservation.get("extra_info"))

		hotel_code_origin = hotel_code
		if extra_info.get("origin_hotel_code"):
			hotel_code_origin = extra_info.get("origin_hotel_code")

			logging.info("Changed hotel code: %s %s", identifier, hotel_code_origin)

		logging.info("[%s] Payload refund: %s" % (identifier, body))
		order = execute_refund_in_gateway(hotel_code_origin, body, reservation, amount_to_be_refund,
										  order_to_refund=order_to_refund,
										  payment=payment)

		extra_info = {}
		extra_params = {}
		if isinstance(order, dict):
			# extra_info = {"pasref": order.get('pasref'), "authcode": order.get('authcode')}
			extra_info = copy.deepcopy(order)
			extra_params = order.get("extra_params", {})
			order = order.get("order_id")

		payment_type = PAYMENT_TYPE_REFUND

		extra_payment = body.get("extra_payment", "") == "on"

		if extra_payment:
			payment_type = PAYMENT_TYPE_REFUND_EXTRA

		error = None
		if "ERROR" in order:
			info_error = order.split(SEPARATOR_INFO_ERROR_ORDER)
			if len(info_error) > 1:
				order = info_error[1]
				error = info_error[0]
			else:
				error = order

		comments = body.get("comments", "").replace("\n", "<br>")

		user = "External"
		if current_sess:
			user = current_sess.get_value('user_name')

		if body.get("automatic_refund"):
			user = "Web automatic refund"
			payment_type = PAYMENT_TYPE_REFUND_AUTOMATIC
			comments = "%s for reservation %s" % (PAYMENT_TYPE_REFUND_AUTOMATIC, identifier)

		if extra_params.get("refund_pending"):
			payment_type = PAYMENT_TYPE_REFUND_PENDING
			comments = "%s for reservation %s" % (PAYMENT_TYPE_REFUND_PENDING, identifier)

		payment_params = {
			"order": order,
			"error": error,
			"comments": comments,
			"type": payment_type,
			"user": user,
			"reservation_identifier": identifier,
			"amount": payed_in_this_transaction,
			"timestamp": today_ts_txt,
			"extra_info": extra_info,
			"excluded_rules": excluded_rules,
			"extra_params": extra_params
		}

		send_customer_email = body.get("customer_email", "") == "on"
		send_hotel_email = body.get("hotel_email", "") == "on"
		send_modification_email = body.get("send_modification_email", "") == "on"
		comment_email = body.get("comment_email", "")
		response = _save_and_send_payments_confirmations(hotel_code, reservation, payment_params,
														 payed_in_this_transaction, extra_payment,
														 send_customer_email, send_modification_email, comment_email,
														 is_refund=True, send_hotel_email=send_hotel_email)
	else:
		logging.info("Not reservation %s found, please check it af first!!", identifier)
		response = {
			"status": "KO",
		}

	return response

def get_interface_controller(hotel_code):
	interface_to_implement = None
	payment_type = get_integration_cobrador_name(hotel_code)
	# payment_type = get_configuration_property_value(hotel_code, 'Use Payment Gateway')
	logging.info("get_interface_controller: %s", payment_type)

	interface_to_implement = get_interface_to_implement(payment_type)

	return interface_to_implement


def get_interface_controller_programation(hotel_code, reservation):
	interface_to_implement = None


	extra_info = json.loads(reservation.get("extraInfo"))
	payment_type = extra_info.get("payment_gateway_used")
	if payment_type:
		interface_to_implement = get_interface_to_implement(payment_type)
		if interface_to_implement:
			logging.info("get_interface_controller_programation payment_gateway_used: %s", payment_type)

			#and trick it in requests for the future
			force_gateway_in_request(payment_type)

			return interface_to_implement

	payment_types = get_integration_cobrador_name(hotel_code)
	for payment_type in payment_types.split(";"):
		interface_to_implement = get_interface_to_implement(payment_type)
		if interface_to_implement and interface_to_implement.reservation_has_token(reservation):
			logging.info("get_interface_controller_programation: %s", payment_types)
			return interface_to_implement

	return interface_to_implement


def get_interface_to_implement(payment_type):
	interface_to_implement = None
	if "RESORTCOM" in payment_type:
		interface_to_implement = ResortcomControler()

	if "SERMEPA" in payment_type:
		interface_to_implement = SermepaControler()

	if "ADDON_PAYMENTS" in payment_type:
		interface_to_implement = AddonsController()

	if "PAYLANDS" in payment_type:
		interface_to_implement = PaylandsController()

	if "CECA" in payment_type:
		interface_to_implement = CecaController()

	if "SIBS2.0" in payment_type:
		return SIBS2Controller()

	if "SIBS" in payment_type:
		interface_to_implement = SibsController()

	if "ADDONS2.0" in payment_type:
		interface_to_implement = Addons2GatewayController()

	if "WORLDLINE" in payment_type:
		interface_to_implement = WorldLineGatewayController()

	if "WORLDLINE_EU" in payment_type:
		interface_to_implement = WorldLineEuGatewayController()

	if "WORLDLINE_EU2" in payment_type:
		interface_to_implement = WorldLineEu2GatewayController()

	if "STRIPE" in payment_type:
		interface_to_implement = StripeController()

	if "OPENPAY" in payment_type:
		interface_to_implement = OpenpayController()

	if "PAYBYRD" in payment_type:
		interface_to_implement = PaybyrdController()

	if "SANTANDER PAYLINKS" in payment_type:
		interface_to_implement = SantanderController()

	if "TRUST" in payment_type:
		interface_to_implement = TrustController()

	if "EVO" in payment_type:
		if "EVO_SESSIONS" in payment_type:
			interface_to_implement = EvoSessionsControler()
		else:
			interface_to_implement = EvoControler()

	if "PAY-U INSITE" in payment_type:
		interface_to_implement = PayuInsiteController()

	if "WOMPI" in payment_type:
		interface_to_implement = WompiController()

	if "SCALAPAY" in payment_type:
		interface_to_implement = ScalapayController()

	'''if "AFFIRM" in payment_type:
		interface_to_implement = AffirmController()'''

	if "W2M" in payment_type:
		interface_to_implement = W2MController()

	if "BANORTE" in payment_type:
		interface_to_implement = BanorteController()

	if "ADYEN" in payment_type:
		interface_to_implement = AdyenController()

	return interface_to_implement


def send_reservation_to_adapter(hotel_code, identifier, avoid_sending_payment=False,payed_in_this_transaction=None, payment_order_id=None):
	logging.info("[%s] send_reservation_to_adapter: %s" %(identifier,hotel_code))
	integrations = list(
		datastore_communicator.get_using_entity_and_params('IntegrationConfiguration', [('downloadBooking', '=', True)],
														   hotel_code=hotel_code))
	push_urls = []

	if integrations and len(integrations) > 0:
		for config in integrations:

			url_found = ""
			default_url_found = ""

			for item in config.get("configurations"):
				key_value = item.split(" @@ ")
				# note that same adapters (iE prestige) hava specials URLS to push reservation
				if key_value[0] == "url push reservation":
					url_found = key_value[1]
				if key_value[0] == "url":
					default_url_found = key_value[1]

			if url_found:
				push_urls.append(url_found)
			elif default_url_found:
				push_urls.append(default_url_found)

	if push_urls:

		# in this moment only usefull for prestige. It indicates that not room/rate(board is changed
		only_force_values_param = "&only_force_values=True"
		if payed_in_this_transaction:
			only_force_values_param += "&payed_in_this_transaction=%s" % payed_in_this_transaction
		if payment_order_id:
			only_force_values_param += "&payment_order_id=%s" % payment_order_id
		if avoid_sending_payment:
			only_force_values_param += "&avoid_sending_payment=%s" % avoid_sending_payment

		for push_url in push_urls:
			push_url += "&identifier=%s%s" % (identifier, only_force_values_param)
			logging.info("pushing reservation to %s", push_url)

			send_reservation_to_adapter_by_url(push_url)


def send_reservation_to_adapter_by_url(push_url, num_try=0):
	if num_try < 3:
		logging.info(f"send_reservation_to_adapter_by_url: {push_url}")
		result = requests.post(push_url)
		if result.status_code != 200:
			logging.info(f"Retry {num_try + 1} for {push_url}")
			time.sleep(3)
			send_reservation_to_adapter_by_url(push_url, num_try + 1)
	else:
		logging.error(f"send_reservation_to_adapter_by_url: {push_url} failed after 3 tries")



# NEW LINK ZONE
@app.route("/pages/cobrador/form_send_link", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_form_new_link():
	current_sess = session_utils.get_current_session()
	session_key = current_sess.get_value("session_id")
	body = dict(request.args)
	identifier = body.get("identifier")
	hotel_code = body.get("hotel_code", current_sess.get_value('hotel_code'))


	logging.info("[%s] New Form to send link for hotel_code: %s" %(identifier,hotel_code))



	reservation = None
	reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))
	if reservations:
		reservation = reservations[0]

	reservation_summary_html = get_resevation_summary_html(hotel_code, session_key, reservation)

	interface_controller = get_interface_controller(hotel_code)
	gateway_has_token = interface_controller.gateway_has_token()

	multiple_tokenization = gateway_allow_multiple_tokenization(hotel_code)

	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	request_params = request.values

	locale_language = request_params.get("language", 'default')

	context = {
		'hotel_code': hotel_code,
		'session_key': session_key,
		'identifier': identifier,
		'reservation_summary_html': reservation_summary_html,
		'gateway_has_token': gateway_has_token,
		"back_button": {"href": "/pages/cobrador/reservations?sessionKey=%s&language=%s&hotel_code=%s" % (session_key, locale_language,hotel_code),
						"label": translations.get("T_VOLVER")},
		"error_limit_amount_link": translations.get("T_ERROR_MAX_NOT_PERMITED_LINK"),
		"locale_language": locale_language,
		"multiple_tokenization": multiple_tokenization,
		'allow_tokenize_with_payments': get_configuration_property_value(hotel_code, "allow tokenize with payments")
	}

	content_page = build_template('pages/cobrador/new_link_form.html', context)
	return content_page


def gateway_allow_multiple_tokenization(hotel_code):
	payment_type = get_integration_cobrador_name(hotel_code)
	for gateway in ["STRIPE"]:
		if gateway in payment_type:
			return True

	return False


@app.route("/pages/cobrador/send_new_link", methods=['POST'])
@login_from_sessionKey
@login_using_referrer
def send_new_link_email_to_customer_from_manager():
	logging.info("entering send_new_link_email_to_customer")

	current_sess = session_utils.get_current_session()
	session_key = current_sess.get_value("session_id")
	user_name = current_sess.get_value('user_name')
	body = dict(request.args)
	hotel_code = body.get("hotel_code", current_sess.get_value('hotel_code'))
	try:
		language = current_sess.get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	request_params = dict(request.form)

	link_sent = send_paymentlink_email_to_customer(hotel_code, request_params, user_name)


	response = {
		"status": "OK" if link_sent == "OK" else "KO",
		"message": translations.get("T_SENT_LINK") if link_sent == "OK" else translations.get("T_WRONG_SENT_LINK"),
	}
	return response


@app.route("/pages/cobrador/build_new_link", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def build_new_link_for_ajax():
	'''
	build a new link from ajax
	'''

	logging.info("entering build_new_link_for_ajax")

	body = dict(request.args)

	hotel_code = None
	if body.get('hotel_code'):
		hotel_code = body.get('hotel_code')

	current_sess = session_utils.get_current_session()

	if current_sess and not hotel_code:
		hotel_code = current_sess.get_value('hotel_code')

	# session_key = current_sess.get_value("session_id")

	identifier = body.get("identifier")
	amount = body.get("amount", "").replace(",", ".")
	type_link = body.get("type_link")


	if not identifier:
		return "ERROR"

	reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))
	if reservations:

		try:
			language = session_utils.get_current_session().get_value("language")
			translations = language_utils.get_web_dictionary(language)
		except:
			translations = language_utils.get_web_dictionary(False)

		reservation = reservations[0]

		link = build_new_link(reservation, type_link, amount, hotel_code)

		return link

	return "ERROR"



@app.route("/pages/cobrador/save_asyncronous_payment", methods=['POST', 'GET'])
def save_asyncronous_payment():
	# todo: we need saving the hotel_code and identifier and recovery it in this endpoint

	# todo: 1. analyzer response received
	# todo: 2. getting persistent info transaction
	# todo: 3. call response controller

	pass


# logging.info("[PAYLANDS] Params from request")
# logging.info(request.body)
#
# body = dict(request.args)
# if not body.get('order', ''):
# 	raise ("Exception")
#
# amount = body.get('order').get("amount")
# amount = float(amount) / 100.0
# identifier = body.get('order').get("additional")
# status = body.get('order').get("status")
#
# if status == SUCCESS:
#
# 	logging.info("[Addon Payments] Updating reservation")
# 	self._updating_payed(identifier, amount)


def get_integration_cobrador_name(hotel_code):
	integration_name = get_configuration_property_value(hotel_code, 'Use Payment Gateway Special Cobrador')
	if not integration_name:
		integration_name = get_configuration_property_value(hotel_code, 'Use Payment Gateway')

	return integration_name


@timed_cache(hours=24)
def get_use_payment_gateway_configuration(hotel_code):
	hotel_info = get_hotel_by_application_id(hotel_code)
	integration_name = get_integration_cobrador_name(hotel_code)

	integration_configurations = get_integration_configuration_of_hotel(hotel_info, integration_name)
	gateway_configuration = {}
	if integration_configurations:

		integration_configuration = integration_configurations[0].get('configurations', [])
		for config in integration_configuration:
			key_and_value = config.split(INTEGRATION_CONFIG_MAP_SEPARATOR)
			gateway_configuration[key_and_value[0]] = key_and_value[1]

	return gateway_configuration


def get_all_valid_hotels_programmatically_payments():
	all_valid_hotels = get_all_valid_hotels()
	all_hotels = []
	for hotel in all_valid_hotels:
		hotel_code = hotel['applicationId']
		try:
			cobrador_available = get_configuration_property_value(hotel_code, COBRADOR_AUTOMATIC_PAYMENT)
			if cobrador_available:
				all_hotels.append(hotel_code)
		except Exception as e:
			logging.info("check_programmatically_payments UNEXPECTED ERROR hotel_code: %s: %s", hotel_code, str(e))
			logging.info("check_programmatically_payments error traceback hotel_code: %s: %s ", hotel_code, str(traceback.format_exc()))
			title_error = "[PAYMENTSEEKER] ERROR in check_programmatically_payments - %s" % hotel_code
			notification_html_error = "Somthing wrong in check_programmatically_payments <br> %s <br> %s" % (str(e), str(traceback.format_exc()).replace("\n", "<br>"))
			sendEmail_localdev("<EMAIL>;<EMAIL>;<EMAIL>", title_error, "", notification_html_error)


	return all_hotels


###################################################################################
# AUTOMATIZACIÓN COBROS PROGRAMADOS ZONE


@app.route("/pages/cobrador/check_programmatically_payments", methods=['GET'])
def check_programmatically_payments():
	'''
	1. check which hotels are enabled to cobrador
	2. check for each hotel if there is a rule that satisfies reservations
	3. make a payments in each reservation
	'''
	logging.info("Calling check_programmatically_payments from cloud scheduler")

	timestamp = datetime.now().strftime('%Y%m%d_%H')

	all_valid_hotels = get_all_valid_hotels_programmatically_payments()
	for hotel_code in all_valid_hotels:

		cobrador_billing_once_a_day = get_configuration_property_value(hotel_code, COBRADOR_AUTOMATIC_BILLING_ONCE_A_DAY)
		hora_actual = datetime.now()
		if cobrador_billing_once_a_day:
			if not (FIRST_HOUR_IN_THE_MORNING < hora_actual.hour < HOUR_NOT_PAYMENT):
				log.info("hotel programmed so that the scheduled payment is made only once in %s", hotel_code)
				continue

		log.info("check_programmatically_payments in %s", hotel_code)

		payload = {'hotel_code': hotel_code}
		try:
			queue_utils.create_task('do_programmatically_payments', json.dumps(payload), queue_name=COBRADOR_QUEUE,
									task_name='%s__%s' % (hotel_code, timestamp))
		except Exception as e:
			logging.warning(f'Error creating task do_programmatically_payments: {e}')
	return 'OK'


@app.route("/pages/cobrador/do_programmatically_payments", methods=['GET'])
def do_programmatically_payments_by_url():
	# example to use this /pages/cobrador/do_programmatically_payments?hotel_code=test-backend3

	hotel_code = request.values.get("hotel_code")
	forced_identifier = request.values.get("identifier")
	function_name = "do_programmatically_payments_by_url"
	lock_key = get_lock_key(hotel_code, function_name)
	if not request.values.get('force_execution') and execution_locked(hotel_code, lock_key):
		send_email_locked_execution(function_name, hotel_code, lock_key)
		return "Execution locked"

	log.info(f"check_programmatically_payments {hotel_code} GET call in method {function_name}")

	if lock_execution(hotel_code, lock_key):
		logging.info(f"{function_name} {hotel_code} locked: {lock_key}")

	do_programmatically_payments(hotel_code, forced_identifier=forced_identifier, function_name=function_name, lock_key=lock_key)
	return ""


@app.route("/execute_task/do_programmatically_payments", methods=['GET', 'POST'])
def execute_task_programmatically_payments():
	logging.info(f"Request execute_task_prgrammatically data json args: {json.loads(request.data)}")
	body = json.loads(request.data)
	hotel_code = body.get("hotel_code")
	function_name = "execute_task_programmatically_payments"
	lock_key = get_lock_key(hotel_code, function_name)

	if not Config.DEV and not body.get('force_execution') and execution_locked(hotel_code, lock_key):
		send_email_locked_execution(function_name, hotel_code, lock_key)
		return "Execution locked"

	log.info(f"check_programmatically_payments {hotel_code} GET call in method {function_name}")
	if lock_execution(hotel_code, lock_key):
		logging.info(f"{function_name} {hotel_code} locked: {lock_key}")

	do_programmatically_payments(hotel_code, lock_key=lock_key, function_name=function_name)

	return ""


def do_programmatically_payments(hotel_code, forced_identifier=None, lock_key="", function_name="do_programmatically_payments"):
	log.info("check_programmatically_payments %s Checking from posible programmed payments", hotel_code)
	all_rules = get_rules_by_type(hotel_code, "programmatically")

	# take all reservations sensible to be payed:
	# all reservations with starDate before 100 days, becasue max days policies is 90 plus a security margin of 10 days

	today_ts = datetime.today()
	days_in_furture_ts = today_ts + timedelta(days=100)
	today_date = today_ts.strftime("%Y-%m-%d")
	days_in_future_start_date = days_in_furture_ts.strftime("%Y-%m-%d")
	if forced_identifier:
		filter_params = [('identifier', '=', forced_identifier)]
	else:
		filter_params = [('startDate', '>=', today_date), ('startDate', '<=', days_in_future_start_date)]
	reservations_to_check = list(
		datastore_communicator.get_using_entity_and_params('Reservation', filter_params, hotel_code=hotel_code))
	if reservations_to_check:
		reservations_to_check = list(filter(lambda l: not l.get("cancelled"), reservations_to_check))

	hotel = get_hotel_by_application_id(hotel_code)
	all_rates = get_rates_of_hotel(hotel, include_removed=True)

	all_rooms = get_rooms_of_hotel(hotel, include_removed=True)
	all_rooms_map = {x.get("key"): x for x in all_rooms}

	rates_map = {x['key']: x for x in all_rates}

	for reservation in reservations_to_check:
		if get_config_property_value(hotel_code, AUTOMATIC_RFESERVATION_CANCELATION):
			if cancel_if_have_incomplete_payments(reservation, all_rules, hotel_code):
				continue
		if reservation_could_be_payed(hotel_code, reservation):
			logging.info("[%s] Processing programmatically payment in %s", reservation.get("identifier"), hotel_code)
			try:
				process_reservation_payment(hotel_code, reservation, all_rules, rates_map, all_rooms_map)
			except Exception as e:
				html_content = f'Error IN do_programmatically_payments PROCESS RESERVATION : {e}'
				logging.error(html_content)
				title = "ERROR PROCESANDO PAGO EN %s en reserva %s" % (hotel_code, reservation.get("identifier"))
				sendEmail("<EMAIL>;<EMAIL>;<EMAIL>", title, "", html_content)

			try:
				send_anticipated_payment_notifications(all_rooms_map, all_rules, hotel_code, rates_map, reservation)
			except Exception as e:
				html_content = f'Error IN do_programmatically_payments SEND RECORDATORIO: {e}'
				logging.error(html_content)
				title = "ERROR ENVIANDO RECORDATORIO PAGO EN %s en reserva %s" % (hotel_code, reservation.get("identifier"))
				sendEmail("<EMAIL>;<EMAIL>;<EMAIL>", title, "", html_content)

		elif list(filter(lambda x: x.get("use_pay_link"), all_rules)) and reservation_could_be_payed(hotel_code, reservation, with_token=False):
			rules_to_use = list(filter(lambda x: x.get("use_pay_link"), all_rules))
			process_reservation_payment(hotel_code, reservation, rules_to_use, rates_map, all_rooms_map)

	if unlock_execution(hotel_code, lock_key):
		logging.info(f"{function_name} {hotel_code} unlocked: {lock_key}")


def send_anticipated_payment_notifications(all_rooms_map, all_rules, hotel_code, rates_map, reservation):
	hotel = get_hotel_by_application_id(hotel_code)
	try:
		extra_info = json.loads(reservation.get("extraInfo", "{}"))
	except:
		extra_info = {}
	if not extra_info.get("programmatically_payment_notification"):
		rules_to_notify = get_rules_to_apply_for_reservation(reservation, all_rules, rates_map, all_rooms_map,
															 hotel_code, skip_conditions_days=True)
		rules_to_notify = [i for i in rules_to_notify if i.get("payment_reminder")]
		if rules_to_notify:
			current_datetime = get_hotel_datetime(hotel_code).replace(tzinfo=None)
			current_datetime = current_datetime.replace(hour=0, minute=0, second=0, microsecond=0)
			rule_candidate = None
			rule_candidate_execution = 100
			for rule in rules_to_notify:
				days_before_payment = int(rule.get("days_before_payment_reminder") or 1)
				reservation_start_date = datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
				conditions_days = timedelta(days=int(rule.get("conditions_days", 0)))
				number_of_days_until_execution = ((reservation_start_date - conditions_days) - current_datetime).days
				if 0 <= number_of_days_until_execution < rule_candidate_execution:
					if number_of_days_until_execution == days_before_payment:
						rule_candidate = rule
						rule_candidate_execution = number_of_days_until_execution

			if rule_candidate:

				payment_seeker_config = get_config_payment_seeker_configuration(hotel_code)

				logo_type = ""
				filter_logo = "survey"
				if payment_seeker_config and payment_seeker_config.get("logo_name"):
					filter_logo = payment_seeker_config.get("logo_name")
				logos = get_hotel_logotypes(hotel_code, filter_logo)
				if logos:
					logo_type = logos[0]

				amount_to_pay = _get_amount_to_be_payed(reservation, rule_candidate, hotel_code)

				currency = extra_info.get("currency", "")
				if not currency:
					currency = get_configuration_property_value("Base Price Currency")
				if not currency:
					currency = "EUR"

				currency = currency.upper()

				language = reservation.get("language") or SPANISH
				translations = language_utils.get_web_dictionary(language)

				payment_amount_message = translations.get("T_AMOUNT_TO_PAY_IN_NUM_DAYS", "")
				# For show custom text we have to have the PAYMENT SEEKER xml configuration and the configuration 'anticipate payment custom text section'
				custom_section_translations = get_web_section(hotel, payment_seeker_config.get(
					"notifications email custom text section"), language, set_languages=True)

				if custom_section_translations and custom_section_translations.get("T_AMOUNT_TO_PAY_IN_NUM_DAYS"):
					payment_amount_message = custom_section_translations.get("T_AMOUNT_TO_PAY_IN_NUM_DAYS")

				payment_amount_message = payment_amount_message.replace("@@num_days@@", str(rule_candidate_execution)).replace("@@NUM_DAYS@@", str(rule_candidate_execution))
				payment_amount_message = payment_amount_message.replace("@@amount@@", "%.2f" % amount_to_pay).replace("@@AMOUNT@@", "%.2f" % amount_to_pay)
				payment_amount_message = payment_amount_message.replace("@@currency@@", currency).replace("@@CURRENCY@@", currency)

				context = {
					"logo_type": logo_type,
					"phone": payment_seeker_config.get("telephone"),
					"email": payment_seeker_config.get("email"),
					"payment_message": payment_amount_message
				}

				if custom_section_translations:
					payment_reminder_notification_title = custom_section_translations.get("T_payment_reminder_notification")
					if payment_reminder_notification_title:
						context["payment_reminder_notification_title"] = payment_reminder_notification_title
					else:
						context["payment_reminder_notification_title"] = translations.get("T_RECORDATORIO_SUBJECT", "Recordatorio de Pago Programado")

				context.update(translations)

				html_content = build_template('pages/cobrador/emails/email_pre_payment_notification_v4.html',
											  context, force_language="SPANISH")

				subject_email = translations.get("T_RECORDATORIO_SUBJECT", "Recordatorio de Pago Programado")
				if custom_section_translations and custom_section_translations.get("T_payment_reminder_notification_subject"):
					subject_email = custom_section_translations.get("T_payment_reminder_notification_subject")

				title = subject_email + " " + reservation.get("identifier")

				email_hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
				if email_hotel_name:
					title += " " + email_hotel_name

				email_sender_config = get_configuration_property_value(hotel_code, EMAIL_SENDER_CONFIG)
				email_sender = build_email_sender_from_hotel_webs_config(email_sender_config,
																		 backup_sender="<EMAIL>")
				sendEmail(reservation.get("email"), title, "", html_content, sender=email_sender,
						  backup_sender="<EMAIL>")

				extra_info["programmatically_payment_notification"] = True
				reservation["extraInfo"] = json.dumps(extra_info)

				reservation_id = int(reservation.key.id)
				datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation,
														 hotel_code=hotel_code)




def cancel_if_have_incomplete_payments(reservation, all_rules, hotel_code):
	current_date = datetime.now()
	reservation_start_date = datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
	identifier = reservation.get("identifier")

	if current_date + timedelta(days=1) < reservation_start_date:
		return False

	try:
		extra_info = json.loads(reservation.get("extraInfo", "{}"))
	except json.decoder.JSONDecodeError as e:
		logging.error("[%s][%s]Error decoding extra_info: %s" % (hotel_code, identifier, str(e)))
		extra_info = {}

	if extra_info.get("programmatically_payment_cobrador_ids"):
		for rule_id in extra_info.get("programmatically_payment_cobrador_ids"):
			rules = list(filter(lambda x: str(x.id) == rule_id and x.get("use_pay_link"), all_rules))
			if rules:
				rule = rules[0]
				amount = _get_amount_to_be_payed(reservation, rule, hotel_code, ignore_applied_rule=True)
				if amount:
					reservation_id = int(reservation.key.id)
					reservation["cancellationTimestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
					reservation["incidents"] = "Reserva cancelada por falta de pago"
					reservation["cancelled"] = True
					reservation_updated_id = save_reservation_and_flush_cache(reservation, hotel_code, reservation_id=reservation_id)


					return True

	return False


def process_reservation_payment(hotel_code, reservation, all_rules, rates_map, rooms_map):
	# Check all the rules by priority and if a rule can be applied, lets make the payment
	# 1. Check by range date: Do we have an specific rule with range date for this reservation?
	# filtered_rules = filter_rules_by_dates(all_rules, reservation)
	# 2. Check by rate: Do we have an specific rule with a rate for this reservation?
	# filtered_rules = filter_rules_by_rates(filtered_rules, reservation, rates_map)
	# 3. check by number of days until arrive
	# filtered_rules = filter_rules_by_conditions_days(filtered_rules, reservation)

	filtered_rules = get_rules_to_apply_for_reservation(reservation, all_rules, rates_map, rooms_map, hotel_code)
	log.info("check_programmatically_payments %s All filtered rules: %s", hotel_code, filtered_rules)
	# in theory here we are going to have only one rule
	# if not is beacuse there are ambiguous rules


	if filtered_rules:
		do_programmatically_payment_in_reservation(hotel_code, reservation, filtered_rules[0])


def filter_rules_by_dates(rules, reservation):
	default_candidates = []
	specific_candidates = []

	for rule in rules:
		start_date_reservation = datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
		end_date_reservation = datetime.strptime(reservation.get("endDate"), "%Y-%m-%d")
		# Checking rules with specific start and end dates
		if rule.get("start_date") and rule.get("end_date"):
			start_date = datetime.strptime(rule["start_date"], "%d/%m/%Y")
			end_date = datetime.strptime(rule["end_date"], "%d/%m/%Y")

			if rule.get("1day_inrange"):
				for single_date in daterange(start_date, end_date):
					if start_date_reservation <= single_date < end_date_reservation:
						rule['restriction_priority'] += 1
						specific_candidates.append(rule)
						break
			elif start_date <= start_date_reservation <= end_date:
				rule['restriction_priority'] += 1
				specific_candidates.append(rule)

		# Checking rules with specific days of the week
		elif rule.get("config_conditions_days"):
			for dates_rule in rule.get("config_conditions_days"):
				start_date = datetime.strptime(dates_rule["start_date"], "%d/%m/%Y")
				end_date = datetime.strptime(dates_rule["end_date"], "%d/%m/%Y")

				if dates_rule.get("1day_inrange"):
					for single_date in daterange(start_date, end_date):
						if start_date_reservation <= single_date < end_date_reservation:
							rule['restriction_priority'] += 1
							specific_candidates.append(rule)
							break
				elif start_date <= start_date_reservation <= end_date:
					rule['restriction_priority'] += 1
					specific_candidates.append(rule)
					break

		# Handling rules configured for all dates
		else:
			specific_candidates.append(rule)

	if specific_candidates:
		return specific_candidates
	else:
		return default_candidates

def daterange(start_date, end_date):
	for n in range(int((end_date - start_date).days) + 1):
		yield start_date + timedelta(n)


def filter_rules_by_rates(rules, reservation, rates_map,hotel_code):
	# 2. Check by rate: Do we have an specific rule with a rate for this reservation?
	# we can find 3 kind of configuration: or rate_policies or rate_words or rates
	default_candidates = []
	specific_candidates = []
	rate_reservations = reservation.get("rate")
	if "PACKAGE" in reservation.get("rate"):
		if len(reservation.get("rate").split("_@_")) >2:
			rate_reservations = reservation.get("rate").split("_@_")[2]


	for rule in rules:
		if rule.get("rates"):
			rate = rates_map.get(rate_reservations, {})
			if check_rule_by_rate(rule, rate):
				rule['restriction_priority'] += 1
				specific_candidates.append(rule)
		elif rule.get("rate_policies"):
			if check_rule_by_rate_policies(rule, reservation, rates_map, hotel_code):
				rule['restriction_priority'] += 1
				specific_candidates.append(rule)
		elif rule.get("rate_words"):
			if check_rule_by_rate_words(rule, reservation, rates_map):
				rule['restriction_priority'] += 1
				specific_candidates.append(rule)
		else:
			# rule configured for all rates
			specific_candidates.append(rule)

	if specific_candidates:
		return specific_candidates
	return default_candidates


def filter_rules_by_rooms(rules, reservation, rooms_map):
	# 3. Check by ROOMS: Do we have an specific rule with a ROOM for this reservation?
	specific_candidates = []

	all_rooms_in_reservation = set()
	if reservation.get("price_days"):
		for room, prices in reservation.get("price_days", {}).items():
			all_rooms_in_reservation.add(room)
	else:
		if reservation.get("extraInfo"):
			extra_info = json.loads(reservation.get("extraInfo"))
			if extra_info and extra_info.get("prices_per_day"):
				for item in extra_info.get("prices_per_day"):
					all_rooms_in_reservation.add(item.split(":")[1].strip())

	for rule in rules:
		if rule.get("rooms") and rule.get("rooms", "") != "@@all@@":

			# what happend if we have more than one rooms...
			for room_key in all_rooms_in_reservation:
				room_key = room_key.split("@@")[0]
				room = rooms_map.get(room_key, {})
				if not room:
					continue
				if str(room['id']) in rule.get("rooms"):
					rule['restriction_priority'] += 1
					specific_candidates.append(rule)
		else:
			# rule configured for all ROOM
			specific_candidates.append(rule)

	return specific_candidates


def filter_rules_by_conditions_days(rules, reservation, hotel_code):
	# 3. check by number of days until arrive

	start_datetime = datetime.strptime(reservation.get('startDate'), '%Y-%m-%d')
	today_datetime = get_hotel_datetime(hotel_code).replace(hour=0, minute=0, second=0, microsecond=0)
	start_datetime = start_datetime.replace(tzinfo=today_datetime.tzinfo)

	logging.info("today: %s , arrival date: %s" % (
		today_datetime.strftime(ISO_DATETIME_FORMAT),
		start_datetime.strftime(ISO_DATETIME_FORMAT)
	))

	logging.info("%s >= %s = %s" % (
		start_datetime.strftime(ISO_DATETIME_FORMAT),
		today_datetime.strftime(ISO_DATETIME_FORMAT),
		start_datetime >= today_datetime
	))

	if start_datetime >= today_datetime:

		difference = start_datetime - today_datetime
		difference_in_days = difference / timedelta(days=1)

		specific_candidates = []

		for rule in rules:
			conditions_days = int(rule.get("conditions_days", "0"))

			if rule.get("type_rule") == 'programmatically':
				if difference_in_days <= conditions_days:
					if rule.get("limit_dates", "") == "on":
						num_limit_dates = 0
						if rule.get("num_limit_dates", 0):
							num_limit_dates = int(rule.get("num_limit_dates", 0))

						if difference_in_days > num_limit_dates:
							rule['restriction_priority'] += 1
							specific_candidates.append(rule)
					else:
						rule['restriction_priority'] += 1
						specific_candidates.append(rule)

			else:
				restriction_priority = 0

				num_limit_dates = 0
				if rule.get("limit_dates", "") == "on" and rule.get("num_limit_dates", 0):
					num_limit_dates = int(rule.get("num_limit_dates", 0))

				# num_limit_dates = 0 when the rule is definied WITH NO conditions days
				if (not num_limit_dates) or difference_in_days > num_limit_dates:
					if rule.get("limit_dates", "") == "on":
						if difference_in_days > int(rule.get("num_limit_dates", 0)):
							restriction_priority += 1

					if difference_in_days <= conditions_days:
						restriction_priority += 1

					if restriction_priority:
						rule['restriction_priority'] += restriction_priority
						specific_candidates.append(rule)

		if specific_candidates:
			return specific_candidates
		return []

	return []


def check_rule_by_rate(rule, rate):
	try:
		return str(rate['id']) in rule.get("rates") or "@@all@@" in rule.get("rates")
	except:
		return False


def check_rule_by_rate_words(rule, reservation, rates_map):
	try:

		rate_key = reservation.get("rate", "")

		if rate_key.startswith("PACKAGE_"):
			if len(rate_key.split("_@_")) > 2:
				rate_key = rate_key.split("_@_")[2]

		rate_local_name = rates_map.get(rate_key, {}).get("localName", "")

		for word in rule.get("rate_words", "").replace(",", ";").split(";"):
			if word in rate_local_name:
				return True
		return False
	except:
		return False


def check_rule_by_rate_policies(rule, reservation, rates_map, hotel_code):
	try:
		rate_key = reservation.get("rate", "")

		if rate_key.startswith("PACKAGE_"):
			if len(rate_key.split("_@_")) > 2:
				rate_key = rate_key.split("_@_")[2]

		cancel_policy = rates_map.get(rate_key, {}).get("cancellationPolicy", "") or ""

		if cancel_policy == 'Multiples politicas':
			cancel_policy = calculate_cancel_policy_for_period(reservation, hotel_code)
		if not cancel_policy:
			return False
		if rule.get("rate_policies", "") == "nr" and cancel_policy == "No cancelable":
			return True
		if rule.get("rate_policies", "") == "pvp" and not cancel_policy == "No cancelable":
			return True

		return False
	except:
		return False


def do_programmatically_payment_in_reservation(hotel_code, reservation, rule):
	log.info("check_programmatically_payments %s do_programmatically_payment_in_reservation for reservation: %s", hotel_code, reservation.get("identifier"))
	log.info("check_programmatically_payments %s Rule to execute: %s", hotel_code, rule)

	amount_to_be_payed = _get_amount_to_be_payed(reservation, rule, hotel_code)
	extra_info = {}
	if reservation.get("extra_info"):
		extra_info = json.loads(reservation.get("extra_info"))


	if amount_to_be_payed:
		log.info("check_programmatically_payments Reservation %s is going to be payed with amount: %s", reservation.get("identifier"),
				 amount_to_be_payed)
		identifier = reservation.get("identifier")
		if not rule.get("use_pay_link"):
			hotel_code_origin = hotel_code
			if extra_info.get("origin_hotel_code"):
				hotel_code_origin = extra_info.get("origin_hotel_code")
				logging.info("Changed hotel code: %s %s", identifier, hotel_code_origin)

			order = execute_payment_in_gateway(hotel_code_origin, reservation, amount_to_be_payed, rule)

			today_ts = datetime.now()
			today_ts_txt = date_to_string(today_ts, format="%Y-%m-%d %H:%M:%S")

			payment_type = PAYMENT_TYPE_PROGRAMATICALLY

			extra_info = {}
			if isinstance(order, dict):
				# extra_info = {"pasref": order.get('pasref'), "authcode": order.get('authcode')}
				extra_info = copy.deepcopy(order)
				if order.get("order_id"):
					order = order.get("order_id")

			error = None
			if "ERROR" in order:
				info_error = order.split(SEPARATOR_INFO_ERROR_ORDER)
				if len(info_error) > 1:
					order = info_error[1]
					error = info_error[0]
				else:
					error = order

				# be sure ALWAYS SEND AN ERROR!!!!!
				if not error:
					error = "UNKNOWN ERROR"

			payment_params = {
				"order": order,
				"error": error,
				"comments": "Pago Programado",
				"type": payment_type,
				"user": "Paraty E-Payments",
				"reservation_identifier": identifier,
				"amount": amount_to_be_payed,
				"timestamp": today_ts_txt,
				"rule_id": str(rule.id),
				"extra_info": extra_info
			}

			extra_payment = False  # obviously

			send_customer_email = rule.get("send_customer_email", "") == "on"
			send_modification_email = rule.get("send_modification_email","") =="on"

			response = _save_and_send_payments_confirmations(hotel_code, reservation, payment_params, amount_to_be_payed,
															   extra_payment, send_customer_email, send_modification_email,"")

			send_payment_link_if_error = rule.get("send_payment_link_if_error", "") == "on"

			if error and send_payment_link_if_error:
				send_error_message_with_payment_link(hotel_code, reservation, amount_to_be_payed, rule)

			if error and rule.get("send_error_payment_channel"):
				send_error_payment_to_adapter(amount_to_be_payed, hotel_code, identifier, reservation)

		else:

			payment_link_params = {
				"identifier": identifier,
				"type_link": "price",
				"hotel_code": hotel_code,
				"amount": str(amount_to_be_payed)
			}

			send_paymentlink_email_to_customer(hotel_code, payment_link_params, "Paraty E-Payments")

			reservation_id = int(reservation.key.id)

			try:
				extra_info = json.loads(reservation.get("extraInfo", "{}"))
			except json.decoder.JSONDecodeError as e:
				logging.error("[%s][%s]Error decoding extra_info: %s" % (hotel_code, identifier, str(e)))
				extra_info = {}

			if extra_info.get("programmatically_payment_cobrador_ids"):
				extra_info['programmatically_payment_cobrador_ids'].append(str(rule.id))
			else:
				extra_info['programmatically_payment_cobrador_ids'] = [str(rule.id)]

			reservation["extraInfo"] = json.dumps(extra_info)

			datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)

			response = {
				"status": "OK",
				"message": "link de pago creado correctamente"
			}

		logging.info("[RESPONSE PROGRAMMATICALLY PAYMENT]: %s", response)
		return response


def send_error_payment_to_adapter(amount_to_be_payed, hotel_code, order, reservation):
	extra_info = json.loads(reservation.get("extraInfo"))
	comments_cobrador = extra_info.get("comments_cobrador")
	if not comments_cobrador:
		comments_cobrador = ""
	comments_cobrador += f"RECHAZO PAGO {order} Cantidad {amount_to_be_payed}. "
	extra_info["comments_cobrador"] = comments_cobrador
	reservation_id = int(reservation.key.id)
	reservation["extraInfo"] = json.dumps(extra_info)
	datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)
	send_reservation_to_adapter(hotel_code, reservation.get("identifier"))


def send_error_message_with_payment_link(hotel_code, reservation, amount_to_be_payed, rule):
	logging.info("[%s] Sending payment link due to scheduled payment failure", reservation.get("identifier"))
	hotel = get_hotel_by_application_id(hotel_code)
	amount = amount_to_be_payed
	payment_seeker_config = get_config_payment_seeker_configuration(hotel_code)
	link = build_new_link(reservation, "percent", amount, hotel_code, unique_usage_link=True)
	language = reservation.get("language") or SPANISH
	translations = language_utils.get_web_dictionary(language)
	translations_section = language_utils._get_translation_section(hotel,"_payment_seeker_error_message", language,True)
	custom_section_translations = get_web_section(hotel, payment_seeker_config.get("notifications email custom text section"), language, set_languages=True)

	logo_type = ""
	filter_logo = "survey"
	if payment_seeker_config and payment_seeker_config.get("logo_name"):
		filter_logo = payment_seeker_config.get("logo_name")
	logos = get_hotel_logotypes(hotel_code, filter_logo)
	if logos:
		logo_type = logos[0]

	phone = "000 000 000"
	email = "<EMAIL>"

	adv_config_phone = get_config_property_value(hotel_code, "Telefonos de contacto")
	if adv_config_phone:
		phone = adv_config_phone

	adv_config_email = get_config_property_value(hotel_code, "Email reservas")
	if adv_config_email:
		email = adv_config_email.split(";")[0]

	if payment_seeker_config.get("telephone"):
		phone = rule.get("phone_if_fail", payment_seeker_config.get("telephone"))

	if payment_seeker_config.get("email"):
		email = rule.get("email_if_fail", payment_seeker_config.get("email"))

	message = translations.get("T_info_payment")
	if custom_section_translations and custom_section_translations.get("T_error_info_payment_with_payment_link"):
		message = custom_section_translations.get("T_error_info_payment_with_payment_link")
	message = message.replace("@@@TELEPHONE@@@", phone)
	message = message.replace("@@@HOURS@@@", rule.get("hours_if_fail","24"))
	not_show_email = payment_seeker_config.get('not show email')
	if not_show_email:
		message = message.replace("@@@EMAIL_SECTION@@@", "")
	else:
		email_message = translations.get("T_email_section")
		email_section = email_message.replace("@@@EMAIL@@@", email)
		message = message.replace("@@@EMAIL_SECTION@@@", email_section)

	not_show_generic_image = payment_seeker_config.get('not show generic image')
	generic_image_path = GENERIC_IMAGE_EMAIL
	context = {
		"logo_type": logo_type,
		"payment_link": link,
		"hi_customer": translations.get("T_hi_customer"),
		"info_payment": message,
		"payment_link_message": translations.get("T_payment_link_message"),
		"color1": payment_seeker_config.get("color link html"),
		"not_show_email": not_show_email,
		"not_show_generic_image": not_show_generic_image,
		"generic_image_path": generic_image_path,
	}

	if translations_section:
		translations_section = {k:v for k, v in translations_section.items() if k.startswith("T_")}
		for translation_key in translations_section:
			context[translation_key] = translations_section[translation_key]

	html_content = build_template('pages/cobrador/emails/email_payment_confirmation_customer_v2.html',
								  context, force_language="SPANISH")
	subject_email = "Error de pago en la reserva"
	title = subject_email + " " + reservation.get("identifier")
	email_hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
	if email_hotel_name:
		title += " " + email_hotel_name
	email_sender_config = get_configuration_property_value(hotel_code, EMAIL_SENDER_CONFIG)
	email_sender = build_email_sender_from_hotel_webs_config(email_sender_config,
															 backup_sender="<EMAIL>")
	sendEmail(reservation.get("email"), title, "", html_content, sender=email_sender,
			  backup_sender="<EMAIL>")

	today_ts = datetime.now()
	today_ts_txt = date_to_string(today_ts, format="%Y-%m-%d %H:%M:%S")
	payment_params = {
		"order": reservation.get("identifier"),
		"error": None,
		"comments": translations.get("T_NEW_LINK_IF_PROGRAMMATICALLY_ERROR"),
		"type": PAYMENT_TYPE_LINK,
		"user": "Paraty E-Payments",
		"reservation_identifier": reservation.get("identifier"),
		"amount": amount_to_be_payed,
		"timestamp": today_ts_txt,
		"failed_payment_link_date": today_ts_txt
	}

	save_secure_payment_reservation(payment_params, hotel_code)


	extra_info =reservation.get("extraInfo")
	if extra_info:
		extra_info = json.loads(extra_info)
		if not extra_info.get("exclude_programmatically_payment_cobrador_ids"):
			extra_info["exclude_programmatically_payment_cobrador_ids"]= []
		extra_info.get("exclude_programmatically_payment_cobrador_ids").append(str(rule.id))
		extra_info["payment_link_send_date"] = get_hotel_datetime(hotel_code).strftime("%Y-%m-%d %H:%M:%S")

		if payment_seeker_config.get("set status pending when send error message with payment link"):
			extra_info["status_reservation"] = "pending"


		reservation_id = int(reservation.key.id)

		reservation["extraInfo"] = json.dumps(extra_info)

		reservation_updated_id = save_reservation_and_flush_cache(reservation, hotel_code, reservation_id=reservation_id)


def reservation_could_be_payed(hotel_code, reservation, with_token=True):
	extra_info = reservation.get("extraInfo", "{}")
	if extra_info:
		try:
			extra_info = json.loads(extra_info)
		except:
			logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
			return False

	interface_controller = get_interface_controller(hotel_code)
	if ((not interface_controller) or not interface_controller.reservation_has_token(reservation)) and with_token and not app.config['DEV']:
		return False

	if extra_info.get("payed"):
		total_payed = float(extra_info.get("payed"))
	else:
		total_payed = 0

	if extra_info.get("bono_gift_used"): #It has gift card
		gift_card = extra_info.get("original_price_before_discount", 0) - extra_info.get("final_discounted_price", 0)

		if gift_card > 0:
			log.info(f"There is a gift card of {gift_card}.")
			total_payed += gift_card

	if extra_info.get("payed_by_cobrador"):
		total_payed += float(extra_info.get("payed_by_cobrador"))

	if extra_info.get("payed_by_tpv_link"):
		for payment_by_link in extra_info["payed_by_tpv_link"]:
			if payment_by_link.get('datatransdata'):
				continue
			total_payed += float(payment_by_link.get("amount"))

	total_price = float(reservation["price"])
	if get_config_property_value(hotel_code, "include accommodation tax in pep"):
		try:
			total_price += float(extra_info.get("price_info").get("taxes").get("accommodation").get("value"))
		except:
			logging.warning(f"Error getting accommodation tax for reservation: {reservation.get('identifier')}")
	if not total_payed:
		return True
	elif float("{:.2f}".format(total_payed)) >= total_price:
		return False

	return True


def _get_amount_to_be_payed(reservation, rule, hotel_code, ignore_applied_rule=False):
	extra_info = reservation.get("extraInfo", "{}")
	payed_day_range = ""
	if extra_info:
		try:
			extra_info = json.loads(extra_info)
		except:
			logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
			return False

	if str(rule.id) in extra_info.get("programmatically_payment_cobrador_ids", []) and not ignore_applied_rule:
		log.info("check_programmatically_payments _get_amount_to_be_payed returning FALSE because rule %s already executed", rule.id)
		return False

	total_payed = 0
	if extra_info.get("payed"):
		total_payed = float(extra_info.get("payed"))

	if extra_info.get("bono_gift_used"): #It has gift card
		gift_card = extra_info.get("original_price_before_discount", 0) - extra_info.get("final_discounted_price", 0)

		if gift_card > 0:
			log.info(f"There is a gift card of {gift_card}.")
			total_payed += gift_card

	if extra_info.get("payed_by_cobrador"):
		total_payed += float(extra_info.get("payed_by_cobrador"))

	if extra_info.get("payed_by_tpv_link"):
		for payment_by_link in extra_info["payed_by_tpv_link"]:
			if payment_by_link.get('datatransdata'):
				continue
			total_payed += float(payment_by_link.get("amount"))

	price_total = float(reservation["price"])
	if reservation.get('priceSupplements', 0):
		price_total += float(reservation['priceSupplements'])

	if get_config_property_value(hotel_code, "include accommodation tax in pep"):
		try:
			price_total += float(extra_info.get("price_info").get("taxes").get("accommodation").get("value"))
		except:
			logging.warning(f"Error getting accommodation tax for reservation: {reservation.get('identifier')}")

	reservation_payment_structure = {
		"price": price_total - total_payed,
		"price_days": {},
		"startDate": reservation.get("startDate"),
		"endDate": reservation.get("endDate"),
		"supplements": reservation.get("priceSupplements")
	}

	if reservation and reservation.get("price_days"):
		# this is called by hotel webs (booking3), so reservation has already "price_days"
		reservation_payment_structure['price_days'] = reservation["price_days"]

	elif extra_info and extra_info.get("prices_per_day"):
		# this is a OR a manual payment in a reservation or a programmatically payment
		if extra_info.get("save_from_manager2") or not reservation.get("modificationTimestamp"):
			for room, days_prices in extra_info['prices_per_day'].items():
				start_date_str = reservation.get("startDate")
				start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
				end_date_str = reservation.get("endDate")
				end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

				room_aux = room.split(":")[1].strip()
				index = int(room.split(":")[0]) - 1
				room_result = room_aux + "@@" + str(index)
				reservation_payment_structure['price_days'][room_result] = []
				while start_date < end_date:
					day = start_date.strftime("%d/%m/%Y")
					reservation_payment_structure['price_days'][room_result].append(float(days_prices[day][2]))

					start_date += timedelta(days=1)

	hotel = get_hotel_by_application_id(hotel_code)
	all_rooms = get_rooms_of_hotel(hotel, include_removed=True)
	all_rooms_map = {x.get("key"): x for x in all_rooms}

	logging.info("GET AMOUNT TO BE PAYED. identifier %s: reservation_payment_structure: %s", reservation.get("identifier"), reservation_payment_structure)
	logging.info("GET AMOUNT TO BE PAYED. identifier  %s: rule applied: %s", reservation.get("identifier"), rule)
	amount_to_pay = calculate_payment_by_type(rule, reservation_payment_structure, all_rooms_map)
	amount_to_pay = float("{:.2f}".format(amount_to_pay))
	logging.info("GET AMOUNT TO BE PAYED. identifier  %s: amount_to_pay calculated: %s", reservation.get("identifier"), amount_to_pay)

	# be careful, maybe this rule is already executed! (imagine that this scritp is executed twice by error)
	return amount_to_pay


def get_rules_to_apply_for_reservation(reservation, all_rules, rates_map, rooms_map, hotel_code, skip_conditions_days=False):
	try:
		filtered_rules = filter_rules_by_cc_expiration(all_rules, reservation)
		if len(filtered_rules) == 1 and filtered_rules[0].get("cc_expire_date_payment") == "on":
			return filtered_rules
	except Exception as e:
		logging.error("Error filtering rules by cc expiration: %s", e)

	filtered_rules = filter_rules_by_country(all_rules, reservation)
	log.info("[COBRADOR] Filter by country: %s", filtered_rules)
	filtered_rules = filter_rules_by_language(filtered_rules, reservation)
	log.info("[COBRADOR] Filter by language: %s", filtered_rules)

	# 1. Check by range date: Do we have an specific rule with range date for this reservation?
	filtered_rules = filter_rules_by_dates(filtered_rules, reservation)
	log.info("[COBRADOR] Filter by dates: %s", filtered_rules)

	filtered_rules = filter_rules_by_user_type(filtered_rules, reservation)
	log.info("[COBRADOR] Filter by user type: %s", filtered_rules)

	# 2. Check by rate: Do we have an specific rule with a rate for this reservation?
	filtered_rules = filter_rules_by_rates(filtered_rules, reservation, rates_map,hotel_code)
	log.info("[COBRADOR] Filter by rate: %s", filtered_rules)

	# 3. check by room filters
	filtered_rules = filter_rules_by_rooms(filtered_rules, reservation, rooms_map)
	log.info("[COBRADOR] Filter by rooms: %s", filtered_rules)
	if not skip_conditions_days:
		# 4. check by number of days until arrive
		filtered_rules = filter_rules_by_conditions_days(filtered_rules, reservation, hotel_code)
		log.info("[COBRADOR] Filter by condition day: %s", filtered_rules)

	# 5. check by number of days payment
	filtered_rules = filter_rules_by_conditions_payed(filtered_rules, reservation, hotel_code)
	log.info("[COBRADOR] Filter by condition payed: %s", filtered_rules)

	# 6. exclude rules form reservations
	filtered_rules = filter_rules_number_days_stay_reservation(filtered_rules, reservation)
	log.info("[COBRADOR] Filter by number days stay reservation: %s", filtered_rules)

	# 7. exclude rules form reservations
	filtered_rules = filter_rules_minimun_amount_reservation(filtered_rules, reservation)
	log.info("[COBRADOR] Filter by limit amount reservation %s", filtered_rules)

	# 8. check by priority
	filtered_rules = filter_rules_by_conditions_priority(filtered_rules, reservation, hotel_code)
	log.info("[COBRADOR] Filter by condition priority: %s", filtered_rules)

	# 9. exclude rules form reservations
	filtered_rules = filter_rules_exclude_rules(filtered_rules, reservation, hotel_code)
	log.info("[COBRADOR] Filter by condition exclude rules: %s", filtered_rules)

	# 10. exclude rules form reservations
	filtered_rules = filter_rules_acumulate_rules(filtered_rules, reservation)
	log.info("[COBRADOR] Filter by acumulate rules: %s", filtered_rules)

	if get_config_property_value(hotel_code, GATEWAY_WITH_BIZUM):
		for rule in filtered_rules:
			if rule.get("gateways"):
				if "BIZUM" in rule.get("gateways") and reservation.get("is_bizum"):
					rule["restriction_priority"] += 1

				elif "BIZUM" in rule.get("gateways"):
					rule["restriction_priority"] -= 1

	if filtered_rules:
		filtered_rules = sorted(filtered_rules, key=lambda l: l.get("restriction_priority"), reverse=True)

	return filtered_rules


@app.route("/pages/cobrador/rules_for_reservation", methods=['POST'])
def rules_for_reservation_endpoint():
	if request.values.get('profile'):
		result = cProfile.runctx('real_rules_for_reservation_endpoint()', globals(), locals(), sort='cumulative')
		return 'OK! (Result can not be returned as we are profiling it)'
	else:
		return real_rules_for_reservation_endpoint()


def real_rules_for_reservation_endpoint():
	body = request.get_json()
	response_amount_to_pay = {}
	log.info("[COBRADOR] Info received: %s", body)
	if body and body.get("reservation"):
		reservation = body.get("reservation")
		if reservation.get("agency_info"):
			reservation = get_reservation_amount_without_commition(reservation)
		hotel_code = request.args.get("hotel_code")
		hotel = get_hotel_by_application_id(hotel_code)
		all_rates = get_rates_of_hotel(hotel, include_removed=True)
		all_rates_map = {x.get("key"): x for x in all_rates}

		all_rules = get_rules_all(hotel_code)
		type_rules_filter = [PAYMENT_TYPE_IN_WEB, PAYMENT_TYPE_EARLY_PAYMENT, PAYMENT_TYPE_CREATE_TOKEN]
		if reservation.get("price_only_flight"):
			type_rules_filter = [PAYMENT_TYPE_FLIGHT]
		filtered_rules = [rule for rule in all_rules if rule.get("type_rule", "") in type_rules_filter]

		all_rooms = get_rooms_of_hotel(hotel, include_removed=True)
		all_rooms_map = {x.get("key"): x for x in all_rooms}

		rules_to_apply_filtered = get_rules_to_apply_for_reservation(reservation, filtered_rules, all_rates_map, all_rooms_map, hotel_code)
		has_optional_tokenizator = False
		has_bizum = get_config_property_value_without_cache(hotel_code, GATEWAY_WITH_BIZUM)

		special_rules = get_configuration_property_value(hotel_code, "special multiple gateways rules")
		if rules_to_apply_filtered:
			if special_rules and not request.values.get('forceGateway'):
				response_amount_to_pay = get_payment_multigateways_to_use(hotel_code, rules_to_apply_filtered, reservation, all_rooms_map)
			else:
				response_amount_to_pay = get_response_amount_to_pay(rules_to_apply_filtered, reservation, all_rooms_map, hotel_code, has_bizum, has_optional_tokenizator)
		else:
			response_amount_to_pay = {
				"amount": -1,
				"rule_applied": "No rule applied"
			}

	log.info("[COBRADOR] Payment: %s", response_amount_to_pay)
	response = make_response(response_amount_to_pay)
	response.headers["Content-Type"] = "application/json"
	return response_amount_to_pay


def calculate_payment_by_type(rule_to_apply, reservation, room_maps):
	amount_to_pay = 0

	num_days_reservation = (datetime.strptime(reservation.get("endDate"), "%Y-%m-%d") - datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")).days

	# TRICKS SPECIAL RULES!!!


	# TRICK 1: Only days in a range
	payed_day_range = rule_to_apply.get("payed_day_range", "off")
	payed_day_range_on = False
	if not rule_to_apply.get("config_conditions_days") and payed_day_range == "on":
		# TRICK the prices days: filter all days in the range
		start_date = rule_to_apply.get("start_date")
		end_date = rule_to_apply.get("end_date")
		num_days_reservation = filter_all_days_in_the_range(num_days_reservation, reservation, start_date, end_date)
		payed_day_range_on = True
	elif rule_to_apply.get("config_conditions_days"):
		for condition in rule_to_apply.get("config_conditions_days"):
			if condition.get("payed_day_range") == "on":
				start_date = condition.get("start_date")
				end_date = condition.get("end_date")
				num_days_reservation = filter_all_days_in_the_range(num_days_reservation, reservation, start_date, end_date)
				if num_days_reservation:
					payed_day_range_on = True
					break

	# TRICK 2: only certain rooms
	if rule_to_apply.get("rooms") and rule_to_apply.get("rooms", "") != "@@all@@":
		if reservation.get("price_days"):
			new_prices_days = {}
			new_price = 0
			for room_key, days_prices in reservation['price_days'].items():
				room_key = room_key.split("@@")[0]
				room = room_maps.get(room_key, {})
				if str(room['id']) in rule_to_apply.get("rooms"):
					new_prices_days[room_key] = days_prices
					new_price += sum([float(x) for x in days_prices])

			reservation['price_days'] = new_prices_days
			supplements =float(reservation.get("supplements")) if reservation.get("supplements") else 0.0
			reservation['price'] = new_price + supplements
	price_total = reservation.get("price")

	# Check if apllied rule has fixed amount
	if rule_to_apply.get("type_amount") == "fixed_amount":
		amount_to_pay = float(rule_to_apply.get("amount", reservation.get("price")))
		if rule_to_apply.get("type_fixed_amount") == "per_room" and reservation.get("price_days"):
			num_rooms = len(reservation.get("price_days"))
			amount_to_pay = amount_to_pay * num_rooms
		if rule_to_apply.get("type_fixed_amount") == "per_night" and reservation.get("price_days") and len(list(reservation.get("price_days").items())) > 0:
			first_room = list(reservation.get("price_days").items())[0]
			num_nights = len(reservation.get("price_days")[first_room[0]])
			amount_to_pay = amount_to_pay * num_nights


	if payed_day_range_on:
		for room, prices in reservation.get("price_days").items():
			amount_to_pay += sum(prices[:int(rule_to_apply['amount'])])

		if amount_to_pay < reservation.get("price"):
			price_total = amount_to_pay

	if reservation.get("price_only_flight"):
		return float(reservation["price_only_flight"])

	if rule_to_apply.get("type_rule") == "early_payment":
		if rule_to_apply.get('amount') and rule_to_apply.get("type_amount") == "fixed_amount":
			amount_to_pay = float(rule_to_apply.get('amount'))
		else:
			amount_to_pay = float(price_total) * ((100.0 - float(rule_to_apply.get("early_payment_percent"))) / 100.0)
			price_total = amount_to_pay

	if rule_to_apply.get("type_amount") == 'percentage':
		amount_to_pay = float(price_total) * (float(rule_to_apply.get("amount")) / 100.0)

	elif rule_to_apply.get("type_amount") == "num_days":
		if not reservation.get("price_days") or rule_to_apply.get("average_price_day"):
			price_per_day = float(price_total) / num_days_reservation
			amount_to_pay = price_per_day * int(rule_to_apply['amount'])

		else:
			for room, prices in reservation.get("price_days").items():
				amount_to_pay += sum(prices[:int(rule_to_apply['amount'])])

			amount_to_pay = min([amount_to_pay, float(price_total)])

		if rule_to_apply.get("include_supplements") and reservation.get("supplements"):
			amount_to_pay += float(reservation.get("supplements", 0.0))

	elif rule_to_apply.get("type_amount") == "supplement":
		reservation_supplements = reservation.get("supplements", 0.0) or 0.0
		amount_to_pay = max(float(reservation_supplements), 0.0)

		if amount_to_pay == 0:
			amount_to_pay = -1

	return amount_to_pay


def get_rules_by_type(hotel_code, type_rule):
	search_params = [("status", "=", "on"), ("type_rule", "=", type_rule)]
	all_rules = list(datastore_communicator.get_using_entity_and_params('PaymentConfiguration', search_params,
																		hotel_code=hotel_code))
	for rule in all_rules:
		rule['restriction_priority'] = 0

	return all_rules


def get_rules_all(hotel_code):
	search_params = [("status", "=", "on")]
	all_rules = list(datastore_communicator.get_using_entity_and_params('PaymentConfiguration', search_params,
																		hotel_code=hotel_code))
	for rule in all_rules:
		rule['restriction_priority'] = 0
		if rule.get("increase_in_priority"):
			rule['restriction_priority'] += rule.get("increase_in_priority")

	return all_rules

@app.route('/pages/show_payment_audits', methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def show_payments_audits():
	request_params = request.values
	locale_language = request_params.get("language", 'default')
	language = language_utils.get_language_in_manager_based_on_locale(locale_language)
	#session_utils.get_current_session().set_value("language", language)

	hotel_code = session_utils.get_current_session().get_value('hotel_code')
	logging.info("Getting payments audits..!!")
	audits_info_map = get_audit_response_with_retry(hotel_code, return_cursor=True, order_by="-timestamp")
	audits_info = []

	for audit in audits_info_map:
		if len(audits_info) >= 10:
			break
		try:
			response = json.loads(audit.get("response", ""))
			dsMerchantParameters = base64.b64decode(response["Ds_MerchantParameters"]).decode("utf-8")
			response["Ds_MerchantParameters"] = dsMerchantParameters
			audit["response"] = json.dumps(response)
		except Exception as e:
			audit['response'] = audit.get("response", "")

		audits_info.append(audit)

	context = {
		'audits_map': audits_info
	}

	content_page = build_template('pages/cobrador/payments_audits.html', context)

	context = {
		"content_page": content_page,
		'active_payments_audits_tag': 'active',
		'is_backend': check_permission_to_manager("backend"),
		'no_cobrador_rules': check_permission_to_manager("no_cobrador_rules"),
		'no_cobrador_payments': check_permission_to_manager("no_cobrador_payments"),
		'locale_language': locale_language
	}

	content = build_template('pages/cobrador/cobrador_base.html', context)
	css_list = ['/static/css/libs/fontawesome5.css',
				'/static/css/pages/cobrador/payment_audits.css',
				'/static/css/pages/cobrador/cobrador.css?v=%s' % VERSION_CSS]

	jsLib_list = ['/static/js/libs/jQuery.min.js']
	# js_list = ['/static/js/pages/cobrador/cobrador.js?v=%s' % VERSION_JS]
	return build_iframe_page(content, css_list, jsLib_list)


@app.route('/pages/show_filter_payments_audits', methods=['POST'])
@login_from_sessionKey
@login_using_referrer
def show_filter_payments_audits():
	hotel_code = session_utils.get_current_session().get_value('hotel_code')
	search_params = request.get_json()
	audits_info_map = []
	logging.info("Searching filtered payments audit..!!")

	if search_params:
		# Always make one call with the first parameter
		first_key, first_value = next(iter(search_params.items()))
		audits_info_map = get_audit_response_with_retry(hotel_code, search_params=[(first_key, '=', first_value)])

		# If there are more parameters, filter results in code
		if len(search_params) > 1 and audits_info_map:
			for key, value in list(search_params.items())[1:]:  # Skip the first parameter
				audits_info_map = [x for x in audits_info_map if value in str(x.get(key, ''))]

	audits_info_map = sorted(audits_info_map, key=lambda l: l.get('timestamp'), reverse=True)

	context = {
		"audits_map": audits_info_map
	}
	return build_template('pages/cobrador/payments_audits_list.html', context)


@app.route('/pages/credit_card_conditional', methods=['POST', 'GET'])
@login_from_sessionKey
@login_using_referrer
def credit_card_conditional():
	hotel_code = session_utils.get_current_session().get_value('hotel_code')
	body = request.get_json()
	num_room = body.get("num_room")
	rates = body.get("rates")
	limit_time = datetime(body.get("limit_time"))
	# late_booking = late_booking_rates(rates)

	config_payment = datastore_communicator.get_using_entity_and_params("PaymentConfiguration", hotel_code=hotel_code,
																		search_params=[
																			('type_rule', '=', 'credit_cart_conditional'),
																			('status', '=', 'on')])

	fecha = datetime.now()

	# credit_cart = late_booking
	for item in config_payment:
		if item.get('number_room') <= 0 and item.get('number_room') <= num_room:
			credit_cart = True
		elif item.get('number_room') >= 0 and item.get('number_room') >= num_room:
			credit_cart = True
		elif limit_time > fecha:
			credit_cart = True
		else:
			credit_cart = False

		if item.get('rates') and rates == item.get('rates'):
			credit_cart = True
		if credit_cart:
			break

	if credit_cart:
		return 200, 'OK'
	else:
		return 200, 'KO'


def filter_rules_by_conditions_payed(rules, reservation, hotel_code):
	specific_candidates = []
	rules = sorted(rules, key=lambda x: x['restriction_priority'])
	if not rules:
		return []
	rule_origen = rules[0]
	amount_to_be_payed = _get_amount_to_be_payed(reservation, rule_origen, hotel_code)

	for rule in rules:
		# if rule_origen.get("restriction_priority") == rule.get("restriction_priority"):
		amount_to_be_payed_rule = _get_amount_to_be_payed(reservation, rule, hotel_code)
		if amount_to_be_payed < amount_to_be_payed_rule:
			rule_origen = rule
			amount_to_be_payed = amount_to_be_payed_rule

	for rule in rules:
		if rule_origen.id == rule.id:
			rule['restriction_priority'] += 1


		specific_candidates.append(rule)

	if specific_candidates:
		return specific_candidates
	return []




def filter_rules_by_conditions_priority(rules, reservation, hotel_code):
	specific_candidates = []
	rules = sorted(rules, key=lambda x: x['restriction_priority'])
	if not rules:
		return []
	rule_origen = rules[0]
	amount_to_be_payed = _get_amount_to_be_payed(reservation, rule_origen, hotel_code)
	max_priority = rules[0].get("restriction_priority")

	for rule in rules:
		if rule.get("restriction_priority") > max_priority:
			max_priority = rule.get("restriction_priority")
		if rule_origen.get("restriction_priority") == rule.get("restriction_priority") and rule_origen.id != rule.id:
			amount_to_be_payed_rule = _get_amount_to_be_payed(reservation, rule, hotel_code)
			if amount_to_be_payed < amount_to_be_payed_rule:
				rule_origen = rule
				amount_to_be_payed = amount_to_be_payed_rule

	for rule in rules:
		if rule_origen.id == rule.id and rule_origen.get("restriction_priority") == max_priority:
			rule['restriction_priority'] += 1


		specific_candidates.append(rule)

	if specific_candidates:
		return specific_candidates
	return []

def email_to_customer(hotel_code, reservation, comments, amount, translations, language, manual_payment=False, is_refund=False, paymentId="0"):
	configuration_payment_seeker = get_config_payment_seeker_configuration(hotel_code)
	if not language:
		language = "SPANISH"

	hotel = get_hotel_by_application_id(hotel_code)

	logo_type = ""
	filter_logo = "survey"
	if configuration_payment_seeker and configuration_payment_seeker.get("logo_name"):
		filter_logo = configuration_payment_seeker.get("logo_name")
	logos = get_hotel_logotypes(hotel_code, filter_logo)
	if logos:
		logo_type = logos[0]

	custom_section_translations = get_web_section(hotel, configuration_payment_seeker.get("notifications email custom text section"), language, set_languages=True)

	if amount < 0:
		# it's a refund!
		info_payment = translations.get("T_REFUND_OK_CUSTOMER_EMAIL")
		body_message = translations.get("T_NEW_PAYMENT_EMAIL_BODY")
		subject_email = translations.get("T_TITLE_EMAIL_REFUND")
		title_in_body = translations.get("T_REFUND_DONE_OK")
		if not comments:
			comments = translations.get("T_TITLE_EMAIL_REFUND", "Se ha procedido al pago programado")
			if is_refund and configuration_payment_seeker and configuration_payment_seeker.get("refund email"):
				comments = translations.get("T_NO_COMMENTS", "")
			comments = comments.replace("\n", "<br>")

		header_image = "https://cdn2.paraty.es/test-backend3/images/b61da8ad8d82873=s600"

	else:
		info_payment, body_message, subject_email, title_in_body = build_new_payment_email_content(translations, custom_section_translations)
		if not comments:
			if manual_payment:
				comments = translations.get("T_PAYMENT_MANUAL", "Se ha procedido al pago programado")
			else:
				comments = translations.get("T_PAYMENT_PROGRAMMATICALLY", "Se ha procedido al pago programado")

		header_image = "https://cdn2.paraty.es/test-backend3/images/6b5712725331c2e=s600"


	try:
		extra_info = json.loads(reservation.get("extraInfo", "{}"))
	except:
		extra_info = {}

	currency = get_configuration_property_value(hotel_code, "Base Price Currency") or "EUR"
	if extra_info.get("currency"):
		currency = extra_info.get("currency")
	currency = get_currency_symbol(currency)

	formated_amount = float("{:.2f}".format(float(amount)))
	not_show_generic_image = configuration_payment_seeker.get("not show generic image")
	generic_image_path = GENERIC_SUCCESSFUL_PAYMENT_IMAGE_EMAIL
	if configuration_payment_seeker.get("successful_transaction_header_image"):
		generic_image_path = "/static/images/cobrador/%s.png" % configuration_payment_seeker.get("successful_transaction_header_image")
	if is_refund and configuration_payment_seeker and configuration_payment_seeker.get("refund email"):
		formated_amount = -1 * formated_amount
		email_version = configuration_payment_seeker.get("refund email").upper()
		successfull_main_refund_title = translations.get("T_successful_refund")
		successfull_main_refund_text = translations.get("T_REFUND_OK_CUSTOMER_EMAIL")
		successfull_greeting_refund_text = translations.get("T_greetings")
		customer_support_text = translations.get("T_costumer_support_v2")
		comments = comments if comments else translations.get("T_COMMENTS")

		custom_section_translations = get_web_section(hotel, configuration_payment_seeker.get("refund custom text section"), language, set_languages=True)
		if custom_section_translations.get("T_main_refund_title"):
			successfull_main_refund_title = custom_section_translations.get("T_main_refund_title")
		if custom_section_translations.get("T_main_refund_text"):
			successfull_main_refund_text = custom_section_translations.get("T_main_refund_text")
		if custom_section_translations.get("T_greeting_refund_text"):
			successfull_greeting_refund_text = custom_section_translations.get("T_greeting_refund_text")
		if custom_section_translations.get("T_customer_support_text"):
			customer_support_text = custom_section_translations.get("T_customer_support_text")

		successfull_main_refund_text = (successfull_main_refund_text.replace("@@amount@@", str(formated_amount))
			.replace("@@currency@@", currency)
			.replace("@@identifier@@", reservation.get("identifier"))
			.replace('@@hotel@@', configuration_payment_seeker.get("name hotel corpo"))
			.replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo"))
			.replace('@@payment_id@@', paymentId)
		)
		successfull_greeting_refund_text = successfull_greeting_refund_text.replace('@@hotel@@', configuration_payment_seeker.get("name hotel corpo")).replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo"))
		template_dict = {
			'successfull_refund_title': successfull_main_refund_title,
			'main_text': successfull_main_refund_text,
			'greeting_text': successfull_greeting_refund_text,
			'customer_support_text': customer_support_text,
			'telephone': configuration_payment_seeker.get("telephone"),
			'email': configuration_payment_seeker.get("email"),
			'color1': configuration_payment_seeker.get("color link html", '#967E2C'),
			"logo_type": logo_type,
			"comment": comments,
			"generic_image_path": generic_image_path,
		}
		html_content = build_template('pages/cobrador/emails/email_refund_%s.html' %(email_version), template_dict, force_language=language)
	elif configuration_payment_seeker and configuration_payment_seeker.get("payment link customer", "") in VERSIONS_EMAIL_PAYMENT:
		# TODO: Pending to create a new configuration as "payment email" only to build a tempalte for new payments
			template_dict = {
				'telephone': configuration_payment_seeker.get("telephone"),
				'email': configuration_payment_seeker.get("email"),
				'greeting_text': translations.get('T_greetings').replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo")),
				'main_text':body_message.replace("@@cliente@@",
											 reservation.get("name") + " " + reservation.get("lastName")),
				'hi_customer': '%s %s %s,' % (translations.get('T_HOLA'), reservation.get('name'), reservation.get('lastName')),
				'color1': configuration_payment_seeker.get("color link html", '#967E2C'),
				"logo_type": logo_type,
				"info_payment": info_payment.replace("@@amount@@", str(formated_amount))
					.replace("@@currency@@", currency)
					.replace("@@identifier@@", reservation.get("identifier"))
					.replace('@@payment_id@@', paymentId),

				"comment": comments,
				"not_show_generic_image": not_show_generic_image,
				"generic_image_path": generic_image_path
			}

			html_content = build_template('pages/cobrador/emails/email_payment_confirmation_customer_v2.html', template_dict,
										  force_language=language)
	else:

		template_dict = {"title": title_in_body,
						 "info_payment": info_payment.replace("@@amount@@", str(formated_amount))
						 	.replace("@@currency@@", currency)
						 	.replace("@@identifier@@", reservation.get("identifier"))
							.replace('@@payment_id@@', paymentId),
						 "body": body_message.replace("@@cliente@@",
													  reservation.get("name") + " " + reservation.get("lastName")),
						 "comment": comments,
						 "header_image": header_image,
						 "logo_type": logo_type
						 }

		html_content = build_template('pages/cobrador/emails/email_payment_confirmation_customer.html', template_dict,
									  force_language=language)
		html_content = html_content.replace("@@comments@@", comments)

	title = subject_email + " " + reservation.get("identifier")
	email_hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
	if email_hotel_name:
		title += " " + email_hotel_name

	return html_content, title


@app.route("/pages/cobrador/do_manual_payment", methods=['POST'])
@login_from_sessionKey
@login_using_referrer
def do_manual_payment_in_reservation():
	try:

		current_sess = session_utils.get_current_session()
		session_key = current_sess.get_value("session_id")
		try:
			language = current_sess.get_value("language")
			translations = language_utils.get_web_dictionary(language)
		except:
			translations = language_utils.get_web_dictionary(False)
		today_ts = datetime.now()
		today_ts_txt = date_to_string(today_ts, format="%Y-%m-%d %H:%M:%S")

		body = dict(request.form)
		hotel_code = body.get("hotel_code",current_sess.get_value('hotel_code'))
		logging.info("do_payment_in_reservation hotel_code %s", hotel_code)

		excluded_rules = request.form.getlist('rules')
		if 'all' in excluded_rules:
			rules = [str(x.id) for x in get_rules_by_type(hotel_code, "programmatically")]
			excluded_rules = rules
		elif excluded_rules:
			excluded_rules = excluded_rules[0].split("||")


		identifier = body.get("identifier")

		interface_to_implement = get_interface_controller(hotel_code)
		config = interface_to_implement.get_configuration(hotel_code)
		time_payments_control = "off"
		if config:
			time_payments_control = extract_payments_time_control(config)

		if exit_payment_in_reservation_in_short_period(today_ts_txt, identifier, hotel_code, time_payments_control):
			logging.info("Error there is payment in a short period of time  hotel_code %s", hotel_code)

			response = {
				"status": "KO",
				"message": translations.get("T_PAYMENT_ERROR_TIME")}
			return response

		extra_payment = body.get("extra_payment", "") == "on"

		logging.info("do_payment_in_reservation. Payload: %s", body)

		payed_in_this_transaction = 0.0
		if body.get("amount"):
			payed_in_this_transaction = float(body.get("amount", "0").replace(",", "."))

		reservations = list(
			datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
															   hotel_code=hotel_code))
		if reservations:
			reservation = reservations[0]

			order = get_next_order(reservation)
			save_last_order_in_reservation(hotel_code, reservation, order, excluded_rules)
			payment_type = "manual_extra"

			payment_params = {
				"order": order,
				"error": None,
				"comments": body.get("comments", "").replace("\n", "<br>"),
				"type": payment_type,
				"user": current_sess.get_value('user_name'),
				"reservation_identifier": identifier,
				"amount": payed_in_this_transaction,
				"timestamp": today_ts_txt,
				"extra_info": None
			}


			send_customer_email = None
			send_modification_email = body.get("send_modification_email","") =="on"
			comment_email = body.get("comment_email", "")

			avoid_sending_payment = False
			if hotel_has_avalon(hotel_code):
				send_modification_to_channel = body.get("send_modification_to_channel")
				avoid_sending_payment = False if send_modification_to_channel == "on" else True

			manual_payment_type = body.get("manual_payment_type", '')
			if manual_payment_type:
				payment_params["extra_info"] = {'manual_payment_type': manual_payment_type}

			if order and not "ERROR" in order:
				response = _save_and_send_payments_confirmations(hotel_code, reservation, payment_params,
																 payed_in_this_transaction, extra_payment,
																 send_customer_email,send_modification_email, comment_email,
																avoid_sending_payment=avoid_sending_payment,
																 manual_payment=True, send_hotel_email=False)
			else:
				response = {
					"status": "KO",
					"message": translations.get("T_unsuccessful_manual_recording")}
			return response

		logging.info("Error in transaction!!! hotel_code %s", hotel_code)
		response = {
			"status": "KO",
			"message": translations.get("T_unsuccessful_manual_recording")}
		return response

	except Exception as e:
		logging.info("Error in transaction!!! Hotel_code %s", hotel_code)
		response = {
			"status": "KO",
			"message": translations.get("T_MESSAGE_RESET_FAILED")}
		return response


def get_next_order(reservation):
	last_order_used = ""
	if reservation.get("extraInfo"):
		extra_info = json.loads(reservation.get("extraInfo"))
		last_order_used = extra_info.get("last_merchant_order_used")

	if last_order_used and not "error" in last_order_used.lower():
		last_order_used = last_order_used[0:8]
		numerical_order = int(last_order_used)
		numerical_order += 1

		id_order = str(numerical_order)

		log.info("forced +1 the identifier to be used as a Order: %s", id_order)
	else:

		id_order = reservation.get("identifier")
		# maybe this reservation was done before gateway was configured. So it could be an alphanumeric
		if not id_order.isnumeric():
			id_order = randint(10000000, 99999000)

		numerical_order = int(id_order)
		numerical_order += 1
		id_order = str(numerical_order)
		log.info("forced +1 the identifier to be used as a Order: %s", id_order)

	return id_order


def save_last_order_in_reservation(hotel_code, reservation, order, excluded_rules):
	extra_info = reservation.get("extraInfo", "{}")

	if extra_info:
		try:
			extra_info = json.loads(extra_info)
		except:
			logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!", reservation.get("identifier"))

			return ""

		reservation_id = int(reservation.key.id)
		extra_info["last_merchant_order_used"] = order
		extra_info["exclude_programmatically_payment_cobrador_ids"] = excluded_rules
		reservation["extraInfo"] = json.dumps(extra_info)

		reservation_updated_id = save_reservation_and_flush_cache(reservation, hotel_code, reservation_id=reservation_id)
		logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"), reservation_id)

		return reservation_updated_id


def filter_rules_exclude_rules(rules, reservation, hotel_code):
	if reservation.get("extraInfo"):
		extra_info = json.loads(reservation.get("extraInfo"))
		if not extra_info.get("exclude_programmatically_payment_cobrador_ids") and not extra_info.get("rule_applied"):
			return rules

		specific_candidates = []


		#this is to check if any scheduled payment rule was excluded when the rule was created.
		original_applied_rule_in_reservation = extra_info.get("rule_applied")
		all_rules = get_rules_all(hotel_code)
		original_applied_rule_entity = list(filter(lambda rule: original_applied_rule_in_reservation == rule.get("description"), all_rules))
		excluded_programmatically_rules = []
		if original_applied_rule_entity:
			if original_applied_rule_entity[0].get("excluded_programmatically_rules"):
				excluded_programmatically_rules = original_applied_rule_entity[0].get("excluded_programmatically_rules").split("||")


		for rule in rules:
			#This check is made on the extraInfo of the reservation
			if extra_info.get("exclude_programmatically_payment_cobrador_ids") and str(rule.id) in extra_info.get("exclude_programmatically_payment_cobrador_ids"):
				continue
			#This check is made directly on the configuration of the payment rule
			if str(rule.id) in excluded_programmatically_rules:
				continue
			specific_candidates.append(rule)

		if specific_candidates:
			return specific_candidates
		return []
	return rules


def filter_rules_acumulate_rules(rules, reservation):
	specific_candidates = []
	if reservation.get("extraInfo"):
		extra_info = json.loads(reservation.get("extraInfo"))
		acumulate_rules = []
		if  extra_info.get("programmatically_payment_cobrador_ids", []):
			for rules_programmatically in  extra_info.get("programmatically_payment_cobrador_ids", []):
				for rule in rules:
					if str(rule.id) in rules_programmatically:
						if not rule.get("acumulate_rules"):
							specific_candidates = []
						else:
							acumulate_rules.extend(rule.get("acumulate_rules"))

			if acumulate_rules:
				for rule in rules:
					if str(rule.id) in acumulate_rules and not str(rule.id) in extra_info.get("programmatically_payment_cobrador_ids", []):
						specific_candidates.append(rule)

		else:
			specific_candidates = rules



		if specific_candidates:
			return specific_candidates
		return []
	return rules


def exit_payment_in_reservation_in_short_period(today_ts_txt, identifier, hotel_code, time_payments_control):
	if time_payments_control == "off":
		return False
	today_ts =datetime.strptime(today_ts_txt, "%Y-%m-%d %H:%M:%S")

	filter_params = [('reservation_identifier', '=', identifier)]

	payments_list_by_cobrador = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=False)

	for item in payments_list_by_cobrador:
		try:
			now_timespam = datetime.strptime(item.get("timestamp"), "%Y-%m-%d %H:%M")
		except	Exception as e:
			now_timespam = datetime.strptime(item.get("timestamp"), "%Y-%m-%d %H:%M:%S")

		result = (today_ts - now_timespam)/ timedelta(seconds=1)
		if result < time_payments_control:
			return True

	return False

@app.route("/pages/cobrador/update_historic_payment", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def update_historic_payment():
	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	body = dict(request.args)
	internal_payment_id = body.get('historic_payment_id')
	payed_by_tpv_link_index = body.get("payed_by_tpv_link_index")
	hotel_code = session_utils.get_current_session().get_value('hotel_code')
	identifier = body.get('identifier')
	action_type = body.get('action_type')

	logging.info("%s: updating historic payments of reservation %s" % (action_type, identifier))


	reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))

	response = translations.get("T_GENERIC_ERROR")
	response_ko = {
			"message": translations.get("T_WRONG_DELETED_PAYMENT") if action_type == "delete" else translations.get("T_WRONG_RECOVERED_PAYMENT"),
			"status": "KO"
		}

	if reservations:
		reservation = reservations[0]
		reservation_id = reservation.id
		extra_info = deepcopy(reservation.get("extraInfo", "{}"))
		response_ok = {
			"message":translations.get("T_SUCCESSFULL_DELETED_PAYMENT") if action_type == "delete" else translations.get("T_SUCCESSFULL_RECOVERED_PAYMENT"),
			"status": "OK"
		}

		if extra_info:
			extra_info = json.loads(extra_info)
			if extra_info.get("payed_by_tpv_link") and "payed_by_tpv_link_" in internal_payment_id:
				logging.info("[%s] Payment done by TPV link, update only extra_info!", identifier)
				payed_by_tpv_link_index = int(payed_by_tpv_link_index)
				current_tpv_link_payment = extra_info.get("payed_by_tpv_link")[payed_by_tpv_link_index]
				if current_tpv_link_payment:
					try:
						reservation_id = reservation.id
						if action_type == "delete":
							current_tpv_link_payment["deleted"] = True
							current_tpv_link_payment["deleted_by_user"] = session_utils.get_current_session().get_value(
								'user_name')
							current_tpv_link_payment["deleted_amount"] = current_tpv_link_payment.get("amount")
							current_tpv_link_payment["amount"] = 0
							reservation["extraInfo"] = json.dumps(extra_info)
						else:
							del current_tpv_link_payment["deleted"]
							current_tpv_link_payment[
								"recovered_by_user"] = session_utils.get_current_session().get_value('user_name')
							current_tpv_link_payment["amount"] = current_tpv_link_payment.get("deleted_amount")
							del current_tpv_link_payment["deleted_amount"]

						reservation["extraInfo"] = json.dumps(extra_info)
						datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation,
																 hotel_code=hotel_code)
					except Exception as e:
						logging.info(" [%s] Error trying to update an historic payment", identifier)
						logging.warning(e)
						response_ko["message"] = translations.get("T_WRONG_DELETED_PAYMENT") if action_type == "delete" else translations.get("T_WRONG_RECOVERED_PAYMENT"),
						return response_ko
			else:

				filter_params = [('reservation_identifier', '=', identifier)]
				payments_list_by_cobrador = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=False)

				current_payment = list(filter(lambda x: x.id == int(internal_payment_id), payments_list_by_cobrador))
				if current_payment:
					current_payment = current_payment[0]
					logging.info("[%s] Payment directly done by cobrador, update extra_info and the payment with order %s!!" %(identifier, current_payment.get("order")))
					try:
						payment_id = current_payment.id
						amount = float(current_payment.get("amount")) if not current_payment.get("deleted_amount") else float(current_payment.get("deleted_amount"))
						if action_type == "delete":
							current_payment["deleted"] = True
							current_payment["deleted_by_user"] = session_utils.get_current_session().get_value('user_name')
							current_payment["deleted_amount"] = current_payment.get("amount")
							current_payment["amount"] = 0
							update_extraInfo_payments(reservation_id, reservation, extra_info, amount, current_payment.get("type"),hotel_code, identifier, updated_type="deleted")
						else:
							del current_payment["deleted"]
							current_payment["recovered_by_user"] = session_utils.get_current_session().get_value('user_name')
							current_payment["amount"] = current_payment.get("deleted_amount")
							del current_payment["deleted_amount"]
							update_extraInfo_payments(reservation_id, reservation, extra_info, amount, current_payment.get("type"),hotel_code, identifier,updated_type="recovered")
						#datastore_communicator.save_to_datastore("PaymentsReservation", payment_id, current_payment, hotel_code=hotel_code)
						save_secure_payment_reservation(current_payment, hotel_code, id_payment=payment_id)
					except Exception as e:
						logging.info(" [%s] Error trying to update an historic payment", identifier)
						logging.warning(e)
						response_ko["message"] = translations.get("T_WRONG_DELETED_PAYMENT") if action_type == "delete" else translations.get("T_WRONG_RECOVERED_PAYMENT"),
						return response_ko
		try:
			logging.info("sending logical actions to adapters: %s", identifier)
			send_reservation_to_adapter(hotel_code, identifier)
		except Exception as e:
			logging.info(" [%s] Error trying to send to adapter  an historic payment", identifier)
			logging.warning(e)


		return response_ok
	return response

@app.route("/pages/cobrador/get_comments", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def get_comments():

	body = dict(request.args)
	hotel_code = body.get('hotel_code')
	payment_kind = 'PaymentsReservation'
	if body.get("original_entity"):
		payment_kind = body.get("original_entity")
	read_key = datastore_communicator.build_key(payment_kind, int(body.get('key')), hotel_code=hotel_code)


	try:
		results = datastore_communicator.get_entity_by_key(read_key, hotel_code)

		if not results:
			response = {
				"message": "Key not found",
				"status": "KO"
			}
			return response

		response = {
			"message": results['comments'],
			"status": "OK"
		}
		return response

	except Exception as e:
		response = {
			"message": e,
			"status": "KO"
		}
		return response


@app.route("/pages/cobrador/update_comment", methods=['GET'])
@login_from_sessionKey
@login_using_referrer
def update_comment():
	try:
		language = session_utils.get_current_session().get_value("language")
		translations = language_utils.get_web_dictionary(language)
	except:
		translations = language_utils.get_web_dictionary(False)

	body = dict(request.args)
	hotel_code = body.get('hotel_code')
	payment_kind = 'PaymentsReservation'
	if body.get("original_entity"):
		payment_kind = body.get("original_entity")
	read_key = datastore_communicator.build_key(payment_kind, int(body.get('key')), hotel_code=hotel_code)
	write_key = int(body.get('key'))
	new_comment = body.get('new_text')
	result = datastore_communicator.get_entity_by_key(read_key, hotel_code)

	response = {
		"message": "Register not found",
		"status": "KO"
	}

	if not result:

		return response

	response = {
		"message": translations.get("T_EDIT_COMMENT_OK"),
		"status": "OK"
	}

	try:
		result['comments'] = new_comment
		result['real_kind'] = payment_kind
		#datastore_communicator.save_to_datastore("PaymentsReservation", write_key, result, hotel_code=hotel_code)
		save_secure_payment_reservation(result, hotel_code, id_payment=write_key)
		return response

	except Exception as e:
		logging.info(" Register [%s] Error trying to update an comment", write_key)
		logging.warning(e)
		response["message"] = "Error editing the comment"

		return response


def update_extraInfo_payments(reservation_id, reservation, extra_info, amount, transaction_type, hotel_code, identifier, updated_type):
	logging.info("[%s] Updating extra info when a payment is %s!" %(identifier,updated_type))
	if transaction_type == "extra":
		extra_payed_by_cobrador = float(extra_info.get("extra_payed_by_cobrador"))
		if updated_type == "deleted":
			extra_payed = extra_payed_by_cobrador - amount
		else:
			extra_payed = extra_payed_by_cobrador + amount
		extra_info["extra_payed_by_cobrador"] = extra_payed
	elif transaction_type == "manual" or transaction_type == "manual_extra":
		payed_by_cobrador = float(extra_info.get("payed_by_cobrador"))
		if updated_type == "deleted":
			payed_by_cobrador = payed_by_cobrador - amount
		else:
			payed_by_cobrador = payed_by_cobrador + amount
		extra_info["payed_by_cobrador"] = payed_by_cobrador

	reservation["extraInfo"] = json.dumps(extra_info)
	datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)



def filter_all_days_in_the_range(num_days_reservation, reservation, start_date, end_date):

	start_date = datetime.strptime(start_date, '%d/%m/%Y')
	end_date = datetime.strptime(end_date, '%d/%m/%Y')
	star_date_reservation = datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
	end_date_reservation = datetime.strptime(reservation.get("endDate"), "%Y-%m-%d") - timedelta(days=1)
	date_list = date_range_list(star_date_reservation, end_date_reservation)
	if reservation.get("price_days"):
		new_prices_days = {}
		for room, days_prices in reservation['price_days'].items():
			new_prices_days[room] = []
			day_cont = 0
			for price in days_prices:

				day = date_list[day_cont]

				if start_date <= day <= end_date:
					new_prices_days[room].append(float(price))

				day_cont += 1

			if not new_prices_days:
				return num_days_reservation
			num_days_reservation = len(new_prices_days[room])
		if new_prices_days and num_days_reservation>0:
			reservation['price_days'] = new_prices_days
	else:
		new_num_days = 0
		for day in date_list:
			if start_date <= day <= end_date:
				new_num_days = num_days_reservation + 1

		if num_days_reservation:
			reservation['price'] = (reservation['price'] / num_days_reservation) * new_num_days
			num_days_reservation = new_num_days
	return num_days_reservation



#SPPECIAL END POINTS
@app.route("/pages/cobrador/get_reservation_metadata", methods=['GET', 'POST'])
def get_reservation_metadata():
	hotel_code = request.values.get('hotel_code')
	sid = request.values.get('sid')
	identifier = request.values.get('identifier')

	query_params = []

	if hotel_code:
		query_params.append(("hotel_code", "=", hotel_code))
	if sid:
		query_params.append(("sid", "=", sid))
	if identifier:
		query_params.append(("identifier", "=", identifier))
	response_json = {}
	reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
																			  query_params)
	if reservation_metadata:
		if request.values.get('get_all'):
			response_json = []
			for x in reservation_metadata:
				extra_info = json.loads(x.get("extraInfo"))
				response_json.append({"sid": x.get("sid"),
									 "identifier": x.get("identifier"),
									 "extra_info": extra_info,
									 "hotel_code": x.get("hotel_code")})

		else:
			reservation_metadata = reservation_metadata[0]
			extra_info = json.loads(reservation_metadata.get("extraInfo"))
			response_json = {"sid": reservation_metadata.get("sid"),
							 "identifier": reservation_metadata.get("identifier"),
							 "extra_info": extra_info,
							 "hotel_code": reservation_metadata.get("hotel_code")}


	response = make_response(response_json)
	response.headers["Content-Type"] = "application/json"
	return response_json


@app.route("/pages/cobrador/get_reservation_payments", methods=['GET'])
def get_reservation_payments():
	identifier = request.args.get("identifier")
	hotel_code = request.args.get("hotel_code")
	only_last_payment = bool(request.args.get("only_last_payment", False))

	filter_params = []
	start_date = request.args.get("startDate")
	if start_date:
		date = datetime.strptime(start_date, "%d/%m/%Y")
		start_date_param = ('timestamp', '>=', date.strftime("%Y-%m-%d 00:00:00"))
		filter_params.append(start_date_param)
	end_date = request.args.get("endDate")
	if end_date:
		date = datetime.strptime(end_date, "%d/%m/%Y")
		end_date_param = ('timestamp', '<=', date.strftime("%Y-%m-%d 23:59:59"))
		filter_params.append(end_date_param)
	all_types = bool(request.args.get("all_types", False))

	only_real_amounts_in_reservation = True
	if all_types:
		only_real_amounts_in_reservation = False

	if hotel_code and not identifier:
		logging.info("Getting all payments by hotel because the request hasn't identifier, maybe has range dates")
		final_real_payments_list = []
		payment_reservation_json_list = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=only_real_amounts_in_reservation)
		for payment in payment_reservation_json_list:
			if is_real_payment_in_reservation(payment) and not payment.get("error") and float(payment.get("amount", 0)) > 0:
				final_real_payments_list.append(payment.get("reservation_identifier"))

		return final_real_payments_list
	else:
		payment_reservation_json_list = get_all_payment_reservation_list(identifier, hotel_code, only_real_amounts_in_reservation)

	if payment_reservation_json_list and only_last_payment:
		last_reservation_payment = sorted(payment_reservation_json_list, key=lambda k: k['timestamp'], reverse=True)[0]
		return last_reservation_payment

	return payment_reservation_json_list

@app.route("/pages/cobrador/save_web_modification_payment/<hotel_code>", methods=['GET','POST'])
def save_web_modification_payment(hotel_code):
	logging.info("New payment comes from modification web!!!")

	payment_params = request.get_json()
	payment_order = payment_params.get("order")
	identifier = payment_params.get("reservation_identifier")
	update_extrainfo = payment_params.get("update_extrainfo", True)

	new_payments_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
	payment_params["timestamp"] = new_payments_timestamp

	payments_in_reservation = list(datastore_communicator.get_using_entity_and_params('PaymentsReservation', [('reservation_identifier', '=', identifier)], hotel_code=hotel_code))
	for payment in payments_in_reservation:
		timestamp = payment.get('timestamp')
		if not correct_timestamp(timestamp, new_payments_timestamp):
			logging.warning("Avoiding save_web_modification_payment because a payment has just been saved")
			return 'KO'

	logging.info("[%s] Saving new web modification payment with order %s", identifier, payment_order)
	save_secure_payment_reservation(payment_params, hotel_code)

	reservation = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))

	if reservation:
		reservation = reservation[0]

		extra_info = reservation.get("extraInfo")
		if extra_info:
			extra_info = json.loads(extra_info)
			logging.info("[%s] Updating extraInfo with new amount paid by cobrador (payed_by_cobrador)", identifier)
			payed_in_this_transaction = float(payment_params.get("amount"))

			accumulated_payed = float(extra_info.get("payed_by_cobrador", "0"))

			accumulated_payed = accumulated_payed + payed_in_this_transaction

			if update_extrainfo:
				extra_info["lastPaymentTimestamp"] = payment_params.get("timestamp", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

				accumulated_payed = float(extra_info.get("payed_by_cobrador", "0"))

				extra_info["payed_by_cobrador"] = accumulated_payed

				extra_info["last_merchant_order_used"] = payment_order

				reservation["extraInfo"] = json.dumps(extra_info)

				reservation_id = int(reservation.key.id)
				datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)

				response = make_response('OK', 200)

				response.headers['Content-Type'] = 'text/plain'
				return response

			return {
				"payed_by_cobrador": accumulated_payed,
				"lastPaymentTimestamp": payment_params.get("timestamp", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
				"last_merchant_order_used": payment_order
			}
	else:
		logging.warning("[%s] Not reservation found to update, please check if everything it's ok!", identifier)
		return "KO"

	return "KO"


def correct_timestamp(original, new_timestamp, seconds=10):
	format = '%Y-%m-%d %H:%M:%S'
	datetime1 = datetime.strptime(original, format)
	datetime2 = datetime.strptime(new_timestamp, format)

	difference = abs((datetime2 - datetime1).total_seconds())

	return difference >= seconds


@retry(Exception, tries=4, delay=3)
def send_modification_emails(hotel_code, reservation):
	identifier = reservation.get("identifier")
	try:
		hotel = get_hotel_by_application_id(hotel_code)
		url = get_inner_url(hotel)
		#send confirmations!!!
		send_confirmation_post = build_encrypted_url("%s/send-confirmation/?id=%s&type=customer&modification=true&source=booking-container" % (
		url, identifier))
		logging.info("[PAYMENT_SEEKER]: sending confirmation for CUSTOMER: %s", send_confirmation_post)
		confirmation_browser = requests.post(send_confirmation_post, json={})
		logging.info("[PAYMENT_SEEKER]: Reservation sended to CUSTOMER: %s", identifier)
		logging.info(confirmation_browser)
		send_confirmation_post = build_encrypted_url("%s/send-confirmation/?id=%s&type=manager&modification=true&source=booking-container" % (
		url, identifier))
		logging.info("[PAYMENT_SEEKER]: sending confirmation for MANAGER: %s", send_confirmation_post)
		confirmation_browser = requests.post(send_confirmation_post, json={})
		logging.info("[PAYMENT_SEEKER] Reservation sended to MANAGER: %s", identifier)
		logging.info(confirmation_browser)
	except Exception as e:
		logging.error("[PAYMENT_SEEKERPEP_LINKS] Something went wrong with finish_paid_reservation_by_tpv_link!! SENDING EMAILS. but lets continue: %s", identifier)
		logging.error(e)


def filter_rules_number_days_stay_reservation(rules, reservation):
	specific_candidates = []
	specific_candidates_aux=[]
	rules = sorted(rules, key=lambda x: x['restriction_priority'])
	if not rules:
		return []


	# Fechas proporcionadas
	start_date = datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
	end_date = datetime.strptime(reservation.get("endDate"), "%Y-%m-%d")

	# Calcular la diferencia en días
	num_days = (end_date - start_date).days

	for rule in rules:
		if rule.get("number_days_stay_reservation"):
			if num_days >= int(rule.get("number_days_stay_reservation")):
				specific_candidates_aux.append(rule)
		else:
			specific_candidates_aux.append(rule)


	for rule in specific_candidates_aux:
		if rule.get("number_days_stay_max_reservation"):
			if num_days <= int(rule.get("number_days_stay_max_reservation")):
				specific_candidates.append(rule)
		else:
			specific_candidates.append(rule)


	if specific_candidates:
		return specific_candidates
	return []




def filter_rules_minimun_amount_reservation(rules, reservation):
	specific_candidates = []
	rules = sorted(rules, key=lambda x: x['restriction_priority'])
	if not rules:
		return []

	amount = reservation.get("price",0)



	for rule in rules:
		if rule.get("minimum_reservation_amount"):
			if float(amount) >= float(rule.get("minimum_reservation_amount")):
				specific_candidates.append(rule)
		else:
			specific_candidates.append(rule)


	if specific_candidates:
		return specific_candidates
	return []



@app.route("/pages/cobrador/send_mail_for_pending_reservation",  methods=['GET','POST'])
def send_mail_for_pending_reservation():
	logging.info("entering send_new_link_email_to_customer")
	hotel_code = request.args.get("hotel_code")
	identifier = request.args.get('identifier')
	discount = request.args.get('discount')
	action = request.args.get('action')
	comments = ""

	configuration_payment_seeker = get_config_payment_seeker_configuration(hotel_code)
	hotel = get_hotel_by_application_id(hotel_code)

	type_link = "pending"

	reservations = list(
		datastore_communicator.get_using_entity_and_params('Reservation', [('identifier', '=', identifier)],
														   hotel_code=hotel_code))
	if not reservations:
		return "No reservations found"

	reservation = reservations[0]
	language = reservation.get("language", "SPANISH")

	extra_info = reservation.get("extraInfo", "{}")
	if extra_info:
		try:
			extra_info = json.loads(extra_info)
		except json.JSONDecodeError:
			logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
			extra_info = {}

	translations = language_utils.get_web_dictionary(language, hotel=hotel)
	customer_email = reservation.get("email")

	email_sender_config = get_configuration_property_value(hotel_code, EMAIL_SENDER_CONFIG)
	email_sender = build_email_sender_from_hotel_webs_config(email_sender_config,
															 backup_sender="<EMAIL>")

	link = build_new_link(reservation, type_link, 0, hotel_code, unique_usage_link="", force_payed_link="")

	logo_type = ""
	filter_logo = "survey"
	if configuration_payment_seeker and configuration_payment_seeker.get("logo_name"):
		filter_logo = configuration_payment_seeker.get("logo_name")
	logos = get_hotel_logotypes(hotel_code, filter_logo)
	if logos:
		logo_type = logos[0]
	amount_text = reservation.get("price")
	currency = extra_info.get("currency")
	expire_hours = configuration_payment_seeker.get("expire_hours", "")
	if expire_hours:
		try:
			# check if this expire hoyr is bigger than arrival client
			current_datetime = datetime.now()

			parsed_start_date = datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
			# Add 72 hours to the current date and time
			today_plus_hours = current_datetime + datetime.timedelta(hours=float(expire_hours))

			if today_plus_hours > parsed_start_date:
				end_day_of_start_date = datetime.strptime(reservation.get("startDate") + " 23:59:59", "%Y-%m-%d %H:%M:%S")

				# Calculate the time difference
				time_difference = end_day_of_start_date - current_datetime

				# Calculate the hours between the two datetime objects
				hours_difference = int(time_difference.total_seconds() / 3600)

				expire_hours = hours_difference
		except:
			logging.warning("Imposible to calculate expire_hours in payment link email %s", identifier)

	translations_section = language_utils._get_translation_section(hotel, "_email_link_payed", language, True)

	user = 'automatic_mail_link'
	if action == "automatic_promotion":
		title = translations.get('T_NEW_LINK_EMAIL_TITLE_PROMOTION', "")
		if translations_section.get("T_NEW_LINK_EMAIL_TITLE_PROMOTION"):
			title = translations_section.get("T_NEW_LINK_EMAIL_TITLE_PROMOTION")

		title = title.replace('@@DISCOUNT', discount)
		email_hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
		if email_hotel_name:
			title += " " + email_hotel_name

		if configuration_payment_seeker and configuration_payment_seeker.get("promotion payment link customer", "")=="V2":
			promotion_email_backgroung = configuration_payment_seeker.get("promotion email backgroung", "")

			save_promotion_text = translations.get('T_safe_paid_promotion', "")
			if translations_section.get("T_safe_paid_promotion"):
				save_promotion_text = translations_section.get("T_safe_paid_promotion")
			save_promotion_text = save_promotion_text.replace('@@discount@@',  discount).replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo"))

			template_dict = {
				'telephone': configuration_payment_seeker.get("telephone"),
				'email': configuration_payment_seeker.get("email"),
				'greeting_text': translations.get('T_greetings').replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo")),
				'main_text': save_promotion_text,
				'hi_customer': '%s %s %s,' % (translations.get('T_HOLA'), reservation.get('name'), reservation.get('lastName')),
				# 'charge_amount': translations.get('T_charge').replace('@@AMOUNT@@', str(amount_text)).replace('@@CURRENCY@@', currency),
				'expire_hours': translations.get('T_will_expire').replace('@@expire_hours@@', str(expire_hours)),
				'expire_link_with_promotion': translations.get('T_expire_link_with_promotion', ''),
				'aprovecha_descuento': translations.get('T_aprovecha_descuento', '').replace('@@discount@@',  discount),
				'exclusive_discount': translations.get('T_exclusive_discount', '').replace('@@discount@@',  discount),
				'color1': configuration_payment_seeker.get("color link html", '#967E2C'),
				'link': link,
				"logo_type": logo_type,
				"promotions": True,
				"discount": discount,
				"promotion_email_backgroung": promotion_email_backgroung
			}

			html_content = build_template('pages/cobrador/emails/email_payment_link_customer_promotions_v2.html', template_dict,
										  force_language=language)

		elif configuration_payment_seeker:
			all_rooms = get_rooms_of_hotel(hotel)
			all_rooms = {x.get("key"): x.get("name") for x in all_rooms}
			all_rates = get_rates_of_hotel(hotel)
			all_rates = {x.get("key"): x.get("name") for x in all_rates}
			all_board = get_boards_of_hotel(hotel)
			all_board = {x.get("key"): x.get("name") for x in all_board}
			rate = all_rates.get(reservation.get("rate"))
			room_name = get_room_name(all_rooms, extra_info.get("original_rooms"))
			board = all_board.get(reservation.get("regimen"))


			template_dict = {
				'logo_type': logo_type,
				'hotelname': configuration_payment_seeker.get("name hotel"),
				'main_text': translations_section.get('T_email_text_v3').replace('@@name@@', reservation.get('name')).replace('@@surname@@',
																															  reservation.get('lastName')),
				'entrydate': reservation.get("startDate"),
				'departuredate': reservation.get("endDate"),
				'regimen': board,
				'rate': rate,
				'roomtype': room_name,
				'identifier': reservation.get("identifier"),
				'total_pending': reservation.get("price"),
				'currency': currency,
				'payment_link': link,
				'color1': configuration_payment_seeker.get("color link html", '#002E54'),
				'phone': configuration_payment_seeker.get("telephone"),
				'whatsapp': configuration_payment_seeker.get("whatsapp", ""),
				'email': configuration_payment_seeker.get("email"),
				'greeting_text': translations_section.get('T_greetings').replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel corpo")),
				"one_step_to_finish": translations_section.get('T_one_step_to_promotion').replace('@@discount@@', discount),
				"comments": comments
			}


			try:
				html_content = build_template('pages/cobrador/emails/email_new_payment_link_customer_V4.html', template_dict, force_language=language)

			except Exception as e:
				logging.info(e)
	elif action == "alert_with_automatic_cancellation" or "alert_expire_hours_link":
		user = action
		title = translations_section.get(f"T_title_{action}", '')
		email_hotel_name = get_configuration_property_value(hotel_code, "Nombre del hotel")
		if email_hotel_name:
			title = title.replace('@@HOTEL@@', email_hotel_name)
		translations_section = language_utils._get_translation_section(hotel, "_email_link_payed", language, True)
		auto_cancel_image = get_picture_from_section(hotel_code, 'automatic_cancelation', translations_section, language, get_url=True) if action == "alert_with_automatic_cancellation" else ""
		alert_expire_image = get_picture_from_section(hotel_code, 'alert_expire_image', translations_section, language, get_url=True) if action == "alert_expire_hours_link" else ""
		almost_expire_hours = configuration_payment_seeker.get("alert_expire_hours_link", 0)
		color_link_html = configuration_payment_seeker.get("color link html", '#002E54')
		template_dict = {
			'logo_type': logo_type,
			'color_hotel': color_link_html,
			'T_RESERVATION_CANCELLED': translations_section.get('T_RESERVATION_CANCELLED'),
			'T_PAYMENT_LINK_EXPIRED': translations_section.get('T_PAYMENT_LINK_EXPIRED'),
			'T_RETRY_SEARCH': translations_section.get('T_RETRY_SEARCH'),
			'T_PAY_NOW': translations_section.get('T_PAY_NOW'),
			'T_LINK_ALMOST_EXPIRED': translations_section.get('T_LINK_ALMOST_EXPIRED'),
			'T_PAYMENT_LINK_ALMOST_EXPIRED': translations_section.get('T_PAYMENT_LINK_ALMOST_EXPIRED'),
			'T_customer_support_': translations_section.get('T_customer_support_',''),
			'T_greetings': translations_section.get('T_greetings','').replace('@@HOTEL@@', configuration_payment_seeker.get("name hotel")),
			'hotel_phone': configuration_payment_seeker.get("telephone"),
			'hotel_email': configuration_payment_seeker.get("email"),
			'retry_link': generate_booking1_direct_search_by_reservation(hotel_code, reservation) if action == "alert_with_automatic_cancellation" else "",
			'payment_link': link if action == "alert_expire_hours_link" else "",
			'auto_cancel_image': auto_cancel_image,
			'alert_expire_image': alert_expire_image,
			'almost_expire_hours': almost_expire_hours,
		}
		# Mark the reservation as alert sent
		if action == "alert_expire_hours_link":
			extra_info['alert_expire_hours_link_sent'] = True

		try:
			html_content = build_template(f'pages/cobrador/emails/email_{action}.html', template_dict, force_language=language)

		except Exception as e:
			logging.info(f"alert_with_automatic_cancellation template error{e}")


	sendEmail(customer_email, title, "", html_content, sender=email_sender, backup_sender="<EMAIL>")
	logging.info("email sent correctly to %s", customer_email)

	link_email_hotel = get_configuration_property_value(hotel_code, "Payment link copy hotel")
	if link_email_hotel:
		sendEmail(link_email_hotel, title, "", html_content, sender=email_sender, backup_sender="<EMAIL>")

	# save a fake payment, for the historic
	error = None
	order = identifier
	if "ERROR" in order:
		info_error = order.split(SEPARATOR_INFO_ERROR_ORDER)
		if len(info_error) > 1:
			order = info_error[1]
			error = info_error[0]
		else:
			error = order

	payment_type = PAYMENT_TYPE_LINK
	today_ts = datetime.now()
	today_ts_txt = date_to_string(today_ts, format="%Y-%m-%d %H:%M:%S")

	payment_params = {
		"order": order,
		"error": error,
		"comments": comments,
		"type": payment_type,
		"user": user,
		"reservation_identifier": identifier,
		"amount": float(reservation.get("price")),
		"timestamp": today_ts_txt,
	}
	payment_id = save_secure_payment_reservation(payment_params, hotel_code)

	extra_info['link_payment_sended'] = "ok"
	extra_info["payment_link_send_date"] = get_hotel_datetime(hotel_code).strftime("%Y-%m-%d %H:%M:%S")
	reservation["extraInfo"] = json.dumps(extra_info)
	logging.info("[%s][UPDATE] Saving link of payment in reservation, hotel_code: %s", identifier, hotel_code)
	reservation_id = int(reservation.key.id)
	datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)

	return "OK"


