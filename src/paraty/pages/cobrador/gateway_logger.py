import os
import pickle

from flask import request
from google.cloud import logging as gcp_logging
from google.cloud.logging_v2 import Resource

from paraty import app
from paraty.pages.cobrador.cobrador_constants import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
from paraty_commons_3.logging.my_gae_logging import get_trace_id, logging
from paraty_commons_3.redis.redis_communicator import build_redis_client
from paraty.config import Config

import uuid


@app.route("/log", methods=["POST"])
def log_handler():
	body = request.get_json(force=True, silent=True)
	session_id = body.get("session")
	message = body.get("message")
	if session_id and message:
		redis_client = build_redis_client(REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD)
		session = redis_client.get("payment_seeker_log_sess_%s" % session_id)
		if session:
			session = pickle.loads(session)
			trace_id = session.get("trace_id")
			project = Config.PROJECT
			if hasattr(logging, "_logger") and logging._logger is not None:
				logger = logging._logger
			else:
				log_client = gcp_logging.Client()
				logger = log_client.logger("appengine.googleapis.com%2Fstdout")

			# trace_str = "projects/{project_id}/traces/{trace_id}".format(project_id=project, trace_id=trace_id)

			resource = Resource(
				type="gae_app",
				labels={
					'module_id': session.get("module_id"),
					'project_id': project,
					'version_id': session.get("version_id")
				}
			)

			if len(message) > 1000:
				message = message[:1000]

			if body.get("level") == "info":
				logger.log_text(message, resource=resource, severity="INFO", trace=trace_id)

			elif body.get("level") == "warning":
				logger.log_text(message, resource=resource, severity="WARNING", trace=trace_id)

			elif body.get("level") == "error":
				logger.log_text(message, resource=resource, severity="ERROR", trace=trace_id)

	return "ok"


def build_logger_context():
	redis_client = build_redis_client(REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD)

	session_data = {
		"trace_id": get_trace_id(),
		"version_id": os.getenv('GAE_VERSION'),
		"module_id": os.getenv('GAE_SERVICE')
	}

	session_id = str(uuid.uuid4())

	redis_client.setex("payment_seeker_log_sess_%s" % session_id, time=10*60, value=pickle.dumps(session_data))

	return session_id
