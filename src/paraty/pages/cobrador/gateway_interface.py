from abc import ABCMeta, abstractmethod
class GatewayInterface():
	'''
	This interface needs to be implemented by any integration using this project as a base
	'''
	__metaclass__ = ABCMeta

	@abstractmethod
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		raise NotImplementedError

	@abstractmethod
	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		raise NotImplementedError

	@abstractmethod
	def reservation_has_token(self, reservation):
		raise NotImplementedError

	@abstractmethod
	def gateway_has_token(self):
		raise NotImplementedError

	@abstractmethod
	def get_initial_order_id_from_extra_info(self):
		raise NotImplementedError

	@abstractmethod
	def translate_error(self):
		raise NotImplementedError



class FormGatewayInterface():
	'''
	This interface needs to be implemented by any integration using this project as a base
	'''
	__metaclass__ = ABCMeta

	@abstractmethod
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, add_button, extra_data):
		'''
		build hmtl form to be injected in hotel_webs
		'''
		raise NotImplementedError

	@abstractmethod
	def process_gateway_response(self, hotel_code, gateway_type, response):
		'''
		process response and inform if payment is correctly don
		'''
		raise NotImplementedError

	@abstractmethod
	def get_fast_response(self, hotel_code, gateway_type, response):
		'''
		get fast response
		'''
		raise NotImplementedError



