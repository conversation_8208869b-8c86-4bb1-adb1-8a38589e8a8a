import json
import logging
import datetime
import base64
import requests

from paraty import app, Config
from xml.dom import minidom
from flask_cors import cross_origin
from flask import request as r, redirect

from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, \
	_get_project_id_and_namespace, add_parameters_and_namespace_to_url, translate_language_code, normalice_txt_for_xml, \
	audit_response
from paraty.utilities.templates.templates_processor import build_template

from paraty.pages.cobrador.cobrador_constants import REDUNICRE, SEND_REDUNICRE_RESPONSE_TO_HOTEL, EMAIL_BOOKING, \
	METADATA_URL, GATEWAY_ERROR_RETURNED, GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.utils.proxy_utils import post_using_generic_proxy
from paraty_commons_3.datastore import datastore_communicator


class RedunicreFormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		integration_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		redunicre_data = get_redunicre_data(integration_configuration)
		formated_amount = gateways_format_price(amount)
		logging.info(f'[REDUNICRE] Price: {amount} -> formated amount: {formated_amount}')
		redunicre_data['Ds_Merchant_Amount'] = formated_amount
		redunicre_data['Ds_Merchant_Order'] = payment_order_id

		_, multitenancy = _get_project_id_and_namespace(get_hotel_by_application_id(hotel_code))
		url_payment = add_parameters_and_namespace_to_url(integration_configuration.get('url_payment', ''), {'sid': sid}, multitenancy)
		# url_payment = add_parameters_and_namespace_to_url("http://127.0.0.1:8080/redunicre/payment", {'sid': sid}, multitenancy)
		context = {
			'form': redunicre_data,
			'integration_type': gateway_type,
			'sid': sid,
			'url_payment': url_payment,
			'hotel_code': hotel_code,
			'identifier': payment_order_id,
			'price': amount,
			'gateway_logos': redunicre_data.get('Ds_Logos_to_display', '')
		}
		template_path = 'pages/cobrador/gateways/_redunicre.html'
		return build_template(template_path, context)

	def process_gateway_response(self, hotel_code, gateway_type, response):
		logging.info("Received POST at ReservationConfirmedWithGatewayHandler (REDUNICRE HANDLER)")
		request_dict = dict(r.args)
		logging.info(f"Response: {response}")
		logging.info(f"Request dict: {request_dict}")
		token = request_dict.get('token')
		sid = request_dict.get('sid')
		reservation_metadata = {}
		extra_info = {}
		try:
			headers = {
				'Content-Type': 'application/json'
			}
			reservation_metadata = requests.get(f"{METADATA_URL}?sid={sid}&get_all=true", headers=headers, timeout=5)
			reservation_metadata = json.loads(reservation_metadata.text)
			reservation_metadata = [x for x in reservation_metadata if x.get('extra_info', {}).get("request_id") == token][0]
			extra_info = reservation_metadata.get('extra_info')
		except Exception as e:
			logging.error(f"Error getting reservation_metadata: {reservation_metadata} [{e}]")

		request_id = extra_info.get('request_id')
		identifier = reservation_metadata.get('identifier')

		cobrador_response = {
			'GATEWAY_ORDER_ID': identifier,
			'PAYMENT_GATEWAY_NAME': REDUNICRE,
			'no_redirect': False,
			'GATEWAY_EXTRA_INFO': {}
		}
		if token and token == request_id:
			redunicre_data, url = get_redunicre_enviroment(gateway_type, hotel_code)
			isValid, amount = _get_payment_info_details(url, redunicre_data, token, hotel_code, identifier)
			if isValid:
				cobrador_response['GATEWAY_PAID_AMOUNT'] = amount
				cobrador_response['CODE'] = GATEWAY_SUCESS_RETURNED
				cobrador_response['GATEWAY_EXTRA_INFO']['sid'] = reservation_metadata.get('sid', '')
				cobrador_response['PAYMENT_FAILED'] = False
				return cobrador_response
		else:
			logging.error(f"Not making reservation {identifier} due to an invalid token: {token}")
			cobrador_response['CODE'] = GATEWAY_ERROR_RETURNED

		cobrador_response['PAYMENT_FAILED'] = True
		return cobrador_response

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


@app.route("/redunicre/payment", methods=["POST"])
@cross_origin(origin='*')
def checkout_redunicre():
	logging.info("[REDUNICRE] Getting url to redirect customer")
	logging.info("Starting process gateway...")
	integration_configuration = {}
	post_data = {}
	url_to_redirect = ''
	data = r.values
	sid = data.get('sid')
	hotel_code = data.get('hotel_code')
	_, multitenancy = _get_project_id_and_namespace(get_hotel_by_application_id(hotel_code))

	try:
		logging.info(f'[REDUNICRE] Received data: {data}')
		integration_type = data.get('integration_type')
		amount = data.get('Ds_Merchant_Amount')
		lang_customer = translate_language_code(data.get('Ds_Merchant_ConsumerLanguage', ""))
		order = data.get('Ds_Merchant_Order')
		taxes = data.get('Ds_Merchant_Taxes', 0)
		country_customer = data.get('Ds_Merchant_ConsumerCountry')
		identifier = data.get('identifier')
		price = data.get('price')

		logging.info(f'[REDUNICRE] Hotel: {hotel_code} - Gateway: {integration_type}')
		integration_configuration = get_payment_gateway_configuration(integration_type, hotel_code)
		logging.info(f'[REDUNICRE] Integration configuration: {integration_configuration}')

		# customer data
		Ds_Merchant_Buyer_Email = data.get('Ds_Merchant_Buyer_Email')
		Ds_Merchant_Buyer_Name = data.get('Ds_Merchant_Buyer_Name')
		Ds_Merchant_Buyer_LastName = data.get('Ds_Merchant_Buyer_LastName')

		redunicre_data = None

		logging.info(f'[REDUNICRE] Getting payment gateway configuration: {integration_type}')
		redunicre_data = get_redunicre_data(integration_configuration)

		if redunicre_data:
			logging.info(f'[REDUNICRE] Redunicre data: {redunicre_data}')
			url = redunicre_data.get('Ds_Merchant_Payment_Url')
			url_ok = add_parameters_and_namespace_to_url(integration_configuration.get('url_ok'), {'sid': sid, 'warm_request': 'true'}, multitenancy)
			url_ko = add_parameters_and_namespace_to_url(integration_configuration.get('url_ko'), {'sid': sid, 'warm_request': 'true'}, multitenancy)

			post_data = {
				'sid': sid,
				"Ds_Merchant_Currency": redunicre_data.get("Ds_Merchant_Currency"),
				"Ds_Merchant_Amount": amount,
				"Ds_Merchant_MerchantCode": redunicre_data.get("Ds_Merchant_MerchantCode"),
				"Ds_Merchant_MerchantName": redunicre_data.get("Ds_Merchant_MerchantName"),
				"Ds_Merchant_UrlOK": url_ok,
				"Ds_Merchant_UrlKO": url_ko,
				"Ds_Merchant_MerchantURL": add_parameters_and_namespace_to_url(redunicre_data.get("Ds_Merchant_MerchantURL"),
				                                                               {'sid': sid, 'warm_request': 'true'}, multitenancy),
				"Ds_Merchant_ConsumerLanguage": lang_customer,
				"Ds_Merchant_Order": order,
				"Ds_Merchant_Date": datetime.datetime.now().strftime("%d/%m/%Y %H:%M"),
				"Ds_Merchant_TransactionType": redunicre_data.get("Ds_Merchant_TransactionType"),
				"Ds_Merchant_Mode": redunicre_data.get("Ds_Merchant_Mode"),
				"Ds_Merchant_ContractNumber": redunicre_data.get("Ds_Merchant_ContractNumber"),
				"Ds_Merchant_Taxes": taxes,
				"Ds_Merchant_SecurityMode": redunicre_data.get("Ds_Merchant_SecurityMode"),
				"Ds_Merchant_SecretKey": redunicre_data.get("Ds_Merchant_SecretKey"),
				"Ds_Merchant_Country": country_customer,
				"Ds_Merchant_Buyer_Email": normalice_txt_for_xml(Ds_Merchant_Buyer_Email),
				"Ds_Merchant_Buyer_Name": normalice_txt_for_xml(Ds_Merchant_Buyer_Name),
				"Ds_Merchant_Buyer_LastName": normalice_txt_for_xml(Ds_Merchant_Buyer_LastName)
			}

			url_to_redirect, request_id = _get_payment_info(url, post_data)
			logging.info(f"[REDUNICRE] url_to_redirect: {url_to_redirect} - request_id: {request_id}")

			extra_info = {
				'GATEWAY_PAID_AMOUNT': price,
				'request_id': request_id
			}

			properties = {
				"sid": sid,
				"identifier": identifier,
				"hotel_code": hotel_code,
				"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
				"extraInfo": json.dumps(extra_info)
			}
			logging.info(f'[REDUNICRE] Saving reservation to reservation_metadata: {properties} {extra_info}')
			datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")
	except Exception as e:
		logging.error(f'REDUNICRE: error in payment [{e}]')
		return redirect(str(add_parameters_and_namespace_to_url(integration_configuration.get('url_ko'), {'sid': sid}, multitenancy)))

	if url_to_redirect:
		logging.info(f"Redirecting to: {url_to_redirect} \n request id: {request_id}")
		logging.info(f"Redunicre SAVING request_id in sess {request_id}")

		return redirect(str(url_to_redirect))


def get_redunicre_logos(integration_configuration):
	redunicre_logos = {}
	if Config.DEV:
		redunicre_logos['Ds_Logos_to_display'] = [
			{"src": "https://lh3.googleusercontent.com/mm6M1k8N0wPvpUsisQb6LPBMpG03Iw6mIf7oIaSlDx-DlA_h2tZdPrzdQ_U4eC7SqE6sV4ymw-coKRhxR7bjxg","advise_popup": ""},
			{"src": "https://lh3.googleusercontent.com/9LGSTE4Hr0cMR-WZSVNYIzfq9ZduL3SYuV9YKX0Bn_Byd2yr9Ejw42trRtim5FoHr7kPYXkt1U5xo6dNK4wzueQ","advise_popup": "visa_rules.html"},
			{"src": "https://lh3.googleusercontent.com/B_OwwbRZrHEOxXZtpRBMVxSxRRerc6ouMw5mplnLSb7V4DXxEGQq2yUB9gJiS5e0hD6DxLeiSgIntcw_X6ZUZg","advise_popup": "https://www.mastercard.us/en-us/businesses.html"}
		]
	else:
		redunicre_logos['Ds_Logos_to_display'] = []
		for key, value in integration_configuration.items():
			if "logo" in key:
				logo_config = value.split("@@")
				logo_src = logo_config[0]
				advise_popup = ""
				if len(logo_config) > 1:
					advise_popup = logo_config[1]
				redunicre_logos['Ds_Logos_to_display'].append({"src": logo_src, "advise_popup": advise_popup})
	return redunicre_logos


def _get_payment_info(url_to_post, data):
	template_path = 'pages/cobrador/gateways/DoWebPayment.xml'
	logging.info("template XML path: %s", template_path)
	logging.info(f"DATA tha will be sent to redunicre url {url_to_post} datas: {data}")

	xml_to_post = build_template(template_path, data)
	logging.info("Generating basic auth: %s:%s" % (data.get("Ds_Merchant_MerchantCode"), data.get("Ds_Merchant_SecretKey")))

	chain_basic_auth = base64.b64encode(("%s:%s" % (data.get("Ds_Merchant_MerchantCode"), data.get("Ds_Merchant_SecretKey"))).encode('utf-8')).decode('utf-8')

	headers = {
		'Content-Type': 'text/xml',
		"Authorization": f"Basic {chain_basic_auth}"
	}

	logging.info(f"basic auth chain : {chain_basic_auth}")

	logging.info(f"XML generated {xml_to_post}")

	result = post_using_generic_proxy(url_to_post, xml_to_post, headers=headers)
	logging.info(f"Redunicre result received  {result}")
	res_xml = ""
	if result.status_code == 200:
		res_xml = result.content
		logging.info(f"Redunicre content received  {res_xml}")

		xmldoc = minidom.parseString(res_xml.decode('utf-8').replace("obj:", "").replace("impl:", "").encode('utf-8'))
		code = xmldoc.getElementsByTagName('code')[0].firstChild.nodeValue
		short_msg = xmldoc.getElementsByTagName('shortMessage')[0].firstChild.nodeValue
		long_msg = xmldoc.getElementsByTagName('longMessage')[0].firstChild.nodeValue

		logging.info(f"first connection to redunicre. Getting real URL to redirect customer. code: {code} short_msg: {short_msg} long_msg: {long_msg}")

		if code == "00000":
			token = xmldoc.getElementsByTagName('token')[0].firstChild.nodeValue
			redirect_url = xmldoc.getElementsByTagName('redirectURL')[0].firstChild.nodeValue
			logging.info(f"Token has been returned {token}")

			return redirect_url, token
		return 'KO'
	else:
		logging.error("Something went wrong when gettint real irl to redirect reservation")
		logging.error(f"Status code: {result.status_code}")
		logging.error("Response: " + res_xml)

		return 'KO'


def get_redunicre_enviroment(gateway_type, hotel_code):
	if Config.DEV:
		redunicre_data = get_redunicre_data()
	else:
		integration_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		redunicre_data = get_redunicre_data(integration_configuration)
	if redunicre_data:
		url = redunicre_data.get('Ds_Merchant_Payment_Url')
		post_data = {
			"Ds_Merchant_Currency": redunicre_data.get("Ds_Merchant_Currency"),
			"Ds_Merchant_MerchantCode": redunicre_data.get("Ds_Merchant_MerchantCode"),
			"Ds_Merchant_MerchantName": redunicre_data.get("Ds_Merchant_MerchantName"),
			"Ds_Merchant_Date": datetime.datetime.now().strftime("%d/%m/%Y %H:%M"),
			"Ds_Merchant_TransactionType": redunicre_data.get("Ds_Merchant_TransactionType"),
			"Ds_Merchant_Mode": redunicre_data.get("Ds_Merchant_Mode"),
			"Ds_Merchant_ContractNumber": redunicre_data.get("Ds_Merchant_ContractNumber"),
			"Ds_Merchant_SecurityMode": redunicre_data.get("Ds_Merchant_SecurityMode"),
			"Ds_Merchant_SecretKey": redunicre_data.get("Ds_Merchant_SecretKey"),
		}
		return post_data, url


def _get_payment_info_details(url_to_post, data, token, hotel_code, identifier):
	template_path = 'pages/cobrador/gateways/GetWebPaymentDetail.xml'
	logging.info("_get_payment_info_details DATA tha will be sent to redunicre url %s token: %s", url_to_post, token)
	context = {
		'token': token
	}
	xml_to_post = build_template(template_path, context)
	logging.info("Generating basic auth: %s:%s" % (data.get("Ds_Merchant_MerchantCode"), data.get("Ds_Merchant_SecretKey")))

	chain_basic_auth = base64.b64encode(("%s:%s" % (data.get("Ds_Merchant_MerchantCode"), data.get("Ds_Merchant_SecretKey"))).encode('utf-8')).decode('utf-8')

	headers = {
		'Content-Type': 'text/xml',
		"Authorization": f"Basic {chain_basic_auth}"
	}

	logging.info(f"basic auth chain : {chain_basic_auth}")

	logging.info(f"XML generated {xml_to_post}")

	result = post_using_generic_proxy(url_to_post, xml_to_post, headers=headers)
	logging.info(f"Redunicre result received  {result}")
	amount_formated = ''
	if result.status_code == 200:
		res_xml = result.content

		try:
			gateway_type = "REDUNICRE"
			audit_response(hotel_code, gateway_type, identifier, "sid", res_xml, type_audit="FINAL_RESPONSE")

		except Exception as e:
			logging.error("Error auditing response for in REDUNICRE Process Response FINAL_RESPONSE")
			logging.error("Error auditing: %s", e)


		logging.info(f"Redunicre content received  {res_xml}")

		xmldoc = minidom.parseString(res_xml.decode('utf-8').replace("obj:", "").replace("impl:", "").encode('utf-8'))
		code = xmldoc.getElementsByTagName('code')[0].firstChild.nodeValue
		short_msg = xmldoc.getElementsByTagName('shortMessage')[0].firstChild.nodeValue
		long_msg = xmldoc.getElementsByTagName('longMessage')[0].firstChild.nodeValue

		logging.info(f"Second connection to redunicre. code: {code} short_msg: {short_msg} long_msg: {long_msg}")
		if code == "00000":
			logging.info(f"Payment ACCEPTED, we received code {code}")
			try:
				amount = xmldoc.getElementsByTagName('amount')[0].firstChild.nodeValue
				amount_formated = float(amount)/100
				logging.info(f"Amount received: {amount} - Amount formated: {amount_formated}")

			except Exception as e:
				logging.warning(f"Error to get real amount paid [{e}]")

			# todo, not implemented because no hotel has SEND_REDUNICRE_RESPONSE_TO_HOTEL config
			# try:
			# 	send_response_to_hotel = get_configuration_property_value(hotel_code, SEND_REDUNICRE_RESPONSE_TO_HOTEL)
			# 	mail_direction = None
			# 	if send_response_to_hotel:
			# 		if "@" in send_response_to_hotel:
			# 			mail_direction = send_response_to_hotel
			# 		else:
			# 			get_configuration_property_value(hotel_code, EMAIL_BOOKING)
			#
			# 	subject_email = f"Payment ACCEPTED in {hotel_code} for reservation: {identifier}"
			# 	content_html = f"{subject_email} <br><br> RESPONSE RECEIVED FROM REDUNICRE:<br><br> {res_xml}"
			# 	if mail_direction:
			# 		logging.info("Deferring the send mail payment confirmation for hotel!")
			# 		# todo deferred.defer(send_email, mail_direction, subject_email, content_html, content_html, _countdown=5)
			# 		   el deferred no está preparado aun en pep
			#
			# except Exception as e:
			# 	logging.warning(f"Error sending email to hotel with redunicre respone [{e}]")

			return True, amount_formated

	return False, ''


def get_redunicre_data(config={}):
	redunicre_data = {
		"Ds_Merchant_SecretKey": "",

		# Payment
		"Ds_Merchant_TransactionType": "",
		"Ds_Merchant_Amount": 0,
		"Ds_Merchant_Currency": "",
		"Ds_Merchant_Mode": "",
		"Ds_Merchant_ContractNumber": "",

		# Order
		"Ds_Merchant_Order": "",
		"Ds_Merchant_Country": "",
		"Ds_Merchant_Taxes": 0,

		# Optional URLs
		"Ds_Merchant_UrlOK": "",
		"Ds_Merchant_UrlKO": "",

		"Ds_Merchant_MerchantName": "",
		"Ds_Merchant_Date": "",
		"Ds_Merchant_ConsumerLanguage": "",
		"Ds_Merchant_SecurityMode": "SSL",
		"Ds_Merchant_Payment_Url": "",
		"Ds_Merchant_Terminal": "001",
		"Ds_Merchant_MerchantSignature": "",
		"Ds_Merchant_ProductDescription": "",
		"Ds_Merchant_MerchantCode": "",
		"Ds_Merchant_MerchantURL": "",
		"Ds_Merchant_MerchantData": "",
		"Ds_Merchant_SumTotal": 0,
		"Ds_Merchant_DateFrecuency": "",
		"Ds_Merchant_ChargeExpiryDate": "",
		"Ds_Merchant_AuthorisationCode": "",
		"Ds_Merchant_TransactionDate": "",

		# Logos to display
		"Ds_Logos_to_display": [
			{"src": "https://lh3.googleusercontent.com/mm6M1k8N0wPvpUsisQb6LPBMpG03Iw6mIf7oIaSlDx-DlA_h2tZdPrzdQ_U4eC7SqE6sV4ymw-coKRhxR7bjxg", "advise_popup": ""},
			{"src": "https://lh3.googleusercontent.com/9LGSTE4Hr0cMR-WZSVNYIzfq9ZduL3SYuV9YKX0Bn_Byd2yr9Ejw42trRtim5FoHr7kPYXkt1U5xo6dNK4wzueQ", "advise_popup": "visa_rules.html"},
			{"src": "https://lh3.googleusercontent.com/B_OwwbRZrHEOxXZtpRBMVxSxRRerc6ouMw5mplnLSb7V4DXxEGQq2yUB9gJiS5e0hD6DxLeiSgIntcw_X6ZUZg", "advise_popup": "https://www.mastercard.us/en-us/businesses.html"}
		]
	}

	if Config.DEV:
		redunicre_data['Ds_Merchant_Currency'] = "978"
		redunicre_data['Ds_Merchant_MerchantCode'] = "90000003041560"
		redunicre_data['Ds_Merchant_TransactionType'] = "101"
		redunicre_data['Ds_Merchant_ContractNumber'] = "00995858"
		redunicre_data['Ds_Merchant_ConsumerLanguage'] = ""
		redunicre_data['Ds_Merchant_Date'] = datetime.datetime.now().strftime("%d-%m-%Y %H:%M")
		redunicre_data['Ds_Merchant_Mode'] = "CPT"
		redunicre_data['Ds_Merchant_SecurityMode'] = "SSL"
		redunicre_data['Ds_Merchant_MerchantName'] = "NAU MONTARGIL ECOMM"
		redunicre_data['Ds_Merchant_UrlOK'] = "http://localhost:8090/booking4"
		redunicre_data['Ds_Merchant_UrlKO'] = "http://localhost:8090/booking3?errorCode=CC_DENIED"
		redunicre_data['Ds_Merchant_SecretKey'] = "CaRiRxbskxgdV5fLguwT"
		redunicre_data['Ds_Merchant_MerchantURL'] = "http://localhost:8090/cobrador/proxy/merchant_url"
		redunicre_data['Ds_Merchant_Payment_Url'] = "https://homologation.payline.com/V4/services/WebPaymentAPI/"
	else:
		redunicre_data['Ds_Merchant_Currency'] = config.get('currency_code')
		redunicre_data['Ds_Merchant_MerchantCode'] = config.get('merchant_code')
		redunicre_data['Ds_Merchant_ContractNumber'] = config.get('contract_number')
		redunicre_data['Ds_Merchant_TransactionType'] = config.get('transaction_type')
		redunicre_data['Ds_Merchant_Mode'] = config.get('mode')
		redunicre_data['Ds_Merchant_SecurityMode'] = config.get('security_mode')
		redunicre_data['Ds_Merchant_MerchantName'] = config.get('merchant_name')
		redunicre_data['Ds_Merchant_UrlOK'] = config.get('url_ok')
		redunicre_data['Ds_Merchant_UrlKO'] = config.get('url_ko')
		redunicre_data['Ds_Merchant_SecretKey'] = config.get('secret_key')
		redunicre_data['Ds_Merchant_MerchantURL'] = config.get('merchant_url', '')
		redunicre_data['Ds_Merchant_Payment_Url'] = config.get('payment_url', '')

	return redunicre_data




