import logging
import hashlib
import base64
import json
import uuid
import securetrading
import requests

from flask import request
from urllib.parse import parse_qs
from datetime import datetime, timezone, timedelta

from paraty import Config
from paraty.utilities.templates.templates_processor import build_template
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, get_integration_name, \
	gateways_format_price, get_reservation_metadata, cancel_reservation_in_manager, audit_response
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.pages.cobrador.cobrador_constants import GATEWAY_SUCESS_RETURNED, GATEWAY_ERROR_RETURNED, MBWAY, MULTIBANCO_PAYMENT_DATA, TRUST, MULTIBANCO, \
	CONTENT_TYPE_TEXT, GATEWAY_PENDING_RETURNED, COBRADOR_QUEUE

from paraty_commons_3 import queue_utils
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore, get_using_entity_and_params, \
	save_entity
from paraty_commons_3.datastore.datastore_utils import get_inner_url
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


NO_SHOW = "no show"
NO_SHOW_CODE = "X"
DELAYED_CHARGES_CODE = "D"
GATEWAY_ORDER_ID = "GATEWAY_ORDER_ID"
PAYMENT_GATEWAY_NAME = "PAYMENT_GATEWAY_NAME"
CODE = "CODE"
GATEWAY_EXTRA_INFO = "GATEWAY_EXTRA_INFO"
GATEWAY_PAID_AMOUNT = "GATEWAY_PAID_AMOUNT"

SETTLE_STATUS = {
	'0': GATEWAY_SUCESS_RETURNED,
	'1': GATEWAY_SUCESS_RETURNED,
	'10': GATEWAY_SUCESS_RETURNED,
	'100': GATEWAY_SUCESS_RETURNED,
	'2': "Transaction suspended",
	'3': "Transaction cancelled",
}

OK_SETTLEMENT_STATUS = ['0', '1', '1O', '100']


class TrustFormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		logging.info("Building TrustPayment form...")
		config = get_payment_gateway_configuration(gateway_type, hotel_code)
		timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')

		tokenize = ''
		if float(amount) == 0.0:
			tokenize = True
			amount = 0.01

		site_reference = get_site_reference_to_use(config, extra_data)
		security_hash, hash_string = get_hash(amount, timestamp, extra_data, config, site_reference)

		context = {
			'sitereference': site_reference,
			'currency': extra_data.get('currency', 'EUR'),
			'amount': amount,
			'url_ok': add_parameter_to_url(config.get('url_ok'), "sid", sid),
			'url_ko': add_parameter_to_url(config.get('url_ko'), "sid", sid),
			'security_hash': security_hash,
			'timestamp': timestamp,
			'hash_string': hash_string,
			'payment_order_id': payment_order_id,
			'merchant_url': add_parameter_to_url(config.get('merchant_url'), "sid", sid),
			'sid': sid,
			'tokenize': tokenize
		}

		if not Config.DEV:
			self.task_check_multibanco(config, payment_order_id, hotel_code)

		return build_template("pages/cobrador/gateways/_trust.html", context)

	def process_gateway_response(self, hotel_code, gateway_type, response):
		logging.info(f"[TRUST] Processing gateway response for {hotel_code}")
		if not isinstance(response, dict):
			raw_response = response
			if raw_response:
				logging.info(f"[TRUST] Raw response: {raw_response}")
				response = parse_qs(raw_response)
				for x, y in response.items():
					response[x] = y[0] if len(y) > 0 else ''
			else:
				response = dict(request.args)
		logging.info(f"[TRUST] Formated response: {response}")
		identifier = response.get('orderreference', '')

		try:
			gateway = "TRUST"
			audit_response(hotel_code, gateway, identifier, "FROM_COBRADOR", response_txt=json.dumps(response), type_audit="Process payment")

		except Exception as e:
			logging.error("Error auditing response for in TRUST PROCESS_PAYMENT")
			logging.error("Error auditing: %s", e)

		cobrador_response = {
			GATEWAY_ORDER_ID: response.get('transactionreference'),
			PAYMENT_GATEWAY_NAME: TRUST,
			'no_redirect': True,
			GATEWAY_EXTRA_INFO: {},
			'IDENTIFIER': identifier
		}
		error_code = response.get('errorcode', '')
		settle_status = response.get('settlestatus', '')



		settle_response = GATEWAY_SUCESS_RETURNED if settle_status in OK_SETTLEMENT_STATUS else SETTLE_STATUS.get(settle_status, 'KO')

		logging.info(f"[TRUST] Settlement status: {settle_status}")
		logging.info(f"[TRUST] Error code: {error_code}")

		if settle_response != GATEWAY_SUCESS_RETURNED:
			cobrador_response[CODE] = GATEWAY_ERROR_RETURNED
			return cobrador_response
		transaction_reference = response.get('transactionreference', '')
		cobrador_response[CODE] = GATEWAY_SUCESS_RETURNED
		cobrador_response[GATEWAY_EXTRA_INFO]['trust_credencials'] = {
			'notificationreference': response.get('notificationreference', ''),
			'paymenttypedescription': response.get('paymenttypedescription', ''),
			'requestreference': response.get('requestreference', ''),
			'transactionreference': transaction_reference
		}

		# if not get_reservation_metadata(hotel_code, identifier):
		# 	# todo lo que no se es como hacer para que no se cree la reserva la primera vez, ya que la primera no se paga, le mando KO con un mensaje?
		# 	save_to_metadata(response.get('sid', ''), identifier, hotel_code)

		payment_description = response.get('paymenttypedescription', '')
		if payment_description == MBWAY or payment_description == MULTIBANCO:
			# config = get_payment_gateway_configuration(gateway_type, hotel_code)
			amount_to_send = 0
			cobrador_response[CODE] = GATEWAY_SUCESS_RETURNED
			cobrador_response[GATEWAY_EXTRA_INFO]['trust_credencials']['payment_status'] = GATEWAY_PENDING_RETURNED
			save_transaction(hotel_code, identifier, transaction_reference)
			# cobrador_response[GATEWAY_EXTRA_INFO][MULTIBANCO_PAYMENT_DATA] = get_multibanco_payment_data(response, config)
		else:
			amount_to_send = response.get('mainamount', 0)

		cobrador_response[GATEWAY_PAID_AMOUNT] = amount_to_send
		cobrador_response[GATEWAY_EXTRA_INFO]['payment_gateway'] = TRUST

		cobrador_response[GATEWAY_EXTRA_INFO]['sid'] = response.get('sid', '')

		logging.info(f"[TRUST] Payment gateway response sent: {str(cobrador_response)}")

		return cobrador_response

	def task_check_multibanco(self, config, checkout_id_data, hotel_code):
		payload = {
			"merchant_url": f"https://payment-seeker.appspot.com/multibanco/check_reservation?hotel_code={hotel_code}&identifier={checkout_id_data}&gateway=trust"
		}

		data = {
			"task_id": str(uuid.uuid4()),
			"data": base64.urlsafe_b64encode(json.dumps(payload).encode("utf-8")).decode("utf-8")
		}

		limit_time_multibanco = int(config.get("limit_time_multibanco", 48)) * 3600

		try:
			queue_utils.create_task('execute_fallback_task', json.dumps(data),
									queue_name=COBRADOR_QUEUE,
									task_name=f'fallback_task__{checkout_id_data}_{hotel_code}',
									in_seconds=limit_time_multibanco)
		except Exception as e:
			logging.warning(
				f"Error controlled. Payment already differed: {checkout_id_data}_{hotel_code}")
			logging.warning("%s" % e)

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


def get_site_reference_to_use(config, extra_data):
	sitereference_to_use = config.get('sitereference')
	multibanco_sitereference = ''
	limit_days_before_start = config.get('limit_days_before_start')
	if extra_data.get("start_date") and limit_days_before_start:
		start_date = datetime.strptime(extra_data.get("start_date"), "%Y-%m-%d")
		time_release_days = start_date - timedelta(days=int(limit_days_before_start))
		today = datetime.today()
		if today < time_release_days:
			multibanco_sitereference = config.get('multibanco_sitereference')
	if multibanco_sitereference:
		sitereference_to_use = multibanco_sitereference
	return sitereference_to_use


def save_to_metadata(sid, payment_order_id, hotel_code):
	properties = {
		"sid": sid,
		"identifier": payment_order_id,
		"hotel_code": hotel_code,
		"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
		"extraInfo": json.dumps({})
	}
	save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")


def get_multibanco_payment_data(response):
	return {
		'date_valid': response.get('datevalid', ''),
		'entity': response.get('entity', ''),
		'reference': response.get('reference', ''),
		'amount': response.get('amount', ''),
		'currency': response.get('currency', '')
	}


class TrustController(GatewayInterface):

	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		logging.info(f"[TRUST] Execute payment in gateway. Reservation: {reservation.get('identifier')}, Amount: {amount}")
		amount = gateways_format_price(amount)
		logging.info(f"[TRUST] Formated amount: {amount}")

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		if gateway_configuration:
			logging.info(f"[TRUST] Gateway configuration: {gateway_configuration}")
			stconfig = securetrading.Config()
			stconfig.username = gateway_configuration.get('api_username')
			stconfig.password = gateway_configuration.get('api_password')
			st = securetrading.Api(stconfig)
			# parent_transaction_reference = "58-9-3703405"
			parent_transaction_reference = self.reservation_has_token(reservation)

			if not parent_transaction_reference:
				logging.info("Reservation doesn't have transactionreference (needed to execute a payment)")
				return GATEWAY_ERROR_RETURNED

			request = {
				"sitereference": gateway_configuration.get('sitereference'),
				"requesttypedescriptions": ["AUTH"],
				"accounttypedescription": "ECOM",
				"currencyiso3a": "EUR",
				"baseamount": amount,
				"orderreference": reservation.get('identifier'),
				"parenttransactionreference": parent_transaction_reference,
				"credentialsonfile": "2",
				"initiationreason": self.get_initiation_reason(reservation),

			}
			logging.info(f"Request sent to trust: {str(request)}")
			strequest = securetrading.Request()
			strequest.update(request)
			stresponse = st.process(strequest)

			response = stresponse.get('responses')[0]
			settle_status = response.get('settlestatus', '')
			error_code = response.get('errorcode', '')
			settle_response = GATEWAY_SUCESS_RETURNED if settle_status in OK_SETTLEMENT_STATUS else SETTLE_STATUS.get(settle_status, 'KO')

			logging.info(f"[TRUST] Settlement status: {settle_status}")
			logging.info(f"[TRUST] Error code: {error_code}")

			if settle_response != GATEWAY_SUCESS_RETURNED:

				try:
					gateway_type = "TRUST"
					audit_response(hotel_code, gateway_type, parent_transaction_reference, "FROM_COBRADOR", json.dumps(stresponse),
								   type_audit="ERROR_EXECUTE")

				except Exception as e:
					logging.error("Error auditing response for in TRUST ERROR_EXECUTE")
					logging.error("Error auditing: %s", e)

				return f"ERROR: {error_code}"

			payment_order_id = response.get('transactionreference')


			try:
				gateway_type = "TRUST"
				audit_response(hotel_code, gateway_type, payment_order_id, "FROM_COBRADOR", json.dumps(stresponse), type_audit="FINAL_OK")

			except Exception as e:
				logging.error("Error auditing response for in TRUST")
				logging.error("Error auditing: %s", e)


			return payment_order_id

	def get_initiation_reason(self, reservation):
		incidents = reservation.get('incidents') or ''
		return NO_SHOW_CODE if reservation.get('cancelled') or NO_SHOW in incidents.lower() else DELAYED_CHARGES_CODE

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		logging.info(f"[TRUST] Execute refund in gateway. Reservation: {reservation.get('identifier')}, Amount: {amount}")
		amount = gateways_format_price(amount)
		logging.info(f"[TRUST] Formated amount: {amount}")

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		if gateway_configuration:
			logging.info(f"[TRUST] Gateway configuration: {gateway_configuration}")
			stconfig = securetrading.Config()
			stconfig.username = gateway_configuration.get('api_username')
			stconfig.password = gateway_configuration.get('api_password')
			st = securetrading.Api(stconfig)
			parent_transaction_reference = order_to_refund if order_to_refund != reservation.get('identifier') else self.reservation_has_token(reservation)

			if not parent_transaction_reference:
				logging.info("Reservation doesn't have transactionreference (needed to execute a refund)")
				return GATEWAY_ERROR_RETURNED

			refund = {
				"requesttypedescriptions": ["REFUND"],
				"sitereference": gateway_configuration.get('sitereference'),
				"parenttransactionreference": parent_transaction_reference,
				"baseamount": amount,
			}

			logging.info(f"Request sent to refund: {str(refund)}")

			strequest = securetrading.Request()
			strequest.update(refund)
			stresponse = st.process(strequest)

			response = stresponse.get('responses')[0]
			settle_status = response.get('settlestatus', '')
			error_code = response.get('errorcode', '')

			settle_response = GATEWAY_SUCESS_RETURNED if settle_status in OK_SETTLEMENT_STATUS else SETTLE_STATUS.get(
				settle_status, 'KO')

			logging.info(f"[TRUST] Settlement status: {settle_status}")
			logging.info(f"[TRUST] Error code: {error_code}")

			if settle_response != GATEWAY_SUCESS_RETURNED:
				logging.info(f"[TRUST] Error in refund identifier: {reservation.get('identifier')} - order: {order_to_refund}")
				return GATEWAY_ERROR_RETURNED
			return order_to_refund

	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def reservation_has_token(self, reservation):
		extra_info = reservation.get('extraInfo', "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning(f"[TRUST] Reservation extra info was not JSON")
				extra_info = {}
			transaction_reference = extra_info.get('trust_credencials', {}).get('transactionreference')
			if transaction_reference:
				return transaction_reference
			return False

	def gateway_has_token(self):
		pass

	def get_initial_order_id_from_extra_info(self, extra_info):
		return extra_info.get("paymentOrderId")

	def translate_error(self, error):
		return error


class TrustWebhookController:
	def trust_webhook(self, hotel_code):
		# test_raw_response = "currencyiso3a=EUR&errorcode=0&mainamount=1.20&notificationreference=56-90976276&orderreference=43863031&paymenttypedescription=MULTIBANCO&settlestatus=10&transactionreference=56-86-222293&transactionstartedtimestamp=2024-07-11 11:00:01"
		# test_raw_response = "notificationreference=1315-17572123&orderreference=14037507&responsesitesecurity=337b1a4313e8798bc5ea312a538f8a81c032fbeb654652aff7fdbbc6aae053bb&settlestatus=100&sitereference=test_paratytech126489&transactionreference=59-86-222495"

		response = ("&".join(["%s=%s" % (x, y) for x, y in request.form.items()])).encode("utf-8")
		logging.info(f"[TRUST] Processing trust webhook for {hotel_code}")
		raw_response = response
		if raw_response:
			if isinstance(raw_response, bytes):
				raw_response = raw_response.decode()
			logging.info(f"[TRUST] Raw response: {raw_response}")
			response = parse_qs(raw_response)
			for x, y in response.items():
				response[x] = y[0] if len(y) > 0 else ''
		else:
			response = dict(request.args)
		logging.info(f"[TRUST] Formated response: {response}")
		identifier = response.get('orderreference', '')

		settle_status = response.get('settlestatus', '')

		settle_response = GATEWAY_SUCESS_RETURNED if settle_status in OK_SETTLEMENT_STATUS else SETTLE_STATUS.get(
			settle_status, 'KO')

		logging.info(f"[TRUST] Settlement status: {settle_status}")
		logging.info(f"[TRUST] Settle response: {settle_response}")

		reservation_exists = get_using_entity_and_params("Reservation", search_params=[("identifier", "=", identifier)],
			                                                 hotel_code=hotel_code)
		if reservation_exists:
			logging.info(f'Reservation found: {identifier} hotel code: {hotel_code}')
			reservation = reservation_exists[0]
			extra_info = json.loads(reservation.get("extraInfo"))
			logging.info(f'[TRUST] Reservation status: {extra_info.get("status_reservation", "")}')
			if settle_response == GATEWAY_SUCESS_RETURNED:
				if extra_info.get("status_reservation", "") != "confirmed":
					extra_info["status_reservation"] = "confirmed"
					if extra_info.get("trust_credencials", {}).get("paymenttypedescription") == 'MULTIBANCO':
						extra_info["trust_credencials"]["payment_status"] = "PAID"
						extra_info["trust_credencials"]["transactionreference"] = response.get("transactionreference")
						extra_info["trust_credencials"]["transaction_timestamp"] = get_current_timestamp()
					logging.info(f"New trust_credencials {str(extra_info.get('trust_credencials', {}))}")
					reservation["extraInfo"] = json.dumps(extra_info)
					save_entity(reservation, hotel_code)
					logging.info(f"[TRUST] Reservation {reservation.get('identifier')} has already been processed pending -> confirmed")
					return "OK"
				else:
					logging.info(f'Response already processed, reservation status: {reservation.get("status_reservation", "")}')
			elif settle_status == "3":
				if not reservation.get('cancelled'):
					logging.info(f'Cancelling reservation {reservation.get("identifier")}')
					reservation["cancellationTimestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
					reservation["incidents"] = "TRUST MULTIBANCO CANCELLATION"
					reservation["cancelled"] = True
					save_entity(reservation, hotel_code)
		else:
			logging.info(f"Reservation not found: {identifier} hotel code: {hotel_code}")

		return "OK"

	def check(self, hotel_code, identifier):
		reservation = get_using_entity_and_params('Reservation', search_params=[("identifier", "=", identifier)],
		                                          hotel_code=hotel_code)
		response = {
			"found": "KO",
			"cancelled": "false"
		}
		if reservation:
			response["found"] = "OK"
			reservation = reservation[0]
			extra_info = json.loads(reservation.get("extraInfo"))
			if cancel_reservation(reservation, extra_info):
				canceled_in_manager = cancel_reservation_in_manager(reservation.key, hotel_code)
				reservation["cancellationTimestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
				reservation["incidents"] = "TRUST MULTIBANCO CANCELLATION"
				if not canceled_in_manager:
					reservation["cancelled"] = True
					save_entity(reservation, hotel_code)
					response["cancelled"] = "true"
				else:
					reservation["cancelled"] = True
					save_entity(reservation, hotel_code)
					response["cancelled"] = "true"

				if response.get('cancelled'):
					hotel = get_hotel_by_application_id(hotel_code)
					url = get_inner_url(hotel)
					send_cancellation_post = f"{url}/utils?action=sendCancelReservation&localizador={identifier}&email={reservation['email']}"
					response_email = requests.get(send_cancellation_post)
					logging.info(f"Response status: {response_email.status_code}")

		return response


def cancel_reservation(reservation, extra_info):
	return (not reservation.get("cancelled")
		and extra_info.get("trust_credencials", {}).get('paymenttypedescription', '') in ["MULTIBANCO", "MBWAY"]
		and extra_info.get("status_reservation", "") != "confirmed")


def get_hash(amount, timestamp, extra_data, config, site_reference):
	# stringToHash = f"USD10.50{config.get('sitereference')}{timestamp}{PASSWORD}"
	stringToHash = f"{extra_data.get('currency', 'EUR')}{amount}{site_reference}{timestamp}{config.get('hash_password')}"
	return f"h{hashlib.sha256(stringToHash.encode()).hexdigest()}", stringToHash


def add_parameter_to_url(url, parameter_name, value):
	separator = '?' if '?' not in url else '&'
	return f"{url}{separator}{parameter_name}={value}"


def save_transaction(hotel_code, identifier, transaction_reference):
	transaction_data = {
		'hotel_code': hotel_code,
		'identifier': identifier,
		'transaction_id': transaction_reference,
	}
	logging.info(f'Saving: {transaction_data} in MultibancoTransactions')
	save_to_datastore("MultibancoTransactions", None, transaction_data, hotel_code="payment-seeker:", exclude_from_indexes=())


def get_current_timestamp():
	import datetime
	return str(datetime.datetime.now())
