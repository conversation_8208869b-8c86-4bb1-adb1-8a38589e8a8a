import json
import urllib
from urllib.parse import urlencode
from flask import request
from paraty import app
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, audit_response
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.utilities.templates.templates_processor import build_template
import requests

from paraty.pages.cobrador.cobrador_constants import CONTENT_TYPE_TEXT
from paraty_commons_3.language_utils import get_language_code
from paraty_commons_3.logging.my_gae_logging import logging

PAYPAL_VERSION = 98
PAYPAL_CURRENCY = 'EUR'
PAYPAL_PAYMENTACTION = 'SALE'
PAYPAL_METHOD_SET = 'SetExpressCheckout'
PAYPAL_METHOD_GET = 'GetExpressCheckoutDetails'
PAYPAL_METHOD_DO = 'DoExpressCheckoutPayment'
PAYPAL_API_HOST_LIVE = 'https://api-3t.paypal.com/nvp'
PAYPAL_API_HOST_SANDBOX = 'https://api-3t.sandbox.paypal.com/nvp'
PAYPAL_HOST_LIVE = "https://www.paypal.com"
PAYPAL_HOST_SANDBOX = "https://www.sandbox.paypal.com"


def get_paypal_api_host(config_as_dict):
	if config_as_dict:
		sanbox_mode = config_as_dict.get('sandbox_mode', "False")
		if sanbox_mode and sanbox_mode.upper() == "TRUE":
			return PAYPAL_API_HOST_SANDBOX

	return PAYPAL_API_HOST_LIVE


def get_integration_config_by_status(integration_config):
	status = integration_config.get("status", "").strip().upper()
	base_configs = {x: integration_config[x] for x in integration_config if "TEST_" not in x}
	base_configs = {x: integration_config[x] for x in base_configs if "DEV_" not in x}
	if not integration_config.get("endpoint"):
		base_configs["endpoint"] = "https://api.sandbox.paypal.com"
	if status == "TEST":
		test_configs = {x.replace("TEST_", ""): integration_config[x] for x in integration_config if "TEST_" in x}
		if not integration_config.get("TEST_endpoint"):
			base_configs["endpoint"] = "https://api.sandbox.paypal.com"
		base_configs.update(test_configs)
	if app.config.get("DEV"):
		test_configs = {x.replace("DEV_", ""): integration_config[x] for x in integration_config if "DEV_" in x}
		base_configs.update(test_configs)
	return base_configs


def get_token(amount, params_dict, merchant_id, url_ko, extra_params):
	localCode = get_language_code(extra_params.get('language')).upper()

	if localCode == "EN":
		localCode = "GB"

	amount = "{:.2f}".format(float(amount))

	params = {
		'PWD': params_dict.get("password"),
		'USER': params_dict.get("user"),
		'SIGNATURE': params_dict.get("signature"),
		'METHOD': PAYPAL_METHOD_SET,
		'VERSION': PAYPAL_VERSION,
		'PAYMENTREQUEST_0_AMT': amount,
		'PAYMENTREQUEST_0_CURRENCYCODE': params_dict.get("currency", PAYPAL_CURRENCY),
		'PAYMENTREQUEST_0_PAYMENTACTION': PAYPAL_PAYMENTACTION,
		'cancelUrl': url_ko,
		'returnUrl': merchant_id,
		'LOCALECODE': localCode

	}

	'''
	TAX ITEMS
	'PAYMENTREQUEST_0_ITEMAMT': 8,
		'PAYMENTREQUEST_0_SHIPPINGAMT': 1,
		'PAYMENTREQUEST_0_TAXAMT': 1,

	BORDER COLORS

	'CARTBORDERCOLOR':'0000CD',

	'''

	logging.info("getting paypal token from params %s and EXTRA params %s ", params, extra_params)

	params_to_send = dict(list(params.items()))

	response = requests.get(url=get_paypal_api_host(params_dict), params=params_to_send)
	response = urllib.parse.unquote(response.text)
	response_dic = dict(urllib.parse.parse_qsl(response))
	return response_dic.get("TOKEN")


def get_express_checkout_details(params_dict, token):


	params = {
		'PWD': params_dict.get("password"),
		'USER': params_dict.get("user"),
		'SIGNATURE': params_dict.get("signature"),
		'METHOD': PAYPAL_METHOD_GET,
		'VERSION': PAYPAL_VERSION,
		'TOKEN': token

	}

	response = requests.get(url=get_paypal_api_host(params_dict), params=params)

	try:
		response = urllib.parse.unquote(response.text)
		response_dic = dict(urllib.parse.parse_qsl(response))
	except Exception as e:

		response_dic = {}

	return response_dic


def do_express_checkout_payment(integration, amount, token, payer_id, currency = PAYPAL_CURRENCY):

	params = {
		'PWD': integration.get("password"),
		'USER': integration.get("user"),
		'SIGNATURE': integration.get("signature"),
		'METHOD': PAYPAL_METHOD_DO,
		'VERSION': PAYPAL_VERSION,
		'TOKEN': token,
		'PAYERID': payer_id,
		'PAYMENTREQUEST_0_AMT': amount,
		'PAYMENTREQUEST_0_PAYMENTACTION': PAYPAL_PAYMENTACTION,
		'PAYMENTREQUEST_0_CURRENCYCODE': currency,

	}


	'''
	'PAYMENTREQUEST_0_ITEMAMT': 8,
		'PAYMENTREQUEST_0_SHIPPINGAMT': 1,
		'PAYMENTREQUEST_0_TAXAMT': 1,
	'''

	response = requests.get(url=get_paypal_api_host(integration), params=params)

	try:
		response = urllib.parse.unquote(response.text)
		response_dic = dict(urllib.parse.parse_qsl(response))
	except Exception as e:

		response_dic = {}

	return response_dic



def get_redirection_url(token, integration):
	return "%s/cgi-bin/webscr?cmd=_express-checkout&token=%s" % (get_paypal_host(integration), token)


def get_paypal_host(config_as_dict):
	if config_as_dict:
		sanbox_mode = config_as_dict.get('sandbox_mode', "False")
		if sanbox_mode and sanbox_mode.upper() == "TRUE":
			return PAYPAL_HOST_SANDBOX

	return PAYPAL_HOST_LIVE


class PaypalV1FormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		logging.info("Price for paypal: %s", str(amount))

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		integration = get_integration_config_by_status(integration)

		merchant_url = integration.get("merchant_url", "")
		merchant_url_params = {
			"sid": sid,
			"reservation": payment_order_id
		}

		merchant_url += "&%s" % urlencode(merchant_url_params)
		if "?" not in merchant_url:
			merchant_url = merchant_url.replace("&", "?", 1)

		url_ko = integration.get("url_ko")
		url_ko += "&%s" % urlencode(merchant_url_params)
		if "?" not in url_ko:
			url_ko = url_ko.replace("&", "?", 1)

		token = get_token(str(amount), integration, merchant_url, url_ko, extra_data)

		myUrl = get_redirection_url(token, integration)

		context = {'post_url': myUrl}
		template_path = "pages/cobrador/gateways/paypal_v1_insite_form.html"
		form_gateway = build_template(template_path, context, False)
		return form_gateway

	def process_gateway_response(self, hotel_code, gateway_type, response):
		payer_id = request.args.get("PayerID")
		token = request.args.get("token", "")
		reservation = request.args.get("reservation", "")

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		integration = get_integration_config_by_status(integration)

		if token and payer_id:
			order = get_express_checkout_details(integration, token)
			logging.info("[PAYPAL][%s][%s][get_order]Response: %s" % (hotel_code, reservation, json.dumps(order)))

			captured_order = do_express_checkout_payment(integration, order.get("AMT"), token, order.get('PAYERID'), currency=order.get("CURRENCYCODE", PAYPAL_CURRENCY))
			logging.info("[PAYPAL][%s][%s][capture]Response: %s" % (hotel_code, reservation, json.dumps(order)))
			try:
				sid = request.values.get('sid', '')
				audit_response(hotel_code, "PAYPAL", reservation, sid, json.dumps(captured_order))

			except Exception as e:
				logging.warning("error creating payment audit")

			if captured_order.get("ACK") in ["Success", 'SuccessWithWarning']:

				return {
					"CODE": "OK",
					"GATEWAY_ORDER_ID": reservation,
					"GATEWAY_EXTRA_INFO": {
						"paypal_token": token,
						"PayedId": payer_id
					},
					'PAYMENT_GATEWAY_NAME': "PAYPAL",
					"GATEWAY_PAID_AMOUNT": order.get("AMT")
				}

		return {
			"CODE": "KO",
			"GATEWAY_ORDER_ID": reservation,
			"GATEWAY_EXTRA_INFO": {
				"paypal_token": token,
				"paypal_error_reason": response
			},
			'PAYMENT_GATEWAY_NAME': "PAYPAL",
		}

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT
