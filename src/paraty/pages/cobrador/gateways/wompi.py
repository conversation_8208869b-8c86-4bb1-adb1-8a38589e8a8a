import base64
import hashlib
import json
import logging
import datetime
import requests
from flask import request, make_response

from paraty import Config
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, \
    add_parameters_and_namespace_to_url, get_config_payment_seeker_configuration, audit_response
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, save_to_datastore


class Wompi:

    def __init__(self, data):
        self.wompi_data = data
        pass

    def build_signature(self, args):
        signature = f"{args['id']}{args['amount']}{args['currency']}{self.wompi_data['secret_key']}"
        signature_encrypt = hashlib.sha256()
        signature_encrypt.update(signature.encode())
        return signature_encrypt.hexdigest()

    def get_acceptance_token(self):
        url_to_get = f"{self.wompi_data['post_url']}/merchants/{self.wompi_data['public_key']}"
        response = requests.get(url_to_get)
        if response.status_code == 200:
            response_json = response.json()
            data = response_json.get("data")

            return data.get("presigned_acceptance", {}).get("acceptance_token")

        return ""

    def get_token(self, result, hotel_code):
        token = request.args.get("token")
        url_to_post = f"{self.wompi_data['post_url']}/payment_sources/"
        payload = {
            "type": request.args.get("type"),
            "token": token,
            "customer_email": request.args.get("email"),
            "acceptance_token": self.get_acceptance_token()
        }

        response_token = requests.post(url_to_post, json.dumps(payload), headers=self.wompi_header())
        if response_token.status_code == 201:
            response_json = response_token.json()

            try:
                sid = request.args.get("sid")
                if not sid:
                    sid = "not received correctly"

                gateway_type = "WOMPI"
                audit_response(hotel_code, gateway_type, result.get("GATEWAY_ORDER_ID"), sid,
                               json.dumps(response_json), type_audit="TOKEN")

            except Exception as e:
                logging.error("Error auditing response for in WOMPI Process Response")
                logging.error("Error auditing: %s", e)

            wompi_response_data = response_json.get("data", {})
            if wompi_response_data.get("status") == "AVAILABLE":
                result["GATEWAY_EXTRA_INFO"]["wompi"] = {
                    "token": wompi_response_data.get("id")
                }

    def get_transaction_by_id(self, result, hotel_code):
        id_wompi = request.args.get("id")
        url_to_post = f"{self.wompi_data['post_url']}/transactions/{id_wompi}"
        response = requests.get(url_to_post)
        if response.status_code == 200:
            response_json = response.json()

            try:
                sid = request.args.get("sid")
                if not sid:
                    sid = "not received correctly"

                gateway_type = "WOMPI"
                audit_response(hotel_code, gateway_type, result.get("GATEWAY_ORDER_ID"), sid, json.dumps(response_json), type_audit="FINAL_RESPONSE")

            except Exception as e:
                logging.warning("Error auditing response for in WOMPI Process Response")
                logging.warning("Error auditing: %s", e)

            logging.info("")
            result["GATEWAY_ORDER_ID"] = response_json.get("data", {}).get("reference")
            amount_to_pay = response_json.get("data", {}).get("amount_in_cents")
            amount_to_pay = float(f"{str(amount_to_pay)[:-2]}.{str(amount_to_pay)[-2:]}")
            result["GATEWAY_PAID_AMOUNT"] = amount_to_pay

            if response_json.get("data", {}).get("status") == "APPROVED":
                result["CODE"] = GATEWAY_SUCESS_RETURNED
                result["GATEWAY_RETURN_URL"] = self.wompi_data.get("url_ok")

    def wompi_header(self):
        return {
            'Authorization': f"Bearer {self.wompi_data['private_key']}",
            'content-type': "application/json",
            'accept': "application/json",
        }

    def do_payment(self, amount, reservation, token, hotel_code):
        new_identifier = self.get_next_id(reservation['identifier'])
        amount = "%.2f" % float(amount)
        amount = f"{amount.split('.')[0]}{amount.split('.')[1]}"

        args = {
            "id": new_identifier,
            "amount": amount,
            "currency": "COP",
        }
        payload = {
            "acceptance_token": self.get_acceptance_token(),
            "amount_in_cents": int(amount),
            "currency": "COP",
            "signature": self.build_signature(args),
            "customer_email": reservation.get("email"),
            "payment_method": {
                "installments": 1
            },
            "reference": new_identifier,
            "payment_source_id": token
        }

        response = requests.post(f"{self.wompi_data['post_url']}/transactions", json.dumps(payload), headers=self.wompi_header())
        logging.info("WOMPI execute payment response:")
        logging.info(response.json())

        try:
            gateway_type = "WOMPI"
            audit_response(hotel_code, gateway_type, new_identifier, "FROM_COBRADOR", json.dumps(response.json()))

        except Exception as e:
            logging.error("Error auditing response for in WOMPI Process Response")
            logging.error("Error auditing: %s", e)

        save_to_datastore("WompiPayments", None, {
            "wompi_identifier": new_identifier,
            "identifier": reservation['identifier'],
            "transaction": response.json().get("data", {}).get("id", f"ERROR: {new_identifier}"),
            "timestamp": response.json().get("data", {}).get("created_at", ""),
            "hotel_code": hotel_code
        }, hotel_code="payment-seeker:", exclude_from_indexes=())
        if response.status_code == 201 and not response.json().get('error'):


            return new_identifier

        return f"ERROR: {response.json().get('error').get('messages')}"

    def do_refund(self, amount, order_to_refund):
        amount = "%.2f" % float(amount)
        amount = f"{amount.split('.')[0]}{amount.split('.')[1]}"
        transaction_id = get_using_entity_and_params("WompiPayments", [("wompi_identifier", "=", order_to_refund)], hotel_code="payment-seeker:")
        if not transaction_id:
            return "ERROR: No transaction found"

        transaction_id = transaction_id[0].get("transaction")

        payload = {
            "amount_in_cents": int(amount)
        }
        response = requests.post(f"{self.wompi_data['post_url']}/transactions/{transaction_id}/void", json.dumps(payload),
                                 headers=self.wompi_header())

        if response.status_code == 201 and response.json().get('data', {}).get("status"):
            return order_to_refund

        return f"ERROR: {response.json().get('data').get('reason')}"

    def get_next_id(self, identifier):
        reservation_payments = get_using_entity_and_params("WompiPayments", [("identifier", "=", identifier)], hotel_code="payment-seeker:")
        reservation_payments = sorted(reservation_payments, key=lambda l: l["timestamp"] if l.get("timestamp") else "", reverse=True)
        if not reservation_payments:
            return f"{identifier}.00"

        else:
            last_identifier = reservation_payments[0].get("wompi_identifier").split(".")[1]
            new_identifier = int(last_identifier) + 1
            new_identifier = f"{identifier}.0{new_identifier}" if new_identifier < 10 else f"{identifier}.{new_identifier}"

            return new_identifier


class WompiFormControler(FormGatewayInterface):
    def addSidToUrlOKandKO(self, url, sid):
        if "?" in url:
            return url + "&sid=" + sid
        else:
            return url + "?sid=" + sid

    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        wompi_data = get_payment_gateway_configuration("WOMPI COBRADOR", hotel_code)

        if Config.DEV:
            wompi_data['merchant_url'] = "http://localhost:8090/cobrador/proxy/merchant_url"

        wompi = Wompi(wompi_data)

        merchant_url = add_parameters_and_namespace_to_url(wompi_data['merchant_url'],
                                                           {'sid': sid,
                                                            'identifier': payment_order_id},
                                                           hotel_code)

        original_amount = amount
        amount = "%.2f" % round(float(amount), 0)
        amount = f"{amount.split('.')[0]}{amount.split('.')[1]}"
        context = {
            "id": payment_order_id,
            "amount": amount,
            "merchant_url": merchant_url,
            "public_key": wompi_data["public_key"],
            "secret_key": wompi_data["secret_key"],
            "currency": "COP",
            "post_url": wompi_data["gateway_url"]
        }
        if amount == "000" or Config.DEV:
            context["tokenize"] = True
        signature = wompi.build_signature(context)
        context["signature"] = signature

        extra_info = {
            "amount": original_amount,
            "currency": extra_data.get("currency")
        }

        hotel_code_corporate = ""
        configuration_payment_seeker = get_config_payment_seeker_configuration(hotel_code)
        if configuration_payment_seeker:
            hotel_code_corporate = configuration_payment_seeker.get("name hotel corpo","")

        properties = {
            "sid": sid,
            "identifier": str(payment_order_id),
            "hotel_code": hotel_code,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "extraInfo": json.dumps(extra_info),
            "hotel_code_corporate": hotel_code_corporate,
        }

        save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")

        language = extra_data.get("language")
        template_path = "pages/cobrador/gateways/wompi_form.html"
        return build_template(template_path, context, language)

    def build_signature_response(self, args, secret_key):
        signature = f"{args['transaction']['id']}{args['transaction']['status']}{args['transaction']['amount_in_cents']}{secret_key}"
        signature_encrypt = hashlib.sha256()
        signature_encrypt.update(bytes(signature.encode()))
        return signature_encrypt.hexdigest()

    def process_gateway_response(self, hotel_code, gateway_type, response):
        body = dict(request.get_json())
        logging.info(f"[WOMPI] Response received to process: %s", body)
        try:
            logging.info(f"[WOMPI] Trying to decode response")
            response = json.loads(base64.b64decode(body.get("response")).decode('utf-8'))
        except Exception as e:
            logging.warning("[WOMPI] Incomming response is already a dict object")
            response = body.get("response")

        if response.get("already_processed", False):
            logging.info("[WOMPI][%s] Transaction already processed", response.get("GATEWAY_ORDER_ID"))
            logging.info("[WOMPI][%s] Process response: %s" % (response.get("GATEWAY_ORDER_ID"), response))
            return response
        wompi_data = get_payment_gateway_configuration("WOMPI COBRADOR", hotel_code)

        result = {
            "CODE": GATEWAY_ERROR_CODE_RETURNED,
            "GATEWAY_ORDER_ID": request.args.get("identifier"),
            "GATEWAY_PAID_AMOUNT": "",
            "GATEWAY_EXTRA_INFO": {},
            "GATEWAY_RETURN_URL": wompi_data.get("url_ko"),
            "PAYMENT_GATEWAY_NAME": "WOMPI"
        }

        wompi = Wompi(wompi_data)

        if request.args.get("token"):
            result["GATEWAY_PAID_AMOUNT"] = 0
            wompi.get_token(result, hotel_code)

        else:
            wompi.get_transaction_by_id(result, hotel_code)

        logging.info(result)

        return result

    def process_webhook_response(self, hotel_code, gateway_type, response):
        signature = response.get("signature", {})
        transaction = response.get("data", {}).get("transaction", {})

        if transaction.get("status") == "DECLINED":
            return make_response("Transaction declined", 200)
        checksum = signature.get("checksum") or request.headers.get("X-Event-Checksum")
        signature_properties = signature.get("properties")
        if not checksum or not signature_properties:
            logging.error("BE CAREFUL!!! This request may not be secure. Bad signature")
            return make_response("Checksum not found", 404)

        concatenated_signature_properties = "".join([transaction.get("id"), transaction.get("status"), str(transaction.get("amount_in_cents"))])
        concatenated_signature_properties += str(response.get("timestamp"))

        gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
        if not gateway_configuration:
            logging.warning("[WOMPI] No wompi integration found for %s. Cannot process webhook response" % hotel_code)
            return make_response("Internal server error processing gateway response", 500)

        concatenated_signature_properties += gateway_configuration.get("event_key", "")

        encrypted_signature = hashlib.sha256(concatenated_signature_properties.encode()).hexdigest()
        if not encrypted_signature == checksum:
            logging.error("[WOMPI] %s: Checksum does not match with out encrypted signature. Bad signature" % hotel_code)
            return make_response("Checksum does not match with Paraty encrpted signature", 500)

        logging.info("[WOMPI] secure checksum: %s", encrypted_signature)


        if not transaction:
            logging.warning("[WOMPI] Transaction data not provided")
            return make_response("Transaction data not found", 404)

        try:
            payment_order_id = transaction.get("reference")
            status = transaction.get("status")
            reservation_metadata = get_using_entity_and_params("ReservationMetadata",search_params=[("identifier", "=", str(payment_order_id)), ("hotel_code", "=", hotel_code)], hotel_code="payment-seeker:")
            if reservation_metadata:
                reservation_metadata = reservation_metadata[0]
                amount = transaction.get("amount_in_cents")
                amount = float(f"{str(amount)[:-2]}.{str(amount)[-2:]}")
                identifier = reservation_metadata.get("identifier")
                hotel_code = reservation_metadata.get("hotel_code")
                sid = reservation_metadata.get("sid")
                config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
                merchant_url = self.addSidToUrlOKandKO(url=config.get("merchant_url"), sid=sid)
                gateway_response = {
                    "GATEWAY_ORDER_ID": identifier,
                    "GATEWAY_PAID_AMOUNT": amount,
                    "GATEWAY_RETURN_URL": gateway_configuration.get("url_ko"),
                    "PAYMENT_GATEWAY_NAME": "WOMPI",
                    "already_processed": True                }

                try:
                    gateway_used = "WOMPI"
                    audit_response(hotel_code, gateway_used, identifier, sid,
                                   json.dumps(gateway_response), type_audit="Webhook Response")

                except Exception as e:
                    logging.error("Error auditing response for in WOMPI Webhook Response")
                    logging.error("Error auditing: %s", e)

                if status == "APPROVED":
                    gateway_response["CODE"] = GATEWAY_SUCESS_RETURNED
                else:
                    gateway_response["CODE"] = GATEWAY_ERROR_CODE_RETURNED


                to_extra_info = {
                    "payment_gateway_name": gateway_type
                }
                to_extra_info["sid"] = sid
                to_extra_info["no_redirect"] = True

                new_extraInfo_key = "wompi_transaction_" + identifier
                to_extra_info[new_extraInfo_key] = {
                    "response_sent_date": response.get("sent_at", ""),
                    "wompi_order_id": transaction.get("id", ""),
                }
                gateway_response["GATEWAY_EXTRA_INFO"] = to_extra_info


                logging.info("[WOMPI][%s] Sending post to merchant url", payment_order_id)

                send_response_to_merchant_url(gateway_response, merchant_url, payment_order_id)
                return make_response("Success", 200)
            else:
                logging.info("[WOMPI][%s] No reservation metadata found" % (payment_order_id))
                return make_response("Internal server error processing gateway response", 200)
        except Exception as e:
            logging.error(e)
            return make_response("Internal server error processing gateway response", 500)



class WompiController(GatewayInterface):

    def get_configuration(self, hotel_code):
        gateway_configuration = get_payment_gateway_configuration("WOMPI COBRADOR", hotel_code)

        return gateway_configuration

    def execute_payment_in_gateway(self, hotel_code, reservation, amount):
        wompi = Wompi(self.get_configuration(hotel_code))
        return wompi.do_payment(amount, reservation, self.reservation_has_token(reservation), hotel_code)

    def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
        wompi = Wompi(self.get_configuration(hotel_code))
        return wompi.do_refund(amount, order_to_refund)

    def translate_error(self, error):
        return error

    def reservation_has_token(self, reservation):
        if Config.DEV:
            return 141703
        extra_info = json.loads(reservation.get("extraInfo"))
        transaction_id = extra_info.get("wompi", {}).get("token")

        return transaction_id

    def gateway_has_token(self):
        return True

    def get_initial_order_id_from_extra_info(self, reservation_extra_info):
        return reservation_extra_info.get("paymentOrderId")

def send_response_to_merchant_url(gateway_response, merchant_url, payment_order_id):
    gateway_response_encode = base64.b64encode(json.dumps(gateway_response).encode('utf-8'))
    header = {
        'Content-Type': "text/plain"
    }
    logging.info("[WOMPI][%s] Formatted response from TPV to send to merchant_url: %s" % (payment_order_id, gateway_response))
    merchant_url_response = requests.post(merchant_url, data=gateway_response_encode, headers=header)