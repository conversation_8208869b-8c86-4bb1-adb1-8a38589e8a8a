import json
import logging
import datetime
import requests
import base64
import html
import re
import urllib.parse

from flask import render_template, request
from flask_cors import cross_origin
from paraty import app

from paraty.utilities.templates.templates_processor import build_template

from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, audit_response
from paraty.pages.cobrador.cobrador_constants import GATEWAY_SUCESS_RETURNED, GATEWAY_ERROR_RETURNED, CONTENT_TYPE_TEXT

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item, get_reservations_of_hotel
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.datastore import datastore_communicator

APPROVED = "approved"
SEQURA = "SEQURA"
EMAIL_SENDER = "Email sender"
URL_PAYMENT_HANDLER = "%s?sid=%s"

class SequraFormController(FormGatewayInterface):

	sequra = {}

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		# Esto construye el formulario oculto, que tiene un script para construir el html
		logging.info("Building Sequra hidden form...")

		config = get_payment_gateway_configuration(gateway_type, hotel_code)

		hotel_name = get_hotel_name(hotel_code)
		payment_url = config['payment_url']
		currency = extra_data['currency']
		price = amount
		merchant_id = config['merchant_id']
		asset_key = config['asset_key']
		url_asset = ("%s/assets/sequra-checkout.min.js" % config['sequra_cdn'])

		logging.info("Config Sequra: %s", config)

		context = {
			'hotel_name': hotel_name,
			'payment_url': payment_url,
			'sid': sid,
			'price': price,
			'currency': currency,
			'merchant': merchant_id,
			'asset_key': asset_key,
			'url_asset': url_asset,
			'payment_order_id': payment_order_id
		}

		return build_template("pages/cobrador/gateways/_sequra_payment_form.html", context)

	def process_gateway_response(self, hotel_code, gateway_type, response):

		logging.info("[SeQura] Processing request IPN ")
		logging.info("Request:")
		logging.info(request)
		logging.info("response_to_process")
		logging.info(response)
		logging.info(f"body {request.json}")
		logging.info(f"response {response}")
		if not isinstance(response, dict):
			decoded_query = urllib.parse.unquote(response)
			json_str = decoded_query.split('copy_payload=')[1].split('&order_ref')[0].replace('+', ' ')
			payload = json.loads(json_str)
		else:
			payload = response

		amount = payload.get('order').get('cart').get('order_total_with_tax')/100
		identifier = payload.get('order').get('payment_order_id')

		logging.info("AMOUNT_SENT_TO_GATEWAY: %s ", amount)

		try:
			gateway_type = "SEQURA"
			audit_response(hotel_code, gateway_type, identifier, "sid", json.dumps(payload))

		except Exception as e:
			logging.warning("Error auditing response for in SEQURA Process Response")
			logging.warning("Error auditing: %s", e)

		# config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
		cobrador_response = {
			'GATEWAY_ORDER_ID': identifier,
			'GATEWAY_PAID_AMOUNT': amount,
			'CODE': GATEWAY_SUCESS_RETURNED,
			'PAYMENT_GATEWAY_NAME': 'Sequra',
		}
		# Added identify reservation  and send it to SeQura
		logging.info(f'identifier: {identifier}')
		hotel = get_hotel_by_application_id(hotel_code)
		if identifier and get_reservations_of_hotel(hotel, None, None, identifier):
			logging.info("Reservation Already made beforely. Confirming Again to SEQURA!")
			sequra_response = confirm_booking_made_to_sequra(payload, hotel_code)
			if sequra_response.status_code != 200:
				cobrador_response['CODE'] = GATEWAY_ERROR_RETURNED
			return cobrador_response

		if payload and payload.get('order', ''):
			response_from_ipn = request.json.get('response')
			search_ = re.search('sq_state=(.*?)&', response_from_ipn)
			if search_ and search_.group(1) == APPROVED:
				sequra_response = confirm_booking_made_to_sequra(payload, hotel_code)
				if sequra_response.status_code != 200:
					cobrador_response['CODE'] = GATEWAY_ERROR_RETURNED
					return cobrador_response
				return cobrador_response

		cobrador_response['CODE'] = GATEWAY_ERROR_RETURNED
		return cobrador_response

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT

def confirm_booking_made_to_sequra(payload, hotel_code):
	identifier = payload.get('order').get('payment_order_id')
	payload['order']['merchant_reference'] = {'order_ref_1': identifier}
	payload = json.dumps(payload)
	logging.info("Payload: %s", payload)
	# Finally, we confirm the creation of reservation
	reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
	                                                                          [("identifier", "=", identifier),
	                                                                           ("hotel_code", "=", hotel_code)])[0]
	extraInfo = json.loads(reservation_metadata.get('extraInfo', {}))
	url = extraInfo.get('url').replace('/form_v2?product=pp3', '')
	authorization = extraInfo.get('authorization')
	logging.info("PUT to SeQura confirming order accomplished...")
	result = requests.put(url, data=payload, headers={'Content-Type': 'application/json', 'Authorization': authorization})
	logging.info("Status: %s", result.status_code)
	logging.info("Message: %s", result.content)
	return result

def get_hotel_name(hotel_code):
	hotel = get_hotel_by_application_id(hotel_code)
	hotel_name = get_hotel_advance_config_item(hotel, EMAIL_SENDER)[0]
	if not hotel_name:
		return None

	splitted_hotel_name = hotel_name.split("-") if '-' in hotel_name else hotel_name
	return splitted_hotel_name

@app.route("/sequra/form", methods=["POST"])
@cross_origin(origin='*')
def sequra_form():
	params_response = dict(request.values)
	logging.info("Params: %s", params_response)

	content = generate_payment_form(params_response)
	return content
	# self.response.write(content)
	# self.response.status_int = 200


def generate_payment_form(data):
	sequra = {}
	logging.info("Building Sequra form...")
	price = data.get('price')
	currency = data.get('currency', '')
	sid = data.get('sid')
	namespace = data.get('namespace')
	type_payment_selected = "SEQURA COBRADOR"

	logging.info("Type Payment Selected: %s", type_payment_selected)
	if type_payment_selected:
		config = get_payment_gateway_configuration(type_payment_selected, namespace)
	else:
		config = get_payment_gateway_configuration(SEQURA, namespace)

	price = str(data.get('price'))
	logging.info("Price: %s" % price)
	logging.info("Currency: %s" % str(data.get('currency')))

	logging.info("AMOUNT_SENT_TO_GATEWAY: %s ", price)

	sequra['amount'] = ("%.2f" % float(data.get('price'))).replace(".", "")
	sequra['currency'] = currency
	sequra['name'] = data.get('name','')
	sequra['surname'] = data.get('surname','')
	sequra['email'] = data.get('email','')
	sequra['checkin'] = data.get('checkin','')
	sequra['checkout'] = data.get('checkout', '')
	sequra['hotel'] = config.get('hotel_name', '')
	sequra['nin'] = data.get('nin', '')
	sequra['order_id'] = id
	sequra['merchant_id'] = config['merchant_id']
	sequra['payment_handler'] = URL_PAYMENT_HANDLER % (config['payment_handler'], sid)
	sequra['notify_url'] = append_param(config['notify_url'], 'sid', sid)
	sequra['return_url'] = append_param(config['return_url'], 'sid', sid)
	sequra['payment_url'] = config['payment_url']
	sequra['user'] = config['user']
	sequra['password'] = config['password']
	sequra['payment_order_id'] = data.get('payment_order_id', '')
	sequra['payload'] = buildBody(sequra)

	logging.info('payment_order_id')
	logging.info(sequra.get('payment_order_id'))

	logging.info("Config Sequra: %s", config)

	sequra['payload'] = json.dumps(sequra['payload'])

	logging.info("JSON request: %s", sequra['payload'])

	credentials_string = "{}:{}".format(sequra['user'], sequra['password'])

	credentials_bytes = credentials_string.encode('utf-8')
	encoded_credentials = base64.b64encode(credentials_bytes)

	sequra_authorization = "Basic {}".format(encoded_credentials.decode('utf-8'))

	logging.info("Post to SeQura to get payment url... ")
	result = requests.post(sequra['payment_url'], data=sequra['payload'],
	                       headers={'Content-Type': 'application/json', 'Authorization': sequra_authorization}, timeout=3)
	logging.info("Post to SeQura to get payment url... status: %s", result.status_code)
	logging.info("Post to SeQura to get payment url... message: %s", result.content)

	if not response_valid(result.status_code):
		return ''

	new_payment_url = result.headers.get("Location")

	new_payment_url = "%s/form_v2?product=pp3" % new_payment_url

	logging.info("Post to SeQura to get the form... ")
	response_form = requests.get(new_payment_url, data=sequra['payload'],
	                             headers={'Content-Type': 'application/json', 'Authorization': sequra_authorization}, timeout=3)
	logging.info("Post to SeQura to get the form... status: %s", result.status_code)
	logging.info("Post to SeQura to get the form... message: %s", result.content)

	logging.info('payment_order_id')
	logging.info(data.get('payment_order_id'))
	extra_info = {
		"url": new_payment_url,
		"authorization": sequra_authorization
	}

	properties = {
		"sid": sid,
		"identifier": data.get('payment_order_id'),
		"hotel_code": namespace,
		"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
		"extraInfo": json.dumps(extra_info)
	}
	datastore_communicator.save_to_datastore("ReservationMetadata", None, properties,
	                                         hotel_code="payment-seeker:")

	if not response_valid(result.status_code):
		return ''

	popup_html = response_form.content

	html_string = popup_html.decode('utf-8')

	unescaped_string = html.unescape(html_string)

	return render_template("pages/cobrador/gateways/_sequra_popup.html", **{'html_sequra': unescaped_string, 'payload': sequra['payload']})


def append_param(url, param, value):
	separator = '?' if '?' not in url else '&'
	return f"{url}{separator}{param}={value}"

def buildBody(sequra):

	payload = {}

	reservation = {}
	reservation["type"] = "lodging"
	reservation['reference'] = "Booking"
	reservation['name'] = "Booking in %s" % sequra['hotel']
	reservation['checkin_date'] = sequra['checkin']
	reservation['checkout_date'] = sequra['checkout']
	reservation['city'] = "unknown"
	reservation['country_code'] = "ES"
	reservation['total_with_tax'] = int(sequra['amount'])
	reservation["downloadable"] = True
	reservation["establishment_name"] = sequra['hotel']
	# reservation["establishment_name"] = 'Ona Los Claveles'

	# reservation['coordinates'] = {}
	# reservation['coordinates']['datum'] = "WGS84"
	# reservation['coordinates']['lat_lng'] = [1.2948883,103.8543904]

	cart = {}
	cart['currency'] = sequra['currency']
	cart['order_total_with_tax'] = int(sequra['amount'])
	cart['items'] = [reservation]
	cart['gift'] = False

	customer = {}
	customer['given_names'] = sequra['name']
	customer['surnames'] = sequra['surname']
	customer['email'] = sequra['email']
	customer['logged_in'] = 'unknown'
	customer['language_code'] = 'es-ES'
	customer['ip_number'] = ""
	customer['user_agent'] = ""
	customer['nin'] = sequra['nin']

	gui = {}
	gui['layout'] = "desktop"

	platform = {}
	platform['name'] = "ParatyTech Hotel Web"
	platform['version'] = "1"
	platform['db_name'] = "datastore"
	platform['db_version'] = "1"
	platform['uname'] = "ParatyTech Hotel Web"

	delivery_address = {}
	delivery_address['name'] = "ONLINE"
	delivery_address['home_delivery'] = False

	options = {}
	options['addresses_may_be_missing'] = True

	order = {}
	order['payment_order_id'] = sequra['payment_order_id']
	order['state'] = "confirmed"
	# order['merchant_reference'] = {
	# 	"order_ref_1": session_manager.get(PAYMENT_GATEWAY_ORDER)
	# }
	order['merchant'] = {}
	order['merchant']['id'] = sequra['merchant_id']
	order['merchant']['notify_url'] = sequra['notify_url']
	order['merchant']['return_url'] = sequra['return_url']
	order['merchant']['options'] = options
	order['merchant']['store_ref'] = sequra['hotel']
	order['merchant']['operator_ref'] = "true"

	order['cart'] = cart
	order['customer'] = customer
	order['gui'] = gui
	order['platform'] = platform
	order['delivery_method'] = delivery_address

	payload['order'] = order
	copy_payload = json.dumps(payload)
	payload['order']['merchant']['notification_parameters'] = {
		'copy_payload': copy_payload
	}

	return payload


def response_valid(status_code):
	return (status_code >= 200 and status_code < 300)


