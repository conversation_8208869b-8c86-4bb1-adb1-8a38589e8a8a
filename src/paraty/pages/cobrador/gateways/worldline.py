import hashlib
import hmac
import json
from base64 import b64encode, b64decode
from operator import attrgetter
from random import randint
from re import sub
from urllib.parse import urlparse, urlencode
import datetime
from wsgiref.handlers import format_date_time
import logging
from time import mktime
import requests
from flask import request, redirect, make_response
from locale import locale_alias
from paraty import app
from Cryptodome.Cipher import AES

from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, GATEWAY_SUCESS_RETURNED, \
	GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT, SEPARATOR_INFO_ERROR_ORDER
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, \
	audit_response, get_integration_name, add_sid_to_url, get_amount_to_pay
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.utilities.languages.language_utils import get_language_in_manager_based_on_locale, get_web_dictionary
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.common_data.common_data_provider import get_web_section, get_hotel_advance_config_value
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_utils import get_inner_url
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_project_and_namespace, \
	get_hotel_by_application_id
from paraty.utilities.languages import language_utils
from paraty_commons_3.session.session_utils import get_session_from_hotel

PAYMENT_SUBDOMAIN = "https://payment."
#SANDBOX_DOMAIN = "eu.sandbox.api-ingenico.com"
SANDBOX_DOMAIN = "world.preprod.api-ingenico.com"
PRODUCTION_DOMAIN = "world.api-ingenico.com"
VALID_PAYMENT_METHODS_FOR_TROKENIZE = ["cardPaymentMethodSpecificOutput", "sepaDirectDebitPaymentMethodSpecificOutput"]

KEY = b")J@NcRfUjXn2r4u7"

class RequestHeader:
	def __init__(self, name, value):
		if name is None or not name.strip():
			raise ValueError("name is required")
		self.__name = name
		self.__value = value

	@property
	def name(self):
		return self.__name

	@property
	def value(self):
		return self.__value.decode('utf-8') if isinstance(self.__value, bytes) else self.__value

	def __str__(self):
		return self.__name + ":" + str(self.__value)


CREDIT_CARD_TYPES = {
	2: "AX",
	130: "CB",
	132: "DN",
	128: "DS",
	125: "JC",
	3: "MC",
	1: "VI",
}


class DefaultAuthenticator:

	def __init__(self, authorization_type, api_id_key, secret_api_key):

		if authorization_type is None:
			raise ValueError("authorization_type is required")
		if secret_api_key is None or not secret_api_key.strip():
			raise ValueError("secret_api_key is required")
		if api_id_key is None or not api_id_key.strip():
			raise ValueError("api_id_key is required")
		self.__authorization_type = authorization_type
		self.__api_id_key = api_id_key
		self.__secret_api_key = secret_api_key

	def create_simple_authentication_signature(self, http_method, resource_uri,
											   http_headers):

		if http_method is None or not http_method.strip():
			raise ValueError("http_method is required")
		if resource_uri is None:
			raise ValueError("resource_uri is required")
		data_to_sign = self.to_data_to_sign(http_method, resource_uri,
											http_headers)
		return "GCS " + self.__authorization_type + ":" + self.__api_id_key + \
			   ":" + self.create_authentication_signature(data_to_sign)

	def to_data_to_sign(self, http_method, resource_uri, http_headers):
		content_type = None
		date = None
		canonicalized_headers = ""
		canonicalized_resource = self.__to_canonicalized_resource(resource_uri)
		xgcs_http_headers = []
		if http_headers is not None:
			for http_header in http_headers:
				if "Content-Type".lower() == http_header.lower():
					content_type = http_headers[http_header]
				elif "Date".lower() == http_header.lower():
					date = http_headers[http_header]
				else:
					name = self.__to_canonicalize_header_name(http_header)
					if name.startswith("x-gcs"):
						value = self.to_canonicalize_header_value(http_headers[http_header])
						xgcs_http_header = RequestHeader(name, value)
						xgcs_http_headers.append(xgcs_http_header)
		xgcs_http_headers.sort(key=attrgetter('name'))
		for xgcs_http_header in xgcs_http_headers:
			canonicalized_headers += xgcs_http_header.name + ":" + xgcs_http_header.value + "\n"
		string = http_method.upper() + "\n"
		if content_type is not None:
			string += content_type + "\n"
		else:
			string += "\n"
		string += str(date) + "\n"
		string += str(canonicalized_headers)
		string += canonicalized_resource + "\n"
		return str(string)

	def __to_canonicalized_resource(self, resource_uri):

		string = ""
		string += resource_uri.path
		if resource_uri.query:
			string += "?" + resource_uri.query
		return str(string)

	def __to_canonicalize_header_name(self, original_name):
		if original_name is None:
			return None
		else:
			return original_name.lower()

	def to_canonicalize_header_value(self, original_value):

		if original_value is None:
			return ""
		return sub(r"\r?\n(?:(?![\r\n])\s)*", " ", original_value).strip()

	def create_authentication_signature(self, data_to_sign):
		sig = hmac.new(self.__secret_api_key.encode("utf-8"), data_to_sign.encode("utf-8"), hashlib.sha256)
		return b64encode(sig.digest()).decode("utf-8").rstrip('\n')


class WorldLineGatewayController(GatewayInterface):
	def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!", reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			extra_info["last_merchant_order_used"] = merchant_order
			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)
			logging.info("New payed updated for reservation %s reservation_id: %s ",  reservation.get("identifier"), reservation_id)

			return reservation_updated_id

	def get_next_order(self, hotel_code, reservation):

		last_merchant_order_used = ""
		if reservation.get("extraInfo"):
			extra_info = json.loads(reservation.get("extraInfo"))
			last_merchant_order_used = extra_info.get("last_merchant_order_used", "")

		if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
			last_merchant_order_used = last_merchant_order_used[0:8]
			numerical_order= int(last_merchant_order_used)
			numerical_order += 1

			merchant_order = str(numerical_order)

			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
		else:

			merchant_order = reservation.get("identifier")
			#maybe this reservation was done before gateway was configured. So it could be an alphanumeric
			if not merchant_order.isnumeric():
				merchant_order = randint(10000000, 99999000)

			numerical_order= int(merchant_order)
			numerical_order += 1
			merchant_order = str(numerical_order)
			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

		self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)

		return merchant_order

	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		integration_name = get_integration_name(hotel_code)
		config = get_payment_gateway_configuration(integration_name, hotel_code=hotel_code)

		prefix = ""
		domain = PRODUCTION_DOMAIN
		if config.get("dev"):
			prefix = "dev_"
			domain = SANDBOX_DOMAIN

		identifier = self.get_next_order(hotel_code, reservation)
		reservation_identifier = reservation.get("identifier")

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation_identifier)
				extra_info = {}

		if extra_info:

			worldline_data = extra_info.get("worldline", {})

			if worldline_data.get("paymentMethod"):
				worldline_data["paymentMethod"] = worldline_data["paymentMethod"].replace("MethodSpecificOutput", "MethodSpecificInput")
			if config.get("country"):
				country = config.get("country")
			else:
				country = "ES"

			if config.get("currency"):
				currency = config.get("currency")
			else:
				currency = "EUR"

			query = {
				"order": {
					"amountOfMoney": {
						"currencyCode": currency,
						"amount": int(gateways_format_price(amount))
					},
					"references": {
						"merchantOrderId": identifier,
						"merchantReference": identifier  # See https://epayments-api.developer-ingenico.com/s2sapi/v1/en_US/json/payments/create.html?paymentPlatform=GLOBALCOLLECT#payments-create-request-example
					},
					"customer": {
						"billingAddress": {
							"city": "Amsterdam"
						}
					}
				},
				worldline_data.get("paymentMethod", "cardPaymentMethodSpecificInput"): {
					"token": worldline_data.get("token"),
					"isRecurring": True,
					"skipFraudService": False,
					"paymentProductId": worldline_data.get("productId"),
					"initialSchemeTransactionId": worldline_data.get("externalPaymentId"),
					"authorizationMode": "SALE",
					"requiresApproval": False,
					"recurring": {
						"recurringPaymentSequenceIndicator": "recurring"
					},
					"threeDSecure": {
						"skipAuthentication": True
					}
				}
			}

			headers = {
				"Content-Type": "application/json",
				"Date": format_date_time(mktime(datetime.datetime.utcnow().timetuple()))
			}

			auth_token = DefaultAuthenticator(authorization_type="v1HMAC", api_id_key=config.get(prefix + "api_key"),
											  secret_api_key=config.get(prefix + "secret_key"))

			sign = auth_token.create_simple_authentication_signature("POST", urlparse(
				"https://%s/v1/%s/payments" % (
					domain, config.get(prefix + "merchant_id"))), headers)

			headers["Authorization"] = sign

			logging.info("[%s][%s] Payment request to worldline: %s" % (hotel_code, reservation_identifier, json.dumps(query)))

			payment_response = requests.post(
				"https://%s/v1/%s/payments" % (
					domain, config.get(prefix + "merchant_id")),
				headers=headers, json=query)

			audit_response(hotel_code, "WORLDLINE", reservation_identifier, "", payment_response.text, payload=json.dumps(query))

			logging.info("[%s][%s] Payment response from worldline: %s" % (hotel_code, reservation_identifier, payment_response.text.replace("\n","")))

			if payment_response.status_code == 201:
				payment_response_json = payment_response.json()
				if payment_response_json.get("status") == "CAPTURE_REQUESTED":
					return identifier

		return GATEWAY_ERROR_RETURNED

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		integration_name = get_integration_name(hotel_code)
		config = get_payment_gateway_configuration(integration_name, hotel_code=hotel_code)

		reservation_identifier = reservation.get("identifier")

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation_identifier)
				return GATEWAY_ERROR_RETURNED

		extraInfo = json.loads(reservation.get("extraInfo"))

		reservation_integration_name = extraInfo.get("payment_gateway", extraInfo.get('payment_gateway_name'))
		if reservation_integration_name:
			config = get_payment_gateway_configuration(reservation_integration_name, hotel_code=hotel_code)

		if not config:
			config = get_payment_gateway_configuration(integration_name, hotel_code=hotel_code)

		worldline_data = extra_info.get("worldline", {})

		prefix = ""
		domain = PRODUCTION_DOMAIN
		if config.get("dev"):
			prefix = "dev_"
			domain = SANDBOX_DOMAIN

		if str(order_to_refund) in reservation_identifier:
			original_payment_id = worldline_data.get("externalPaymentId")
		else:

			headers = {
				"Content-Type": "application/json",
				"Date": format_date_time(mktime(datetime.datetime.utcnow().timetuple()))
			}

			url_to_call = "https://%s/v1/%s/payments?merchantOrderId=%s" % (domain, config.get("merchant_id"), order_to_refund)

			auth_token = DefaultAuthenticator(authorization_type="v1HMAC", api_id_key=config.get("api_key"),
											  secret_api_key=config.get("secret_key"))

			sign = auth_token.create_simple_authentication_signature("GET", urlparse(url_to_call), headers)

			headers["Authorization"] = sign

			response = requests.get(url_to_call, headers=headers)

			if response.status_code != 200:
				return "%s%s%s" % (GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, "Error finding payment: %s" % response.text)

			response_json = response.json()

			if not response_json.get("payments"):
				return "%s%s%s" % (GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, "Payment not found")

			original_payment_id = response_json["payments"][0]["id"]

		if config.get("country"):
			country = config.get("country")
		else:
			country = "ES"

		request_params = {
			"amountOfMoney": {
				"amount": int(gateways_format_price(amount)),
				"currencyCode": worldline_data.get("currency", "EUR")
			},
			"customer": {
			"address": {
					"countryCode": country.upper()
				}
			},
			"refundDate": datetime.datetime.now().strftime("%Y%m%d"),
			worldline_data.get("paymentMethod", "cardPaymentMethodSpecificInput"): {
				"token": worldline_data.get("token"),
				"paymentProductId": worldline_data.get("productId")
			}
		}
		headers = {
			"Content-Type": "application/json",
			"Date": format_date_time(mktime(datetime.datetime.utcnow().timetuple()))
		}

		auth_token = DefaultAuthenticator(authorization_type="v1HMAC", api_id_key=config.get(prefix + "api_key"),
										  secret_api_key=config.get(prefix + "secret_key"))

		sign = auth_token.create_simple_authentication_signature("POST", urlparse(
			"https://%s/v1/%s/payments/%s/refund" % (
			domain, config.get(prefix + "merchant_id"), original_payment_id)), headers)

		headers["Authorization"] = sign

		logging.info("[%s][%s] Refund request to worldline: %s" % (
			hotel_code, reservation_identifier, json.dumps(request_params)))

		response_gateway = requests.post(
			"https://%s/v1/%s/payments/%s/refund" % (
			domain, config.get(prefix + "merchant_id"), original_payment_id), headers=headers, json=request_params)

		logging.info("[%s][%s] Refund response from worldline: %s" % (
			hotel_code, reservation_identifier, response_gateway.text.replace("\n","")))

		audit_response(hotel_code, "WORLDLINE", reservation_identifier, "", response_gateway.text, type_audit="Refund", payload=json.dumps(request_params))

		if response_gateway.status_code == 201:
			return order_to_refund

		return GATEWAY_ERROR_RETURNED


	def reservation_has_token(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			# and str(extra_info.get("sermepa_cof_txnid", "")) #maybe is is not mandatory.
			if extra_info.get("worldline", {}).get("token"):
				return extra_info.get("worldline", {}).get("token")

		return False

	def gateway_has_token(self):
		return True

	def get_initial_order_id_from_extra_info(self, reservation_extra_info):
		return reservation_extra_info.get("paymentOrderId")

	def translate_error(self, error):
		return error

	def get_configuration(self, hotel_code):
		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

def get_personal_details_from_session(sid, hotel_code):

	response_json = get_session_from_hotel(hotel_code, sid)

	personal_details = response_json.get("personal_details_", {})

	if isinstance(personal_details, str):
		prebooking = response_json.get("info_details_prebooking")
		if prebooking:
			try:
				personal_details = json.loads(prebooking.get("personal_details"))
			except Exception as e:
				logging.error("Error decoding personal details: %s" % str(e))
				personal_details = {}

	result = {
		"contactDetails": {
			"emailAddress": personal_details.get("email", ""),
			"emailMessageType": "html",
			"phoneNumber": personal_details.get("telephone", "")
		},
		"billingAddress": {
			"countryCode": personal_details.get("country", "ES")
		},
		"merchantCustomerId": personal_details.get("personalId", ""),
		"personalInformation": {
			"name": {
				"firstName": personal_details.get("name", ""),
				"surname": personal_details.get("lastName1", "")
			}
		},
		"device": {}
	}


	return result

class WorldLineFormController(FormGatewayInterface):

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
		msi_payments = False
		installments = []
		translations = language_utils.get_web_dictionary(extra_data.get("language","SPANISH"))
		reservation_amount = amount
		hotel = get_hotel_by_application_id(hotel_code)
		rate_condition = "nrf" if extra_data.get("nrf") else "flex"
		num_nights = extra_data.get("num_nights")
		original_reservation_num_nights = (datetime.datetime.strptime(extra_data.get("end_date"), "%Y-%m-%d") - datetime.datetime.strptime(extra_data.get("start_date"), "%Y-%m-%d")).days
		gotrip_dis_perc = False
		gotrip_dis_msi = False
		if extra_data.get("gotrip"):
			if config.get("disable msi payments for gotrip users"):
				gotrip_dis_msi = True
			if config.get("disable percent payments for gotrip users"):
				gotrip_dis_perc = True

		amounts = ["100"]
		multiple_flex_prices_per_night = config.get("multiple_flex_prices_per_night", "False").lower() == "true"
		only_100_in_nrf = config.get("no_100_in_flex", "False").lower() == "true"
		flex_amounts = config.get("flex_amounts")
		if flex_amounts and not extra_data.get("nrf") and not extra_data.get("from_tpv_link") and not gotrip_dis_perc:
			if only_100_in_nrf:
				amounts = []
			if multiple_flex_prices_per_night:
				current_flex_amount = "%s-day" % num_nights
				if current_flex_amount in flex_amounts:
					if int(num_nights) > original_reservation_num_nights:
						current_flex_amount = "%s-day" % original_reservation_num_nights
						amounts.append(current_flex_amount)

			else:
				amounts.extend(flex_amounts.strip(";").split(";"))
			amounts = [str(i) for i in amounts]

		section_name = get_configuration_property_value(hotel_code,"Texto a reemplazar")
		translations_section = get_web_section(hotel, section_name, extra_data.get("language","SPANISH") , set_languages=True)
		custom_text = {}
		custom_amounts = {}
		currency_value = translations.get("T_currency_value")
		b3_custom_text_from_pep = get_hotel_advance_config_value(hotel_code,"b3 custom text from pep")
		if b3_custom_text_from_pep and b3_custom_text_from_pep.lower() == "true":
			for option_amount in amounts:
				if "-day" in option_amount:
					total_reservation_nights = extra_data.get("num_nights")
					num_nights_to_pay = option_amount.split("-day")[0]
					amount_to_pay = str(get_amount_to_pay(reservation_amount, total_reservation_nights=total_reservation_nights, num_nights_to_pay=num_nights_to_pay, prices_per_day= extra_data.get("prices_per_day")))
					current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace("@@amount@@", amount_to_pay).replace("@@currency@@", extra_data.get("currency"))
					translate = translations_section.get("T_wordline_n_day").replace("@@amount@@", amount_to_pay).replace("@@currency@@", extra_data.get("currency")).replace("@@currency_value@@", current_currency_value)
					num_days = option_amount.split("-day")[0]
					final_translate = translate.replace("@@night@@", num_days)
					custom_text[option_amount] = final_translate
				else:
					current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace("@@amount@@", reservation_amount).replace("@@currency@@", extra_data.get("currency"))
					amount_to_pay = str(get_amount_to_pay(reservation_amount, percent=option_amount))
					if option_amount == "100":
						final_translate = translations_section.get("T_wordline_100").replace("@@amount@@",reservation_amount).replace("@@currency@@", extra_data.get("currency")).replace("@@currency_value@@", current_currency_value)
					else:
						final_translate = translations_section.get("T_wordline_percent").replace("@@percent@@", option_amount).replace("@@amount@@", amount_to_pay).replace("@@currency@@", extra_data.get("currency")).replace("@@currency_value@@", current_currency_value)
					custom_text[option_amount] = final_translate
					custom_amounts[option_amount] = amount_to_pay

		payment_info = {
			"hotel_code": hotel_code,
			"gateway_type": gateway_type,
			"payment_order_id": payment_order_id,
			"amount": amount,
			"sid": sid,
			"extra_data": extra_data,
			"amounts": amounts
		}

		payment_info_string = json.dumps(payment_info)

		encryptor = AES.new(KEY, AES.MODE_EAX)

		payload = encryptor.encrypt(payment_info_string.encode("utf-8"))

		payload = b64encode(encryptor.nonce + payload).decode("utf-8")

		region_block = False

		if config.get("msi_only_for_mexico"):
			if extra_data.get("country", "ES").upper() != "MX":
				region_block = True

		msi_installments_custom_text = translations_section.get("T_wordline_n_installments")
		if config.get("msi payment") and not region_block and not extra_data.get("from_tpv_link") and not gotrip_dis_msi:
			msi_payments = True
			valid_installments = config.get("msi payment", "3").split(";")
			installments = []
			if msi_installments_custom_text:
				current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace("@@amount@@", reservation_amount).replace("@@currency@@", extra_data.get("currency"))
				msi_installments_custom_text = msi_installments_custom_text.replace("@@currency_value@@", current_currency_value)
				msi_installments_custom_text = msi_installments_custom_text.replace("@@amount@@",reservation_amount).replace("@@currency@@", extra_data.get("currency"))
				custom_text['radiomsi'] = msi_installments_custom_text
			for valid_installment in valid_installments:
				installments.append({
					"price": float(amount.split("-day")[0]) / float(valid_installment),
					"installment": valid_installment
				})


		context = {
			"msi_payments": msi_payments,
			"installments": installments,
			"dev": app.config.get("DEV"),
			"payload": payload,
			"amounts": amounts,
			"num_nights": extra_data.get("num_nights"),
			"add_fake_payment_methods": config.get("fake payment methods"),
			"custom_text": custom_text,
			"custom_amounts": custom_amounts,
			"msi_installments_custom_text": msi_installments_custom_text,
			"currency": extra_data.get("currency"),
			"rate_condition": rate_condition
		}
		if extra_data.get("prices_per_day"):
			context["prices_per_day"] = extra_data.get("prices_per_day")

		#context.update(get_web_dictionary(extra_data.get("language", "SPANISH")))

		template_path = "pages/cobrador/gateways/worldline_insite_form.html"
		form_gateway = build_template(template_path, context, extra_data.get("language", "SPANISH"))

		return form_gateway

	def process_gateway_response(self, hotel_code, gateway_type, response):
		config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
		prefix = ""
		domain = PRODUCTION_DOMAIN
		if config.get("dev"):
			prefix = "dev_"
			domain = SANDBOX_DOMAIN

		hosted_checkout_id = request.args.get("hostedCheckoutId", "")

		auth_token, response_gateway = self.get_hosted_form(config, domain, hosted_checkout_id, prefix)

		payment_status = {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"GATEWAY_ORDER_ID": request.args.get("reservation"),
			"GATEWAY_PAID_AMOUNT": 0
		}

		audit_response(hotel_code, "WORLDLINE", request.args.get("reservation"), "", response_gateway.text, payload="{}")

		#GATEWAY_EXTRA_INFO
		if response_gateway.status_code == 200:
			response_json = response_gateway.json()

			status = response_json.get("createdPaymentOutput", {}).get("paymentStatusCategory", "KO")
			amount = response_json \
				.get("createdPaymentOutput", {}) \
				.get("payment", {}) \
				.get("paymentOutput", {}) \
				.get("amountOfMoney", {}).get("amount", 0)

			amount = str(amount)

			if len(amount) > 2:
				price_euros = amount[:-2]
				price_cents = amount[-2:]
				amount = price_euros + "." + price_cents

			amount = float(amount)
			payment_status["GATEWAY_PAID_AMOUNT"] = amount

			if status == "SUCCESSFUL":
				payment_status["CODE"] = GATEWAY_SUCESS_RETURNED

				payment_id = response_json.get("createdPaymentOutput", {}).get("payment", {}).get("id")


				payment_methods_keys = response_json.get("createdPaymentOutput", {}).get("payment", {}).get("paymentOutput", {}).keys()
				payment_methods_keys = list(filter(lambda x: x.endswith("MethodSpecificOutput"), payment_methods_keys))
				if payment_methods_keys:
					payment_method_name = payment_methods_keys[0]
				else:
					payment_method_name = ""

				if payment_id:
					if payment_method_name in VALID_PAYMENT_METHODS_FOR_TROKENIZE:
						response_gateway_token = self.get_token(auth_token, config, domain, payment_id, prefix)

						audit_response(hotel_code, "WORLDLINE", request.args.get("reservation"), "", response_gateway_token.text, type_audit="Create token", payload="{}")

						if response_gateway_token.status_code in [200, 201]:
							response_gateway_token_json = response_gateway_token.json()

							credit_card_info = response_json.get("createdPaymentOutput", {}).get("payment", {}).get("paymentOutput",{}).get("cardPaymentMethodSpecificOutput", {})
							credit_card_type = credit_card_info.get("paymentProductId", 0)
							credit_card_type = CREDIT_CARD_TYPES.get(credit_card_type, "")
							credit_card = credit_card_info.get("card", {}).get("cardNumber", "")


							payment_status["GATEWAY_EXTRA_INFO"] = {
								"payment_gateway": gateway_type,
								"worldline": {
									"token": response_gateway_token_json.get("token", ""),
									"paymentMethod": payment_method_name,
									"currency": response_json.get("createdPaymentOutput", {}).get("payment", {}).get("paymentOutput", {}).get("amountOfMoney", {}).get("currencyCode", "USD"),
									"externalPaymentId": payment_id,
									"productId": response_json.get("createdPaymentOutput", {}).get("payment", {}).get("paymentOutput", {}).get(payment_method_name, {}).get("paymentProductId", 1)
								},
								"creditcard": {
									"type": credit_card_type,
									"card": credit_card
								}
							}

						self.add_expiration_date_to_response(payment_status, response_json)
		else:
			search_response = self.get_payment_by_order_id(config, domain, request.args.get("reservation"), prefix)
			if search_response.status_code == 200:
				payments = search_response.json().get("payments", [])
				pruned_payments = []
				for payment in payments:
					if payment.get("hostedCheckoutSpecificOutput", {}).get("hostedCheckoutId") == hosted_checkout_id:
						pruned_payments.append(payment)

				payments = pruned_payments

				if payments:
					payment = payments[0]

					status = payment.get("status")

					if status in ["SUCCESSFUL", "CAPTURE_REQUESTED"]:
						payment_status["CODE"] = GATEWAY_SUCESS_RETURNED
						payment_id = payment.get("id")

						amount = payment.get("paymentOutput", {}).get("amountOfMoney", {}).get("amount", 0)
						amount = str(amount)

						if len(amount) > 2:
							price_euros = amount[:-2]
							price_cents = amount[-2:]
							amount = price_euros + "." + price_cents

						amount = float(amount)
						payment_status["GATEWAY_PAID_AMOUNT"] = amount

						payment_methods_keys = payment.get("paymentOutput", {}).keys()
						payment_methods_keys = list(
							filter(lambda x: x.endswith("MethodSpecificOutput"), payment_methods_keys))

						if payment_methods_keys:
							payment_method_name = payment_methods_keys[0]
						else:
							payment_method_name = ""

						if payment_method_name in VALID_PAYMENT_METHODS_FOR_TROKENIZE:

							credit_card_info = payment.get("paymentOutput", {}).get("cardPaymentMethodSpecificOutput", {})
							credit_card_type = credit_card_info.get("paymentProductId", 0)
							credit_card_type = CREDIT_CARD_TYPES.get(credit_card_type, "")
							credit_card = credit_card_info.get("card", {}).get("cardNumber", "")

							payment_status["GATEWAY_EXTRA_INFO"] = {
								"payment_gateway": gateway_type,
								"worldline": {
									"token": credit_card_info.get("token", ""),
									"paymentMethod": payment_method_name,
									"currency": payment.get("paymentOutput", {}).get("amountOfMoney", {}).get("currencyCode", "USD"),
									"externalPaymentId": payment_id,
									"productId": payment.get("paymentOutput", {}).get(payment_method_name, {}).get("paymentProductId", 1)
								},
								"creditcard": {
									"type": credit_card_type,
									"card": credit_card
								}
							}

							try:
								card_data = credit_card_info.get("card", {})
								expiry_date = card_data.get("expiryDate")

								if expiry_date and len(expiry_date) >= 4:
									month = expiry_date[0:2]
									year = expiry_date[2:4]
									payment_status["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{month}/{year}"
									logging.info("[WORLDLINE] Added expiration date to search response: %s", f"{month}/{year}")
							except Exception as e:
								logging.warning("[WORLDLINE] Error adding expiration date to search response: %s", str(e))

		return payment_status

	def add_expiration_date_to_response(self, payment_status, response_json):
		logging.info("[WORLDLINE] Trying to add expiration date")
		try:
			card_info = response_json.get("createdPaymentOutput", {}).get("payment", {}).get("paymentOutput", {}).get("cardPaymentMethodSpecificOutput", {})
			if card_info and "card" in card_info:
				card_data = card_info.get("card", {})
				expiry_date = card_data.get("expiryDate")

				if expiry_date:
					if len(expiry_date) >= 4:
						month = expiry_date[0:2]
						year = expiry_date[2:4]
						if "GATEWAY_EXTRA_INFO" in payment_status:
							payment_status["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{month}/{year}"
							logging.info("[WORLDLINE] Added expiration date to response: %s", payment_status["GATEWAY_EXTRA_INFO"]["cc_expire_date"])
		except Exception as e:
			logging.warning("[WORLDLINE] Error adding expiration date to response: %s", str(e))

	def get_token(self, auth_token, config, domain, payment_id, prefix):
		headers = {
			"Content-Type": "application/json",
			"Date": format_date_time(mktime(datetime.datetime.utcnow().timetuple()))
		}
		sign = auth_token.create_simple_authentication_signature("POST", urlparse(
			"https://%s/v1/%s/payments/%s/tokenize" % (
				domain, config.get(prefix + "merchant_id"), payment_id)), headers)
		headers["Authorization"] = sign
		response_gateway_token = requests.post(
			"https://%s/v1/%s/payments/%s/tokenize" % (
				domain, config.get(prefix + "merchant_id"), payment_id), headers=headers, json={})
		return response_gateway_token

	def get_hosted_form(self, config, domain, hosted_checkout_id, prefix):
		headers = {
			"Content-Type": "application/json",
			"Date": format_date_time(mktime(datetime.datetime.utcnow().timetuple()))
		}
		auth_token = DefaultAuthenticator(authorization_type="v1HMAC", api_id_key=config.get(prefix + "api_key"),
										  secret_api_key=config.get(prefix + "secret_key"))
		sign = auth_token.create_simple_authentication_signature("GET", urlparse(
			"https://%s/v1/%s/hostedcheckouts/%s" % (domain, config.get(prefix + "merchant_id"), hosted_checkout_id)),
																 headers)
		headers["Authorization"] = sign
		response_gateway = requests.get(
			"https://%s/v1/%s/hostedcheckouts/%s" % (domain, config.get(prefix + "merchant_id"), hosted_checkout_id),
			headers=headers)
		return auth_token, response_gateway

	def get_payment_by_order_id(self, config, domain, payment_order_id, prefix):
		headers = {
			"Content-Type": "application/json",
			"Date": format_date_time(mktime(datetime.datetime.utcnow().timetuple()))
		}
		auth_token = DefaultAuthenticator(authorization_type="v1HMAC", api_id_key=config.get(prefix + "api_key"),
										  secret_api_key=config.get(prefix + "secret_key"))
		sign = auth_token.create_simple_authentication_signature("GET", urlparse(
			"https://%s/v1/%s/payments?merchantOrderId=%s" % (domain, config.get(prefix + "merchant_id"), payment_order_id)),
																 headers)
		headers["Authorization"] = sign
		response_gateway = requests.get(
			"https://%s/v1/%s/payments?merchantOrderId=%s" % (domain, config.get(prefix + "merchant_id"), payment_order_id),
			headers=headers)
		return response_gateway

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


@app.route("/worldline/redirect", methods=["POST"])
def worldline_redirect():
	form_data = request.form

	installments = form_data.get("installments")
	payment_method = form_data.get("payment-method", "")
	amount_percent = form_data.get("payment-amount", "100")
	payload = form_data.get("payload")

	payload = b64decode(payload)

	nounce = payload[:16]

	payload = payload[16:]

	encryptor = AES.new(KEY, AES.MODE_EAX, nonce=nounce)

	payload = encryptor.decrypt(payload)

	payment_data = json.loads(payload)

	hotel_code = payment_data.get("hotel_code")


	#TODO: fix this with config
	hotel_code_translated = ""
	if hotel_code == "parkroyal-villascancun":
		hotel_code_translated = "pr-villascancun"
	else:
		hotel_code_translated = hotel_code



	gateway_type = payment_data.get("gateway_type")
	payment_order_id = payment_data.get("payment_order_id")
	amount = payment_data.get("amount")
	amount = float(amount)
	sid = payment_data.get("sid")
	extra_data = payment_data.get("extra_data")
	amounts = payment_data.get("amounts")

	logging.info("[%s][%s] Request from booking3 form_data: %s , payload: %s" % (hotel_code, payment_order_id, json.dumps(form_data), json.dumps(payment_data)))

	if payment_method == "card-payment" and amount_percent != "on":
		if amount_percent in amounts:
			if amount_percent.endswith("-day"):

				nights_to_pay = int(amount_percent.replace("-day", ""))
				num_nights = int(extra_data.get("num_nights"))
				if nights_to_pay < num_nights:

					if extra_data.get("prices_per_day"):
						amount = get_amount_to_pay(amount, total_reservation_nights=num_nights,
										  num_nights_to_pay=nights_to_pay,
										  prices_per_day=extra_data.get("prices_per_day"))
					else:
						amount = round((amount/num_nights) * nights_to_pay, 2)
			else:
				amount_percent = float(amount_percent)

				amount = round((amount_percent * amount)/100, 2)

	use_token = False
	if "TOKEN" in gateway_type:
		use_token = True

	config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
	prefix = ""
	domain = PRODUCTION_DOMAIN
	if config.get("dev"):
		prefix = "dev_"
		domain = SANDBOX_DOMAIN

	merchant_url = config.get(prefix + "merchant_url")


	params_in_url = {"sid": sid,
					 "reservation": payment_order_id,
					 "payment_method": payment_method,
					 "amount_percent": amount_percent}

	params_merchant_url  = urlencode(params_in_url)
	if "?" in merchant_url:
		merchant_url = merchant_url + "&" + params_merchant_url
	else:
		merchant_url = merchant_url + "?" + params_merchant_url

	if config.get("locale", "").lower() in locale_alias:
		locale = config.get("locale", "")
	else:
		locale = "es_ES"

	if config.get("country"):
		country = config.get("country")
	elif extra_data.get("country"):
		country = extra_data.get("country")
	else:
		country = "ES"

	if config.get("currency"):
		currency = config.get("currency")
	elif extra_data.get("currency"):
		currency = extra_data.get("currency")
	else:
		currency = "EUR"

	customer_information = get_personal_details_from_session(sid, hotel_code)

	query = {
		"order": {
			"amountOfMoney": {
				"currencyCode": currency,
				"amount": int(gateways_format_price(amount))
			},
			"additionalInput": {

			},
			"references":{
				"merchantOrderId": int(payment_order_id),
				"merchantReference": "%s:%s" % (hotel_code_translated, str(payment_order_id))
			},
			"customer": customer_information
		},
		"hostedCheckoutSpecificInput": {
			"returnUrl": merchant_url,
			"variant": config.get(prefix + "template", "default"),
			"locale": locale,
			"showResultPage": False,
			"paymentProductFilters": {
				"tokensOnly": use_token
			}
		},
		"redirectPaymentMethodSpecificInput": {
			"requiresApproval": False
		},
		"cardPaymentMethodSpecificInput": {
			"authorizationMode": "SALE",
			"recurring": {
				"recurringPaymentSequenceIndicator": "first"
			},
			"threeDSecure": {
				"challengeIndicator": "challenge-required",
				"skipAuthentication": False
			}
		}
	}


	region_block = False
	if config.get("msi_only_for_mexico"):
		if extra_data.get("country", "ES").upper() != "MX":
			region_block = True

	if installments and payment_method == "msi-payment" and config.get("msi payment") and not region_block:
		avalible_installments = config.get("msi payment", "3").split(";")
		if installments not in avalible_installments:
			installments = "3"

		installments_config = {
			"amountOfMoneyPerInstallment": {
				"amount": int(int(gateways_format_price(amount)) / int(installments)),
				"currencyCode": currency
			},
			"numberOfInstallments": int(installments)
		}

		query["order"]["additionalInput"]["installments"] = installments_config

	if form_data.get("metadata"):
		try:
			metadata = json.loads(form_data.get("metadata"))
		except:
			metadata = {}

		if metadata.get("timeoffset"):
			query["order"]["customer"]["device"]["timezoneOffsetUtcMinutes"] = metadata.get("timeoffset")

		if metadata.get("locale"):
			query["order"]["customer"]["device"]["locale"] = metadata.get("locale")

		if metadata.get("userAgent"):
			query["order"]["customer"]["device"]["userAgent"] = metadata.get("userAgent")

		if metadata.get("userAgent"):
			query["order"]["customer"]["device"]["userAgent"] = metadata.get("userAgent")

		query["order"]["customer"]["device"]["browserData"] = {}

		if metadata.get("colorDepth"):
			query["order"]["customer"]["device"]["browserData"]["colorDepth"] = metadata.get("colorDepth")

		if metadata.get("height"):
			query["order"]["customer"]["device"]["browserData"]["screenHeight"] = metadata.get("height")

		if metadata.get("width"):
			query["order"]["customer"]["device"]["browserData"]["screenWidth"] = metadata.get("width")

		if metadata.get("javaEnabled"):
			query["order"]["customer"]["device"]["browserData"]["javaEnabled"] = metadata.get("javaEnabled")

		query["order"]["customer"]["device"]["browserData"]["javaScriptEnabled"] = True
		query["order"]["customer"]["device"]["acceptHeader"] = request.headers.get("Accept", "")
		query["order"]["customer"]["device"]["ipAddress"] = request.remote_addr

	logging.info("[%s][%s] Hosted checkout request to worldline: %s" % (
		hotel_code, payment_order_id, json.dumps(query)))

	response = create_hosted_form(config, domain, prefix, query)

	logging.info("[%s][%s] Hosted checkout response from worldline: %s" % (
		hotel_code, payment_order_id, response.text.replace("\n","")))

	audit_response(hotel_code, "WORLDLINE", payment_order_id, sid, response.text, payload=json.dumps(query), type_audit="Form Payment")

	if response.status_code == 201:
		if not datastore_communicator.get_using_entity_and_params("ReservationMetadata", [("identifier", "=", int(payment_order_id)), ("hotel_code", "=", hotel_code)], hotel_code="payment-seeker"):
			extra_info = {
				"merchant_url": merchant_url
			}
			properties = {
				"sid": sid,
				"identifier": int(payment_order_id),
				"hotel_code": hotel_code,
				"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
				"extraInfo": json.dumps(extra_info)
			}

			datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")

		response_json = response.json()

		return redirect(PAYMENT_SUBDOMAIN + response_json.get('partialRedirectUrl', ""), 302)
	
	try:
		response_json = response.json()
		error_code = response_json.get("errors")
		error_message = error_code[0].get("message")
		logging.info("Received errorCode from gateway:%s" % error_message)
		url_ko = add_sid_to_url(config.get("url_ko", request.referrer) + "&errorCode=" + error_message, sid)
	except Exception as e:
		url_ko = add_sid_to_url(config.get("url_ko", request.referrer), sid)

	return redirect(url_ko, 302)


def create_hosted_form(config, domain, prefix, query):
	headers = {
		"Content-Type": "application/json",
		"Date": format_date_time(mktime(datetime.datetime.utcnow().timetuple()))
	}
	auth_token = DefaultAuthenticator(authorization_type="v1HMAC", api_id_key=config.get(prefix + "api_key"),
									  secret_api_key=config.get(prefix + "secret_key"))
	sign = auth_token.create_simple_authentication_signature("POST", urlparse(
		"https://%s/v1/%s/hostedcheckouts" % (domain, config.get(prefix + "merchant_id"))), headers)
	headers["Authorization"] = sign
	response = requests.post("https://%s/v1/%s/hostedcheckouts" % (domain, config.get(prefix + "merchant_id")),
							 json=query, headers=headers)
	return response


@app.route("/worldline/webhook", methods=["GET", "POST"])
def worldline_webhook():

	logging.info("[WORLDLINE][WEBHOOK] args %s" % json.dumps(request.args.to_dict()))

	if request.is_json:
		logging.info("[WORLDLINE][WEBHOOK] body %s" % json.dumps(request.json))
		response_body = request.json

		hotel_code = request.args.get("hotel_code", "")

		errors = response_body.get("payment", {}).get("statusOutput", {}).get("errors", [])

		temporal_errors = [i for i in errors if i.get("message", "") == "Not authorised"]

		if response_body.get("type", "") in ["payment.rejected"] and not temporal_errors:
			identifier = response_body.get("payment", {}).get("paymentOutput", {}).get("references", {}).get("merchantOrderId", 0)

			reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata", [("identifier", "=", identifier), ("hotel_code", "=", hotel_code)])
			if reservation_metadata:
				reservation_metadata = reservation_metadata[0]
				reservation_metadata_extra_info = json.loads(reservation_metadata.get("extraInfo", "{}"))
				if not reservation_metadata_extra_info.get("reservation_created"):

					reservation_metadata_extra_info["reservation_created"] = True
					reservation_metadata["extraInfo"] = json.dumps(reservation_metadata_extra_info)
					reservation_metadata_id = int(reservation_metadata.key.id)
					datastore_communicator.save_to_datastore("ReservationMetadata", reservation_metadata_id, reservation_metadata, hotel_code="payment-seeker:")

					merchant_url = reservation_metadata_extra_info.get("merchant_url", "")

					params_in_url = {
						"hostedCheckoutId": response_body.get("payment", {}).get("hostedCheckoutSpecificOutput", {}).get("hostedCheckoutId", ""),
						"no_redirect": True
					}

					params_merchant_url = urlencode(params_in_url)
					if "?" in merchant_url:
						merchant_url = merchant_url + "&" + params_merchant_url
					else:
						merchant_url = merchant_url + "?" + params_merchant_url

					merchant_response = requests.get(merchant_url)

	echo_token = request.headers.get("X-GCS-Webhooks-Endpoint-Verification", "")

	response = make_response(echo_token, 200)
	response.headers["X-GCS-Webhooks-Endpoint-Verification"] = echo_token

	return response

@app.template_filter()
def money_format(amount):
	if int(amount) != amount:
		return "%.2f" % amount
	else:
		return "%d" % amount

