import datetime
import json
import uuid

from random import randint


from Cryptodome.Signature.pss import MGF1

from Cryptodome.Cipher import PKCS1_OAEP
import urllib.parse
from Cryptodome.PublicKey import RSA

from Cryptodome.Protocol.KDF import PBKDF2
from Cryptodome.Hash import SHA1, SHA256
import base64


from flask import request
from flask_cors import cross_origin

from paraty import  app
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, GATEWAY_SUCESS_RETURNED, \
	GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, audit_response, get_amount_to_pay
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.pages.cobrador.gateway_logger import build_logger_context
from paraty.utilities.languages import language_utils
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty.utilities.templates.templates_processor import build_template

from Cryptodome.Cipher import AES

from paraty_commons_3.common_data.common_data_provider import get_web_section, get_hotel_advance_config_value
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.logging.my_gae_logging import logging
KEY = b")RF@aTfUjXn2r4u9"

response_banorte = {
	"A": "Aprobada",
	"D": "Declinada",
	"R": "Rechazada",
	"T": "Sin respuesta del autorizador",
	"N": "No se procesó a Payworks por validación no exitosa de 3D Secure y Cybersource",
	"E": "No se procesó a Payworks por errores internos en la aplicación"
}



class BanorteController(GatewayInterface):
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		return False

	def reservation_has_token(self, reservation):
		return False

	def gateway_has_token(self):
		return False

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		return False

	def translate_error(self, error):
		return error
	
	def gateway_has_token(self):
		return False

	def get_initial_order_id_from_extra_info(self, extra_info):
		return extra_info.get("paymentOrderId", 0)


class BanorteFormController(FormGatewayInterface):

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
		msi_payments = False
		installments = []

		for r_param in ["base_url", "host_url", "host", "root_url", "url", "url_root"]:
			if hasattr(request, r_param):
				logging.info("Request parameter %s: %s" % (r_param, getattr(request, r_param, "")))

		translations = language_utils.get_web_dictionary(extra_data.get("language","SPANISH"))
		reservation_amount = amount
		hotel = get_hotel_by_application_id(hotel_code)
		rate_condition = "nrf" if extra_data.get("nrf") else "flex"

		amounts = ["100"]

		if config.get("flex_amounts") and not extra_data.get("nrf") and not extra_data.get("from_tpv_link"):
			amounts.extend(config.get("flex_amounts").strip(";").split(";"))
			mounts = [str(x) for x in amounts]

		section_name = get_configuration_property_value(hotel_code, "Texto a reemplazar")
		translations_section = get_web_section(hotel, section_name, extra_data.get("language", "SPANISH"),
											   set_languages=True)
		custom_text = {}
		custom_amounts = {}
		currency_value = translations.get("T_currency_value")
		b3_custom_text_from_pep = get_hotel_advance_config_value(hotel_code, "b3 custom text from pep")
		if b3_custom_text_from_pep and b3_custom_text_from_pep.lower() == "true":
			for option_amount in amounts:
				if "-day" in option_amount:
					total_reservation_nights = extra_data.get("num_nights")
					num_nights_to_pay = option_amount.split("-day")[0]
					amount_to_pay = str(
						get_amount_to_pay(reservation_amount, total_reservation_nights=total_reservation_nights,
										  num_nights_to_pay=num_nights_to_pay,
										  prices_per_day=extra_data.get("prices_per_day")))
					current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace(
						"@@amount@@", amount_to_pay).replace("@@currency@@", extra_data.get("currency"))
					translate = translations_section.get("T_wordline_n_day").replace("@@amount@@",
																					 amount_to_pay).replace(
						"@@currency@@", extra_data.get("currency")).replace("@@currency_value@@",
																			current_currency_value)
					num_days = option_amount.split("-day")[0]
					final_translate = translate.replace("@@night@@", num_days)
					custom_text[option_amount] = final_translate
				else:
					current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace(
						"@@amount@@", reservation_amount).replace("@@currency@@", extra_data.get("currency"))
					amount_to_pay = str(get_amount_to_pay(reservation_amount, percent=option_amount))
					if option_amount == "100":
						final_translate = translations_section.get("T_wordline_100").replace("@@amount@@",
																							 reservation_amount).replace(
							"@@currency@@", extra_data.get("currency")).replace("@@currency_value@@",
																				current_currency_value)
					else:
						final_translate = translations_section.get("T_wordline_percent").replace("@@percent@@",
																								 option_amount).replace(
							"@@amount@@", amount_to_pay).replace("@@currency@@", extra_data.get("currency")).replace(
							"@@currency_value@@", current_currency_value)
					custom_text[option_amount] = final_translate
					custom_amounts[option_amount] = amount_to_pay

		payment_info = {
			"hotel_code": hotel_code,
			"gateway_type": gateway_type,
			"payment_order_id": payment_order_id,
			"amount": amount,
			"sid": sid,
			"extra_data": extra_data,
			"amounts": amounts
		}

		logging.info("BANORTE BUILD FORM. payment info (for payload encrypted): %s", payment_info)

		payment_info_string = json.dumps(payment_info)

		encryptor = AES.new(KEY, AES.MODE_EAX)
		payload = encryptor.encrypt(payment_info_string.encode("utf-8"))
		payload = base64.b64encode(encryptor.nonce + payload).decode("utf-8")

		region_block = False

		if config.get("msi_only_for_mexico"):
			if extra_data.get("country", "ES").upper() != "MX":
				region_block = True

		msi_installments_custom_text = translations_section.get("T_wordline_n_installments")
		if config.get("msi payment") and not region_block:
			msi_payments = True
			valid_installments = config.get("msi payment", "3").split(";")

			msi_installments_custom_text_description = translations_section.get("T_wordline_n_installments_description")

			if msi_installments_custom_text:
				current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace("@@amount@@", reservation_amount).replace("@@currency@@", extra_data.get("currency"))
				msi_installments_custom_text = msi_installments_custom_text.replace("@@currency_value@@", current_currency_value)
				msi_installments_custom_text = msi_installments_custom_text.replace("@@amount@@",reservation_amount).replace("@@currency@@", extra_data.get("currency"))
				custom_text['radiomsi'] = msi_installments_custom_text

			if msi_installments_custom_text_description:
				custom_text['msi_installments_custom_description'] = msi_installments_custom_text_description

			for valid_installment in valid_installments:
				installments.append({
					"price": float(amount) / float(valid_installment),
					"installment": valid_installment
				})
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

		merchant_url = gateway_configuration.get("merchant_url", "")
		if "?" in merchant_url:
			merchant_url = merchant_url + "&sid=" + sid + "&reservation=" + payment_order_id
		else:
			merchant_url = merchant_url + "?sid=" + sid + "&reservation=" + payment_order_id

		context = {
			"msi_payments": msi_payments,
			"installments": installments,
			"mode": gateway_configuration.get("mode"),
			"merchant_url": merchant_url,
			"payload": payload,
			"amounts": amounts,
			"custom_text": custom_text,
			"custom_amounts": custom_amounts,
			"msi_installments_custom_text": msi_installments_custom_text,
			"currency": extra_data.get("currency"),
			"rate_condition": rate_condition,
			"num_nights": extra_data.get("num_nights"),
			"logging_session": build_logger_context(),
			"hotel_code": hotel_code,
			"payment_order_id": payment_order_id
		}

		logging.info("BANORTE BUILD FORM. CONTEXT: %s", context)

		if extra_data.get("prices_per_day"):
			context["prices_per_day"] = extra_data.get("prices_per_day")


		template_path = "pages/cobrador/gateways/banorte_form.html"
		form_gateway = build_template(template_path, context, extra_data.get("language", "SPANISH"))
		return form_gateway

	def process_gateway_response(self, hotel_code, gateway_type, response):
		identifier = request.args.get("reservation")
		# legacy compatibility
		sid = request.args.get("sid")
		if request.args.get("intent"):
			sid = request.args.get("intent")

		data = request.args.get("data")

		amount_sent_to_gateway = "0"

		logging.info(f'Data received: {data}')
		result_response = {}

		cod_response = ""
		posible_reservations_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata", [("sid", "=", sid),("hotel_code","=",hotel_code)])
		if posible_reservations_metadata:
			# be carefull in reservtion_metada we will found all tries in the session in others gateways!
			# we have to check which is the correct one
			for reservation_metadata in posible_reservations_metadata:

				extra_info = json.loads(reservation_metadata.get("extraInfo"))

				if extra_info.get("pPassPhrase"):
					pPassPhrase = extra_info.get("pPassPhrase")
					pVi = extra_info.get("pVi")
					pSalt = extra_info.get("pSalt")
					amount_sent_to_gateway = extra_info.get("amount") or "0"
					try:
						result = AESDecryptString(pPassPhrase, pVi, pSalt, data)
						logging.info("Possible Response banorte:" + result)
						result_response = json.loads(result)

						cod_response = result_response.get("resultadoPayw")

						if cod_response == "A":
							logging.info("CORRECT cod_response found!: %s", cod_response)
							break
					except:
						logging.warning(f'There was an error trying to decrypt {pPassPhrase}')
						continue

		result = {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"PAYMENT_GATEWAY_NAME": "Banorte"
		}

		order_id = ""

		if cod_response == "A":
			order_id = result_response.get("codigoAut")
			real_identifier = result_response.get("numeroControl")
			result['GATEWAY_ORDER_ID'] = order_id
			# banorte don't send the amount, we have to take it from params
			result['GATEWAY_PAID_AMOUNT'] = amount_sent_to_gateway
			result['CODE'] = GATEWAY_SUCESS_RETURNED
			result["GATEWAY_EXTRA_INFO"] ={
				"banorte_info": result_response
			}
			if real_identifier:
				result["IDENTIFIER"] = real_identifier
			logging.info(f"Transacción aprobada.{response_banorte.get(cod_response)}")

		else:
			# result['CODE'] = GATEWAY_ERROR_RETURNED
			logging.info(f"Transacción denegada.{response_banorte.get(cod_response)}")
		try:
			audit_response(hotel_code, gateway_type, order_id, sid, result_response)

		except Exception as e:
			logging.error("Error auditing response for in BANORTE")
			logging.error("Error auditing: %s", e)

		return result

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


def save_data_encrypt(hotel_code, sid,payment_order_id, extra_info):

	properties = {
		"sid": sid,
		"identifier": str(payment_order_id),
		"hotel_code": hotel_code,
		"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
		"extraInfo": json.dumps(extra_info)
		}
	datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code = "payment-seeker:")


def get_json(payment_data, gateway_configuration, installments, payment_method, amount_percent):
	try:
		if not gateway_configuration.get("gateway_type", "").lower() == "banorte":
			return {}

		payment_order_id = payment_data.get("payment_order_id")
		amount = payment_data.get("amount")
		extra_data = payment_data.get("extra_data")
		amount = amount.replace(",", ".")
		amount = float(amount)

		logging.info("BANORTE BUILD REDIRECT (json) %s (payment_order_id)", payment_order_id)
		logging.info("BANORTE BUILD REDIRECT (json) %s extra_data: %s", payment_order_id, extra_data)
		logging.info("BANORTE BUILD REDIRECT (json) %s amount: %s", payment_order_id, amount)
		logging.info("BANORTE BUILD REDIRECT (json) %s amount_percent: %s", payment_order_id, amount_percent)
		logging.info("BANORTE BUILD REDIRECT (json) %s installments: %s", payment_order_id, installments)
		logging.info("BANORTE BUILD REDIRECT (json) %s payment_method: %s", payment_order_id, payment_method)


		if amount_percent:
			amounts = ["100"]
			amounts.extend(gateway_configuration.get("flex_amounts", "100").strip(";").split(";"))
			amounts = [str(x) for x in amounts]

			if amount_percent in amounts:

				if amount_percent.endswith("-day"):

					nights_to_pay = int(amount_percent.replace("-day", ""))
					num_nights = int(extra_data.get("num_nights"))
					if nights_to_pay < num_nights:
						if extra_data.get("prices_per_day"):
							amount = get_amount_to_pay(amount, total_reservation_nights=num_nights,
													   num_nights_to_pay=nights_to_pay,
													   prices_per_day=extra_data.get("prices_per_day"))
						else:
							amount = round((amount / num_nights) * nights_to_pay, 2)

				else:
					amount_percent = float(amount_percent)

					amount = round((amount_percent * amount) / 100, 2)

		amount = "{:.2f}".format(amount)

		language = "ES" if extra_data.get('language', 'SPANISH') == "SPANISH" else "EN"

		data = {
			"merchantId": gateway_configuration.get("merchant_id_afiliacion"),
			"name": gateway_configuration.get("merchant_name", ""),
			"password": gateway_configuration.get("merchant_code"),
			"mode": gateway_configuration.get("mode"),
			"controlNumber": payment_order_id,
			"terminalId": gateway_configuration.get("merchant_terminal"),
			"amount": amount,
			"merchantName": gateway_configuration.get("gateway_comercio"),
			"merchantCity": "Monterrey",
			"lang": language,
			"customerRef1": payment_order_id,
			"customerRef2": "",
			"customerRef3": "",
			"customerRef4": "",
			"customerRef5": ""
			# "billToFirst Name": "",
			# "billToLastName": "",
			# "billToStreet": "",
			# "billToStreetNumber": "",
			# "billToStreetNumbe2": "",
			# "billToStreet2Col": "",
			# "billToStreet2De|": "",
			# "billToCitv": "",
			# "billToState": "",
			# "billToCountry": "",
			# "billToPhoneNumber": "",
			# "billToPostalCode": "",
			# "billToEmail": "",
			# "billToCustomerld": "",
			# "billToCustomerPassword": "",
			# "billTo DateOfBirth": "",
			# "billToHostname": "",
			# "billToHttoBrowserEmail": "",
			# "billTolpAddress": "",
			# "comments": "",
			# "shipToFirstName": "",
			# "shipToLastName": "",
			# "shipToStreetNumber": "",
			# "shipToStreetNumber2": "",
			# "shipToStreet2Col": "",
			# "shipToStreet2Del": "",
			# "shipToCity": "",
			# "shipToState": "",
			# "shipToCountry": "",
			# "shipToPostalCode": "",
			# "shipToPhoneNumber": "",
			# "merchantDefinedDataField3": ""
			# "merchantDefinedDataField4": "",
			# "merchantDefinedDataField5": "",
			# "merchantDefinedDataField8": "",
			# "merchantDefinedDataField6": "",
			# "merchantDefinedDataField7": "",
			# "merchantDefinedDataField9": "",
			# "merchantDefinedDataField10": "",
			# "merchantDefinedDataField11": "",
			# "merchantDefinedDataField100": "",

		}

		region_block = False
		if gateway_configuration.get("msi_only_for_mexico"):
			if extra_data.get("country", "ES").upper() != "MX":
				region_block = True

		#ATTENTION! change merchant to MX is FOR ALL PAYMENTS FROM MX, not only for MSI
		if not region_block:
			if installments and payment_method == "msi-payment" and gateway_configuration.get("msi payment"):
				avalible_installments = gateway_configuration.get("msi payment", "3").split(";")
				if installments not in avalible_installments:
					installments = "3"

				data["initialDeferment"] = "00"
				data["paymentsNumber"] = "0" + installments
				data["planType"] = "03"

			if gateway_configuration.get("merchant_name_mx"):
				data["name"] = gateway_configuration.get("merchant_name_mx")
			if gateway_configuration.get("merchant_code_mx"):
				data["password"] = gateway_configuration.get("merchant_code_mx")
			if gateway_configuration.get("merchant_id_afiliacion_mx"):
				data["merchantId"] = gateway_configuration.get("merchant_id_afiliacion_mx")
				logging.info("BANORTE BUILD REDIRECT (json) %s merchant CHANGED TO MX!", payment_order_id)
			if gateway_configuration.get("merchant_terminal_mx"):
				data["terminalId"] = gateway_configuration.get("merchant_terminal_mx")
			if gateway_configuration.get("gateway_comercio_mx"):
				data["merchantName"] = gateway_configuration.get("gateway_comercio_mx")

		logging.info(f"BANORTE BUILD REDIRECT (json) %s Final Data: %s", payment_order_id, data)

		return data
	except Exception as e:
		return {}


def AESEncryptString(datosJSONtxt):
	# Se generan las llaves aleatorias.
	passphrase = createRandom(True, 16)
	vil = createRandom(False, 16)
	saltl = createRandom(False, 16)
	viHex = get_hex(vil)
	saltHex = get_hex(saltl)
	viByte = bytes.fromhex(viHex)
	saltByte = bytes.fromhex(saltHex)

	key = PBKDF2(passphrase, saltByte, dkLen=16, count=1000, hmac_hash_module=SHA1)
	data = json.dumps(datosJSONtxt)
	# Se cifra la información con AES
	cipher = AES.new(key, AES.MODE_CTR, initial_value=viByte, nonce=b'')
	encryptedBytes = cipher.encrypt(data.encode())
	cypher_data = base64.b64encode(encryptedBytes).decode()

	logging.info(f"viHex: {viHex}    salt:{saltHex}     pass_phrase: {passphrase}")
	# Se regresa la información cifrada con los elementos para que pueda descifrarse
	return {
		'vi': viHex,
		'salt': saltHex,
		'pass_phrase': passphrase,
		'cypher_data': cypher_data
	}


def createRandom(extendido, largo):
	key = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvuxyz*&-%/!?*+=C)"
	rndkey = "0123456789abcdefghiklmnopqrstuwuxyz"
	sb = []
	cadena = key if extendido else rndkey
	while len(sb) < largo:
		index = randint(0, len(cadena) - 1)
		sb.append(cadena[index])
	return ''.join(sb)


# De donde se coge la cadena de certificación de banorte
def RSAEncriptWithPK(pdata):

	data_bytes = pdata.encode('utf - 8')
	pem_public_key = RSA.import_key(open("certificate.pem").read())
	rsa_cipher = PKCS1_OAEP.new(pem_public_key, hashAlgo=SHA256, mgfunc=lambda x, y: MGF1(x, y, SHA1))
	encrypted_bytes = rsa_cipher.encrypt(data_bytes)
	return base64.b64encode(encrypted_bytes).decode('utf - 8')

	return encrypted_base64


def AESDecryptString(passPhrase, vi, salt, data_result):
	# cipherData = "QVNRcEZHWHVPZWQxdjc2Y21kY0NLVzNXVzRHUUJmR21TMzYzUVJSQ1FJeVVtNjdCT05uZVpXeWNFbGdnL0grWXlTSFoyTnRwcU1IT2cxNTFRRzZkd2VGV0tPNHcveHFFVzdZK1JSeU9hakwzSUgvUEE4emNJVWhUbkFzZk1WZVRCTUluL012d1FvZ1BxMlBiSlFmb0MxT1NqN0NrRGlTS0tNWTRjT2ZxTU51WnNwQ0k3UWF6cjZSNWlkeDJJeVJ2MVRaelFxdU1qVUJ5SFNJeDRrOTN4RE1USUwrTnpUUlpZclZTNHg2S0xlZzltY3BBNjJ0SXVvS3d4aER0QUljL1YraHdZMUg0MXlXRkdiMU9PK1lhak9laitENDNUZi9oaWJXRFVGeUYvTXIwL3ZXSWxYbUNuV0krdzZpTkNXQ2FFMkJ1L3c9PQ=="

	passphrase = passPhrase

	viByte = bytes.fromhex(vi)
	saltByte = bytes.fromhex(salt)

	# Se reconstruye la Secret Key
	iterations = 1000
	key = PBKDF2(passphrase, saltByte, dkLen=16, count=iterations, hmac_hash_module=SHA1)

	cipher = AES.new(key, AES.MODE_CTR, initial_value=viByte, nonce=b'')
	data_result1 = urllib.parse.unquote_plus(data_result)


	data_result2 = base64.b64decode(data_result1).decode()
	data_result3 = base64.b64decode(data_result2)

	decryptedBytes = cipher.decrypt(data_result3)

	# Se regresa el resultado
	return decryptedBytes.decode("utf-8",errors="ignore")


def get_hex(value):
	return value.encode('utf-8').hex()


@app.route("/banorte/redirect", methods=["POST"])
@cross_origin(origin='*')
def banorte_redirect():
	form_data = request.json

	installments = form_data.get("installments")
	payment_method = form_data.get("payment-method", "")
	amount_percent = form_data.get("payment-amount", "100")
	payload = form_data.get("payload")
	payload = base64.b64decode(payload)

	nounce = payload[:16]

	payload = payload[16:]

	encryptor = AES.new(KEY, AES.MODE_EAX, nonce=nounce)

	payload = encryptor.decrypt(payload)

	payment_data = json.loads(payload)

	hotel_code = payment_data.get("hotel_code")
	gateway_type = payment_data.get("gateway_type")
	payment_order_id = payment_data.get("payment_order_id")
	amount = payment_data.get("amount")
	sid = payment_data.get("sid")
	extra_data = payment_data.get("extra_data")

	log_prefix = "[BANORTE][%s][%s]" % (hotel_code, payment_order_id)

	logging.info("%sUser agent of the user: %s" % (log_prefix, request.headers.get("User-Agent", "")))

	gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
	data = get_json(payment_data, gateway_configuration, installments, payment_method, amount_percent)
	if not data:
		return "Error"
	data_encrypt = AESEncryptString(data)

	pPassPhrase = data_encrypt.get("pass_phrase")
	pVi=  data_encrypt.get("vi")
	pSalt = data_encrypt.get("salt")
	extra_info = {
		"pPassPhrase": pPassPhrase,
		"pVi": pVi,
		"pSalt": pSalt,
		"amount": data.get("amount")
	}

	fake_sid = "intent_%s" % str(uuid.uuid4())
	save_data_encrypt(hotel_code, fake_sid, payment_order_id, extra_info)

	cad1 = pVi + "::" + pSalt + "::" + pPassPhrase
	cad1_codf = RSAEncriptWithPK(cad1)
	cad2 = data_encrypt.get("cypher_data")
	cad = cad1_codf + ":::" + cad2

	merchant_url = gateway_configuration.get("merchant_url", "")


	if "?" in merchant_url:

		merchant_url = merchant_url + "&sid=" + sid +"&reservation=" + payment_order_id
	else:
		merchant_url = merchant_url + "?sid=" + sid +"&reservation=" + payment_order_id

	context = {
		"dev": app.config.get("DEV"),
		"mode": gateway_configuration.get("mode"),
		"data": cad,
		"merchant_url": merchant_url,
		"intent": fake_sid
	}

	logging.info("%sPayload for init banorte: %s" % (log_prefix, json.dumps(context)))

	return context
