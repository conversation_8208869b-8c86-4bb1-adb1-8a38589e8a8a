import json
import logging
from datetime import datetime

import requests
from flask import request as r, render_template
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, GATEWAY_SUCESS_RETURNED, \
	GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, audit_response, get_integration_name
from base64 import b64encode
import json
from random import randint
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


class EvoControler(GatewayInterface):
	def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.info("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
							 reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			extra_info["last_merchant_order_used"] = merchant_order
			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
																			  reservation, hotel_code=hotel_code)
			logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
						 reservation_id)

			return reservation_updated_id

	def get_next_evo_order(self, hotel_code, reservation):

		last_merchant_order_used = ""
		if reservation.get("extraInfo"):
			extra_info = json.loads(reservation.get("extraInfo"))
			last_merchant_order_used = extra_info.get("last_merchant_order_used")

		if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
			last_merchant_order_used = last_merchant_order_used[0:8]
			numerical_order = int(last_merchant_order_used)
			numerical_order += 1

			merchant_order = str(numerical_order)

			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
		else:

			merchant_order = reservation.get("identifier")
			# maybe this reservation was done before gateway was configured. So it could be an alphanumeric
			if not merchant_order.isnumeric():
				merchant_order = randint(10000000, 99999000)

			numerical_order = int(merchant_order)
			numerical_order += 1
			merchant_order = str(numerical_order)
			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

		# IMPORTANT: Even redsys return us an erro, they save ALWAYS the order.
		# so we have to save it in reservation for not received a duplicate order error in next payment!
		self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)

		return merchant_order
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		integration = self.get_configuration(hotel_code)

		merchant_id = integration.get("merchant_id", "")
		username = "merchant.%s" % merchant_id
		password = integration.get("api_password", "")

		try:
			extra_info = json.loads(reservation.get("extraInfo"))
		except:
			extra_info = {}

		gateway_data = extra_info.get("gateway_data", {})
		token = gateway_data.get("token")
		funds_type = gateway_data.get("funds_type")

		currency = extra_info.get("currency")
		order_id = reservation.get("identifier")
		payment_id = self.get_next_evo_order(hotel_code, reservation)


		payload = {
			"apiOperation": "PAY",
			"order": {
				"amount": str(amount),
				"currency": currency
			},
			"referenceOrderId": order_id,
			"sourceOfFunds": {
				"token": token,
				"type": "SCHEME_TOKEN"
			},
			"transaction": {
				"acquirer": {
					"transactionId": payment_id
				}
			}
		}

		payment_url = "https://evopaymentsmexico.gateway.mastercard.com/api/rest/version/100/merchant/%s/order/%s/transaction/%s" % (merchant_id, payment_id, payment_id)

		response = requests.put(payment_url, auth=(username, password), json=payload)

		return GATEWAY_ERROR_RETURNED

	def reservation_has_token(self, reservation):
		# try:
		# 	extra_info = json.loads(reservation.get("extraInfo"))
		# except:
		# 	extra_info = {}
		#
		# gateway_data = extra_info.get("gateway_data", {})
		#
		# if gateway_data.get("gateway_name") == "evo" and gateway_data.get("token"):
		# 	return True

		return False

	def gateway_has_token(self):
		return False

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):

		return GATEWAY_ERROR_RETURNED

	def translate_error(self, error):
		return error

	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration


class EvoFormControler(FormGatewayInterface):

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)

		merchant_id = integration.get("merchant_id", "")
		username = "merchant.%s" % merchant_id
		password = integration.get("api_password", "")

		currency = "MXN"
		if extra_data.get("currency"):
			currency = extra_data.get("currency")

		currency = currency.upper()

		cancel_url = integration.get("url_ko", "")
		cancel_url += "&sid=%s" % sid
		if "?" not in cancel_url:
			cancel_url = cancel_url.replace("&", "?", 1)

		confirm_url = integration.get("url_ok", "")
		confirm_url += "&sid=%s&need_to_wait=true" % (sid)
		if "?" not in confirm_url:
			confirm_url = confirm_url.replace("&", "?", 1)

		merchant_url = integration.get("merchant_url", "")
		merchant_url += "&sid=%s&reservation=%s" % (sid, payment_order_id)
		if "?" not in merchant_url:
			merchant_url = merchant_url.replace("&", "?", 1)

		notification_url = merchant_url + "&no_redirect=true"

		prefix_reference = integration.get("prefix_reference", hotel_code + ":")
		description_reference = "%s%s" % (prefix_reference, payment_order_id)

		payload = {
			"apiOperation": "INITIATE_CHECKOUT",
			"checkoutMode": "WEBSITE",
			"interaction": {
				"operation": "PURCHASE",
				"merchant": {
					"name": "Hotel test"
				},
				"displayControl": {
					"billingAddress": "MANDATORY",
					"customerEmail": "MANDATORY"
				},
				"cancelUrl": cancel_url,
				"returnUrl": merchant_url
			},
			"order": {
				"currency": currency,
				"amount": amount,
				"id": payment_order_id,
				"reference": description_reference,
				"description": description_reference,
				"customerReference": description_reference,
				"notificationUrl": notification_url
			}
		}

		if integration.get("enable msi"):
			payload["constraints"] = {
				"paymentPlans": {
					"supported": []
				}
			}

			msi_rules = integration.get("enable msi", "").strip(";").split(";")

			available_installments = get_available_msi_installments(integration, float(amount), currency)

			for msi_rule in msi_rules:
				if msi_rule not in available_installments:
					continue
				payload["constraints"]["paymentPlans"]["supported"].append(msi_rule)

		set_customization(hotel_code, integration, payload)

		request_url = "https://evopaymentsmexico.gateway.mastercard.com/api/rest/version/100/merchant/%s/session" % merchant_id

		response = requests.post(request_url, auth=(username, password), json=payload)

		audit_response(hotel_code, "EVO", payment_order_id, sid, response.text,
					payload=json.dumps(payload), type_audit="Form Payment")

		if response.status_code in [201]:
			response_json = response.json()
			session_id = response_json.get("session").get("id")

			properties = {
				"sid": sid,
				"identifier": payment_order_id,
				"hotel_code": hotel_code,
				"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
				"extraInfo": json.dumps(response_json)
			}

			datastore_communicator.save_to_datastore("ReservationMetadata", None, properties,
													 hotel_code="payment-seeker")


			context = {
				"sessionid": session_id,
				"merchant": merchant_id,
				"amount": amount,
				"id": payment_order_id,
				"successs_indicator": response_json.get("successIndicator"),
				"confirm_url": confirm_url,
				"from_callcenter": extra_data.get("from_callcenter") or False
			}
			template_path = "pages/cobrador/gateways/evo_insite_form.html"
			form_gateway = render_template(template_path, **context)
			return form_gateway

		return "KO"


	def process_gateway_response(self, hotel_code, gateway_type, datas):
		#TODO
		payment_order_id = r.args.get("reservation")
		sid = r.args.get("sid")
		success_indicator_evo_request = r.values.get("resultIndicator")

		logging.info("[EVO][%s][%s]Success indicator for validate payment: %s" % (hotel_code, payment_order_id, success_indicator_evo_request))

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)

		merchant_id = integration.get("merchant_id", "")
		username = "merchant.%s" % merchant_id
		password = integration.get("api_password", "")

		reservations_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
																				  [("identifier", "=", payment_order_id), ("hotel_code", "=", hotel_code)])
		result_metadata = {}

		for reservation_metadata in reservations_metadata:
			try:
				session_metadata = json.loads(reservation_metadata.get("extraInfo", "{}"))
				result_metadata[session_metadata.get("successIndicator")] = session_metadata
			except:
				continue

		if reservations_metadata:
			reservation_metadata = reservations_metadata[0]
			session = None
			if success_indicator_evo_request and result_metadata.get(success_indicator_evo_request):
				session_metadata = result_metadata.get(success_indicator_evo_request)
				logging.info("[EVO][%s][%s]Success indicator validated" % (hotel_code, payment_order_id))
				session_id = session_metadata.get("session").get("id")

				transaction_url = "https://evopaymentsmexico.gateway.mastercard.com/api/rest/version/100/merchant/%s/session/%s" % (merchant_id, session_id)

				response = requests.get(transaction_url, auth=(username, password))

				audit_response(hotel_code, "EVO", payment_order_id, sid, response.text, payload=session_id, type_audit="Process payment")

				if response.status_code in [200, 201]:
					session = response.json()

			elif not success_indicator_evo_request and datas:
				try:
					if not isinstance(datas, dict):
						session = json.loads(datas)
					else:
						session = datas
				except:
					pass

				audit_response(hotel_code, "EVO", payment_order_id, sid, datas, payload="",
							   type_audit="Process payment")

			if session:
				payment_ok_from_b3 = session.get("session", {}).get("updateStatus") in ["SUCCESS"]
				payment_ok_from_wh = session.get("result") in ["SUCCESS"] and session.get("order", {}).get("status") in ["CAPTURED", "SUCCESS"]
				payment_is_ok = payment_ok_from_wh or payment_ok_from_b3

				if session.get("authentication"):
					if session["authentication"].get("payerInteraction") in ["REQUIRED"]:
						payment_is_ok = False

				if payment_is_ok:
					logging.info("[EVO][%s][%s]Payment success" % (hotel_code, payment_order_id))
					funds_type = session.get("sourceOfFunds", {}).get("type", "")


					gateway_extra_info = {
						"gateway_data": {
							"gateway_name": "evo",
							# "token": token.get("token") or "",
							"funds_type": funds_type
						}
					}

					return {
						"CODE": GATEWAY_SUCESS_RETURNED,
						"GATEWAY_ORDER_ID": payment_order_id,
						"GATEWAY_EXTRA_INFO": gateway_extra_info,
						"PAYMENT_GATEWAY_NAME": "Evo",
						"GATEWAY_PAID_AMOUNT": float(session.get("order").get("amount") or 0)
					}


		return {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"GATEWAY_ORDER_ID": payment_order_id
		}

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


def set_customization(hotel_code, integration, payload):
	theme = integration.get("theme")
	accent_color = integration.get("accent_color")
	merchant_name = integration.get("merchant_name")
	email = integration.get("merchant_email")
	logo = integration.get("merchant_logo")
	phone = integration.get("merchant_phone")
	landing = integration.get("landing_url")

	if not merchant_name:
		hotel = get_hotel_by_application_id(hotel_code)
		merchant_name = hotel.get("name") or ""

	if theme or accent_color:
		payload["interaction"]["style"] = {}
		if theme:
			payload["interaction"]["style"]["theme"] = theme

		if accent_color:
			payload["interaction"]["style"]["accentColor"] = accent_color

	payload["interaction"]["merchant"] = {}
	payload["interaction"]["merchant"]["name"] = merchant_name

	if email:
		payload["interaction"]["merchant"]["email"] = email

	if logo:
		payload["interaction"]["merchant"]["logo"] = logo

	if phone:
		payload["interaction"]["merchant"]["phone"] = phone

	if landing:
		payload["interaction"]["merchant"]["url"] = landing


@timed_cache(hours=2)
def get_available_msi_installments(config, amount, currency):
	merchant_id = config.get("merchant_id", "")
	username = "merchant.%s" % merchant_id
	password = config.get("api_password", "")

	plans_url = "https://evopaymentsmexico.gateway.mastercard.com/api/rest/version/100/merchant/%s/paymentOptionsInquiry" % merchant_id

	response = requests.post(plans_url, auth=(username, password))

	if response.status_code != 201:
		return []

	response_json = response.json()

	plans = response_json.get("paymentTypes", {}).get("card", {}).get("paymentPlanOffer", {})
	pruned_plans = []

	for plan_id, plan in plans.items():
		if plan.get("currency", "").upper() != currency.upper():
			continue

		if plan.get("minimumOrderAmount") and amount < plan.get("minimumOrderAmount"):
			continue

		pruned_plans.append(plan_id)

	return pruned_plans
