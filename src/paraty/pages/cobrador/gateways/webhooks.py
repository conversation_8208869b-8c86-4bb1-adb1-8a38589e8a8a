from flask import request
from flask.views import MethodView

from paraty.pages.cobrador.cobrador_gateway_form import get_form_interface_controller
from paraty.pages.cobrador.cobrador_utils import get_integration_name, extract_params_from_request
from paraty.pages.cobrador.gateways.adyen import AdyenWebhookController
from paraty.pages.cobrador.gateways.w2m import W2MWebhookController


class RefundWebhookController(MethodView):

    def get(self):
        return self.post()

    def post(self):
        # hotel_code = request.args.get("hotel_code")
        # gateway = get_integration_name(hotel_code)

        gateway = request.args.get("force_gateway")

        response = ""
        if "w2m" in gateway.lower():
            response = W2MWebhookController().refund_webhook()

        elif "adyen" in gateway.lower():
            response = AdyenWebhookController().refund_webhook()

        return response

