import datetime
import json
import os
from random import randint
import base64
from urllib.parse import urlencode
import urllib
import re
import xml.etree.ElementTree as xee

import requests
from cryptography.hazmat.primitives import padding
from flask import request
from paraty import app
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, \
    GATEWAY_SUCESS_RETURNED, GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, add_sid_to_url, \
    send_paymentlink_email_to_customer, get_hotel_datetime, get_integration_name, \
    audit_error_web_payment, audit_response, filter_reservation_metadata_by_hotel_code
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.utilities.languages.language_utils import SPANISH
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.logging.my_gae_logging import logging
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad, unpad
from Cryptodome.Random import get_random_bytes
from base64 import b64encode
from xml.etree.ElementTree import fromstring
from urllib.parse import unquote

from paraty_commons_3.utils import proxy_utils

KEY = "5dcc67393750523cd165f17e1efadd21"
DEFAULT_PERCENTS = "100"

def decrypt_aes_cbc(ciphertext, key):
    cipher = AES.new(key, AES.MODE_CBC, IV=b'\x00' * 16)

    decrypted_data = unpad(cipher.decrypt(ciphertext), AES.block_size)
    decrypted_data = str(decrypted_data).replace("'", "")
    decrypted_data = "<CENTEROFPAYMENTS>" + decrypted_data.split("<CENTEROFPAYMENTS>")[1]
    return decrypted_data

def decrypt_gateway_respone(response, KEY):
    key = bytes.fromhex(KEY)
    encrypted_str = unquote(response)

    encrypted_bytes = base64.b64decode(encrypted_str)

    decrypted_data = decrypt_aes_cbc(encrypted_bytes, key)

    formated_decrypted_data = decrypted_data.replace("\\n", '')

    xml_response = fromstring(formated_decrypted_data)
    response_status = xml_response.find('.//response')

    return response_status.text, xml_response, formated_decrypted_data

# This function build the params for web regardless of the token. WPP = Web Pay Plus
def build_web_pay_plus_params(paylink_data, gateway_configuration, charge_with_token=False):
    paylink_data["id_company"] = gateway_configuration.get("id_company", "")
    paylink_data["id_branch"] = gateway_configuration.get("id_branch", "")
    paylink_data["user"] = gateway_configuration.get("user", "")
    paylink_data["password"] = gateway_configuration.get("password", "")
    paylink_data["associated_trade_number"] = gateway_configuration.get("associated_trade_number", "")
    paylink_data["url"] = gateway_configuration.get("url_gen", "")
    paylink_data["token_url_rest"] = gateway_configuration.get("url_token", "")
    paylink_data["refund_url_rest"] = gateway_configuration.get("refund_url_rest", "")

#This function build the params for transactions with token like automatic charge. CAI = Cargos Automáticos Individuales
def build_cai_business_params(paylink_data, gateway_configuration):
    paylink_data["id_company"] = gateway_configuration.get("id_company", "")
    paylink_data["id_branch"] = gateway_configuration.get("id_branch_token", "")
    paylink_data["user"] = gateway_configuration.get("user_token", "")
    paylink_data["password"] = gateway_configuration.get("password_token", "")
    paylink_data["associated_trade_number"] = gateway_configuration.get("associated_trade_number", "")
    paylink_data["url"] = gateway_configuration.get("url_gen", "")
    paylink_data["token_url_rest"] = gateway_configuration.get("url_token", "")
    paylink_data["refund_url_rest"] = gateway_configuration.get("refund_url_rest", "")


def get_error_description( error_code):
    with open('paraty/utilities/payments_error_codes/santander_paylinks_error_codes.json') as file:
        error_file = json.load(file)
        if error_file.get(error_code):
            error_result = error_file[error_code]
        else:
            error_result = error_code
    return error_result

class SantanderController(GatewayInterface):
    def get_configuration(self, hotel_code):

        integration_name = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

        return gateway_configuration




    def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
        extra_info = reservation.get("extraInfo", "{}")
        if extra_info:
            try:
                extra_info = json.loads(extra_info)
            except:
                logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
                          reservation.get("identifier"))

                return ""

            reservation_id = int(reservation.key.id)
            extra_info["last_merchant_order_used"] = merchant_order
            reservation["extraInfo"] = json.dumps(extra_info)

            reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
                                                                              reservation, hotel_code=hotel_code)
            logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
                     reservation_id)

            return reservation_updated_id

    def update_extraInfo_in_reservation(self, hotel_code, reservation, santander_order_id, gateway_response, type="payment"):
        logging.info("[%s] Updating extra info with a succesfull payment!!!", santander_order_id)
        extra_info = reservation.get("extraInfo", "{}")
        santander_order_id = santander_order_id
        if extra_info:
            try:
                extra_info = json.loads(extra_info)
            except:
                logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
                              reservation.get("identifier"))

                return ""

            reservation_id = int(reservation.key.id)
            new_extraInfo_key = "santander_paylinks_transaction_" + str(santander_order_id)
            extra_info[new_extraInfo_key] = {
                "santander_order_id": gateway_response.find("foliocpagos").text,
                "type": type
            }

            cc_expmonth = gateway_response.find("cc_expmonth")
            if cc_expmonth is not None:
                extra_info[new_extraInfo_key]["cc_expmonth"] = cc_expmonth.text

            cc_expyear = gateway_response.find("cc_expyear")
            if cc_expyear is not None:
                extra_info[new_extraInfo_key]["cc_expyear"] = cc_expyear.text

            auth = gateway_response.find("auth")
            if auth is not None:
                extra_info[new_extraInfo_key]["transaction_auth"] = auth.text

            reservation["extraInfo"] = json.dumps(extra_info)

            reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
                                                                              reservation, hotel_code=hotel_code)
            logging.info("ExtraInfo update for reservation %s", reservation.get("identifier"))

            return reservation_updated_id
    def get_next_santander_order(self, hotel_code, reservation):

        last_merchant_order_used = ""
        if reservation.get("extraInfo"):
            extra_info = json.loads(reservation.get("extraInfo"))
            last_merchant_order_used = extra_info.get("last_merchant_order_used")

        if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
            last_merchant_order_used = last_merchant_order_used[0:8]
            numerical_order = int(last_merchant_order_used)
            numerical_order += 1

            merchant_order = str(numerical_order)

            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
        else:

            merchant_order = reservation.get("identifier")
            # maybe this reservation was done before gateway was configured. So it could be an alphanumeric
            if not merchant_order.isnumeric():
                merchant_order = randint(10000000, 99999000)

            numerical_order = int(merchant_order)
            numerical_order += 1
            merchant_order = str(numerical_order)
            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

        # IMPORTANT: Even redsys return us an erro, they save ALWAYS the order.
        # so we have to save it in reservation for not received a duplicate order error in next payment!
        self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)

        return merchant_order


    def translate_error(self, error):
        return error
    # def translate_error(self, error):
    #     #TODO: implementar traducción del error a devolver
    #     error_result = error
    #     if not "ERROR||" in error_result:
    #         try:
    #             error_result = self.get_error_description(error_result)
    #         except:
    #             logging.info("No se ha podido traducir el error")
    #             return "Error al ejecutar el pago"
    #     else:
    #         info_error = error.split(SEPARATOR_INFO_ERROR_ORDER)
    #         if len(info_error) == 2:
    #             error_code = info_error[0].split("ERROR: ")
    #             if len(error_code) == 2:
    #                 error_code = error_code[1].strip()
    #                 error_result = self.get_error_description(error_code)
    #     return error_result
    #
    # def get_error_description(self, error_code):
    #     with open('paraty/utilities/payments_error_codes/santander_paylinks_error_codes.json') as file:
    #         error_file = json.load(file)
    #         if error_file.get(error_code):
    #             error_result = "ERROR: " + error_file[error_code]
    #         else:
    #             error_result = "ERROR: " + error_code
    #     return error_result
    def execute_payment_in_gateway(self, hotel_code, reservation, amount):
        identifier = reservation.get("identifier", "")
        next_santander_order = self.get_next_santander_order(hotel_code, reservation)
        logging.info("[SANTANDER PAYLINKS][NEW PAYMENT][%s] New payment for transaction with order %s", identifier, next_santander_order)
        integration_name = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

        if re.match("^.+-(.*)$", identifier):
            parts = re.search("^.+-(.*)$", identifier)
            identifier = parts.groups()[0]

        try:
            extra_info = json.loads(reservation.get("extraInfo", {}))
        except:
            extra_info = {}

        transaction_extrainfo_key = "santander_paylinks_transaction_" + identifier
        transaction_extra_info = extra_info.get(transaction_extrainfo_key)

        try:
            paylink_data = {}
            logging.info("[SANTANDER PAYLINKS][NEW PAYMENT][%s] Building cai payment params. Santander requires the authorized credentials CAI.",identifier)
            build_cai_business_params(paylink_data, gateway_configuration)
            logging.info("[SANTANDER PAYLINKS][REFUND][%s] CAI new payment params built: %s ", identifier, paylink_data)

            currency = extra_info.get("currency", "")
            if not currency:
                currency = get_configuration_property_value("Base Price Currency")
            if not currency:
                currency = "MXN"
            paylink_data["currency"] = currency

            token_id = extra_info.get("token_id")
            paylink_data["reference"] = next_santander_order
            paylink_data["amount"] = amount
            paylink_data["token_id"] = token_id
            paylink_data["cc_expmonth"] = transaction_extra_info.get("cc_expmonth")
            paylink_data["cc_expyear"] = transaction_extra_info.get("cc_expyear")

            if currency == "MXN":
                merchant_token = gateway_configuration.get("merchant_token_mxn")
            else:
                merchant_token = gateway_configuration.get("merchant_token_usd")

            paylink_data["merchant_token"] = merchant_token

            xml_string = build_template("pages/cobrador/gateways/santander_paylinks/payment_token_form.xml", paylink_data)

            AES_KEY = gateway_configuration.get("semilla_aes", KEY)
            aes_encryptor = AES128Encryption()
            ciphertext = aes_encryptor.encrypt(xml_string, AES_KEY)

            encodedString = urllib.parse.quote("<pgs><data0>%s</data0><data>%s</data></pgs>" % (paylink_data["associated_trade_number"], ciphertext.decode()),
                                               "UTF-8")

            payload = 'xml=' + encodedString
            #payload = payload.replace("%", "%25").replace(" ", "%20").replace("+", "%2B").replace("=", "%3D").replace("/", "%2F")

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            #response = proxy_utils.post_using_generic_proxy_tls_1_2_USA(paylink_data["token_url_rest"], payload, headers,timeout=120)
            response = requests.post(paylink_data["token_url_rest"], data=payload, headers=headers, timeout=120)
            try:
                audit_response(hotel_code, integration_name, next_santander_order, "FROM_COBRADOR", response.text,
                               type_audit="EXECUTE_PAYMENT")

            except Exception as e:
                logging.error("Error auditing response for in MITEC in EXECUTE")
                logging.error("Error auditing: %s", e)

            if response.status_code == 200 and response.text:
                logging.info("[SANTANDER PAYLINKS][%s] Transaction made with order: %s", identifier, next_santander_order)
                response_decrypted = aes_encryptor.decrypt(AES_KEY, response.text)
                gateway_response = fromstring(response_decrypted)
                gateway_response_status = gateway_response.find('response').text

                logging.info("[SANTANDER PAYLINKS][NEW PAYMENT][%s] New payment response received from gateway: %s", identifier, gateway_response)


                if gateway_response_status == "approved":
                    logging.info("[SANTANDER PAYLINKS][NEW PAYMENT][%s] Success new payment in %s" % (identifier, hotel_code))
                    self.update_extraInfo_in_reservation(hotel_code, reservation, next_santander_order, gateway_response)
                    return gateway_response.find('reference').text
                else:
                    if gateway_response_status == "denied":
                        logging.warning("[SANTANDER PAYLINKS][NEW PAYMENT][%s]. Denied new payment in %s" % (identifier, hotel_code))
                        response_code = gateway_response.find('cd_response').text
                        error_message = get_error_description(response_code)
                        logging.error("[SANTANDER PAYLINKS][%s] Code: %s. Message: %s", identifier, response_code, error_message)
                    else:
                        logging.warning("[SANTANDER PAYLINKS][NEW PAYMENT][%s] NEW PAYMENT. Error in payment in %s" % (identifier, hotel_code))
                        response_code = gateway_response.find('cd_error').text
                        error_message = gateway_response.find('nb_error').text
                        logging.error("[SANTANDER PAYLINKS][%s] Code: %s. Message: %s", identifier, response_code, error_message)
                    return GATEWAY_ERROR_RETURNED

                    # TODO: refund the error to translate
                    # return "%s%s%s" % (GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, error_message)

        except Exception as e:
            logging.warning("[SANTANDER PAYLINKS][REFUND][%s] Something went wrong during the new payment in reservation %s",next_santander_order, identifier)
            logging.error("Exception from santander: %s" % e)

        return GATEWAY_ERROR_RETURNED



#
    def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None, integration_name=None):
        identifier = reservation.get("identifier", "")
        logging.info("[SANTANDER PAYLINKS][REFUND][%s] Trying to refund the transaction with order %s", identifier, order_to_refund)
        if not integration_name:
            integration_name = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

        logging.info("[SANTANDER PAYLINKS][REFUND][%s] Gateway %s for process refund %s", identifier, integration_name, order_to_refund)

        if re.match("^.+-(.*)$", identifier):
            parts = re.search("^.+-(.*)$", identifier)
            identifier = parts.groups()[0]

        try:
            extra_info = json.loads(reservation.get("extraInfo", {}))
        except:
            extra_info = {}

        transaction_extrainfo_key = "santander_paylinks_transaction_" + order_to_refund
        transaction_extra_info = extra_info.get(transaction_extrainfo_key)

        try:
            paylink_data = {}
            logging.info("[SANTANDER PAYLINKS][REFUND][%s] Building refund params. Santander requires the corresponding credentials for each type of transaction", identifier)
            if not payment:
                logging.info("[SANTANDER PAYLINKS][REFUND][%s] We assume this is the initial web transaction", identifier)
                build_web_pay_plus_params(paylink_data, gateway_configuration)
                logging.info("[SANTANDER PAYLINKS][REFUND][%s] Web Pay Plus refund params built: %s ", identifier, paylink_data)
            else:
                build_cai_business_params(paylink_data, gateway_configuration)
                logging.info("[SANTANDER PAYLINKS][REFUND][%s] CAI refund params built: %s ", identifier, paylink_data)

            currency = extra_info.get("currency", "")
            if not currency:
                currency = get_configuration_property_value("Base Price Currency")
            if not currency:
                currency = "MXN"
            paylink_data["currency"] = currency

            next_santander_order = self.get_next_santander_order(hotel_code, reservation)
            token_id = extra_info.get("token_id")
            paylink_data["order"] = transaction_extra_info.get("santander_order_id")
            paylink_data["amount"] = amount
            paylink_data["token_id"] = token_id
            paylink_data["cc_expmonth"] = transaction_extra_info.get("cc_expmonth")
            paylink_data["cc_expyear"] = transaction_extra_info.get("cc_expyear")
            paylink_data["merchant_affiliation"] = gateway_configuration.get("merchant_affiliation")
            paylink_data["auth"] = transaction_extra_info.get("transaction_auth")

            if currency == "MXN":
                merchant_token = gateway_configuration.get("merchant_token_mxn")
            else:
                merchant_token = gateway_configuration.get("merchant_token_usd")

            paylink_data["merchant_token"] = merchant_token

            xml_string = build_template("pages/cobrador/gateways/santander_paylinks/refund_form.xml",
                                        paylink_data)

            AES_KEY = gateway_configuration.get("semilla_aes", KEY)
            aes_encryptor = AES128Encryption()
            ciphertext = aes_encryptor.encrypt(xml_string, AES_KEY)

            encodedString = urllib.parse.quote("<pgs><data0>%s</data0><data>%s</data></pgs>" % (
            paylink_data["associated_trade_number"], ciphertext.decode()),
                                               "UTF-8")

            payload = 'xml=' + encodedString
            #payload = payload.replace("%", "%25").replace(" ", "%20").replace("+", "%2B").replace("=", "%3D").replace("/", "%2F")

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            #response = proxy_utils.post_using_generic_proxy_tls_1_2_USA(paylink_data["refund_url_rest"], payload, headers, timeout=120)
            response = requests.post(paylink_data["refund_url_rest"], data=payload, headers=headers, timeout=120)

            if response.status_code == 200 and response.text:
                logging.info("[SANTANDER PAYLINKS][REFUND][%s] Transaction made with order: %s", identifier, next_santander_order)
                response_decrypted = aes_encryptor.decrypt(AES_KEY, response.text)
                gateway_response = fromstring(response_decrypted)
                gateway_response_status = gateway_response.find('response').text

                logging.info("[SANTANDER PAYLINKS][REFUND][%s] Refund response received from gateway: %s", identifier, response_decrypted)

                if gateway_response_status == "approved":
                    logging.info("[SANTANDER PAYLINKS][REFUND][%s] Success refund in %s" % (identifier, hotel_code))
                    self.update_extraInfo_in_reservation(hotel_code, reservation, next_santander_order,gateway_response, type="refund")
                    return gateway_response.find('reference').text
                else:
                    if gateway_response_status == "denied":
                        logging.warning("[SANTANDER PAYLINKS][REFUND][%s] Denied refund in %s" % (identifier, hotel_code))
                        response_code = gateway_response.find('cd_response').text
                        error_message = get_error_description(response_code)
                        logging.error("[SANTANDER PAYLINKS][REFUND][%s] Code: %s. Message: %s", identifier, response_code, error_message)
                    else:
                        logging.warning("[SANTANDER PAYLINKS][REFUND][%s] Error in refund in %s" % (identifier, hotel_code))
                        response_code = gateway_response.find('cd_error').text
                        error_message = gateway_response.find('nb_error').text
                        logging.error("[SANTANDER PAYLINKS][REFUND][%s] Code: %s. Message: %s", identifier, response_code, error_message)

                    return GATEWAY_ERROR_RETURNED
                    #TODO: refund the error to translate
                    # return GATEWAY_ERROR_RETURNED + "||" + errorCode +","+ error_message + SEPARATOR_INFO_ERROR_ORDER + next_santander_order

        except Exception as e:
            if not payment:
                logging.warning("[SANTANDER PAYLINKS][REFUND][%s] Something went wrong during the refund of a web payment", identifier)
            logging.error("Exception from santander: %s" % e)

        return GATEWAY_ERROR_RETURNED


    def reservation_has_token(self, reservation):
        try:
            extra_info = json.loads(reservation.get("extraInfo"))
            identifier = reservation.get("identifier")
            transaction_extrainfo_key = "santander_paylinks_transaction_" + identifier
            transaction_extra_info = extra_info.get(transaction_extrainfo_key)
            if transaction_extra_info and extra_info.get("token_id"):
                return True
            else:
                return False
        except:
            return False
#
    def gateway_has_token(self):
        return True
#
    def get_initial_order_id_from_extra_info(self, reservation_extra_info):
        return reservation_extra_info.get("paymentOrderId")




class SantanderPaylinksFormController(FormGatewayInterface):
    def aux_decrypt_gateway_response(self, hotel_code, response, gateway_configuration):
        logging.info("[SANTANDER PAYLINKS] Fast decrypt because of we have a response for corporate hotel")
        integration_name = get_integration_name(hotel_code)
        encrypted_str = response.replace("strResponse=", "")
        AES_KEY = gateway_configuration.get("semilla_aes", KEY)
        transaction_status, gateway_response, formated_decrypted_data = decrypt_gateway_respone(encrypted_str, AES_KEY)
        response = {}

        sid = ""

        if transaction_status and encrypted_str:
            identifier = gateway_response.find('.//reference').text
            reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata", [("identifier", "=", identifier)])
            reservation_metadata = filter_reservation_metadata_by_hotel_code(reservation_metadata, hotel_code)
            if reservation_metadata:
                reservation_metadata = reservation_metadata[0]
                sid = reservation_metadata.get("sid")
                response = {
                    "original_hotel_code": reservation_metadata.get("hotel_code"),
                    "sid": sid
                }

        logging.info("[SANTANDER PAYLINKS] Returning data after decrypt %s" % response)

        try:
            audit_response(hotel_code, integration_name, identifier, sid, formated_decrypted_data, type_audit="CORPORATE_HOTEL")

        except Exception as e:
            logging.error("Error auditing response for in MITEC in PROCESS CORPORATE")
            logging.error("Error auditing: %s", e)

        return response

    def addSidToUrlOKandKO(self, url, sid):

        if "?" in url:
            return url + "&sid=" + sid
        else:
            return url + "?sid=" + sid

    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        logging.info("[SANTANDER PAYLINKS][%s] Creating Gateway Form", extra_data.get("original_identifier"))
        gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

        percents_text = DEFAULT_PERCENTS
        if gateway_configuration.get("percents", ""):
            percents_text = gateway_configuration.get("percents", "").replace(",", ";")

        percents = [100]

        post_url = gateway_configuration.get("payment_url", "")
        params = {"sid": sid,
                  "percents": percents,
                  "payment_order_id": payment_order_id,
                  "hotel_code": hotel_code,
                  "payment_url": post_url,
                  "special_tpv_link": gateway_configuration.get("special_tpv_link", ""),
                  "amount": amount,
                  "currency": extra_data.get("currency"),
                  "DEV": app.config.get("DEV"),
                  "original_identifier": extra_data.get("original_identifier")
                  }

        extra_info = {
            "amount": amount,
            "currency": extra_data.get("currency")
        }

        properties = {
            "sid": sid,
            "identifier": str(payment_order_id),
            "hotel_code": hotel_code,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "extraInfo": json.dumps(extra_info)
        }
        datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")

        language = extra_data.get("language", SPANISH)
        paylinks_template = "pages/cobrador/gateways/santander_links_form.html"
        form_gateway = build_template(paylinks_template, params, language)

        return form_gateway

    def process_payment_link(self, hotel_code, gateway_type, datas):
        if not datas:
            logging.warning("[SANTANDER PAYLINKS] The gateway return an error because of no datas found. hotel_code: %s. ", hotel_code)
            return ""

        gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
        sid = datas.get("sid", "")
        logging.info("[SANTANDER PAYLINKS] Processing paylink form into %s with sid %s", gateway_type, sid)


        if gateway_configuration:
            paylink_data = datas.get("paylink_data")
            if datas.get("original_identifier"):
                paylink_data["identifier"] = datas.get("original_identifier")
            logging.info("[SANTANDER PAYLINKS] Gateway_configuration FOUND. hotel code: %s, gateway_type: %s", hotel_code, gateway_type)

            url_ok = gateway_configuration.get("url_ok", "")
            url_ok = add_sid_to_url(url_ok, sid)

            url_ko = gateway_configuration.get("url_ko", "")
            url_ko = add_sid_to_url(url_ko, sid)

            build_web_pay_plus_params(paylink_data, gateway_configuration)

            AES_KEY = gateway_configuration.get("semilla_aes", KEY)

            paylink_data["sid"] = sid

            xml_string = build_template("pages/cobrador/gateways/santander_paylinks/paylink_form.xml", paylink_data)
            aes_encryptor = AES128Encryption()
            ciphertext = aes_encryptor.encrypt(xml_string, AES_KEY)

            encodedString = urllib.parse.quote("<pgs><data0>%s</data0><data>%s</data></pgs>" % (paylink_data["associated_trade_number"],ciphertext.decode()),"UTF-8")
            payload = 'xml=' + encodedString

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            #payload = payload.replace("%", "%25").replace(" ", "%20").replace("+", "%2B").replace("=", "%3D").replace("/", "%2F")
            #response = proxy_utils.post_using_generic_proxy_tls_1_2_USA(paylink_data["url"], payload, headers,timeout=120)
            response = requests.post(paylink_data["url"], data=payload, headers=headers, timeout=120)

            if response.status_code == 200 and response.text:
                logging.info("[SANTANDER PAYLINKS][%s] Response received with encrypted link: %s", paylink_data.get("identifier", ""), response.text)
                try:
                    link_response_decrypted = aes_encryptor.decrypt(AES_KEY, response.text)
                    logging.info("[SANTANDER PAYLINKS][%s] Response recieved from gateway: %s", paylink_data.get("identifier", ""), link_response_decrypted)
                    xml_response = fromstring(link_response_decrypted)
                    url_xml_response = xml_response.find('.//nb_url').text
                    if xml_response is not None and xml_response.find(".//cd_response").text == "success" and url_xml_response:
                        final_link = url_xml_response
                        logging.info("[SANTANDER PAYLINKS][%s] We have a valid link to redirect to user: %s", paylink_data.get("identifier", ""), final_link)
                        return final_link
                    elif xml_response is not None and xml_response.find('.//cd_response').text == "error":
                        logging.warning("[ERROR][SANTANDER PAYLINKS][%s] We have received an error getting TPV link.",paylink_data.get("identifier", ""))
                        error = xml_response.find(".//nb_response").text
                        error = base64.urlsafe_b64encode(error.encode("utf-8")).decode("utf-8")
                        final_url_ko = url_ko + "&errorCode=PAYMENT&&errorForcedMessage=%s" % error
                        return final_url_ko
                    else:
                        return url_ko
                except Exception as e:
                    logging.error("[ERROR][SANTANDER PAYLINKS][%s] Error while trying to get TPV link.", paylink_data.get("identifier", ""))
                    logging.error(e)
                    return url_ko

            logging.error("[ERROR][SANTANDER PAYLINKS][%s] REDIRECTING USER TO URL_KO. hotel_code: %s. url: %s", paylink_data.get("identifier", ""), hotel_code, url_ko)
            return None
        logging.error("[ERROR][SANTANDER PAYLINKS] Something went wrong while trying to get TPV link. Make sure the hotel %s is configured correctly.", hotel_code)
        return ""


    def process_gateway_response(self, hotel_code, gateway_type, response):
        logging.info("[SANTANDER PAYLINKS] Processing gateway response with %s gateway", gateway_type)
        gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

        body = dict(request.get_json())

        logging.info("[SANTANDER PAYLINKS] Body params: %s", body)

        hotel_code = request.args.get("hotel_code")
        if not hotel_code:
            hotel_code = request.args.get("namespace")
        encrypted_str = body.get("response")

        encrypted_str = encrypted_str.replace("strResponse=", "")
        clean_encrypted_str = encrypted_str.replace(" ", "+")
        original_encrypted_response = encrypted_str

        AES_KEY = gateway_configuration.get("semilla_aes", KEY)
        try:
            logging.info("[SANTANDER PAYLINKS] Trying to decrypt original response")
            transaction_status, gateway_response, formated_decrypted_data = decrypt_gateway_respone(encrypted_str, AES_KEY)
        except Exception as e:
            logging.warning(e)
            logging.warning("[SANTANDER PAYLINKS] We retry to decrypt the cleaned response")
            transaction_status, gateway_response, formated_decrypted_data = decrypt_gateway_respone(clean_encrypted_str, AES_KEY)

        identifier = gateway_response.find('.//reference').text
        logging.info("Paraty identifier obteined from reference: %s", identifier)

        reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
                                                                                  [("identifier", "=", identifier),
                                                                                   ("hotel_code", "=", hotel_code)])

        if len(reservation_metadata) > 0:
            reservation_metadata = reservation_metadata[0]

        reservation_metadata_extra_info = json.loads(reservation_metadata.get("extraInfo"))

        sid = ""
        sid_xml_tag = gateway_response.find("./datos_adicionales/data[@id='1']")
        if sid_xml_tag:
            sid = sid_xml_tag.find("value").text
        if not sid and reservation_metadata:
            reservation_metadata = reservation_metadata[0]
            sid = reservation_metadata.get('sid')

            logging.info("SID  obteined from reservation_metadata: %s", sid)

        try:
            audit_response(hotel_code, gateway_type, identifier, sid, formated_decrypted_data,
                           type_audit="PROCESS_RESPONSE")

        except Exception as e:
            logging.error("Error auditing response for in MITEC in PROCESS")
            logging.error("Error auditing: %s", e)


        cobrador_response = {
            "GATEWAY_ORDER_ID": identifier
        }

        to_extra_info = {
            "payment_gateway_name": gateway_type
        }
        to_extra_info["sid"] = sid
        to_extra_info["no_redirect"] = True
        new_extraInfo_key = "santander_paylinks_transaction_" + identifier
        if transaction_status == "approved":
            logging.info("[%s][SANTANDER PAYLINKS] Transaction approved, pending to create!!!", identifier)
            code = GATEWAY_SUCESS_RETURNED

            external_id = gateway_response.find("foliocpagos").text
            amount = float(gateway_response.find("amount").text)

            to_extra_info[new_extraInfo_key] = {
                "payment_link_send_date": get_hotel_datetime(hotel_code).strftime("%Y-%m-%d %H:%M:%S"),
                "santander_order_id": external_id,
            }

            cc_expmonth = gateway_response.find("cc_expmonth")
            if cc_expmonth is not None:
                to_extra_info[new_extraInfo_key]["cc_expmonth"] = cc_expmonth.text

            cc_expyear = gateway_response.find("cc_expyear")
            if cc_expyear is not None:
                to_extra_info[new_extraInfo_key]["cc_expyear"] = cc_expyear.text

            auth = gateway_response.find("auth")
            if auth is not None:
                to_extra_info[new_extraInfo_key]["transaction_auth"] = auth.text

            token = gateway_response.find("number_tkn")
            if token is not None:
                to_extra_info["token_id"] = token.text
            else:
                try:
                    token = self.recovery_token(gateway_configuration, identifier)
                    if token:
                        to_extra_info["token_id"] = token
                except Exception as e:
                    logging.warning(e)
                    logging.info("[SANTANDER PAYLINKS][ERROR] We cannot recover the token of reservation %s", identifier)


        else:
            logging.info("[%s]Transaction declined, pending to create!!!", identifier)
            code = GATEWAY_ERROR_CODE_RETURNED

            external_id = transaction_status
            amount = reservation_metadata_extra_info.get("amount", "0")

            to_extra_info[new_extraInfo_key] = {
                "payment_link_send_date": get_hotel_datetime(hotel_code).strftime("%Y-%m-%d %H:%M:%S"),
                "santander_order_id": identifier,
            }

            cobrador_response["GATEWAY_ERROR_MESSAGE"] = gateway_response.find("nb_error").text
            audit_error_web_payment(hotel_code, identifier, amount, gateway_response.find("nb_error").text)

        cobrador_response["CODE"] = code
        cobrador_response["GATEWAY_PAID_AMOUNT"] = amount
        cobrador_response["GATEWAY_EXTRA_INFO"] = to_extra_info
        cobrador_response["GATEWAY_EXTERNAL_ID"] = external_id
        cobrador_response["IDENTIFIER"] = identifier

        self.add_expiration_date_to_response(cobrador_response, to_extra_info, new_extraInfo_key)
        logging.info("[SANTANDER PAYLINKS] Returning cobrador response to merchant: %s", cobrador_response)
                                                                
        return cobrador_response

    def add_expiration_date_to_response(self, cobrador_response, extra_info, extra_info_key):
        try:
            logging.info("[SANTANDER PAYLINKS] Trying to add expiration date")
            if extra_info_key in extra_info:
                transaction_info = extra_info[extra_info_key]
                if "cc_expmonth" in transaction_info and "cc_expyear" in transaction_info:
                    month = transaction_info["cc_expmonth"]
                    year = transaction_info["cc_expyear"]
                    # Formato estándar MM/YY
                    cobrador_response["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{month}/{year}"
                    logging.info("[SANTANDER PAYLINKS] Added expiration date to response: %s", cobrador_response["GATEWAY_EXTRA_INFO"]["cc_expire_date"])
        except Exception as e:
            logging.error("[SANTANDER PAYLINKS] Error adding expiration date to response: %s", str(e))

    def _send_email_with_payment_link(self, datas, hotel_code):
            logging.info("sending payment link id for payment_order_id: %s", datas.get("payment_order_id"))

            # calculate the amount
            identifier = datas.get("payment_order_id")
            user_name = datas.get("user_name", "WEB_BOOKING3")
            built_link = datas.get("built_link")

            expire_hours = ""
            gateway_configuration = get_payment_gateway_configuration("SANTANDER PAYLINKS COBRADOR", hotel_code)
            if gateway_configuration:
                expire_hours = gateway_configuration.get("expire_hours_link")

            params = {
                "type_link": "price",
                "identifier": identifier,
                "expire_hours": expire_hours,
                "built_link": built_link,
                "amount": datas.get("amount")
            }

            link_sent = send_paymentlink_email_to_customer(hotel_code, params, user_name)

            if not "ERROR" in link_sent:
                logging.info("_send_email_with_payment_link LInk sent correctly")
                return "OK"

            logging.warning("_send_email_with_payment_link ERROR Email LINK not sent")
            return "ERROR Email not sent"

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT

    def recovery_token(self, gateway_configuration, identifier):
        logging.info("[SANTANDER PAYLINKS] Recovering token for reservation %s" % identifier)
        headers = {
            'Content-Type': 'application/xml'
        }

        url_to_post = gateway_configuration.get("url_token_recovery")
        paylink_data = {
            "user": gateway_configuration.get("user_token_recovery"),
            "pwd": gateway_configuration.get("password_token_recovery"),
            "id_company": gateway_configuration.get("id_company"),
            "id_branch": gateway_configuration.get("id_branch_token"),
            "reference": identifier,
            "date": datetime.datetime.now().strftime("%d/%m/%Y"),
            "associated_trade_number": gateway_configuration.get("associated_trade_number")
        }

        AES_KEY = gateway_configuration.get("semilla_aes", KEY)
        params_template = ("<user>%s</user><pwd>%s</pwd><id_company>%s</id_company><date>%s</date><id_branch>%s</id_branch><reference>%s</reference>" % (paylink_data["user"],paylink_data["pwd"],paylink_data["id_company"], paylink_data["date"], paylink_data["id_branch"],paylink_data["reference"]))
        aes_encryptor = AES128Encryption()
        ciphertext = aes_encryptor.encrypt(params_template, AES_KEY).decode()

        paylink_data["ciphertext"] = ciphertext
        payload = build_template("pages/cobrador/gateways/santander_paylinks/recovery_token.xml", paylink_data)
        response = requests.post(url_to_post, headers=headers, data=payload)

        if response.status_code == 200 and response.text:
            response = response.text
            response_token = xee.fromstring(response)
            namespaces = {
                'soap': 'http://schemas.xmlsoap.org/soap/envelope/',
                'ns1': 'http://wstrans.cpagos'
            }
            encrypted_token = response_token.find('.//ns1:out',namespaces).text
            decrypted_token = aes_encryptor.decrypt(AES_KEY, encrypted_token)
            logging.info("[SANTANDER PAYLINKS] Recovered token recieved: %s", decrypted_token)
            mocked_token = xee.fromstring('''<root>%s</root>''' % decrypted_token)

            token = mocked_token.find(".//token").text

            return token

        return ""

def encrypt_aes128(xml_string, key):
    key = key.encode('utf-8')
    iv = get_random_bytes(16)
    cipher = AES.new(key, AES.MODE_CBC, iv)
    padded_data = pad(xml_string.encode('utf-8'), AES.block_size)
    ciphertext = cipher.encrypt(padded_data)
    encrypted_data = b64encode(iv + ciphertext).decode('utf-8')
    return encrypted_data

class AES128Encryption:
    """ Clase que contiene metodos para el cifrado y descifrado de informacion con AES """

    pkcs5_padding = lambda x, y: (x + (y - len(x) % y) * chr(y - len(x) % y)).encode("utf-8")
    pkcs5_unpadding = lambda x: x[:-ord(x[-1])]

    def AES128Encryption(self):
        # logging.info("Initializing AES-128 CBC Encryptor")
        self.BLOCK_SIZE = 128

    """ Generates a String containing a concatenation of the IV and the ciphertext from encryption
        :param plaintext
        :param hex_key
    """

    def encrypt(self, plaintext: str, hex_key: str) -> bytes:
        self.validate_enc_input(hex_key, plaintext)
        raw_key = bytes.fromhex(hex_key)
        iv = os.urandom(16)
        padder = padding.PKCS7(algorithms.AES.block_size).padder()
        padded_data = padder.update(plaintext.encode("UTF-8")) + padder.finalize()
        cipher = Cipher(algorithms.AES(raw_key), modes.CBC(iv))
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        #ciphertext = encryptor.update(pad(plaintext.encode('utf-8'), AES.block_size)) + encryptor.finalize()

        return base64.b64encode(iv + ciphertext)

        #return base64.b64encode(cryptogram)

    """ Generates a String containing a plaintext obtained from the given ciphertext
        :param hex_key
        :param B64 ciphertext
    """

    def decrypt(self, hex_key: str, b64_iv_ciphertext: str) -> str:
        self.validate_dec_input(hex_key, b64_iv_ciphertext)
        byte_iv_ciphertext = base64.b64decode(b64_iv_ciphertext)
        l = len(byte_iv_ciphertext)
        print("Len: " + str(l))
        raw_iv = byte_iv_ciphertext[0:16]  # The first 32 chars of hex string that correspond to 16 bytes
        raw_ciphertext = byte_iv_ciphertext[16:l]
        raw_key = bytes.fromhex(hex_key)

        # logging.info("IV: " + raw_iv.hex())
        # logging.info("CT: " + raw_ciphertext.hex())
        # Construct a Cipher object, with the key, iv
        cipher = Cipher(algorithms.AES(raw_key), modes.CBC(raw_iv))
        decryptor = cipher.decryptor()
        plaintext = decryptor.update(raw_ciphertext) + decryptor.finalize()
        return AES128Encryption.pkcs5_unpadding(plaintext.decode("UTF-8"))

    """ Validates encryption input
    """

    def validate_enc_input(self, key: str, plaintext: str):
        key_error = False
        if (len(key) == 32):
            try:
                bytes.fromhex(key)
            except:
                key_error = True
        else:
            key_error = True
        if key_error:
            raise Exception(
                "ENCRYPTION ERROR: Key Must be a 32 chars even len Hex String, Example: 1460C8BD91DB352E78604983F82CDA3A")
        if (len(plaintext) < 1):
            raise Exception("ENCRYPTION ERROR: Plaintext Must not be Empty")

    """ Validates decryption input
    """

    def validate_dec_input(self, key: str, ciphertext: str):
        key_error = False
        ciphertext_error = False
        if (len(key) == 32):
            try:
                bytes.fromhex(key)
            except:
                key_error = True
        else:
            key_error = True
        if key_error:
            raise Exception(
                "DECRYPTION ERROR: Key Must be a 32 chars len even Hex String, Example: 1460C8BD91DB352E78604983F82CDA3A")
        # Data representation Validation
        try:
            base64.b64decode(ciphertext)
        except:
            ciphertext_error = True
        if ciphertext_error:
            raise Exception(
                "DECRYPTION ERROR: Ciphertext must be a base 64 String. Remember, it contains IV + ciphertext")


