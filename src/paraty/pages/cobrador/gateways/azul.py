import hashlib
import hmac
import json

from flask import request

from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, \
	audit_response
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.language_utils import get_language_code
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.session.session_utils import get_session_from_hotel


def tpv_format_to_float(amount: str) -> float:
	if len(amount) > 2:
		euros = amount[:-2]
		cents = amount[-2:]

		result_str = "%s.%s" % (euros, cents)
	else:
		result_str = "0.%s" % amount

	return float(result_str)


def get_integration_config_by_status(integration_config):
	status = integration_config.get("status", "")

	if not integration_config.get("url_post"):
		integration_config["url_post"] = "https://pagos.azul.com.do/PaymentPage/Default.aspx"
	if not integration_config.get("url_post_alternative"):
		integration_config["url_post"] = "https://pagos.azul.com.do/PaymentPage/Default.aspx"

	if status == "TEST":
		test_configs = {x.replace("TEST_", ""): integration_config[x] for x in integration_config if "TEST_" in x}
		integration_config.update(test_configs)

		if not integration_config.get("url_post"):
			integration_config["url_post"] = "https://pruebas.azul.com.do/paymentpage/Default.aspx"
		if not integration_config.get("url_post_alternative"):
			integration_config["url_post"] = "https://pruebas.azul.com.do/paymentpage/Default.aspx"

	return integration_config

def calculate_sign(context, only_tokenizer):
	sign = ""
	if only_tokenizer:
		sign += context.get("merchant_name")
		sign += context.get("merchant_type")
		sign += "CREATE"
		sign += context.get("approved_url")
		sign += context.get("declined_url")
		sign += context.get("cancel_url")
		sign += context.get("use_custom1")
		sign += context.get("custom1_label")
		sign += context.get("custom1_value")
		sign += context.get("use_custom2")
		sign += context.get("custom2_label")
		sign += context.get("custom2_value")
		sign += context.get("auth_key")
	else:
		sign += context.get("merchant_id")
		sign += context.get("merchant_name")
		sign += context.get("merchant_type")
		sign += context.get("currency_code")
		sign += context.get("order_number")
		sign += context.get("amount")
		sign += context.get("itbis")
		sign += context.get("approved_url")
		sign += context.get("declined_url")
		sign += context.get("cancel_url")
		sign += context.get("use_custom1")
		sign += context.get("custom1_label")
		sign += context.get("custom1_value")
		sign += context.get("use_custom2")
		sign += context.get("custom2_label")
		sign += context.get("custom2_value")
		sign += context.get("auth_key")

	return hmac.new(bytearray(context.get("auth_key"), 'utf-8'), bytearray(sign, 'utf-8'), hashlib.sha512).hexdigest()


def calculate_auth_hash_response(integration_config):
	auth_string = ""
	auth_string += str(request.args.get("OrderNumber", ""))
	auth_string += str(request.args.get("Amount"))
	auth_string += str(request.args.get("AuthorizationCode", ""))
	auth_string += str(request.args.get("DateTime"))
	auth_string += str(request.args.get("ResponseCode") or "")
	auth_string += str(request.args.get("IsoCode") or "")
	auth_string += str(request.args.get("ResponseMessage") or "")
	auth_string += str(request.args.get("ErrorDescription") or "")
	auth_string += str(request.args.get("RRN") or "")
	auth_string += str(integration_config.get("auth_key"))

	logging.info("Validation hash payload: %s" % auth_string)



	result = hmac.new(bytearray(integration_config.get("auth_key"), 'ascii'), bytearray(auth_string, 'utf-16-le'), hashlib.sha512).hexdigest()

	logging.info("Validation hash: %s" % result)

	return result



class AzulFormController:
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		integration = get_integration_config_by_status(integration)

		logging.info("[AZUL] Creating Gateway Form")

		currency = "eur"
		if integration.get("currency"):
			currency = integration.get("currency")
		elif extra_data.get("currency"):
			currency = extra_data.get("currency")
		currency = currency.upper()

		only_tokenizer = False

		if amount == 0:
			only_tokenizer = True

		approved_url = integration.get("approved_url")
		approved_url += "&reservation=%s&sid=%s" % (payment_order_id, sid)
		if "?" not in approved_url:
			approved_url = approved_url.replace("&", "?", 1)

		cancel_url = integration.get("cancel_url")
		cancel_url += "&sid=%s" % (sid)
		if "?" not in cancel_url:
			cancel_url = cancel_url.replace("&", "?", 1)

		declined_url = integration.get("declined_url")
		declined_url += "&errorCode=PAYMENT&sid=%s" % (sid)
		if "?" not in declined_url:
			declined_url = declined_url.replace("&", "?", 1)

		context = {
			"merchant_id": integration.get("merchant_id", ""),
			"merchant_name": integration.get("merchant_name", ""),
			"merchant_type": integration.get("merchant_type", ""),
			"currency_code": integration.get("currency_code", "$"),
			"auth_key": integration.get("auth_key"),
			"order_number": payment_order_id,
			"amount": gateways_format_price(amount),
			"itbis": "000",
			"approved_url": approved_url,
			"declined_url": declined_url,
			"cancel_url": cancel_url,
			"auth_hash": "",
			"use_custom1": "0",
			"custom1_label": "",
			"custom1_value": "",
			"use_custom2": "0",
			"custom2_label": "",
			"custom2_value":"",
			"datavaulttoken": integration.get("data_vault_token"),
			"only_tokenizer": only_tokenizer,
			"url_post": integration.get("url_post"),
			"url_post_alternative": integration.get("url_post_alternative")
		}

		context["auth_hash"] = calculate_sign(context, only_tokenizer)

		if only_tokenizer:
			context['url_post'] += "?Datavault=1"
			context['url_post_alternative'] += "?Datavault=1"

		audit_response(hotel_code, gateway_type, payment_order_id, sid, "", type_audit="Build form",payload=json.dumps(context))

		language_locale = "EN"
		if extra_data.get('language'):
			language_session = get_language_code(extra_data.get('language')).upper()
			if language_session and language_session in ["ES", "EN"]:
				language_locale = language_session
		context['language_locale'] = language_locale

		return build_template("pages/cobrador/gateways/azul_insite_form.html", context, False)

	def process_gateway_response(self, hotel_code, gateway_type, response):

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		integration = get_integration_config_by_status(integration)

		sid = request.args.get('sid')
		reservation = request.args.get('reservation')
		isocode = request.args.get('IsoCode')
		amount = request.args.get('Amount')
		error = request.args.get('ErrorDescription')
		datavault_token = request.args.get('DataVaultToken')
		datavault_expiration = request.args.get('DataVaultExpiration')
		datavault_brand = request.args.get('DataVaultBrand')
		auth_hash = request.args.get('AuthHash')

		logging.info("Request args")

		audit_response(hotel_code, gateway_type, reservation, sid, json.dumps(request.args), payload="")

		for k, v in request.args.items():
			logging.info("%s: %s" % (k, v))

		if error:
			logging.warning("[%s][%s] Error: %s" % (hotel_code, reservation, error))

		validation_hash = calculate_auth_hash_response(integration)

		if auth_hash == validation_hash:

			if isocode == "00":
				amount_to_return = tpv_format_to_float(amount)

				session = get_session_from_hotel(hotel_code, sid)
				if session:
					gateway_currency_conversion = session.get("GATEWAY_CURRENCY_CONVERSION")

					if gateway_currency_conversion:
						exchange_rate = gateway_currency_conversion.get("exchange_rate") or 1
						amount_to_return = round(amount_to_return * float(exchange_rate), 2)


				return {
					"CODE": "OK",
					"GATEWAY_ORDER_ID": reservation,
					"GATEWAY_PAID_AMOUNT": amount_to_return,
					"GATEWAY_EXTRA_INFO": {
						"card_token": datavault_token,
						"payment_gateway": "AZUL",
						"azul_credencials": {
							"token_card": datavault_token,
							"brand_card": datavault_brand,
							"expiration_token": datavault_expiration
						}
					}
				}

		return {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"GATEWAY_ORDER_ID": reservation,
			"GATEWAY_PAID_AMOUNT": amount,
			"GATEWAY_EXTRA_INFO": {}
		}

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


