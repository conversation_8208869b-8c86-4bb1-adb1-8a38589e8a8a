import json
import uuid
from base64 import b64encode, b64decode
from collections import defaultdict
from random import randint

import requests
from flask import render_template, request
# from threading import Lock
from flask_cors import cross_origin

from paraty import app
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, get_integration_name, audit_response
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.utilities.languages import language_utils
from paraty.utilities.languages.language_utils import SPANISH, get_language_code
from paraty_commons_3.common_data.common_data_provider import get_rooms_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from Cryptodome.Cipher import AES

from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.session.session_utils import get_session_from_hotel

KEY = b")J@N1SffeXn2d4u4"

# CAPTURE_LOCKS = defaultdict(lambda: Lock())

def get_integration_config_by_status(integration_config):
	status = integration_config.get("status", "").strip().upper()
	base_configs = {x: integration_config[x] for x in integration_config if "TEST_" not in x}
	base_configs = {x: integration_config[x] for x in base_configs if "DEV_" not in x}
	if not integration_config.get("endpoint"):
		base_configs["endpoint"] = "https://api.scalapay.com"
	if status == "TEST":
		test_configs = {x.replace("TEST_", ""): integration_config[x] for x in integration_config if "TEST_" in x}
		if not integration_config.get("TEST_endpoint"):
			base_configs["endpoint"] = "https://integration.api.scalapay.com"
		base_configs.update(test_configs)

	if app.config.get("DEV"):
		test_configs = {x.replace("DEV_", ""): integration_config[x] for x in integration_config if "DEV_" in x}
		base_configs.update(test_configs)

	return base_configs



class ScalapayController(GatewayInterface):
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		return "ERROR | Este tpv no permite cobros manuales"

	def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.info("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
							 reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			extra_info["last_merchant_order_used"] = merchant_order
			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
																			  reservation, hotel_code=hotel_code)
			logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
						 reservation_id)

			return reservation_updated_id

	def get_initial_order_id_from_extra_info(self, reservation_extra_info):
		return reservation_extra_info.get("paymentOrderId")

	def get_next_scalapay_order(self, hotel_code, reservation):

		last_merchant_order_used = ""
		if reservation.get("extraInfo"):
			extra_info = json.loads(reservation.get("extraInfo"))
			last_merchant_order_used = extra_info.get("last_merchant_order_used")
			if not last_merchant_order_used and self.get_initial_order_id_from_extra_info(extra_info):
				last_merchant_order_used = self.get_initial_order_id_from_extra_info(extra_info)
				try:
					int(last_merchant_order_used)
				except:
					last_merchant_order_used = ""

		if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
			last_merchant_order_used = last_merchant_order_used[0:8]
			numerical_order = int(last_merchant_order_used)
			numerical_order += 1

			merchant_order = str(numerical_order)

			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
		else:

			merchant_order = reservation.get("identifier")
			# maybe this reservation was done before gateway was configured. So it could be an alphanumeric
			if not merchant_order.isnumeric():
				merchant_order = randint(10000000, 99999000)

			numerical_order = int(merchant_order)
			numerical_order += 1
			merchant_order = str(numerical_order)
			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

		# IMPORTANT: Even redsys return us an erro, they save ALWAYS the order.
		# so we have to save it in reservation for not received a duplicate order error in next payment!
		self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)

		return merchant_order


	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund="", payment=None):
		integration = self.get_configuration(hotel_code)

		try:
			extra_info = json.loads(reservation.get("extraInfo"))
		except:
			extra_info = {}

		currency = extra_info.get("currency")

		gateway_data = extra_info.get("gateway_data", {})
		order_id = gateway_data.get("scalapay_order_id")

		refund_id = self.get_next_scalapay_order(hotel_code, reservation)

		payload = {
			"refundAmount": {
				"amount": str(amount),
				"currency": currency
			},
			"merchantRefundReference": refund_id
		}

		headers = {
			"Authorization": "Bearer %s" % integration.get("api_key"),
			"accept": "application/json",
			"content-type": "application/json"
		}

		url_to_post = "%s/v2/payments/%s/refund" % (integration.get("endpoint"), order_id)

		refund_response = requests.post(url_to_post, json=payload, headers=headers)
		logging.info("[%s][SCALAPAY][%s] Refund response: %s" % (hotel_code, reservation.get("identifier"), refund_response.text))

		audit_response(hotel_code, "SCALAPAY", refund_id, "", refund_response.text, payload=json.dumps(payload), type_audit="Refund")

		if refund_response.status_code == 200:
			return refund_id

		return "ERROR"

	def reservation_has_token(self, reservation):
		try:
			extra_info = json.loads(reservation.get("extraInfo"))
		except:
			extra_info = {}

		gateway_data = extra_info.get("gateway_data", {})

		if gateway_data.get("gateway_name") == "scalapay" and gateway_data.get("scalapay_order_id"):
			return True

		return False

	def gateway_has_token(self):
		return True

	def translate_error(self, error):
		return error

	def get_configuration(self, hotel_code):
		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)
		gateway_configuration = get_integration_config_by_status(gateway_configuration)

		return gateway_configuration





class ScalapayFormController(FormGatewayInterface):
	def build_form_html(self, hotel_code: str, gateway_type: str, payment_order_id: str, amount, sid: str, add_button: str, extra_data: dict) -> str:

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		integration = get_integration_config_by_status(integration)
		hotel = get_hotel_by_application_id(hotel_code)

		language = extra_data.get("language", SPANISH)
		translations = language_utils.get_web_dictionary(language)

		currency = "EUR"
		if integration.get("currency"):
			currency = integration.get("currency")
		elif extra_data.get("currency"):
			currency = extra_data.get("currency")
		currency = currency.lower()

		amounts = ["3:pay-in-3"]
		if integration.get("amounts"):
			amounts = integration.get("amounts").split(";")

		payment_options = build_payment_options(amounts, translations)

		payment_info = {
			"hotel_code": hotel_code,
			"gateway_type": gateway_type,
			"sid": sid,
			"payment_order_id": payment_order_id,
			"amount": amount,
			"currency": currency,
			"merchant_url": integration.get("merchant_url"),
			"url_ko": integration.get("url_ko"),
			"api_key": integration.get("api_key"),
			"endpoint": integration.get("endpoint"),
			"valid_payment_options": payment_options,
			"language": language,
			"start_date": extra_data.get("start_date"),
			"end_date": extra_data.get("end_date")
		}

		payment_info_string = json.dumps(payment_info)
		encryptor = AES.new(KEY, AES.MODE_EAX)
		payload = encryptor.encrypt(payment_info_string.encode("utf-8"))
		payload = b64encode(encryptor.nonce + payload).decode("utf-8")

		context = {
			"payload": payload,
			"locale": get_language_code(language),
			"amount": amount,
			"currency": currency,
			"payment_options": payment_options,
			"multiple_options": len(payment_options) > 1,
		}

		return render_template("pages/cobrador/gateways/scalapay_insite_form.html", **context)

	def process_gateway_response(self, hotel_code, gateway_type, response):
		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		integration = get_integration_config_by_status(integration)
		hotel = get_hotel_by_application_id(hotel_code)

		order_id = request.args.get("orderToken")
		payment_order_id = request.args.get("reservation")
		sid = request.args.get("sid")

		response = {
			"CODE": "KO",
			"GATEWAY_ORDER_ID": payment_order_id,
			"PAYMENT_GATEWAY_NAME": "Scalapay",
			"GATEWAY_EXTRA_INFO": {
				"gateway_data": {
					"gateway_name": "scalapay",
					"scalapay_order_id": order_id
				}
			}
		}

		headers = {
			"Authorization": "Bearer %s" % integration.get("api_key"),
			"accept": "application/json"
		}

		url_to_post = "%s/v2/payments/%s" % (integration.get("endpoint"), order_id)

		scalapay_response = requests.get(url_to_post, headers=headers)
		logging.info("[%s][SCALAPAY][%s] Payment retrive response: %s" % (hotel_code, payment_order_id, scalapay_response.text))

		if scalapay_response.status_code == 200:
			scalapay_response_json = scalapay_response.json()
			status = scalapay_response_json.get("status")
			capture_status = scalapay_response_json.get("captureStatus")

			if status == "authorized" and capture_status == "pending":
				capture_payload = {
					"token": order_id
				}

				headers["content-type"] = "application/json"

				url_to_post = "%s/v2/payments/capture" % integration.get("endpoint")

				capture_response = requests.post(url_to_post, json=capture_payload, headers=headers)
				logging.info("[%s][SCALAPAY][%s] Payment capture response: %s" % (hotel_code, payment_order_id, capture_response.text))

				audit_response(hotel_code, "EVO", payment_order_id, sid, capture_response.text, payload=json.dumps(capture_payload), type_audit="Process payment")

				if capture_response.status_code == 200:
					capture_response_json = capture_response.json()
					capture_status = capture_response_json.get("status")
					if capture_status == "APPROVED":
						response.update({
							"CODE": "OK",
							"GATEWAY_PAID_AMOUNT": capture_response_json.get("totalAmount", {}).get("amount", 0),
						})

			elif status in ["authorized", "charged"] and capture_status == "captured":
				response.update({
					"CODE": "OK",
					"GATEWAY_PAID_AMOUNT": scalapay_response_json.get("totalAmount", {}).get("amount", 0),
				})

		return response


@app.route("/scalapay/checkout", methods=["POST"])
@cross_origin(origin='*')
def create_scalapay_checkout():

	if not request.is_json:
		return {
			"error": "Request error"
		}, 400

	request_json = request.json

	personal_details = request_json.get("personal_details") or {}

	payload = request_json.get("payload")
	scalapay_option = request_json.get("scalapay_option")
	payload = b64decode(payload)

	nounce = payload[:16]
	payload = payload[16:]

	encryptor = AES.new(KEY, AES.MODE_EAX, nonce=nounce)

	payload = encryptor.decrypt(payload)

	payment_data = json.loads(payload)

	hotel_code = payment_data.get("hotel_code")
	gateway_type = payment_data.get("gateway_type")
	sid = payment_data.get("sid")
	payment_order_id = payment_data.get("payment_order_id")
	amount = payment_data.get("amount")
	currency = payment_data.get("currency")
	merchant_url = payment_data.get("merchant_url")
	url_ko = payment_data.get("url_ko")
	valid_payment_options = payment_data.get("valid_payment_options") or []

	valid_payment_options = {i.get("id"): i for i in valid_payment_options}

	if scalapay_option not in valid_payment_options:
		return {
				   "error": "Request error"
			   }, 400
	else:
		product = valid_payment_options.get(scalapay_option, {}).get("product_id", "")

	merchant_url = "%s&%s=%s" % (merchant_url, "sid", sid)
	merchant_url = "%s&%s=%s" % (merchant_url, "reservation", payment_order_id)
	merchant_url = "%s&%s=%s" % (merchant_url, "namespace", hotel_code)
	if "?" not in merchant_url:
		merchant_url = merchant_url.replace("&", "?", 1)

	url_ko = "%s&%s=%s" % (url_ko, "sid", sid)
	if "namespace" not in url_ko:
		url_ko = "%s&%s=%s" % (url_ko, "namespace", hotel_code)
	if "?" not in url_ko:
		url_ko = url_ko.replace("&", "?", 1)

	scalapay_payload = {
		"totalAmount": {
			"currency": currency,
			"amount": amount
		},
		"consumer": {
			"givenNames": personal_details.get("name"),
			"surname": personal_details.get("surname"),
			"email": personal_details.get("email"),
			"phoneNumber": personal_details.get("phone")
		},
		"merchantReference": str(payment_order_id),
		"items": get_reservation_items(hotel_code, sid, payment_data.get("language"), currency),
		"merchant": {
			"redirectCancelUrl": url_ko,
			"redirectConfirmUrl": merchant_url
		},
		"type": "online",
		"product": product,
		"frequency": {
			"number": 1,
			"frequencyType": "monthly"
		},
		"extensions": {
			"industry": {
				"travel": {
					"startDate": payment_data.get("start_date"),
					"endDate": payment_data.get("end_date")
				}
			}
		},
		"orderExpiryMilliseconds": 1000 * 60 * 60
	}

	if product == "pay-now-checkout":
		scalapay_payload["frequency"] = {
			"number": 0,
			"frequencyType": "daily"
		}

	headers = {
		"Authorization": "Bearer %s" % payment_data.get("api_key"),
		"accept": "application/json",
		"content-type": "application/json"
	}

	url_to_post = "%s/v2/orders" % payment_data.get("endpoint")

	scalapay_response = requests.post(url_to_post, json=scalapay_payload, headers=headers)

	logging.info("[%s][SCALAPAY][%s] Payment creation response: %s" % (hotel_code, payment_order_id, scalapay_response.text))
	audit_response(hotel_code, "SCALAPAY", payment_order_id, sid, scalapay_response.text, payload=json.dumps(scalapay_payload), type_audit="Form Payment")

	if scalapay_response.status_code != 200:
		return {
			"error": "Request error"
		}, 400

	response_json = scalapay_response.json()

	return {
		"checkout_url": response_json.get("checkoutUrl")
	}, 200

def build_payment_options(amounts, translations):
	payment_options = []
	for option_amount in amounts:

		amount, product_id = option_amount.split(":")

		t_payment_msi_t_a = translations.get("T_payment_msi_option_a")
		t_payment_msi_t_b = translations.get("T_payment_msi_option_b")

		label = "%s %s %s" % (t_payment_msi_t_a, amount, t_payment_msi_t_b)

		payment_options.append({
			"type": "msi",
			"amount": amount,
			"product_id": product_id,
			"label": label,
			"id": str(uuid.uuid4())
		})

	return payment_options


def get_reservation_items(hotel_code, sid, language, currency):

	hotel = get_hotel_by_application_id(hotel_code)
	all_rooms = get_rooms_of_hotel(hotel, language, include_removed=True)
	all_rooms = {i["key"]: i for i in all_rooms}

	session_content = get_session_from_hotel(hotel_code, sid)

	rooms_combinations = session_content.get("selectedOption_").split(";")
	rooms_count = defaultdict(lambda: 0)
	for room_combination in rooms_combinations:
		rooms_count[room_combination] += 1

	results = []
	for room_combination in list(set(rooms_combinations)):
		price_option = session_content.get("price_option_%s" % room_combination)
		rate_key = price_option[0]
		room_key = price_option[1]
		room_data = all_rooms.get(room_key)

		item = {
			"quantity": rooms_count[room_combination],
			"price": {
				"amount": price_option[3],
				"currency": currency
			},
			"category": "Rooms",
			"name": room_data.get("name"),
			"sku": "%s-%s" % (rate_key, room_key)
		}

		if room_data.get("pictures"):
			item["imageUrl"] = room_data["pictures"][0]

		results.append(item)

	return results