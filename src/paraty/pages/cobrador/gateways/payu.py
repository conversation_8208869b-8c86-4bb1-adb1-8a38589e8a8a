import hashlib
import json
import urllib

from flask import request
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, audit_response
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.logging.my_gae_logging import logging
from urllib.parse import unquote
STATUS_TEST = "TEST"
APPROVED = "4"
DECLINED = "6"
EXPIRED = "5"
ERROR = "104"
PENDING = "7"
def get_integration_config_by_status(integration_config):
    status = integration_config.get("status", "")
    if not integration_config.get("url_post"):
        integration_config["url_post"] = "https://sandbox.checkout.payulatam.com/ppp-web-gateway-payu"
    if status == "TEST":
        test_configs = {x.replace("TEST_", ""): integration_config[x] for x in integration_config if "TEST_" in x}
        integration_config.update(test_configs)
        if not integration_config.get("url_post"):
            integration_config["url_post"] = "https://sandbox.checkout.payulatam.com/ppp-web-gateway-payu"
    return integration_config
def generate_signature(integration, amount, payment_order_id, currency,transaction_id=None):
    signature = "%s~%s~%s~%s~%s" % (integration.get("api_key"), integration.get("merchant_id"), payment_order_id, amount, currency)
    if transaction_id:
        transaction_id = float(transaction_id)
        signature = "%s~%s" % (signature, transaction_id)
    result = hashlib.md5(signature.encode())
    result = result.hexdigest()
    logging.info("keys signature: %s", signature)
    logging.info("signature: %s", result)
    return result



class PayuFormController:
    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        logging.info("[PayU] Creating Gateway Form")
        integration = get_payment_gateway_configuration(gateway_type, hotel_code)
        integration = get_integration_config_by_status(integration)
        logging.info("Price: %s" % str(amount))
        price_before_tax = 0
        tax = 0

        if extra_data.get('tax'):
            tax = extra_data.get('price_tax')
            price_before_tax = extra_data.get('price_before_tax')

        amount = "%.2f" % round(float(amount), 2)
        currency = extra_data.get('currency', '')
        signature = generate_signature(integration, amount, payment_order_id, currency)
        test = 0
        if integration.get("status") == "TEST":
            test = 1
        context = {
            "merchant_id": integration.get("merchant_id", ""),
            "reference_code": payment_order_id,
            "description": integration.get("description", ""),
            "amount": amount,
            "tax": ("%.2f" % float(tax)),
            "tax_return_base":("%.2f" % float(price_before_tax)),
            "signature": signature,
            "account_id": integration.get("account_id"),
            "currency": extra_data.get('currency', ''),
            "buyer_full_name": "",
            "buyer_email": "",
            "url_checkout": integration.get("url_post"),
            "test": test,
            "extra1": sid,
            # "sourceUrl": f"{integration.get('url_ko')}?sid={sid}&namespace={hotel_code}",
            "confirmationUrl": f"{integration.get('merchant_url')}?sid={sid}&namespace={hotel_code}",
            "responseUrl": f"{integration.get('response_url')}?sid={sid}&namespace={hotel_code}"
        }
        logging.info("[PAYU] context: %s", context)
        return build_template("pages/cobrador/gateways/payu_insite_form.html", context, False)

    def process_gateway_response(self, hotel_code, gateway_type, response):
        logging.info("[PAYU] Processing response request...")
        logging.info("Request Content:")
        integration = get_payment_gateway_configuration(gateway_type, hotel_code)
        integration = get_integration_config_by_status(integration)
        if not isinstance(response, dict):
            response = urllib.parse.unquote(response)
            response_dic = dict(urllib.parse.parse_qsl(response))
        else:
            response_dic = response
        if response_dic:

            reservation = response_dic.get('reference_sale')

            try:

                sid = request.args.get("sid")
                if not sid:
                    sid = "not received correctly"
                gateway_type = "PAYU"
                audit_response(hotel_code, gateway_type, reservation, sid, json.dumps(response_dic))

            except Exception as e:
                logging.error("Error auditing response for in PAYU in process response")
                logging.error("Error auditing: %s", e)


            currency = response_dic.get('currency')
            amount = response_dic.get('value', 0)
            amount = float(amount)
            signature = response_dic.get('sign')
            transaction_state = response_dic.get('state_pol')

            message = response_dic.get('response_message_pol')
            reference_pol = response_dic.get("response_message_pol")
            transaction_id = response_dic.get("transaction_id")
            cus = response_dic.get("cus")
            pol_payment_method = response_dic.get("payment_method_name")
            lap_payment_method = response_dic.get("payment_method_id")
            pol_payment_method_type = response_dic.get("transaction_bank_id")
            lap_payment_method_type = response_dic.get("cardType")
            tax = response_dic.get("tax")
            status = response_dic.get("state_pol")
            signature2 = generate_signature(integration, amount, reservation, currency, transaction_state)
            if signature!=signature2:
                logging.info("Validations response KO disting signature")
            else:
                logging.info("Validations response OK disting signature")
            #     return {
            #         "CODE": GATEWAY_ERROR_CODE_RETURNED,
            #         "GATEWAY_ORDER_ID": reservation,
            #         "GATEWAY_PAID_AMOUNT": amount,
            #         "GATEWAY_EXTRA_INFO": {}
            #     }
        else:
            reservation = request.args.get("referenceCode")
            currency =  request.args.get('currency')
            amount =  request.args.get('TX_VALUE')
            amount = float(amount)
            signature = request.args.get('signature')
            transaction_state = request.args.get('polTransactionState')

            message = request.args.get('message')
            reference_pol = request.args.get("reference_pol")
            transaction_id = request.args.get("transactionId")
            cus = request.args.get("cus")
            pol_payment_method = request.args.get("lapPaymentMethod")
            lap_payment_method = request.args.get("polPaymentMethod")
            pol_payment_method_type = request.args.get("transaction_bank_id")
            lap_payment_method_type = request.args.get("lapPaymentMethodType")
            tax = request.args.get("TX_TAX")
            status = request.args.get("transactionState")
            signature2 = generate_signature(integration, amount, reservation, currency, transaction_state)
            if signature != signature2:
                logging.info("Validations response KO disting signature")
            else:
                logging.info("Validations response OK disting signature")
                # return {
                #     "CODE": GATEWAY_ERROR_CODE_RETURNED,
                #     "GATEWAY_ORDER_ID": reservation,
                #     "GATEWAY_PAID_AMOUNT": amount,
                #     "GATEWAY_EXTRA_INFO": {}
                # }
        logging.info("Request args")
        for k, v in request.args.items():
            logging.info("%s: %s" % (k, v))
        if message:
            logging.warning("[%s][%s] Error: %s" % (hotel_code, reservation, message))
        if status == APPROVED:
            return {
                "CODE": "OK",
                "GATEWAY_ORDER_ID": reservation,
                "GATEWAY_PAID_AMOUNT": amount,
                "GATEWAY_EXTRA_INFO": {
                    "payment_gateway": "PAYU",
                    "payu_info": {
                        "tax_value": amount,
                        "tax": tax,
                        "lap_payment_method_type": lap_payment_method_type,
                        "transaction_state":transaction_state,
                        "reference_pol":reference_pol,
                        "transaction_id":transaction_id,
                        "cus":cus,
                        "pol_payment_method":pol_payment_method,
                        "lap_payment_method": lap_payment_method,
                        "pol_payment_method_type":pol_payment_method_type
                    }
                }
            }
        return {
            "CODE": GATEWAY_ERROR_CODE_RETURNED,
            "GATEWAY_ORDER_ID": reservation,
            "GATEWAY_PAID_AMOUNT": amount,
            "GATEWAY_EXTRA_INFO": {}
        }

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT
