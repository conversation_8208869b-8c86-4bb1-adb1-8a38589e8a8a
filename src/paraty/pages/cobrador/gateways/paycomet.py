import logging
import requests
import hashlib
import json
import datetime

from paraty import app
from flask_cors import cross_origin
from flask import redirect
from flask import request as r
from random import randint
from urllib.parse import parse_qs

from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.pages.cobrador.cobrador_utils import get_config_property_value, get_payment_gateway_configuration, \
	gateways_format_price, _get_project_id_and_namespace, add_parameters_and_namespace_to_url, get_amount_to_pay, \
	audit_response
from paraty.pages.cobrador.cobrador_constants import CUSTOM_DOMAIN, GATEWAY_SUCESS_RETURNED, GATEWAY_ERROR_RETURNED, PAYCOMET, CONTENT_TYPE_TEXT
from paraty.utilities.languages import language_utils
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty_commons_3.common_data.common_data_provider import get_web_section
from paraty.utilities.languages.language_utils import build_custom_text_b3_by_key

from paraty.utilities.templates.templates_processor import build_template

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


OPERATION_TYPE_PAYMENT = 1
OPERATION_TYPE_TOKEN = 107

class PaycometController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		logging.info("[PAYCOMET] Creating Gateway Form")
		config = get_payment_gateway_configuration(gateway_type, hotel_code)
		if not config:
			logging.error("[PAYCOMET] No integration configuration found")
		logging.info(str(config))

		logging.info("Amount: %s" % str(amount))

		translations = language_utils.get_web_dictionary(extra_data.get("language", "SPANISH"))
		hotel = get_hotel_by_application_id(hotel_code)
		section_name = get_configuration_property_value(hotel_code, "b3 custom text from pep by keys")
		custom_text_by_key = {}
		custom_amounts_by_key = {}
		try:
			if section_name:
				logging.info("[PAYCOMET] assembling the texts for the booking3 from section %s", section_name)
				translations_section = get_web_section(hotel, section_name, extra_data.get("language", "SPANISH"),
													   set_languages=True)
				reservation_amount = amount
				amount_to_pay = str(get_amount_to_pay(amount, prices_per_day=extra_data.get("prices_per_day")))
				custom_text_by_key, custom_amounts_by_key = build_custom_text_b3_by_key(translations,
																						translations_section,
																						extra_data, reservation_amount,
																						amount_to_pay, hotel)
		except Exception as e:
			logging.warning(e)
			logging.info("[PAYCOMET] Error building custom texts by key")

		context = {
			'url_payment': config['url_payment'],
			'sid': sid,
			'gateway_type': gateway_type,
			'price_paycomet': amount,
			'currency_paycomet': extra_data.get('currency', ''),
			'bookingid_paycomet': payment_order_id,
			'hotel_code': hotel_code
		}

		if custom_text_by_key:
			context["custom_text_by_key"] = custom_text_by_key
		if custom_amounts_by_key:
			context["custom_amounts_by_key"] = custom_amounts_by_key

		template_path = "pages/cobrador/gateways/paycomet_form.html"

		return build_template(template_path, context)

	def process_gateway_response(self, hotel_code, gateway_type, response):
		logging.info("[PAYCOMET] Processing merchant request")
		logging.info("Request Content: %s", 1)
		logging.info("Post Response: %s", response)

		if not response:
			logging.error("[PAYCOMET] No response found, potentially error of PAYCOMET. Check the response received from the gateway in hotel webs.")
			cobrador_response = {
				'PAYMENT_GATEWAY_NAME': PAYCOMET,
				'no_redirect': True,
				'CODE': GATEWAY_ERROR_RETURNED
			}
			return cobrador_response
		if not isinstance(response, dict):
			response = parse_qs(response)
			data = {}
			for x, y in response.items():
				data[x] = y[0] if len(y) > 0 else ''
		else:
			data = response

		logging.info("Formated Post Response: %s", response)

		identifier = data.get('Order', '')

		try:
			sid = r.values.get('sid', '')
			audit_response(hotel_code, "PAYCOMET", identifier, sid, json.dumps(data))

		except Exception as e:
			logging.warning("error creating payment audit")

		cobrador_response = {
			'GATEWAY_ORDER_ID': identifier,
			'PAYMENT_GATEWAY_NAME': PAYCOMET,
			'no_redirect': True,
			'GATEWAY_EXTRA_INFO': {}
		}

		response_pay = data.get('Response', '')
		error_code = response.get("ErrorID", '')
		logging.info("Response Param Received from Paycomet: %s", response_pay)
		logging.info("ErrorID Param Received from Paycomet: %s", error_code)
		logging.info("Order Param Received from Paycomet: %s", identifier)

		if response_pay != "OK":
			if error_code and error_code != "1196":
				logging.info("[PAYCOMET] Payment unsuccessful, check error code: %s", error_code)
				cobrador_response['CODE'] = GATEWAY_ERROR_RETURNED
				return cobrador_response
		try:
			reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
																		[("identifier", "=", identifier), ("hotel_code", "=", hotel_code)])

			if reservation_metadata:
				reservation_metadata = reservation_metadata[0]
				extra_info = json.loads(reservation_metadata.get('extraInfo', '{}'))
				iduser_paycomet = extra_info.get('iduser_paycomet')
				token_paycomet = extra_info.get('token_paycomet')

				cobrador_response['GATEWAY_PAID_AMOUNT'] = extra_info.get('GATEWAY_PAID_AMOUNT', '')
				cobrador_response['CODE'] = GATEWAY_SUCESS_RETURNED

				token_user = response.get("TokenUser", "")
				id_token_user = response.get("IdUser", "")
				unique_ref = response.get("Order", "")

				cobrador_response['GATEWAY_EXTRA_INFO']['paycomet_credencials'] = {
					'token_card': token_paycomet,
					'token_user': token_user,
					'id_token_user': id_token_user,
					'unique_ref': unique_ref,
					'customer': iduser_paycomet,
				}
				cobrador_response['GATEWAY_EXTRA_INFO']['sid'] = reservation_metadata.get('sid', '')
				return cobrador_response
			else:
				logging.error("[PAYCOMET] No reservation metadata found")
		except:
			logging.error('[PAYCOMET] Failed response: %s', response)
			cobrador_response['CODE'] = GATEWAY_ERROR_RETURNED
			return cobrador_response

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


@app.route("/paycomet/payment", methods=["GET"])
@cross_origin(origin='*')
def checkout_paycomet():
	data = r.args
	gateway_type = data.get('gateway_type')
	hotel_code = data.get('namespace')
	sid = data.get('sid')

	price = data.get('price_paycomet')
	currency = data.get('currency_paycomet')
	booking_id = data.get('bookingid_paycomet')

	logging.info("price: %s", price)
	logging.info("booking_id: %s", booking_id)
	logging.info("currency: %s", currency)

	paycomet_data = get_paycomet_data(gateway_type, hotel_code)
	_, multitenancy = _get_project_id_and_namespace(get_hotel_by_application_id(hotel_code))
	paycomet_data['url_ok'] = add_parameters_and_namespace_to_url(paycomet_data.get('url_ok'), {'sid': sid}, multitenancy)
	paycomet_data['url_ko'] = add_parameters_and_namespace_to_url(paycomet_data.get('url_ko'), {'sid': sid}, multitenancy)

	formated_price = gateways_format_price(price)
	logging.info("Post to Paycomet to get payment url... ")

	id_user = get_uuid()
	token = get_id_random()
	operation = OPERATION_TYPE_PAYMENT
	operation_type = OPERATION_TYPE_PAYMENT
	if float(price) == 0:
		operation = OPERATION_TYPE_TOKEN
		operation_type = OPERATION_TYPE_TOKEN

	paycomet_data['operation'] = operation
	paycomet_data['order'] = booking_id
	paycomet_data['amount'] = formated_price
	paycomet_data['currency'] = currency
	paycomet_data['id_user'] = id_user
	paycomet_data['token'] = token

	extra_info = {
		'iduser_paycomet': id_user,
		'token_paycomet': token,
		'price_paycomet': formated_price,
		'currency_paycomet': currency,
		'bookingid_paycomet': booking_id,
		'GATEWAY_PAID_AMOUNT': price
	}

	properties = {
		"sid": sid,
		"identifier": booking_id,
		"hotel_code": hotel_code,
		"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
		"extraInfo": json.dumps(extra_info)
	}
	datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")

	# params = {
	# 	"MERCHANT_MERCHANTCODE": paycomet_data.get('merchant_code'),
	# 	"MERCHANT_TERMINAL": paycomet_data.get('terminal'),
	# 	"OPERATION": 1,
	# 	"LANGUAGE": "es",
	# 	"MERCHANT_MERCHANTSIGNATURE": get_sign(paycomet_data),
	# 	"MERCHANT_ORDER": booking_id,
	# 	"MERCHANT_AMOUNT": price,
	# 	"MERCHANT_CURRENCY": currency
	# }
	payment = {
		'terminal': paycomet_data.get('terminal'),
		'methodId': 1,
		# 'excludedMethods': "",
		'order': booking_id,
		'amount': formated_price,
		'currency': currency,
		'originalIp': getUserIp(hotel_code, r),
		'userInteraction': 1,
		'secure': 1,
		'scoring': 0,
		'productDescription': "",
		'merchantDescriptor': "",
		'notifyDirectPayment': 1,
		'urlOk': paycomet_data.get('url_ok'),
		'urlKo': paycomet_data.get('url_ko')
	}
	payload = {
		'payment': payment,
		'operationType': operation_type,
		'language': 'es',
		'terminal': paycomet_data.get('terminal'),
		'productDescription': ''
	}

	logging.info("Payload to Paycomet: %s", payload)

	headers = {
		'Content-Type': 'application/json',
		'Accept': 'application/json',
		'PAYCOMET-API-TOKEN': paycomet_data.get('api_key')
	}

	url_post = "%s/v1/form" % paycomet_data.get('url_rest')

	logging.info("Send to Paycomet: %s", url_post)
	logging.info("Send with headers: %s", headers)

	try:
		result = requests.post(url_post, data=json.dumps(payload), headers=headers)

		logging.info("result response: %s", result.text)

		body = json.loads(result.text)
		challengeUrl = body.get("challengeUrl")

		return redirect(str(challengeUrl))

	except Exception as e:
		logging.info("Failed response from Paycomet: %s", e)
		_, multitenancy = _get_project_id_and_namespace(get_hotel_by_application_id(hotel_code))
		return redirect(str(add_parameters_and_namespace_to_url(paycomet_data.get('url_ko'), {'sid': sid, 'hotel_code': hotel_code}, multitenancy)))
	pass


def get_paycomet_data(gateway_type, hotel_code):
	integration_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
	if not integration_configuration:
		logging.error(f"[PAYCOMET] XML configuration not found for {gateway_type} in {hotel_code}")
		return {}
	logging.info(str(integration_configuration))
	return integration_configuration


def paycomet_data_for_test():
	return {
		'api_key': '729c4d59d25bbfcf88010ddab7a9e3e0544137d1',
		'url_rest': 'https://rest.paycomet.com',
		'url_payment': 'http://localhost:8090/paycomet/payment',
		'terminal': '19035',
		'url_iframe': 'https://api.paycomet.com/gateway/ifr-bankstore',
		'merchant_code': 'j5w3x5y8',
		'password': '76RKNwYUqTBXV410CLph',
		'url_watcher': 'https://test-backend-dot-integration-test-hotel.appspot.com/booking3',
		'url_ok': 'https://test-backend2-dot-integration-test-hotel.appspot.com/booking4',
		'url_ko': 'https://test-backend2-dot-integration-test-hotel.appspot.com/booking3',
		'url_merchant': 'https://test-backend2-dot-integration-test-hotel.appspot.com/paycomet/url_merchant',
		'status': 'TEST',
		'amount': '',
		'currency': ''
	}


def _paycomet_data(config, is_test=False):
	if is_test:
		prefix = 'TEST_'
	else:
		prefix = ''

	return {
		'api_key': config.get(prefix + 'api_key'),
		'url_rest': config.get(prefix + 'url_rest'),
		'url_payment': config.get(prefix + 'url_payment'),
		'terminal': config.get(prefix + 'terminal'),
		'url_iframe': config.get(prefix + 'url_iframe'),
		'merchant_code': config.get(prefix + 'merchant_code'),
		'password': config.get(prefix + 'api_key'),
		'url_watcher': config.get(prefix + 'url_watcher'),
		'url_ok': config.get(prefix + 'url_ok'),
		'url_ko': config.get(prefix + 'url_ko'),
		'url_merchant': config.get(prefix + 'url_merchant'),
		'status': 'TEST' if is_test else config.get('status'),
		'amount': '',
		'currency': ''
		}


def get_id_random():
	id = ""
	for x in range(9):
		id = id + str(randint(0, 10))

	return id


def get_uuid():
	id = ""
	for x in range(9):
		id = id + str(randint(0, 8))

	return int(id)


def get_sign(data):
	merchant_code = data.get('merchant_code', '')
	terminal = data.get('terminal', '')
	operation = data.get('operation', '')
	order = data.get('order', '')
	amount = data.get('amount', '')
	currency = data.get('currency', '')

	password = data.get('password', '')
	hashed_password = hashlib.sha512(password.encode()).hexdigest()

	sign_target = "{}{}{}{}{}{}{}".format(
		merchant_code, terminal, operation, order, amount, currency, hashed_password
	)

	logging.info("sign_target: %s", sign_target)

	result = hashlib.sha512(sign_target.encode()).hexdigest()

	return result


def getUserIp(hotel_code, request):
	'''
	Returns the IP of the current user, note that if the call is proxied (i.e. DistilNetworks) it has to be retrieved from a header
	'''

	if request.headers.get('X-Real-Ip') and get_config_property_value(hotel_code, CUSTOM_DOMAIN):
		return request.headers.get('X-Real-Ip')

	if request.headers.get('X-Forwarded-For'):
		logging.info("X-Forwarded-For: %s", request.headers.get('X-Forwarded-For'))
		logging.info("X-Real-Ip: %s", request.headers.get('X-Real-Ip'))
		result = request.headers.get('X-Forwarded-For').split(",")[0].strip()
		return result

	return request.remote_addr


def data_to_dict(data):
	import re
	data_str = data.decode('utf-8')
	key_value_pairs = data_str.split('&')

	result_dict = {}
	for pair in key_value_pairs:
		key, value = pair.split('=')
		result_dict[key] = value

	return result_dict
