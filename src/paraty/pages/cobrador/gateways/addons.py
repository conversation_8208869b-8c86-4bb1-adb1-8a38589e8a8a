import urllib
from datetime import datetime
import hashlib
import json
import logging
import random
from flask import request
from Cryptodome.Cipher import AES
from flask_cors import cross_origin
import base64

import requests

from paraty import app
from paraty.pages.cobrador.cobrador_constants import SID_FROM_COBRADOR, GATEWAY_ERROR_RETURNED, \
	SEPARATOR_INFO_ERROR_ORDER, GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, get_integration_name, \
	audit_response, gateways_format_price

from paraty.pages.cobrador.gateway_interface import GatewayInterface

from paraty.utilities.templates.templates_processor import build_template
from flask import render_template


import xml.etree.ElementTree as xee

from paraty_commons_3.common_data.common_data_provider import get_web_section
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.utils.proxy_utils import post_using_generic_proxy, post_using_generic_proxy_tls_1_2
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.session.session_utils import get_session_from_hotel

TIMESTAMP_FORMAT = "%Y%m%d%H%M%S"

SPANISH = 'SPANISH'
ENGLISH = 'ENGLISH'
GERMAN = 'GERMAN'
FRENCH = 'FRENCH'
DUTCH = 'DUTCH'
RUSSIAN = 'RUSSIAN'
PORTUGUESE = 'PORTUGUESE'
POLISH = 'POLISH'
CATALAN = 'CATALAN'
FINNISH = 'FINNISH'
ITALIAN = 'ITALIAN'
SWEDISH = 'SWEDISH'
JAPANESE = 'JAPANESE'
KOREAN = 'KOREAN'
CHINESE_TRADITIONAL = 'CHINESE_TRADITIONAL'
CHINESE_SIMPLIFIED = 'CHINESE_SIMPLIFIED'

LANGUAGE_CODES = {
	SPANISH: 'es',
	ENGLISH: 'en',
	GERMAN: 'de',
	FRENCH: 'fr',
	PORTUGUESE: 'pt',
	POLISH: 'pl',
	CATALAN: 'ca',
	RUSSIAN: 'ru',
	DUTCH: 'nl',
	FINNISH: 'fi',
	ITALIAN: 'it',
	SWEDISH: 'sv',
	JAPANESE: 'ja',
	KOREAN: 'ko',
	CHINESE_TRADITIONAL: 'zh-Hant',
	CHINESE_SIMPLIFIED: 'zh-Hans'
}

TIMESTAMP_FORMAT = "%Y%m%d%H%M%S"
SHA1HASH_FIRST = "%s.%s.%s.%s.%s.%s.%s"
SHA1HASH_FINAL = "%s.%s"

SHA1HASH_FIRST_SELECT_CARD = "%s.%s.%s.%s.%s.%s."
SHA1HASH_RECEIPT = "%s.%s.%s.%s.%s.%s"
SHA1HASH_RECEIPT_RESPONSE = "%s.%s.%s.%s.%s.%s.%s"

URL_SEND_XML_TEST = "https://remote.sandbox.addonpayments.com/remote"
URL_SEND_XML = "https://remote.addonpayments.com/remote"

MESSAGE_KEY = b")J@NcRfAjXe2r4u7"


def get_dcc_info(amount, rate_type, currency):
	return {
		"ccp": "EUROCONEX",
		"type": "1",
		"rate": "1",
		"rate_type": rate_type,
		"currency": currency,
		"amount": amount
	}


class AddonsController(GatewayInterface):

	def translate_error(self, error):
		return error

	def get_initial_order_id_from_extra_info(self, reservation_extra_info):
		return ""

	@staticmethod
	def getLanguageCode(language):
		return LANGUAGE_CODES.get(language, 'en')

	def get_currency(self):
		return "EUR"

	def get_sign_refund_hash(self, rebase_secret):
		sign = rebase_secret
		sha1hash = hashlib.sha1(sign.encode("utf-8")).hexdigest()
		return sha1hash

	def get_sign_refund_response_hash(self, timestamp, merchant_id, order_id, amount, currency, share_secret):
		sign = SHA1HASH_RECEIPT % (timestamp, merchant_id, order_id, amount, currency, "")
		sha1_hash_1 = hashlib.sha1(sign.encode("utf-8"))
		sign = SHA1HASH_FINAL % (sha1_hash_1.hexdigest(), share_secret)
		sha1hash = hashlib.sha1(sign.encode("utf-8")).hexdigest()
		return sha1hash

	def get_sign_refund(self, timestamp, merchant_id, order_id, amount, currency, share_secret):
		sign = SHA1HASH_RECEIPT % (timestamp, merchant_id, order_id, amount, currency, "")
		sha1_hash_1 = hashlib.sha1(sign.encode("utf-8"))
		sign = SHA1HASH_FINAL % (sha1_hash_1.hexdigest(), share_secret)
		sha1hash = hashlib.sha1(sign.encode("utf-8")).hexdigest()
		return sha1hash

	def get_sign(self, timestamp, merchant_id, order_id, amount, currency, pmt_ref, share_secret):
		sign = SHA1HASH_RECEIPT % (timestamp, merchant_id, order_id, amount, currency, pmt_ref)
		sha1_hash_1 = hashlib.sha1(sign.encode("utf-8"))
		sign = SHA1HASH_FINAL % (sha1_hash_1.hexdigest(), share_secret)
		sha1hash = hashlib.sha1(sign.encode("utf-8")).hexdigest()
		return sha1hash

	def get_sign_receipt(self, timestamp, merchant_id, order_id, status, message, pasref, authcode, share_secret):

		sign = SHA1HASH_RECEIPT_RESPONSE % (timestamp, merchant_id, order_id, status, message, pasref, authcode)
		sha1_hash_1 = hashlib.sha1(sign.encode("utf-8"))
		sign = SHA1HASH_FINAL % (sha1_hash_1.hexdigest(), share_secret)
		sha1hash = hashlib.sha1(sign.encode("utf-8")).hexdigest()
		return sha1hash

	def get_url_post(self):
		return "/pages/cobrador/save_payment"

	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def get_authcode_pasref(self, payment, reservation):

		if payment:
			extra_info = payment.get("extra_info", "{}")
			if extra_info:
				try:
					return extra_info.get('authcode'), extra_info.get('pasref')
				except:
					logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
					extra_info = {}

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("addon_credencials", ""):
				credentials = extra_info.get("addon_credencials", "")
				return credentials.get("authcode", ""), credentials.get("pasref", "")

		return "", ""

	def get_card_user(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("addon_credencials", ""):
				credencials = extra_info.get("addon_credencials", "")
				if credencials.get("token_payer", ""):
					return credencials.get("token_payer", "")

		return ""

	def get_token(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("addon_credencials", ""):
				credencials = extra_info.get("addon_credencials", "")
				if credencials.get("token_card", ""):
					return credencials.get("token_card", "")

		return ""

	def _get_timestamp(self):
		return datetime.now().strftime(TIMESTAMP_FORMAT)

	def get_uuid(self):
		selectedUUID = '%d' % random.randint(10000000, 99999999)
		return selectedUUID
	def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!", reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			extra_info["last_merchant_order_used"] = merchant_order
			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)
			logging.info("New payed updated for reservation %s reservation_id: %s ",  reservation.get("identifier"), reservation_id)

			return reservation_updated_id
	def get_new_order_id(self, hotel_code, reservation):
		last_merchant_order_used = ""
		if reservation.get("extraInfo"):
			extra_info = json.loads(reservation.get("extraInfo"))
			last_merchant_order_used = extra_info.get("last_merchant_order_used")

		if last_merchant_order_used:
			last_merchant_order_used = last_merchant_order_used[0:8]
			numerical_order= int(last_merchant_order_used)
			numerical_order += 1

			merchant_order = str(numerical_order)

			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
		else:

			merchant_order = reservation.get("identifier")
			#maybe this reservation was done before gateway was configured. So it could be an alphanumeric
			if not merchant_order.isnumeric():
				merchant_order = self.get_uuid()

			numerical_order= int(merchant_order)
			numerical_order += 1
			merchant_order = str(numerical_order)
			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

		self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)
		return merchant_order


	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		config = self.get_configuration(hotel_code)
		amount = ("%.2f" % float(amount)).replace('.', '')
		currency = self.get_currency()

		merchant_id = config.get("merchant_id")
		account = config.get("account")
		share_secret = config.get("share_secret")

		for _ in range(3):
			try:

				timestamp = self._get_timestamp()
				url_post = self.get_url_post()
				order_id = self.get_new_order_id(hotel_code, reservation)
				pmt_ref = self.get_card_user(reservation)

				sha1hash = self.get_sign(timestamp, merchant_id, order_id, amount, currency, pmt_ref, share_secret)

				params = {
					"timestamp": timestamp,
					"merchant_id": merchant_id,
					"account": account,
					"order_id": order_id,
					"amount": amount,
					"currency": currency,
					"sha1hash": sha1hash,
					"payer_ref": pmt_ref
				}

				if config.get("add_dcc_info"):
					rate_type = "S"
					params["dcc_info"] = get_dcc_info(amount, rate_type, currency)

				result_html = render_template("pages/cobrador/gateways/_addon_response.xml", **params)

				url_post = URL_SEND_XML
				if (config.get("status_cobrador").lower() == "test"):
					url_post = URL_SEND_XML_TEST

				response = post_using_generic_proxy_tls_1_2(url_post, result_html)
				response.raise_for_status()

				try:
					gateway_type = "addons"
					audit_response(hotel_code, gateway_type, order_id, SID_FROM_COBRADOR, response.text, payload=json.dumps(params))

				except Exception as e:
					logging.error("Error auditing response for in ADDONS")
					logging.error("Error auditing: %s", e)


				tree = xee.fromstring(response.content)

				timestamp = tree.attrib.get('timestamp')
				status = safe_get_tag_value(tree.find("result"), "")
				sha1hash = safe_get_tag_value(tree.find("sha1hash"), "")
				message = safe_get_tag_value(tree.find("message"), "")
				pasref = safe_get_tag_value(tree.find("pasref"), "")
				authcode = safe_get_tag_value(tree.find("authcode"), "")


				extra_info = {'order_id': order_id, 'authcode': authcode, 'pasref': pasref }

				valid = self.get_sign_receipt(timestamp, merchant_id, order_id, status, message, pasref, authcode, share_secret)
				if sha1hash != valid:
					logging.info("[ADDONS][%s] An error has occurred during the new payment. Maybe we have an error message from gateway: %s" %(order_id, message))
					logging.info("Sign compare is wrong: %s vs %s" % (sha1hash, valid))
					return GATEWAY_ERROR_RETURNED + ":" + status + SEPARATOR_INFO_ERROR_ORDER + order_id

				if status == "00":
					return extra_info
				elif status == "501":
					continue
				else:
					return "ERROR: " + status
			except Exception as e:
				logging.warning("Exception at get_token [ADDONS]: %s", e)
				break

		return "ERROR: Revisar si tiene la configuracion status_cobrador"


	def reservation_has_token(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("addon_credencials", ""):
				credencials = extra_info.get("addon_credencials", "")
				if credencials.get("token_card", ""):
					return True

		return False

	def gateway_has_token(self):
		return True

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):

		try:

			config = self.get_configuration(hotel_code)

			timestamp = self._get_timestamp()
			currency = self.get_currency()

			order_id = order_to_refund

			authcode, pasref = self.get_authcode_pasref(payment, reservation)

			merchant_id = config.get("merchant_id")
			account = config.get("account")
			share_secret = config.get("share_secret")
			rebase_secret = config.get("rebase_secret")
			url_post = config.get("rebase_url")

			amount = ("%.2f" % float(amount)).replace('.', '')

			sha1hash = self.get_sign_refund(timestamp, merchant_id, order_id, amount, currency, share_secret)
			sha1refundhash = self.get_sign_refund_hash(rebase_secret)

			params = {
				"timestamp": timestamp,
				"merchant_id": merchant_id,
				"account": account,
				"order_id": order_id,
				"amount": amount,
				"currency": currency,
				"sha1hash": sha1hash,
				"rebase": rebase_secret,
				"pasref": pasref,
				"authcode": authcode,
				"refundhash": sha1refundhash
			}

			if config.get("add_dcc_info"):
				rate_type = "R"
				params["dcc_info"] = get_dcc_info(amount, rate_type, currency)

			result_html = render_template("pages/cobrador/gateways/_addon_refund.xml", **params)

			url_post = URL_SEND_XML
			if (config.get("status_cobrador").lower() == "test"):
				url_post = URL_SEND_XML_TEST

			response = post_using_generic_proxy_tls_1_2(url_post, result_html)
			response.raise_for_status()

			try:
				gateway_type = "addons"
				audit_response(hotel_code, gateway_type, order_id, SID_FROM_COBRADOR, response.text, type_audit="Refund", payload=json.dumps(params))

			except Exception as e:
				logging.error("Error auditing response for in ADDONS")
				logging.error("Error auditing: %s", e)

			tree = xee.fromstring(response.content)


			timestamp = tree.attrib.get('timestamp')
			status = safe_get_tag_value(tree.find("result"), "")
			sha1hash = safe_get_tag_value(tree.find("sha1hash"), "")
			message = safe_get_tag_value(tree.find("message"), "")
			pasref = safe_get_tag_value(tree.find("pasref"), "")
			authcode = safe_get_tag_value(tree.find("authcode"), "")


			valid = self.get_sign_receipt(timestamp, merchant_id, order_id, status, message, pasref, authcode, share_secret)
			if sha1hash != valid:
				logging.info("Sign compare is wrong: %s vs %s" % (sha1hash, valid))
				return GATEWAY_ERROR_RETURNED + ":" + status + SEPARATOR_INFO_ERROR_ORDER + order_id

			if status == "00":
				return order_id
			else:
				return "ERROR: " + status
		except Exception as e:
			logging.warning("Exception at get_token [ADDONS]: %s", e)

		return "ERROR: Unknown"


class AddonsFormController:
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		integration = get_payment_gateway_configuration(gateway_type, hotel_code)

		currency = "eur"
		if integration.get("currency"):
			currency = integration.get("currency")
		elif extra_data.get("currency"):
			currency = extra_data.get("currency")
		currency = currency.upper()

		language = extra_data.get("language", SPANISH)

		my_timestamp = datetime.now().strftime(TIMESTAMP_FORMAT)
		merchant_id = integration.get("merchant_id")

		merchant_url = integration.get("merchant_response_url")

		merchant_url += "&sid=" + sid
		merchant_url += "&reservation=" + payment_order_id
		merchant_url += "&language=" + language
		if "?" not in merchant_url:
			merchant_url = merchant_url.replace("&", "?", 1)

		sign = SHA1HASH_FIRST % (
			my_timestamp,
			merchant_id,
			payment_order_id,
			gateways_format_price(amount),
			currency,
			sid,
			sid
		)

		sha1_hash_1 = hashlib.sha1(sign.encode("utf-8"))
		sign = SHA1HASH_FINAL % (sha1_hash_1.hexdigest(), integration.get("share_secret"))
		sha1hash = hashlib.sha1(sign.encode("utf-8")).hexdigest()
		


		if "INSITE" in gateway_type:

			merchant_url += "&no_redirect=true"

			js_context = {
				"TIMESTAMP": my_timestamp,
				"MERCHANT_ID": merchant_id,
				"ACCOUNT": integration.get("account", "internet"),
				"ORDER_ID": payment_order_id,
				"AMOUNT": gateways_format_price(amount),
				"CURRENCY": currency,
				"SHA1HASH": sha1hash,
				"AUTO_SETTLE_FLAG": 1,
				"HPP_LANG": LANGUAGE_CODES.get(extra_data.get("language", SPANISH)),
				"HPP_VERSION": 2,
				"MERCHANT_RESPONSE_URL": merchant_url,
				"HPP_POST_RESPONSE": integration.get("domain", ""),
				"HPP_LISTENER_URL": integration.get("domain", ""),
				# "HPP_POST_RESPONSE": "http://127.0.0.1:8080",
				# "HPP_LISTENER_URL": "http://127.0.0.1:8080",
				"CARD_STORAGE_ENABLE": 1,
				"OFFER_SAVE_CARD": 0,
				"PAYER_EXIST": 0,
				"PAYER_REF": sid,
				"PMT_REF": sid
			}



			if float(amount) == 0:
				js_context["VALIDATE_CARD_ONLY"] = 1
				js_context["CARD_PAYMENT_BUTTON"] = ""


			data = {
				"hotel_code": hotel_code,
				"sid": sid
			}

			encryptor = AES.new(MESSAGE_KEY, AES.MODE_CFB)
			encrypted_data = encryptor.encrypt(json.dumps(data).encode("utf-8"))
			encrypted_data = base64.urlsafe_b64encode(encryptor.iv + encrypted_data).decode("utf-8")

			url_validation = "/addons/booking_finished?data=%s" % encrypted_data

			context = {
				"js_context": json.dumps(js_context, indent=4),
				"dev": app.config.get("DEV"),
				"merchant_response_url": merchant_url,
				"payer_ref": sid,
				"pmt_ref": sid,
				"payment_url": integration.get("payment_url", ""),
				"psd2_enabled": integration.get("psd2_enabled"),
				"billing_country": "US",
				"url_validation": url_validation
			}

			url_ok = integration.get("url_ok")
			url_ok += "&sid=" + sid
			if "?" not in url_ok:
				url_ok = url_ok.replace("&", "?", 1)

			audit_response(hotel_code, "ADDONS", payment_order_id, sid, "", payload=json.dumps(context))

			template_path = "pages/cobrador/gateways/addons_payment_form.html"

			form_gateway = build_template(template_path, context, False)

		else:
			context = {
				"dev": app.config.get("DEV"),
				"my_timestamp": my_timestamp,
				"merchant_id": merchant_id,
				"account": integration.get("account", "internet"),
				"order_id": payment_order_id,
				"amount": gateways_format_price(amount),
				"currency": currency,
				"sha1hash": sha1hash,
				"language_code": LANGUAGE_CODES.get(extra_data.get("language", SPANISH)),
				"merchant_response_url": merchant_url,
				"payer_ref": sid,
				"pmt_ref": sid,
				"T_message_button_token": "",
				"payment_url": integration.get("payment_url", ""),
				"billing_country": "US",
				"force_phone": integration.get("force_phone")
			}


			if not integration.get("force_info_hotel"):
				context["psd2_enabled"] = integration.get("psd2_enabled")
			else:

				context["force_info_hotel"] = integration.get("force_info_hotel")
				context["billing_street"] = integration.get("billing_street")
				context["billing_city"] = integration.get("billing_city")
				context["billing_postalcode"] = integration.get("billing_postalcode")
				context["billing_country"] = integration.get("billing_country")


			if float(amount) == 0:
				context["tokenizator"] = True

			audit_response(hotel_code, "ADDONS", payment_order_id, sid, "", payload=json.dumps(context))

			template_path = "pages/cobrador/gateways/addons_insite_form.html"

			form_gateway = build_template(template_path, context, False)
		return form_gateway

	def process_gateway_response(self, hotel_code, gateway_type, response):

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		logging.info("addons_merchant_body: %s" % request.data)
		logging.info("addons_merchant_body_is_json: %s" % request.is_json)
		logging.info("addons_merchant_params: %s" % request.path)
		sid = request.args.get("sid")
		reservation = request.args.get("reservation")

		url_ok = integration.get("url_ok")
		url_ok += "&sid=" + sid
		if "?" not in url_ok:
			url_ok = url_ok.replace("&", "?", 1)

		url_ko = integration.get("url_ko")
		url_ko += "&sid=" + sid
		if "?" not in url_ko:
			url_ko = url_ko.replace("&", "?", 1)

		try:
			if not isinstance(response, dict):
				response_json = dict(urllib.parse.parse_qsl(response))
			else:
				response_json = response
		except Exception as e:
			logging.error("[ADDONS][%s] %s" % (reservation, e))
			response_json = {}

		audit_response(hotel_code, "ADDONS", str(reservation), str(sid), json.dumps(response_json), payload="")

		result = {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"GATEWAY_ORDER_ID": reservation
		}

		if response_json.get("RESULT") == "00":
			context = {
				"message": response_json.get("MESSAGE"),
				"url": url_ok,
				"status_ok": True
			}

			amount = str(response_json.get("AMOUNT", "000"))
			if amount and len(amount) >= 3:
				euros = amount[:-2]
				cents = amount[-2:]
				amount = float(euros + "." + cents)

			result["GATEWAY_PAID_AMOUNT"] = amount

			result["GATEWAY_EXTRA_INFO"] = {
				"addon_credencials": {
					"token_card": sid,
					"token_payer": sid,
					"pasref": response_json.get('PASREF'),
					"authcode": response_json.get('AUTHCODE')
				}
			}
			result["CODE"] = GATEWAY_SUCESS_RETURNED


		else:
			context = {
				"message": response_json.get("MESSAGE"),
				"url": url_ko,
				"status_ok": False
			}

		form_gateway_response = build_template("pages/cobrador/gateways/addons_response_button.html", context, False)
		section_name = integration.get("use custom section for response", "")
		if not context.get("status_ok"):
			section_name = integration.get("use custom section for wrong response", "")
		hotel = get_hotel_by_application_id(hotel_code)
		if section_name and hotel:
			language = request.args.get("language", SPANISH)
			section = get_web_section(hotel, section_name, language)
			if section.get("description"):
				template_body = section.get("description")
				for value_key in context:
					if isinstance(context[value_key], str):
						template_body = template_body.replace("@@%s@@" % value_key, context[value_key])
				form_gateway_response = template_body

		result["CUSTOM_RESPONSE"] = form_gateway_response

		return result

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


@app.route("/addons/booking_finished")
@cross_origin(origin='*')
def booking_finished():
	data = request.args.get("data")
	if data:
		json_data = {}
		try:
			payload = base64.urlsafe_b64decode(str(request.args.get("data")))
			if len(payload) > 16:
				iv = payload[:16]
				payload = payload[16:]
				encryptor = AES.new(MESSAGE_KEY, AES.MODE_CFB, IV=iv)
				payload = encryptor.decrypt(payload)
				json_data = json.loads(payload)
		except Exception as e:
			return "false"

		session = get_session_from_hotel(json_data.get("hotel_code"), json_data.get("sid"))

		if session.get('finished'):
			return "true"

		return "false"

def safe_get_tag_value(label, default_value=None):
	if label is not None:
		return label.text
	else:
		return default_value