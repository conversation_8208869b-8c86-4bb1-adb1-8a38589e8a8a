import json
import time
from datetime import datetime

import requests
from flask import request as r, request
from paraty.pages.cobrador.cobrador_constants import CUSTOM_DOMAIN, KEY_DOMAIN, GATEWAY_ERROR_CODE_RETURNED, \
    CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, get_config_property_value, \
    audit_response
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.language_utils import get_language_code
from paraty_commons_3.logging.my_gae_logging import logging

import re
import os
import hashlib

TOKEN_OK = "000"
PAYMENT_OK = "000"

SESSION_TOKEN_URL_TEST = "https://test.imspagofacil.es/client2/token-pro"
SESSION_TOKEN_URL_LIVE = "https://imspagofacil.es/client2/token"

JAVASCRIPT_URL_TEST = "https://test.imspagofacil.es/client2/load"
JAVASCRIPT_URL_LIVE = "https://imspagofacil.es/client2/load"

BASE_URL_TEST = "https://cashierui.test.universalpay.es/ui/cashier"
BASE_URL_LIVE = " https://cashierui.universalpay.es/ui/cashier"

LIVE = "LIVE"
TEST = "TEST"

ACTION_PURCHASE = "PURCHASE"


def get_integration_config_by_status(integration_config):
    status = integration_config.get("status", "")
    if not integration_config.get("url_post"):
        integration_config["url_post"] = "https://sandbox.checkout.payulatam.com/ppp-web-gateway-payu"
    if status == "TEST":
        test_configs = {x.replace("TEST_", ""): integration_config[x] for x in integration_config if "TEST_" in x}
        integration_config=test_configs
        if not integration_config.get("url_post"):
            integration_config["url_post"] = "https://sandbox.checkout.payulatam.com/ppp-web-gateway-payu"
    return integration_config








def get_signature(config, sid, amount, operation):
    url_ok = config.get("url_ok")
    url_ko = config.get("url_ko")
    url_response = config.get("url_merchant")
    password = config.get("password")

    content = "%s%s%s%s%s%s%s" % (config.get("merchant_id"), amount, operation, url_response, url_ok, url_ko, password)

    result = hashlib.sha256(content.encode())
    result = result.hexdigest()
    logging.info("CONTENT FOR SIGNATURE: %s", content)
    logging.info("SIGNATURE: %s", str(result))
    return result



def get_token(config, sid, amount,payment_order_id,extra_data):
    try:
        operation = "UPAY%s" % datetime.now().strftime("%y%m%d%H%M%S")
        url_ok = config.get("url_ok")
        url_ko = config.get("url_ko")
        url_response = config.get("url_merchant")

        signature = get_signature(config, sid,amount,operation)

        payload = {
            "MERCHANT_IDENTIFIER": config.get("merchant_id"),
            "AMOUNT": amount,
            "OPERATION": operation,
            "URL_RESPONSE": url_response,
            "URL_OK": url_ok,
            "URL_KO": url_ko,
            "SIGNATURE": signature,
            "DESCRIPTION": payment_order_id,
            "CURRENCY": "978",
            "LOCALE": "es",
            "PARAMS": {'PERSONAL_IDENTITY_NUMBER': payment_order_id,
                       'REQUIRE_CARDHOLDER': True}

        }

        payload = json.dumps(payload)
        url = config.get("token_url")

        logging.info("[UNIVERSALPAY] Request Getting Token: %s", url)
        logging.info("[UNIVERSALPAY] Request Getting Token Payload: %s", payload)

        headers = {}
        headers['Content-Type'] = "application/json"

        response = requests.request("POST", url, data=payload, headers=headers)

        logging.info("[UNIVERSALPAY] Response Getting Token: %s", response)

        # url = "https://api.test.universalpay.es/token"
        #
        # # Obtener el timestamp actual en milisegundos
        # timestamp = int(time.time() * 1000)
        #
        # # Definir los datos para la solicitud POST
        # data = {
        #     "merchantId": config.get("brand_id"),  # Completar con el ID del comerciante
        #     "password": config.get("password"),  # Completar con la contraseña
        #     "action": "PURCHASE",  # Ejemplo de acción (puede ser AUTH, PURCHASE, VERIFY)
        #     "timestamp": timestamp,
        #     "channel": "ECOM",
        #     "country": "",  # Completar con el país si es necesario
        #     "allowOriginUrl": "null",
        #     "merchantNotificationUrl": config.get("url_merchant"),  # Completar con la URL de notificación del comerciante
        #     "merchantLandingPageUrl": "",  # Completar con la URL de la página de aterrizaje del comerciante
        #     "merchantLandingPageRedirectMethod": "GET",
        #     "paymentSolutionId": "500",
        #     "brandId": config.get("brand_id"),# Completar con el ID de la marca si es necesario
        #     "language": "es",
        #     "amount": amount,
        #     "currency": "EUR",
        #     "customerFirstName": "David",
        #     "customerLastName": "Lopez",
        #     "customerId": "DavidUP7",
        #     "customerBrowser[browserAcceptHeader]": "*/*",
        #     "customerBrowser[browserJavaEnabled]": "true",
        #     "customerBrowser[browserJavascriptEnabled]": "true",
        #     "customerBrowser[browserLanguage]": "ES",
        #     "customerBrowser[browserColorDepth]": "8",
        #     "customerBrowser[browserScreenHeight]": "800",
        #     "customerBrowser[browserScreenWidth]": "1200",
        #     "customerBrowser[browserTZ]": "1",
        #     "sdkAppInfo": "null"
        # }
        #
        # # Realizar la solicitud POST
        # response = requests.post(url, data=data)

        # Mostrar el resultado de la solicitud
        print(response.text)

        if response.status_code != requests.codes.ok:
            raise Exception("Response Code: %s", response.status_code)

        logging.info("Response Body: %s", response.text)
        response_json = json.loads(response.text)
        if not response_json:
            raise Exception("Token not found")

        # if not response_json.get("TOKEN_RESULT", ''):
        #     raise Exception("Token not found")

        token = None
        # if response_json.get("TOKEN_RESULT").get("CODE") != TOKEN_OK:
        #     message = response_json.get("TOKEN_RESULT").get("DESCRIPTION")
        #     raise Exception("Token not found: %s", message)



        token = response_json.get("TOKEN", None)
        return token

    except Exception as e:
        logging.warning("Exception at get_token [UNIVERSALPAY]: %s", e)

        return None


class UniversalPayFormController:
    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        integration = get_payment_gateway_configuration(gateway_type, hotel_code)
        integration = get_integration_config_by_status(integration)

        logging.info("Price: %s" % str(extra_data.get('price')))
        amount = str('%.2f' % float(amount)).replace('.', '')


        country = get_language_code(extra_data.get('language', '')).upper()
        # signature = get_signature(integration, amount,payment_order_id)
        token = get_token(integration, sid, amount,payment_order_id,extra_data)


        if token:
            logging.info("Token obtained: %s", token)

        context = {
            'javascript_url': integration.get("javascript_url"),
            'base_url': integration.get("base_url"),
            'merchant_id': integration.get("merchant_id"),
            'token': token,
            'url_iframe': integration.get("javascript_url"),
            "base_url": r.host_url
        }
        return build_template("pages/cobrador/gateways/universalpay_form.html", context, False)

    def process_gateway_response(self, hotel_code, gateway_type, response):
        logging.info("[UNIVERSALPAY] Processing response request...")
        logging.info("Request Content:")
        integration = get_payment_gateway_configuration(gateway_type, hotel_code)
        integration = get_integration_config_by_status(integration)
        try:

            logging.info("[UNIVERSALPAY] Processing response")
            logging.info("response:")
            logging.info(response)
            amount =""
            reservation = ""

            try:
                gateway_type = "UNIVERSALPAY"
                audit_response(hotel_code, gateway_type, reservation, "sid", response)

            except Exception as e:
                logging.error("Error auditing response for in STRIPE EXECUTE UNIVERSALPAY")
                logging.error("Error auditing: %s", e)

            reponse = json.loads(request.body)
            if reponse.get("PAYMENT_CODE") != PAYMENT_OK:
                return {
                    "CODE": GATEWAY_ERROR_CODE_RETURNED,
                    "GATEWAY_ORDER_ID": reservation,
                    "GATEWAY_PAID_AMOUNT": amount,
                    "GATEWAY_EXTRA_INFO": {}
                }
            return {
                "CODE": "OK",
                "GATEWAY_ORDER_ID": reservation,
                "GATEWAY_PAID_AMOUNT": amount,
                "GATEWAY_EXTRA_INFO": {
                    "payment_gateway": "PAYU",
                    "payu_info": {
                        "tax_value": amount


                    }
                }
                }


        except Exception as e:


            logging.critical(e)
        # if status == APPROVED:
        #     return {
        #         "CODE": "OK",
        #         "GATEWAY_ORDER_ID": reservation,
        #         "GATEWAY_PAID_AMOUNT": amount,
        #         "GATEWAY_EXTRA_INFO": {
        #             "payment_gateway": "PAYU",
        #             "payu_info": {
        #                 "tax_value": amount,
        #                 "tax": tax,
        #                 "lap_payment_method_type": lap_payment_method_type,
        #                 "transaction_state":transaction_state,
        #                 "reference_pol":reference_pol,
        #                 "transaction_id":transaction_id,
        #                 "cus":cus,
        #                 "pol_payment_method":pol_payment_method,
        #                 "lap_payment_method": lap_payment_method,
        #                 "pol_payment_method_type":pol_payment_method_type
        #             }
        #         }
        #     }
        # return {
        #     "CODE": GATEWAY_ERROR_CODE_RETURNED,
        #     "GATEWAY_ORDER_ID": reservation,
        #     "GATEWAY_PAID_AMOUNT": amount,
        #     "GATEWAY_EXTRA_INFO": {}
        # }

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT
