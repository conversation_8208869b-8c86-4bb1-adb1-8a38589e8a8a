from decimal import Decimal
import decimal
import json
import logging
import urllib.parse

import datetime
from flask import request


import requests
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, GATEWAY_SUCESS_RETURNED, \
	COBRADOR_SERVER_PATH, GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, add_sid_to_url, \
	send_cobrador_response_to_client_server, build_common_iframe_html, get_integration_name, \
	audit_response, decrypt_paraty_own_response_from_api
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.utilities.languages.language_utils import get_language_in_manager_based_on_locale
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.utils import proxy_utils
from zeep import Client, helpers
from zeep.wsse.username import UsernameToken

RESORTCOM_STATUS_OK = 6
INVALID_CARD_NUMBER_RESORTCOM_ERROR = "Invalid number. Just numbers and white spaces are accepted on the string."

class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return {'__Decimal__': str(obj)}
        # Let the base class default method raise the TypeError
        return json.JSONEncoder.default(self, obj)


class ResortcomControler(GatewayInterface):
	#FOR THE MOMENT RESoRTCOM HASN'T COBRADOR!
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		#TODO
		ds_order = ""
		return ds_order
		logging.error("integration_configuration NOt found!")
		return GATEWAY_ERROR_RETURNED

	def translate_error(self, error):
		return error

	def get_initial_order_id_from_extra_info(self, reservation_extra_info):
		return reservation_extra_info.get("ds_MerchantIdentifier")

	def reservation_has_token(self, reservation):
		return False

	def gateway_has_token(self):
		return False


	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		logging.info("RESORTCOM. execute_refund_in_gateway. reservation: %s amount: %s", reservation.get("identifier"), amount)
		#take SERMEPA configuration
		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		if gateway_configuration:
			logging.info("integration_configuration found!")

			payment_url = gateway_configuration.get("payment_url")
			merchant_id = gateway_configuration.get("merchant_code")
			currency_code = gateway_configuration.get("currency_code")

			user = gateway_configuration.get("user")
			password = gateway_configuration.get("password")


			client = Client(payment_url, wsse=UsernameToken(user, password))


			request_data = {
				"authId": order_to_refund,
				"adjustedAmount": amount,
			}

			response_resort_com = client.service.ReduceAuthorization(**request_data)
			logging.info("RESORTCOM RESPONSE response: %s", response_resort_com)

			resortcom_response_ok = False
			text_error = ""
			if response_resort_com:

				result_resort_com = helpers.serialize_object(response_resort_com)
				resortcom_response_ok = result_resort_com and result_resort_com.get("CardAuthorizationStatus") == RESORTCOM_STATUS_OK

				if not resortcom_response_ok:
					text_error = result_resort_com.get("CardAuthorizationDetailStatus")

			if resortcom_response_ok:

				return str(order_to_refund)


		return "%s:%s" % (GATEWAY_ERROR_RETURNED, text_error)



class ResortcomFormControler(FormGatewayInterface):

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		#this method has to return ALAWAYS an inframe, we are going to use PEP common solution
		form_gateway = build_common_iframe_html(hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data)
		return form_gateway



	def process_gateway_response(self, hotel_code, gateway_type, original_response):
		if not isinstance(original_response, dict):
			response = dict(urllib.parse.parse_qsl(original_response))
		else:
			response = original_response
		cobrador_parameters = response.get("cobrador_parameters")
		cobrador_signature = response.get("cobrador_signature")

		return decrypt_paraty_own_response_from_api(hotel_code, gateway_type, cobrador_parameters, cobrador_signature)




	def execute_payment_in_gateway_by_api(self, gateway_type, hotel_code, datas):
		'''
		CardAuthorizationRequest
		{
		decimal Amount
		string CurrencyCode int MerchantId
		string CardHolderName string CardNumber
		int CardSecurityNumber
		int CardExpirationMonth
		int CardExpirationYear
		string CardBillingAddress
		string CardBillingZipCode
		string ExternalReference
		}
		:param datas:
		datas:
				{"company": body_post.get("company"),
				"cardNumber": body_post.get("cardNumber"),
				"cvv": body_post.get("cvv"),
				"expiryMonth": body_post.get("expiryMonth"),
				"expiryYear": body_post.get("expiryYear"),
				"amount": body_post.get("amount"),
				"sid": body_post.get("sid"),
				"payment_order_id": body_post.get("payment_order_id"),
				"holder_name": body_post.get("ccOwnerName"),
				"billing_address": body_post.get("billingAddress"),
				"billing_cp": body_post.get("billingZipCode"),
				}
		:return:
		'''
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		payment_url = gateway_configuration.get("payment_url")
		merchant_id = gateway_configuration.get("merchant_code")
		currency_code = gateway_configuration.get("currency_code")

		user = gateway_configuration.get("user")
		password = gateway_configuration.get("password")
		resort_number = gateway_configuration.get("resort_number", 0)


		client = Client(payment_url, wsse=UsernameToken(user, password))

		logging.info("sending payment to RESORTCOM. payment_order_id: %s", datas.get("payment_order_id"))

		request_data = {
			"cardAuthRequest":{
				"Amount": datas.get("amount"),
				"CardBillingAddress": datas.get("billing_address"),
				"CardBillingZipCode": datas.get("billing_cp"),
				"CardExpirationMonth": datas.get("expiryMonth"),
				"CardExpirationYear": datas.get("expiryYear"),
				"CardHolderName": datas.get("holder_name"),
				"CardNumber": datas.get("cardNumber"),
				"CardSecurityNumber": datas.get("cvv"),
				"CurrencyCode": currency_code,
				"ExternalReference": datas.get("payment_order_id"),
				"ResortNumber": resort_number,
				"MerchantId": merchant_id
			}
		}

		#response_resort_com = client.service.AuthorizeSettle(**request_data)
		try:
			response_resort_com = client.service.Authorize(**request_data)
		except Exception as e:
			if INVALID_CARD_NUMBER_RESORTCOM_ERROR in str(e):
				logging.warning(e)
				response_resort_com = ""
			else:
				raise e

		resortcom_response_ok = False
		text_error = ""
		if response_resort_com:

			result_resort_com = helpers.serialize_object(response_resort_com)
			logging.info("RESORT RESPONSE response: %s", result_resort_com)
			resortcom_response_ok = result_resort_com and result_resort_com.get("CardAuthorizationStatus") == RESORTCOM_STATUS_OK

			try:
				response_resort_txt = json.dumps(result_resort_com, cls=DecimalEncoder)
				audit_response(hotel_code, gateway_type, datas.get("payment_order_id", ""), datas.get("sid", ""), response_resort_txt)
			except Exception as e:
				logging.error("Error auditing response for in RESORTCOM")
				logging.error("Error auditing: %s", e)

			if not resortcom_response_ok:
				text_error = result_resort_com.get("CardAuthorizationDetailStatus")


		if resortcom_response_ok:

			#make reservation in hotel webs
			amount = datas.get("amount")
			payment_order_id = result_resort_com.get("CardAuthorizationNumber")
			sid = datas.get("sid")
			res_client_server = send_cobrador_response_to_client_server(gateway_configuration, sid, amount, payment_order_id)
			if res_client_server:
				return str(payment_order_id)

		return "ERROR %s" % text_error

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


