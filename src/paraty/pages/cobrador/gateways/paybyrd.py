import base64
import datetime
import json
from random import randint

from Cryptodome.Cipher import A<PERSON>
from flask import request, redirect
from requests import post, get
import pycountry
import urllib.parse

from paraty import Config, app
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, GATEWAY_ERROR_CODE_RETURNED, \
    CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, audit_response, \
    gateways_format_price, get_integration_name
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.logging.my_gae_logging import logging

PAYBYRD_CREATE_ORDER_URL = "https://gateway.paybyrd.com/api/v2/orders"
PAYBYRD_QUERY_ORDER_URL = "https://gateway.paybyrd.com/api/v2/orders/%s"
PAYBYRD_QUERY_TOKEN_URL = "https://gateway.paybyrd.com/api/v2/tokens?customReference=%s"
PAYBYRD_QUERY_PAYMENT_URL = "https://gateway.paybyrd.com/api/v2/payment"
PAYBYRD_QUERY_REFUND_URL = "https://gateway.paybyrd.com/api/v2/refund/%s"


class PaybyrdController(GatewayInterface):
    def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order, external_id):
        extra_info = reservation.get("extraInfo", "{}")
        if extra_info:
            try:
                extra_info = json.loads(extra_info)
            except:
                logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
                          reservation.get("identifier"))

                return ""

            reservation_id = int(reservation.key.id)
            extra_info["last_merchant_order_used"] = merchant_order

            transactions_index = extra_info.get("paybyrd_transactions_index", {})
            transactions_index[merchant_order] = external_id
            extra_info["paybyrd_transactions_index"] = transactions_index
            reservation["extraInfo"] = json.dumps(extra_info)

            reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
                                                                              reservation, hotel_code=hotel_code)
            logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
                     reservation_id)

            return reservation_updated_id

    def get_next_stripe_order(self, hotel_code, reservation):

        last_merchant_order_used = ""
        if reservation.get("extraInfo"):
            extra_info = json.loads(reservation.get("extraInfo"))
            last_merchant_order_used = extra_info.get("last_merchant_order_used")

        if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
            last_merchant_order_used = last_merchant_order_used[0:8]
            numerical_order = int(last_merchant_order_used)
            numerical_order += 1

            merchant_order = str(numerical_order)

            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
        else:

            merchant_order = reservation.get("identifier")
            # maybe this reservation was done before gateway was configured. So it could be an alphanumeric
            if not merchant_order.isnumeric():
                merchant_order = randint(10000000, 99999000)

            numerical_order = int(merchant_order)
            numerical_order += 1
            merchant_order = str(numerical_order)
            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

        # IMPORTANT: Even redsys return us an erro, they save ALWAYS the order.
        # so we have to save it in reservation for not received a duplicate order error in next payment!

        return merchant_order

    def execute_payment_in_gateway(self, hotel_code, reservation, amount):
        integration_name = get_integration_name(hotel_code)
        integration = get_payment_gateway_configuration(integration_name, hotel_code)

        try:
            extra_info = \
                json.loads(reservation.get("extraInfo", {}))
        except:
            extra_info = {}

        token_reference = extra_info.get("paybyrd_token")

        currency = extra_info.get("currency", "")
        if not currency:
            currency = get_configuration_property_value("Base Price Currency")
        if not currency:
            currency = "EUR"

        currency = currency.upper()

        paybyrd_header = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "x-api-key": integration.get("api_key")
        }

        response = get(PAYBYRD_QUERY_TOKEN_URL % token_reference, headers=paybyrd_header)

        if response.status_code == 200:
            response_json = response.json()
            if response_json.get("data", []):
                token_data = response_json["data"][0]
                token_id = token_data["tokenId"]

                next_transaction_id = self.get_next_stripe_order(hotel_code, reservation)

                payment_request = {
                    "type": "Card",
                    "acceptTokenization": False,
                    "isPreAuth": False,
                    "isoAmount": gateways_format_price(amount),
                    "currency": currency,
                    "orderRef": next_transaction_id,
                    "tokenId": token_id
                }

                payment_response = post(PAYBYRD_QUERY_PAYMENT_URL, json=payment_request, headers=paybyrd_header)
                audit_response(hotel_code, "PAYBYRD", next_transaction_id, "", payment_response.text, payload=json.dumps(payment_request), type_audit="Payment")
                if payment_response.status_code == 201:
                    payment_response_json = payment_response.json()
                    if payment_response_json.get("status") == "Success":

                        self.save_last_order_in_reservation(hotel_code, reservation, next_transaction_id, payment_response_json.get("transactionId"))
                        return next_transaction_id

        return GATEWAY_ERROR_RETURNED

    def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
        integration_name = get_integration_name(hotel_code)
        integration = get_payment_gateway_configuration(integration_name, hotel_code)

        try:
            extra_info = \
                json.loads(reservation.get("extraInfo", {}))
        except:
            extra_info = {}


        transaction_id = extra_info.get("paybyrd_transactions_index", {}).get(order_to_refund)
        if not transaction_id and reservation.get("identifier") == order_to_refund:
            transaction_id = extra_info.get("paybyrd_first_transaction")

        if transaction_id:

            paybyrd_header = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-api-key": integration.get("api_key")
            }

            refund_request = {
                "isoAmount": gateways_format_price(amount)
            }

            refund_response = post(PAYBYRD_QUERY_REFUND_URL % transaction_id, headers=paybyrd_header, json=refund_request)
            if refund_response.status_code == 201:
                refund_response_json = refund_response.json()
                if not refund_response_json.get("isError"):
                    next_transaction_id = self.get_next_stripe_order(hotel_code, reservation)

                    self.save_last_order_in_reservation(hotel_code, reservation, next_transaction_id,
                                                        refund_response_json.get("transactionId"))

                    audit_response(hotel_code, "PAYBYRD", next_transaction_id, "", refund_response.text,
                                   payload=json.dumps(refund_request), type_audit="Refund")

                    return next_transaction_id



        return GATEWAY_ERROR_RETURNED

    def reservation_has_token(self, reservation):

        try:
            extra_info = json.loads(reservation.get("extraInfo"))
        except:
            extra_info = {}

        if extra_info.get("paybyrd_token"):
            return True
        else:
            return False

    def gateway_has_token(self):
        return True

    def translate_error(self, error):
        return error

    def get_configuration(self, hotel_code):
        integration_name = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

        return gateway_configuration

    def get_initial_order_id_from_extra_info(self, reservation_extra_info):
        return reservation_extra_info.get("paymentOrderId")


class PaybyrdFormController(FormGatewayInterface):
    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        gateway_type = [x for x in gateway_type.split(";") if "PAYBYRD" in x][0]

        payload = {
            "hotel_code": hotel_code,
            "gateway_type": gateway_type,
            "payment_order_id": payment_order_id,
            "amount": amount,
            "sid": sid,
            "add_button": add_button,
            "extra_data": extra_data
        }

        payment_info_string = json.dumps(payload)

        encryptor = AES.new(KEY, AES.MODE_EAX)

        payload = encryptor.encrypt(payment_info_string.encode("utf-8"))

        encrypted_payload = base64.urlsafe_b64encode(encryptor.nonce + payload).decode("ascii")

        context = {
            "payload": encrypted_payload
        }

        template_path = "pages/cobrador/gateways/paybyrd/paybyrd_form.html"
        form_gateway = build_template(template_path, context, "SPANISH")

        return form_gateway

    def process_gateway_response(self, hotel_code, gateway_type, response):

        #return {"CODE": "ERROR"}

        gateway_type = [x for x in gateway_type.split(";") if "PAYBYRD" in x][0]
        paybyrd_data = get_payment_gateway_configuration(gateway_type, hotel_code)
        paybyrd_order_id = request.values.get("orderId")
        if isinstance(response, dict):
            from_webhook = response.get("from_webhook")
        else:
            from_webhook = None

        if from_webhook:
            paybyrd_order_id = response.get("orderId")

        if Config.DEV:
            paybyrd_data['url_ko'] = "http://localhost:8090/booking3"
            paybyrd_data['url_ok'] = "http://localhost:8090/booking4"

        paybyrd_header = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "x-api-key": paybyrd_data.get("api_key")
        }
        sid = request.values.get("sid", "")

        logging.info("[%s][GET_ORDER] request to paybyrd: %s" % (sid, PAYBYRD_QUERY_ORDER_URL % paybyrd_order_id))

        response = get(PAYBYRD_QUERY_ORDER_URL % paybyrd_order_id, headers=paybyrd_header)
        logging.info("[%s][GET_ORDER] response from paybyrd: %s" % (sid, response.text))
        response_json = response.json()
        payment_order = response_json.get("orderRef")

        if from_webhook:
            paybyrd_data['url_ko'] = ""
            paybyrd_data['url_ok'] = ""
            sid = response_json.get("shopper", {}).get("shopperId")
            hotel_code = response_json.get("orderOptions").get("redirectUrl").split("hotel_code=")[-1].split("&")[0]

        audit_response(hotel_code, "PAYBYRD", payment_order, sid, response.text, payload=PAYBYRD_QUERY_ORDER_URL % paybyrd_order_id)

        sucess_response = {
            "CODE": GATEWAY_ERROR_CODE_RETURNED,
            "GATEWAY_ORDER_ID": payment_order,
            "GATEWAY_PAID_AMOUNT": response_json.get("amount"),
            "GATEWAY_EXTRA_INFO": {
                "paybyrd_token": response_json.get('paymentOptions', {}).get('tokenOptions', {}).get('customReference', ""),
                "paybyrd_first_transaction": response_json.get("lastTransactionId", "")
            }
        }

        if response_json.get("status") == 'paid':
            sucess_response['CODE'] = "OK"
        elif response_json.get("status") == 'pending':
            sucess_response['CODE'] = "PENDING"

        return sucess_response

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT


KEY = b")J@NcRfUjXn2r4u7"

@app.route("/paybyrd/redirect", methods=["GET", "POST"])
def paybyrd_redirect():
    data = request.form
    payload = data.get("payload")
    payload = urllib.parse.unquote(payload)
    payload = base64.urlsafe_b64decode(payload.encode("ascii"))
    nounce = payload[:16]
    payload = payload[16:]

    encryptor = AES.new(KEY, AES.MODE_EAX, nonce=nounce)

    payload = encryptor.decrypt(payload)

    payment_data = json.loads(payload)

    hotel_code = payment_data.get("hotel_code")
    gateway_type = payment_data.get("gateway_type")
    payment_order_id = payment_data.get("payment_order_id")
    amount = payment_data.get("amount")
    sid = payment_data.get("sid")
    add_button = payment_data.get("add_button")
    extra_data = payment_data.get("extra_data")
    language = extra_data.get("language")

    paybyrd_data = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)

    currency = "eur"
    if paybyrd_data.get("currency"):
        currency = paybyrd_data.get("currency")
    elif extra_data.get("currency"):
        currency = extra_data.get("currency")
    currency = currency.upper()
    merchant_url = paybyrd_data.get('merchant_url', "")

    if Config.DEV:
        merchant_url = "http://localhost:8090/paybyrd/merchant_url"

    merchant_url += "&sid=%s&hotel_code=%s&reservation=%s" % (sid, hotel_code, payment_order_id)
    if "?" not in merchant_url:
        merchant_url = merchant_url.replace("&", "?", 1)

    payload = {
        "amount": amount,
        "currency": currency,
        "orderRef": payment_order_id,
        "shopper": {
            "email": data.get("shopper_email"),
            "firstName": data.get("shopper_first_name"),
            "lastName": data.get("shopper_last_name"),
            "phoneNumber": data.get("shopper_telephone"),
            "shippingCountry": pycountry.countries.get(alpha_2=data.get("shopper_country") or "ES").alpha_3
        },
        "orderOptions": {
            "redirectUrl": merchant_url
        },
        "paymentOptions": {
            "tokenOptions": {
                "customReference": payment_order_id,
                "tokenizationIsRequired": True,
                "tokenizationMethod": "subscription"
            }
        }
    }

    if paybyrd_data.get("dev_test"):
        payload["paymentOptions"]["useSimulated"] = True

    logging.info("Payload Paybyrd")
    logging.info(json.dumps(payload))
    paybyrd_header = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "x-api-key": paybyrd_data.get("api_key")
    }

    response = post(PAYBYRD_CREATE_ORDER_URL, json.dumps(payload), headers=paybyrd_header)

    audit_response(hotel_code, "PAYBYRD", payment_order_id, sid, response.text,
                   payload=json.dumps(payload), type_audit="Form Payment")

    if response.status_code == 201:

        response_json = response.json()

        if not datastore_communicator.get_using_entity_and_params("ReservationMetadata",
                                                                  [("identifier", "=", int(payment_order_id))],
                                                                  hotel_code="payment-seeker"):
            extra_info = {
                "merchant_url": merchant_url,
                "gateway_type": gateway_type
            }
            properties = {
                "sid": "FAKE_SID_" + response_json.get("orderId"),
                "identifier": int(payment_order_id),
                "hotel_code": hotel_code,
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "extraInfo": json.dumps(extra_info)
            }

            datastore_communicator.save_to_datastore("ReservationMetadata", None, properties,
                                                     hotel_code="payment-seeker:")

            return redirect(response_json.get("checkoutUrl"))

    else:
        url_ko = paybyrd_data.get("url_ko", "")
        url_ko += "&sid=%s&namespace=%s" % (sid, hotel_code)


        return redirect(url_ko)

@app.route("/paybyrd/webhook", methods=["POST", "GET"])
def paybyrd_webhook():
    if request.is_json:
        request_json = request.json

        if not request_json.get("orderId", ""):
            logging.warning("No orderId from webhook")
            return "KO", 200
        reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
                                                                                  [("sid", "=", "FAKE_SID_" + request_json.get("orderId", ""))])
        if reservation_metadata:
            reservation_metadata = reservation_metadata[0]

            try:
                extra_info = json.loads(reservation_metadata.get("extraInfo"))
            except:
                extra_info = {}

            hotel_code = reservation_metadata.get("hotel_code", "")
            identifier = reservation_metadata.get("identifier", "")
            gateway_type = extra_info.get("gateway_type", "")
            paybyrd_data = get_payment_gateway_configuration(gateway_type, hotel_code)
            paybyrd_order_id = request_json.get("orderId", "")

            logging.info("[paybyrd][%s][%s] Webhook call: %s" % (hotel_code, identifier, request.data.decode("utf-8").replace("\n", "")))

            paybyrd_header = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-api-key": paybyrd_data.get("api_key")
            }

            response = get(PAYBYRD_QUERY_ORDER_URL % paybyrd_order_id, headers=paybyrd_header)
            logging.info("[paybyrd][%s][%s] Order status change: %s" % (hotel_code, identifier, response.text))

            if response.status_code in [200, 201]:
                return "OK", 200
    return "KO", 400