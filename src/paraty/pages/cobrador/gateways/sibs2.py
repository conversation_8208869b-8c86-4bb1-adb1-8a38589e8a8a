import base64
import json
import uuid

import pytz
from Cryptodome.Cipher import AES
from flask import request as r, request, make_response
from datetime import timedel<PERSON>, datetime

import requests
from flask.views import MethodView
from flask_cors import cross_origin

from paraty import Config, app
from paraty.pages.cobrador.cobrador_constants import GATEWAY_PENDING_RETURNED, GATEWAY_SUCESS_RETURNED, \
	GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT, COBRADOR_QUEUE
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, add_sid_to_url, \
	get_integration_name, audit_response, cancel_reservation_in_manager
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.utilities.encryption_utils import build_encrypted_url
from paraty.utilities.languages.language_utils import get_language_code
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3 import queue_utils
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore, get_using_entity_and_params, \
	save_entity
from paraty_commons_3.datastore.datastore_utils import get_inner_url
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.logging.my_gae_logging import logging


class SIBS2FormControler(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		logging.info("Generating Payment Form 2!!!!!")
		sibs_data = get_payment_gateway_configuration("SIBS2.0 COBRADOR", hotel_code)

		if not request.args.get("get_form"):
			template_path = "pages/cobrador/gateways/sibs/_sibs2_form.html"
			context = {
				"no_form": True,
				"hotel_code": hotel_code,
				"gateway_type": gateway_type,
				"payment_order_id": payment_order_id,
				"amount": amount,
				"sid": sid,
				"add_button": add_button,
				"extra_data": json.dumps(extra_data),
				"url": "https://payment-seeker.ew.r.appspot.com/sibs/get_form?get_form=true"
			}
			if Config.DEV:
				context["url"] = "http://localhost:8084/sibs/get_form?get_form=true"
			return build_template(template_path, context, extra_data.get("language"))

		sibs_data['currency'] = "EUR"

		if Config.DEV:
			sibs_data['merchant_url'] = "http://localhost:8090/cobrador/proxy/merchant_url"

		sibs_data['id'] = payment_order_id
		sibs_data['amount'] = amount
		sibs_data['sid'] = sid
		sibs_data['hotel_code'] = hotel_code
		#
		# if Config.DEV:
		# 	sibs_data['amount'] = 0.0

		language = extra_data.get("language")
		checkout_id_data = self.get_checkout(sibs_data, extra_data)
		logging.info(checkout_id_data)
		language_code = get_language_code(language)
		merchant_url = add_sid_to_url(sibs_data['merchant_url'], sid)
		merchant_url += f'&transaction_id={checkout_id_data.get("transactionID")}'
		form_config = {
			"paymentMethodList": [],
			"amount": {"value": sibs_data['amount'], "currency": sibs_data['currency']},
			"language": language_code if language_code in ["en", "pt"] else "en",
			"redirectUrl": merchant_url,
			"customerData": None
		}

		if extra_data.get("force_token"):
			form_config["labelPay"] = "Authorize"
			form_config["hideAmount"] = True

		context = {
			"transactionID": checkout_id_data.get("transactionID"),
			"form_context": checkout_id_data.get("formContext"),
			"form_config": json.dumps(form_config),
			"base_url": r.host_url,
			"endpoint": sibs_data['endpoint'],
			"test": sibs_data.get("test_enviroment")
		}

		sibs_transaction_data = {
			"hotel_code": hotel_code,
			"identifier": payment_order_id,
			"transaction_id": checkout_id_data.get("transactionID"),
			"sid": sid,
			"merchant_url": f"{merchant_url}&avoid_mbway_check=true"
		}
		save_to_datastore("MultibancoTransactions", None, sibs_transaction_data, hotel_code="payment-seeker:", exclude_from_indexes=())

		if not Config.DEV:
			self.task_check_multibanco(sibs_data, checkout_id_data, hotel_code)
			context['base_url'] = context['base_url'].replace("http", "https")

		logging.info("Context: %s", context)

		template_path = "pages/cobrador/gateways/sibs/_sibs2_form.html"
		return build_template(template_path, context, language)

	def task_check_multibanco(self, sibs_data, checkout_id_data, hotel_code):
		payload = {
			"merchant_url": f"https://payment-seeker.appspot.com/multibanco/check_reservation?hotel_code={hotel_code}&identifier={sibs_data['id']}&gateway=sibs"
		}

		data = {
			"task_id": str(uuid.uuid4()),
			"data": base64.urlsafe_b64encode(json.dumps(payload).encode("utf-8")).decode("utf-8")
		}

		limit_time_multibanco = int(sibs_data.get("limit_time_multibanco", 48)) * 3600

		try:
			queue_utils.create_task('execute_fallback_task', json.dumps(data),
									queue_name=COBRADOR_QUEUE,
									task_name=f'fallback_task__{checkout_id_data.get("transactionID")}_{hotel_code}',
									in_seconds=limit_time_multibanco)
		except Exception as e:
			logging.warning(
				f"Error controlled. Payment already differed: {checkout_id_data.get('transactionID')}_{hotel_code}")
			logging.warning("%s" % e)

	def sibs_header(self, sibs_data):
		return {
			'Authorization': f"Bearer {sibs_data['bearer']}",
			'X-IBM-Client-Id': sibs_data['x-ibm-client-id'],
			'content-type': "application/json",
			'accept': "application/json",
		}

	def get_checkout(self, sibs_data, extra_data):
		timestamp = datetime.utcnow().isoformat() + 'Z'
		limit_time_multibanco = int(sibs_data.get("limit_time_multibanco", 48))

		if Config.DEV:
			limit_time_multibanco = 1

		limit_time_multibanco *= 3600

		current_date = datetime.utcnow()
		if sibs_data.get("timezone"):
			current_date = current_date.astimezone(pytz.timezone(sibs_data['timezone']))

		limit_multibanco = (current_date + timedelta(seconds=int(limit_time_multibanco))).isoformat() + 'Z'
		personal_data = json.loads(request.values.get("personal_data"))
		payload = {
			"merchant": {
				"terminalId": int(sibs_data['terminal_id']),
				"channel": sibs_data['channel'],
				"merchantTransactionId": sibs_data['id']
			},
			"transaction": {
				"transactionTimestamp": timestamp,
				"description": sibs_data['id'],
				"moto": False,
				"paymentType": "PURS",
				"amount": {
					"value": float(sibs_data['amount']),
					"currency": sibs_data['currency']
				},
				"paymentMethod": [
					"CARD",
					"MBWAY",
					"REFERENCE"
				],
				"paymentReference": {
					"initialDatetime": timestamp,
					"finalDatetime": limit_multibanco,
					"maxAmount": {
						"value": float(sibs_data['amount']),
						"currency": sibs_data['currency']
					},
					"minAmount": {
						"value": float(sibs_data['amount']),
						"currency": sibs_data['currency']
					},
					"entity": sibs_data['entity']
				}
			},
			"customer": {
				"customerInfo": {
					"customerEmail": personal_data.get("email"),
					"shippingAddress": {
						"street1": personal_data.get("full_address"),
						"city": personal_data.get("city"),
						"postcode": personal_data.get("postal_code"),
						"country": personal_data.get("country")
					},
					"billingAddress": {
						"street1": personal_data.get("full_address"),
						"city": personal_data.get("city"),
						"postcode": personal_data.get("postal_code"),
						"country": personal_data.get("country")
					}
				}
			}
		}

		if extra_data.get("start_date") and (sibs_data.get("release_multibanco") or sibs_data.get("limit_days_before_start")):
			release_multibanco = sibs_data.get('release_multibanco') or sibs_data.get('limit_days_before_start')

			start_date = datetime.strptime(extra_data.get("start_date"), "%Y-%m-%d")
			time_release_days = start_date - timedelta(days=int(release_multibanco))
			today = datetime.today()
			if today > time_release_days:
				payload['transaction']['paymentMethod'] = ['CARD', 'MBWAY']

		if not personal_data.get("country") == "PT":
			payload['transaction']['paymentMethod'] = ['CARD']

		if extra_data.get("force_token") or float(sibs_data['amount']) == 0.0:
			payload['transaction']['paymentMethod'] = ['CARD']
			payload['transaction']['paymentType'] = "AUTH"
			payload['transaction']['amount']['value'] = 0.0
			payload['merchantInitiatedTransaction'] = {
				"type": "UCOF",
				"amountQualifier": "ESTIMATED"
			}

		headers = self.sibs_header(sibs_data)

		logging.info("[SIBS] payload: %s", json.dumps(payload))
		logging.info("[SIBS] header: %s", json.dumps(headers))
		response = requests.post(f"{sibs_data['endpoint']}/sibs/spg/v2/payments", json.dumps(payload), headers=headers)
		logging.info(response.json())
		audit_response(sibs_data["hotel_code"], "SIBS2.0", sibs_data['id'], sibs_data['sid'], response.text, type_audit="Form Payment", payload=json.dumps(payload))
		response = response.json() if response.status_code == 200 else {}
		return response

	def process_gateway_response(self, hotel_code, gateway_type, response):
		sibs_data = get_payment_gateway_configuration("SIBS2.0 COBRADOR", hotel_code)
		result = {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"GATEWAY_ORDER_ID": "",
			"GATEWAY_PAID_AMOUNT": "",
			"GATEWAY_EXTRA_INFO": {},
			"GATEWAY_RETURN_URL": sibs_data.get("url_ko")
		}

		if Config.DEV:
			result["GATEWAY_RETURN_URL"] = "http://localhost:8090/booking3"

		try:
			headers = self.sibs_header(sibs_data)
			transaction_id = r.values.get('transaction_id')
			logging.info("[SIBS] payload: %s", "%s/sibs/spg/v2/payments/%s/status" % (sibs_data['endpoint'], transaction_id))
			logging.info("[SIBS] header: %s", json.dumps(headers))
			url_get = f"{sibs_data['endpoint']}/sibs/spg/v2/payments/{transaction_id}/status"
			response = requests.get(url_get, headers=headers)
			response_json = response.json()
			logging.info(response_json)
			identifier = response_json.get("merchant", {}).get("merchantTransactionId")
			try:
				audit_response(hotel_code, "SIBS2.0", identifier, request.args.get('sid'), json.dumps(response_json), payload=url_get, type_audit="Process payment")
			except Exception as e:
				logging.error("Error auditing response for in SIBS2.0")
				logging.error("Error auditing: %s", e)

			payment_method = response_json.get("paymentMethod")
			transaction_id = response_json.get("transactionID")
			result["GATEWAY_ORDER_ID"] = response_json.get("merchant", {}).get("merchantTransactionId")
			result["GATEWAY_PAID_AMOUNT"] = response_json.get("amount", {}).get("value")
			result["PAYMENT_GATEWAY_NAME"] = payment_method
			result["GATEWAY_EXTRA_INFO"] = {
				"SIBS": {
					"payment_method": payment_method,
					"transaction_id": transaction_id
				},
				"paymentOrderId": response_json.get("merchant", {}).get("merchantTransactionId")
			}

			payment_status = response_json.get("paymentStatus")

			if payment_method == "CARD" and response_json.get("token"):
				result["GATEWAY_EXTRA_INFO"]["SIBS"]["token"] = response_json["token"]["value"]
				result["GATEWAY_PAID_AMOUNT"] = float(r.args.get("amount_sent_to_gateway", 0.0))

				if result["GATEWAY_PAID_AMOUNT"] > 0.0:
					payload_mit = {
						"merchant": {
							"terminalId": sibs_data['terminal_id'],
							"channel": sibs_data['channel'],
							"merchantTransactionId": result["GATEWAY_ORDER_ID"]
						},
						"transaction": {
							"transactionTimestamp": datetime.utcnow().isoformat() + 'Z',
							"description": result["GATEWAY_ORDER_ID"],
							"amount": {
								"value": result["GATEWAY_PAID_AMOUNT"],
								"currency": "EUR"
							}
						}
					}
					response_mit = requests.post(f"{sibs_data['endpoint']}/api/v2/payments/{transaction_id}/mit", json.dumps(payload_mit), headers=headers)

					if response_mit.status_code == 200:
						transaction_id_mit = response_mit.json().get("transactionID")
						response_capture = requests.post(f"{sibs_data['endpoint']}/api/v2/payments/{transaction_id_mit}/capture", json.dumps(payload_mit), headers=headers)
						if response_capture.status_code == 200:
							result["GATEWAY_EXTRA_INFO"]["SIBS"]["first_transaction_id"] = transaction_id_mit

			if payment_status == "Success":
				result["CODE"] = GATEWAY_SUCESS_RETURNED

			elif payment_status == "Pending" and payment_method == "REFERENCE":
				result["CODE"] = GATEWAY_SUCESS_RETURNED
				limit_time_multibanco = sibs_data.get("limit_time_multibanco", 172800)

				if Config.DEV:
					limit_time_multibanco = 432000

				result["GATEWAY_EXTRA_INFO"]["SIBS"]["MULTIBANCO"] = response_json.get("paymentReference")
				result["GATEWAY_EXTRA_INFO"]["SIBS"]["MULTIBANCO"]["expire_seconds"] = limit_time_multibanco
				expire_date = response_json.get("paymentReference").get("expireDate")
				expire_date_day = "/".join(list(reversed(expire_date.split("T")[0].split("-"))))
				expire_date_time = expire_date.split("T")[1].split(".")[0]
				result["GATEWAY_EXTRA_INFO"]["SIBS"]["MULTIBANCO"]["expireDate"] = f"{expire_date_day} {expire_date_time}"
				result["GATEWAY_PAID_AMOUNT"] = 0.0
				result["GATEWAY_EXTRA_INFO"]["SIBS"]["MULTIBANCO"]["to_pay"] = response_json.get("amount", {}).get("value")

			elif payment_status == "Pending" and payment_method == "MBWAY":
				result["CODE"] = GATEWAY_PENDING_RETURNED
				result["GATEWAY_EXTRA_INFO"]["MBWAY_WAITING"] = True
				result["GATEWAY_RETURN_URL_OK_MBWAY"] = f"{sibs_data.get('url_ok')}?sid={request.args.get('sid')}"
				if Config.DEV:
					result["GATEWAY_RETURN_URL_OK_MBWAY"] = f"http://localhost:8090/booking4?sid={request.args.get('sid')}"

			if result["CODE"] == GATEWAY_SUCESS_RETURNED and not result["GATEWAY_EXTRA_INFO"].get("MBWAY_WAITING"):
				if Config.DEV:
					result["GATEWAY_RETURN_URL"] = "http://localhost:8090/booking4"

				else:
					result["GATEWAY_RETURN_URL"] = sibs_data.get("url_ok")

		except Exception as e:
			pass

		result["GATEWAY_RETURN_URL"] += f"?sid={request.args.get('sid')}"
		if result["CODE"] == GATEWAY_ERROR_CODE_RETURNED:
			result["GATEWAY_RETURN_URL"] += "&errorCode=PAYMENT"
		logging.info(result)
		return result

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


class SIBS2WebhookController:
	def decrypt(self, hotel_code):
		sibs_data = get_payment_gateway_configuration("SIBS2.0 COBRADOR", hotel_code)
		logging.info(r.headers.get("X-Initialization-Vector"))
		logging.info(r.headers.get("X-Authentication-Tag"))
		logging.info(r.data)
		if not r.data:
			logging.warning("No data from webhook")
			return json.dumps({
				"statusCode": "200",
				"statusMsg": "Success",
				"notificationID": ""
			})
		decrypt_key = sibs_data.get("decrypt_webhook")
		logging.info(decrypt_key)
		decrypt_info = self.decrypt_AES_GCM(decrypt_key)
		logging.info(decrypt_info)
		decrypt_json = json.loads(decrypt_info)
		sibs_transaction_data = get_using_entity_and_params("MultibancoTransactions", [("transaction_id", "=", decrypt_json.get("transactionID"))], hotel_code="payment-seeker:")
		if sibs_transaction_data:
			sibs_transaction_data = sibs_transaction_data[0]

			if decrypt_json.get("paymentStatus") == "Success":
					hotel_code = sibs_transaction_data.get("hotel_code")
					identifier = sibs_transaction_data.get("identifier")
					reservation_exists = get_using_entity_and_params("Reservation", search_params=[("identifier", "=", identifier)], hotel_code=hotel_code)

					if decrypt_json.get("paymentMethod") == "REFERENCE" and reservation_exists:
						reservation_exists = reservation_exists[0]
						extra_info = json.loads(reservation_exists.get("extraInfo"))
						if not extra_info.get("status_reservation", "") == "confirmed":
							extra_info["status_reservation"] = "confirmed"
							extra_info["sibs_transaction_datetime"] = decrypt_json.get("transactionDateTime")
							if extra_info.get("SIBS", {}).get("MULTIBANCO"):
								extra_info["SIBS"]["MULTIBANCO"]["status"] = "PAID"
								extra_info["SIBS"]["MULTIBANCO"]["amount"]["value"] = decrypt_json.get("amount", {}).get("value")
								extra_info["payed"] = decrypt_json.get("amount", {}).get("value")

							reservation_exists["extraInfo"] = json.dumps(extra_info)
							save_entity(reservation_exists, hotel_code)

					elif decrypt_json.get("paymentMethod") == "MBWAY" and not reservation_exists:
						requests.get(sibs_transaction_data.get("merchant_url"))

			elif decrypt_json.get("paymentMethod") == "MBWAY" and decrypt_json.get("paymentStatus") == "Declined":
				requests.get(sibs_transaction_data.get("merchant_url") + "&mbway_declined=true")

		return json.dumps({
			"statusCode": "200",
			"statusMsg": "Success",
			"notificationID": decrypt_json.get("notificationID")
		})

	def decrypt_AES_GCM(self, secretKey):
		iv = base64.b64decode(r.headers.get("X-Initialization-Vector"))
		encryptedMsg = base64.b64decode(r.data)
		secretKey = base64.b64decode(secretKey)
		authTag = base64.b64decode(r.headers.get("X-Authentication-Tag"))
		aesCipher = AES.new(secretKey, AES.MODE_GCM, iv)
		plaintext = aesCipher.decrypt_and_verify(encryptedMsg, authTag)
		return plaintext

	def check(self, hotel_code, identifier):
		reservation = get_using_entity_and_params('Reservation', search_params=[("identifier", "=", identifier)], hotel_code=hotel_code)
		response = {
			"found": "KO",
			"cancelled": "false"
		}

		if reservation:
			response["found"] = "OK"
			reservation = reservation[0]
			extra_info = json.loads(reservation.get("extraInfo"))
			if not reservation.get("cancelled") and extra_info.get("gateway_type", "") == "SIBS_MULTIBANCO" and extra_info.get("status_reservation", "") != "confirmed":
				logging.info("Cancel reservation multibanco")
				canceled_in_manager = cancel_reservation_in_manager(reservation.key, hotel_code)

				reservation["cancellationTimestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
				reservation["incidents"] = "SIBS MULTIBANCO CANCELATION"
				if not canceled_in_manager:
					reservation["cancelled"] = True
					save_entity(reservation, hotel_code)
					response["cancelled"] = "true"
				else:
					reservation["cancelled"] = True
					save_entity(reservation, hotel_code)
					response["cancelled"] = "true"

				if response.get('cancelled'):
					hotel = get_hotel_by_application_id(hotel_code)
					url = get_inner_url(hotel)
					send_cancellation_post = f"{url}/utils?action=sendCancelReservation&localizador={identifier}&email={reservation['email']}"
					response_manager = requests.get(send_cancellation_post)
					logging.info(f"Response status: {response_manager.status_code}")

		return response


@app.route("/sibs/get_form", methods=["POST"])
@cross_origin(origin='*')
def sibs2_form():
	data = {x: y for x, y in request.values.items() if not x in ["get_form", "personal_data"]}
	data["extra_data"] = json.loads(data["extra_data"])
	sibs_controller = SIBS2FormControler()
	sibs_form = sibs_controller.build_form_html(**data)
	return sibs_form


class SIBS2Controller(GatewayInterface):

	def get_configuration(self, hotel_code):
		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def sibs_header(self, sibs_data):
		return {
			'Authorization': f"Bearer {sibs_data['bearer']}",
			'X-IBM-Client-Id': sibs_data['x-ibm-client-id'],
			'content-type': "application/json",
			'accept': "application/json",
		}

	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		mit_payment = self.do_post_sibs(hotel_code, reservation, amount, "mit")
		if not "ERROR" in mit_payment:
			return self.do_post_sibs(hotel_code, reservation, amount, "capture", mit_payment)
		return mit_payment

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		return self.do_post_sibs(hotel_code, reservation, amount, "refund", order_to_refund)

	def do_post_sibs(self, hotel_code, reservation, amount, operation, force_transaction_id=None):
		sibs_data = self.get_configuration(hotel_code)
		payload = self.build_payload(sibs_data, reservation, amount)

		extra_info = json.loads(reservation.get("extraInfo"))
		token = extra_info.get("SIBS", {}).get("token")
		payment_method = extra_info.get("SIBS", {}).get("payment_method")
		if operation == "mit" and payment_method == "CARD" and not token:
			return "ERROR: Not valid operation"

		transaction_id = self.reservation_has_token(reservation)
		if force_transaction_id:
			if operation == "refund" and force_transaction_id == reservation["identifier"]:
				transaction_id = extra_info.get("SIBS", {}).get("transaction_id")

			elif force_transaction_id == reservation["identifier"]:
				extra_info = json.loads(reservation["extraInfo"])
				transaction_id = extra_info.get("SIBS", {}).get("first_transaction_id")

			else:
				transaction_id = force_transaction_id

		logging.info(f"Operation: {operation} - identifier: {reservation['identifier']} - transaction: {transaction_id}")
		logging.info(f"Payload: {json.dumps(payload)}")
		if transaction_id:
			response = requests.post(f"{sibs_data['endpoint']}/api/v2/payments/{transaction_id}/{operation}",
									 json.dumps(payload), headers=self.sibs_header(sibs_data))
			logging.info(response.text)

			try:
				audit_response(hotel_code, "SIBS2.0", transaction_id, web_sid="", response_txt=response.text, type_audit="Execute payment")
			except Exception as e:
				logging.error("Error auditing response for in SIBS2.0 Execute Payment")
				logging.error("Error auditing: %s", e)

			if response.status_code == 200:
				response_json = response.json()
				if response_json.get("paymentStatus") == "Success":
					return response_json.get("transactionID")

		return "ERROR"

	def build_payload(self, sibs_data, reservation, amount):
		return {
			"merchant": {
				"terminalId": sibs_data['terminal_id'],
				"channel": sibs_data['channel'],
				"merchantTransactionId": reservation.get("identifier")
			},
			"transaction": {
				"transactionTimestamp": datetime.utcnow().isoformat() + 'Z',
				"description": reservation.get("identifier"),
				"amount": {
					"value": float(amount),
					"currency": "EUR"
				}
			}
		}

	def translate_error(self, error):
		return error

	def reservation_has_token(self, reservation):
		extra_info = json.loads(reservation.get("extraInfo"))
		transaction_id = extra_info.get("SIBS", {}).get("transaction_id")
		token = extra_info.get("SIBS", {}).get("token")
		payment_method = extra_info.get("SIBS", {}).get("payment_method")
		if token or (payment_method == "CARD" and transaction_id):
			return transaction_id

		return False

	def gateway_has_token(self):
		return True

	def get_initial_order_id_from_extra_info(self, reservation_extra_info):
		return reservation_extra_info.get("paymentOrderId")