import json
import logging

import Crypto
import base64
import Crypto.Random
import requests
from Cryptodome.Cipher import AES
from hashlib import md5, sha256
from urllib.parse import urlencode, quote_plus
from Cryptodome.Util.Padding import pad, unpad

#from Cryptodome.Util.Padding import pad
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, \
	GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, get_integration_name, audit_response
from paraty.utilities.templates.templates_processor import build_template
from xml.etree.ElementTree import fromstring


def prepare_params(params, passwd):
	# pad = lambda s: s + (AES.block_size - len(s) % AES.block_size) * chr(AES.block_size - len(s) % AES.block_size)
	params = urlencode(params, quote_via=quote_plus)

	iv = Crypto.Random.OSRNG.posix.new().read(AES.block_size)
	ivb64 = base64.b64encode(iv).decode("utf-8")

	try:
		hash_params = sha256(params.encode('utf8')).hexdigest()
		aes_cipher = AES.new(passwd, AES.MODE_CBC, iv)
		encrypted_data_utf8 = base64.b64encode(
			aes_cipher.encrypt(pad(params.encode("utf-8"), block_size=AES.block_size))).decode("utf-8")
	except Exception as e:
		print(e)

	return encrypted_data_utf8, ivb64, hash_params


class Addons2GatewayController:
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		integration_name = get_integration_name(hotel_code)
		integration = get_payment_gateway_configuration(integration_name, hotel_code)

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

		request_params = {
			"merchantId": int(integration.get("merchant_id", 0)),
			"productId": int(integration.get("product_id", 0)),
			"paymentSolution": "creditcards",
			"operationType": "DEBIT",
			"amount": float(amount),
			"merchantExemptionsSca": "TRA",
			"currency": "EUR",
			"country": "ES",
			"description": "",
			"merchantTransactionId": reservation.get("identifier"),
			"customerId": reservation.get("identifier"),
			"language": "ES",
			"internalToken": extra_info.get("addons2", {}).get("cardToken")
		}

		params, iv, integrity = prepare_params(request_params, integration.get("shared_passwd", ""))

		headers = {
			"encryptionMode": "CBC",
			"iv": iv,
			"APIVersion": "3"
		}
		query = {
			"merchantId": int(integration.get("merchant_id", 0)),
			"encrypted": params,
			"integrityCheck": integrity
		}

		response = requests.post("https://checkout-stg.easypaymentgateway.com/EPGCheckout/rest/online/pay",
								 params=query, headers=headers)

		audit_response(hotel_code, integration_name, reservation.get("identifier"), "", response.text, type_audit="Process payment", payload=json.dumps(request_params))

		#
		if response.status_code == 200:

			xml = fromstring(response.text)
			transaction_id = xml.find(".//operation/payFrexTransactionId")
			return transaction_id.text

		return GATEWAY_ERROR_RETURNED + response.status_code + SEPARATOR_INFO_ERROR_ORDER + order_id

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		integration_name = get_integration_name(hotel_code)
		integration = get_payment_gateway_configuration(integration_name, hotel_code)

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

		request_params = {
			"merchantId": int(integration.get("merchant_id", 0)),
			"transactionId": extra_info.get("addons2", {}).get("payFrexTransactionId"),
			"paymentSolution": extra_info.get("addons2", {}).get("paymentSolution"),
			"amount": amount,
			"merchantTransactionId": reservation.get("identifier")
		}

		params, iv, integrity = prepare_params(request_params, integration.get("shared_passwd", ""))

		headers = {
			"encryptionMode": "CBC",
			"iv": iv,
			"APIVersion": "3"
		}
		query = {
			"merchantId": int(integration.get("merchant_id", 0)),
			"encrypted": params,
			"integrityCheck": integrity
		}

		response = requests.post("https://checkout-stg.easypaymentgateway.com/EPGCheckout/rest/online/rebate",
								 params=query, headers=headers)

		audit_response(hotel_code, integration_name, reservation.get("identifier"), "", response.text, type_audit="Process refund", payload=json.dumps(request_params))

		if response.status_code == 200:
			return order_to_refund

		return GATEWAY_ERROR_RETURNED

	def reservation_has_token(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			# and str(extra_info.get("sermepa_cof_txnid", "")) #maybe is is not mandatory.
			if extra_info.get("addons2", {}).get("cardToken"):
				return extra_info.get("addons2", {}).get("cardToken")

		return False

	def gateway_has_token(self):
		return True

	def get_initial_order_id_from_extra_info(self, reservation_extra_info):
		return reservation_extra_info.get("paymentOrderId")

	def translate_error(self, error):
		return error


class Addons2FormController:

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)

		merchant_url = integration.get("merchant_url", "")

		merchant_url_params = {
			"sid": sid,
			"reservation": payment_order_id
		}

		if "?" in merchant_url:
			merchant_url = merchant_url + "&" + urlencode(merchant_url_params, quote_via=quote_plus)
		else:
			merchant_url = merchant_url + "?" + urlencode(merchant_url_params, quote_via=quote_plus)

		url_ok = integration.get("url_ok", "")

		if "?" in url_ok:
			url_ok = url_ok + "&" + urlencode({"sid": sid}, quote_via=quote_plus)
		else:
			url_ok = url_ok + "?" + urlencode({"sid": sid}, quote_via=quote_plus)

		url_ko = integration.get("url_ko", "")

		if "?" in url_ko:
			url_ko = url_ko + "&" + urlencode({"sid": sid}, quote_via=quote_plus)
		else:
			url_ko = url_ko + "?" + urlencode({"sid": sid}, quote_via=quote_plus)



		request_params = {
			"merchantId": int(integration.get("merchant_id", 0)),
			"productId": int(integration.get("product_id", 0)),
			"paymentSolution": "creditcards",
			"operationType": "DEBIT",
			"amount": float(amount),
			"paymentRecurringType": "delayed",
			"merchantExemptionsSca": "TRA",
			"currency": "EUR",
			"country": "ES",
			"description": "",
			"merchantTransactionId": payment_order_id,
			"customerId": payment_order_id,
			"language": "ES",
			"statusURL": merchant_url,
			"successURL": url_ok,
			"errorURL": url_ko,
			"cancelURL": url_ko,
			"checkoutTitle": ""
		}

		params, iv, integrity = prepare_params(request_params, integration.get("shared_passwd", ""))

		headers = {
			"encryptionMode": "CBC",
			"iv": iv,
			"APIVersion": "3"
		}
		query = {
			"merchantId": int(integration.get("merchant_id", 0)),
			"encrypted": params,
			"integrityCheck": integrity
		}

		response = requests.post("https://checkout-stg.easypaymentgateway.com/EPGCheckout/rest/online/tokenize", params=query, headers=headers)

		audit_response(hotel_code, gateway_type, payment_order_id, sid, response.text, type_audit="Build form", payload=json.dumps(request_params))

		if response.status_code == 200:

			context = {
				"payment_gateway_url": response.text
			}

			template_path = "pages/cobrador/gateways/addons2_insite_form.html"
			form_gateway = build_template(template_path, context, False)

			return form_gateway

		return "KO"

	def process_gateway_response(self, hotel_code, gateway_type, response):
		xml = fromstring(response)

		cobrador_response = {
			"CODE": "",
			"GATEWAY_ORDER_ID": "",
			"GATEWAY_PAID_AMOUNT": ""
		}

		status_code = xml.find(".//code")
		gateway_order_id = xml.find(".//merchantTransactionId")
		amount = xml.find(".//amount")
		card_token = xml.find(".//operation/paymentDetails/cardNumberToken")
		transaction_id = xml.find(".//operation/payFrexTransactionId")
		payment_solution = xml.find(".//operation/paymentSolution")
		subscription_plan = xml.find(".//operation/subscriptionPlan")

		audit_response(hotel_code, gateway_type, gateway_order_id, "", response, payload="{}")

		if status_code.text:
			if int(status_code.text or -1) == 0:
				cobrador_response["CODE"] = GATEWAY_SUCESS_RETURNED
				cobrador_response["GATEWAY_ORDER_ID"] = gateway_order_id.text
				cobrador_response["GATEWAY_PAID_AMOUNT"] = float(amount.text)

				if card_token != None and transaction_id != None and payment_solution != None:
					cobrador_response["GATEWAY_EXTRA_INFO"] = {
						"addons2": {
							"cardToken": card_token.text,
							"payFrexTransactionId": transaction_id.text,
							"paymentSolution": payment_solution.text,
							"subscriptionPlan": subscription_plan.text if subscription_plan else ""
						}
					}
			else:
				cobrador_response["CODE"] = GATEWAY_ERROR_CODE_RETURNED
				reason = xml.find(".//operation/message")
				if reason:
					logging.info("[addons2][%s][%s] %s" % (status_code.text, str(gateway_order_id), reason.text))
		else:
			cobrador_response["CODE"] = GATEWAY_ERROR_CODE_RETURNED

		return cobrador_response

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT
