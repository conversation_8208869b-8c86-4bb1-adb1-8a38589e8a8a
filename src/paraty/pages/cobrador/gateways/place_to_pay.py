import datetime
import hashlib
import json
import string
import random
import base64
import uuid
from urllib.parse import urlencode

import requests
from flask import request, redirect
from paraty import app
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED, \
	GATEWAY_PENDING_RETURNED, CONTENT_TYPE_TEXT, COBRADOR_QUEUE
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, \
    limit_amount_excedeed, get_limit_error_message, get_amount_to_pay, audit_response
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.utilities import session_utils
from paraty.utilities.languages import language_utils
from paraty.utilities.languages.language_utils import SPANISH
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3 import queue_utils
from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_utils import get_inner_url
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.session.session_utils import get_session_from_hotel
MOCKED_STATUS_REJECTED = {
    "status": "REJECTED",
    "reason": "XN",
    "message": "Se ha rechazado la petición",
    "date": "2024-11-14T22:05:30+00:00"
  }
MOCKED_STATUS_APPROVED = {
    "status": "APPROVED",
    "reason": "00",
    "message": "La petición ha sido aprobada exitosamente",
    "date": "2024-11-14T22:05:30+00:00"
  }
MOCKED_REQUEST_ID = *********
MOCKED_REFERENCE = "33347473"
MOCKED_AMOUNT = 2391.9
MOCKED_EXTERNAL_ID = 23456
MOCKED_REJECTED_RESPONSE = {
  "requestId": MOCKED_REQUEST_ID,
  "status": MOCKED_STATUS_APPROVED,
  "request": {
    "locale": "es_CO",
    "payer": {
      "document": "1122334455",
      "documentType": "CC",
      "name": "John",
      "surname": "Doe",
      "company": "Evertec",
      "email": "<EMAIL>",
      "mobile": "+5731111111111",
      "address": {
        "street": "Calle falsa 123",
        "city": "Medellín",
        "state": "Poblado",
        "postalCode": "55555",
        "country": "Colombia",
        "phone": "+573111111111"
      }
    },
    "buyer": {
      "document": "1122334455",
      "documentType": "CC",
      "name": "John",
      "surname": "Doe",
      "company": "Evertec",
      "email": "<EMAIL>",
      "mobile": "+5731111111111",
      "address": {
        "street": "Calle falsa 123",
        "city": "Medellín",
        "state": "Poblado",
        "postalCode": "55555",
        "country": "Colombia",
        "phone": "+573111111111"
      }
    },
    "payment": {
      "reference": "12345",
      "description": "Prueba de pago",
      "amount": {
        "currency": "COP",
        "total": MOCKED_AMOUNT,
        "taxes": [
          {
            "kind": "valueAddedTax",
            "amount": 1000,
            "base": 0
          }
        ],
        "details": [
          {
            "kind": "discount",
            "amount": 1000
          }
        ]
      },
      "allowPartial": False,
      "shipping": {
        "document": "1122334455",
        "documentType": "CC",
        "name": "John",
        "surname": "Doe",
        "company": "Evertec",
        "email": "<EMAIL>",
        "mobile": "+5731111111111",
        "address": {
          "street": "Calle falsa 123",
          "city": "Medellín",
          "state": "Poblado",
          "postalCode": "55555",
          "country": "Colombia",
          "phone": "+573111111111"
        }
      },
      "items": [
        {
          "sku": "12345",
          "name": "product_1",
          "category": "physical",
          "qty": "1",
          "price": 1000,
          "tax": 0
        }
      ],
      "fields": [
        {
          "keyword": "_test_field_value_",
          "value": "_test_field_",
          "displayOn": "approved"
        }
      ],
      "recurring": {
        "periodicity": "D",
        "interval": "1",
        "nextPayment": "2019-08-24",
        "maxPeriods": 1,
        "dueDate ": "2019-09-24",
        "notificationUrl ": "https://checkout.placetopay.com"
      },
      "subscribe": False,
      "dispersion": [
        {
          "agreement": "1299",
          "agreementType": "MERCHANT",
          "amount": {
            "currency": "USD",
            "total": 200
          }
        }
      ],
      "modifiers": [
        {
          "type": "FEDERAL_GOVERNMENT",
          "code": 17934,
          "additional": {
            "invoice": "123345"
          }
        }
      ]
    },
    "subscription": {
      "reference": "12345",
      "description": "Ejemplo de descripción",
      "fields": {
        "keyword": "1111",
        "value": "lastDigits",
        "displayOn": "none"
      }
    },
    "fields": [
      {
        "keyword": "_processUrl_",
        "value": "https://checkout.redirection.test/session/1/a592098e22acc709ec7eb30fc0973060",
        "displayOn": "none"
      }
    ],
    "paymentMethod": "visa",
    "expiration": "2019-08-24T14:15:22Z",
    "returnUrl": "https://commerce.test/return",
    "cancelUrl": "https://commerce.test/cancel",
    "ipAddress": "127.0.0.1",
    "userAgent": "PlacetoPay Sandbox",
    "skipResult": False,
    "noBuyerFill": False,
    "type": "checkin"
  },
  "payment": [
    {
      "status": {
        "status": "APPROVED",
        "reason": "00",
        "message": "La petición ha sido aprobada exitosamente",
        "date": "2022-07-27T14:51:27-05:00"
      },
      "internalReference": MOCKED_EXTERNAL_ID,
      "reference": MOCKED_REFERENCE,
      "paymentMethod": "visa",
      "paymentMethodName": "Visa",
      "issuerName": "JPMORGAN CHASE BANK, N.A.",
      "amount": {
        "from": {
          "currency ": "COP",
          "total ": 10000
        },
        "to": {
          "currency ": "COP",
          "total ": 10000
        },
        "factor": 1
      },
      "receipt": "************",
      "franchise": "PS_VS",
      "refunded": False,
      "authorization": "965960",
      "processorFields": [
        {
          "keyword": "1111",
          "value": "lastDigits",
          "displayOn": "none"
        }
      ],
      "dispersion": None,
      "agreement": None,
      "agreementType": None,
      "discount": {
        "base": 3000,
        "code": "17934",
        "type": "FRANCHISE",
        "amount": 1000
      },
      "subscription": None
    }
  ],
  "subscription": {
    "status": {
      "status": "OK",
      "reason": "00",
      "message": "La petición ha sido aprobada exitosamente",
      "date": "2022-07-27T14:51:27-05:00"
    },
    "type": "token",
    "instrument": [
      {
        "keyword": "token",
        "value": "a3bfc8e2afb9ac5583922eccd6d2061c1b0592b099f04e352a894f37ae51cf1a",
        "displayOn": "none"
      },
      {
        "keyword": "subtoken",
        "value": "****************",
        "displayOn": "none"
      },
      {
        "keyword": "franchise",
        "value": "visa",
        "displayOn": "none"
      },
      {
        "keyword": "franchiseName",
        "value": "Visa",
        "displayOn": "none"
      },
      {
        "keyword": "issuerName",
        "value": "JPMORGAN CHASE BANK, N.A.",
        "displayOn": "none"
      },
      {
        "keyword": "lastDigits",
        "value": "1111",
        "displayOn": "none"
      },
      {
        "keyword": "validUntil",
        "value": "2029-12-31",
        "displayOn": "none"
      },
      {
        "keyword": "installments",
        "value": None,
        "displayOn": "none"
      }
    ]
  }
}



def get_session(sid, hotel_code):
	query = {
		"action": "temporal_read_session",
		"namespace": hotel_code,
		"sid": sid
	}

	hotel = get_hotel_by_application_id(hotel_code)
	ruta = get_inner_url(hotel)

	result = {}

	query_string = urlencode(query)

	url = "%s/utils?%s" % (ruta, query_string)

	response = requests.get(url)

	if response.status_code == 200:
		response_json = response.json()

		result = response_json

	return result

def _generate_expiration_for_TPV(increment_seconds=900):
	today = datetime.datetime.today()
	expiry_date = today + datetime.timedelta(0, increment_seconds)
	expire = expiry_date.replace(microsecond=0)
	return "{}+00:00".format(expire.isoformat())

def _generate_expiration():
	today = datetime.datetime.today()
	expiry_date = today + datetime.timedelta(0, 900)
	expire = expiry_date.replace(microsecond=0)
	return "{}+00:00".format(expire.isoformat())

def _generate_auth(gateway_config):
    #SEED:
    seed = datetime.datetime.now(datetime.timezone.utc).isoformat()

    #NONCE AND RAW_NONCE
    raw_nonce = random.getrandbits(128).to_bytes(16, byteorder="big")
    nonce = base64.b64encode(raw_nonce).decode("utf-8")

    #TRANKEY
    key = raw_nonce + seed.encode() + gateway_config.get("merchant_code").encode()
    hashed_key = hashlib.sha256(key).digest()
    encoded_tranKey = base64.b64encode(hashed_key).decode("utf-8")

    auth_structure = {
            "login": gateway_config.get("login"),
            "seed": seed,
            "nonce": nonce,
            "tranKey": encoded_tranKey
    }

    return auth_structure, seed, nonce, encoded_tranKey


def _generate_seed():
	naive = datetime.datetime.today().replace(microsecond=0)
	seed = "{}+00:00".format(naive.isoformat())

	return seed


def _generate_nonce():
	stryu = string.ascii_uppercase + string.ascii_lowercase + string.digits
	nonce = ''.join(random.choice(stryu) for _ in range(32))
	return nonce

def request_result(config, request_id):
    url = "%s/%s" % (config.get("payment_url", ""), request_id)
    auth_structure, seed, nonce, encoded_tranKey = _generate_auth(config)
    post_data = {
        "auth":auth_structure
    }
    headers = {
		'Content-Type': 'application/json'
	}
    response = requests.post(url, json.dumps(post_data), headers=headers)
    if response:
        response_json = json.loads(response.text)
        return response_json
    return {}

def get_payment_info(payment_url, data, hotel_code, form_data):
    #make sure that expiration and seed are always update at last time
    data["expiration"] = _generate_expiration_for_TPV()
    logging.info(f"[PLACETOPAY][%s] Built data for send to payment url: %s" %(data.get("payment", {}).get("reference"), data))
    headers = {
        'Content-Type': 'application/json'
    }
    response = requests.post(payment_url, json.dumps(data), headers=headers)

    logging.info("[PLACETOPAY] response from place to pay: %s" % response.text)
    if 'OK' in response.text:
        session_response = json.loads(response.text)
        logging.info('[PLACETOPAY] New session processed successfully!!. Response: %s' % response.text)

        response = {
            "process_url": session_response.get('processUrl'),
            "request_id": session_response.get('requestId')
        }
    else:
        session_response = json.loads(response.text)
        status = session_response.get('status')
        logging.error("[PLACETOPAY][%s] Error initializing a new seasson. Error received from TPV: %s" % (data.get("payment", {}).get("reference"), session_response))
        response = {
            "error": "PAYMENT",
            "response": response.text,
            "message": status.get("message")
        }

    return response


class PlaceToPayFormController(FormGatewayInterface):
    check_payment_url = "https://payment-seeker.ew.r.appspot.com/placetopay/check_payment"
    def addSidToUrlOKandKO(self, url, sid):
        if "?" in url:
            return url + "&sid=" + sid
        else:
            return url + "?sid=" + sid
    def _check_signature(self, signature, request_id, status, date, merchant_code):
        if not signature:
            return False
        try:
            checked_signature = hashlib.sha1(request_id + status + date + merchant_code) == signature
        except Exception as e:
            logging.warning(e)
            combined_string = str(request_id) + status + date + merchant_code
            hashed_signature = hashlib.sha1(combined_string.encode('utf-8')).hexdigest()
            checked_signature = hashed_signature == signature
        return checked_signature


    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        logging.info("[PLACETOPAY][%s] Creating Gateway Form" % (payment_order_id or extra_data.get("original_identifier")))
        config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)

        merchant_url = self.addSidToUrlOKandKO(config.get("merchant_url"), sid)
        merchant_url + "&reservation=%s" % payment_order_id

        user_info = get_session_from_hotel(hotel_code, sid)
        language = extra_data.get("language", SPANISH)
        currency = config.get("default_currency", "COP")
        #be careful with gateways that currency comes from configs (not from request)
        num_nights = extra_data.get("num_nights")
        if limit_amount_excedeed(hotel_code, currency, num_nights, amount, sid=sid):
            return get_limit_error_message(language)
        context = {
            "form": {
                "Ds_Merchant_Currency": currency,
				"Ds_Merchant_Amount": get_amount_to_pay(amount, prices_per_day=extra_data.get("prices_per_day")),
				"Ds_Merchant_MerchantCode": config.get("merchant_code", ""),
				"Ds_Merchant_MerchantName": config.get("merchant_name", ""),
				"Ds_Merchant_UrlOK": self.addSidToUrlOKandKO(config.get("url_ok", ""), sid),
				"Ds_Merchant_UrlKO": self.addSidToUrlOKandKO(config.get("url_ko", ""), sid),
				"DS_Merchant_Real_UrlKo": self.addSidToUrlOKandKO(config.get("real_url_ko", ""), sid),
				"Ds_Merchant_MerchantURL": merchant_url,
				"Ds_Merchant_Expiration": _generate_expiration(),
				"Ds_Merchant_Logo": config.get("logo", ""),
				"Ds_Merchant_ipAddress": user_info.get("request_info", {}).get("ip", "*************"),
				"Ds_Merchant_UserAgent": user_info.get("request_info", {}).get("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/111.0"),
				"Ds_Merchant_ConsumerLanguage": config.get("default_language"),
				"Ds_Merchant_Order": payment_order_id,
				"DS_Login": config.get("login"),
				"Ds_Merchant_Payment_Url": config.get("payment_url", "")
			},
			"sid": sid,
			"hotel_code": hotel_code,
			"gateway_type": gateway_type,
			"language": language
		}
        template = "pages/cobrador/gateways/place_to_pay_insite_form.html"
        return build_template(template, context, extra_data.get("language", "SPANISH"))

    def process_gateway_response(self, hotel_code, gateway_type, response):
        body = dict(request.get_json())
        logging.info("[PLACETOPAY] Body params: %s", body)
        if not isinstance(body.get("response"), dict):
            try:
                response = json.loads(body.get("response"))
            except Exception as e:
                logging.warning(e)
                logging.warning("[PLACETOPAY] Error trying to get response: %s" % body.get("response"))
                logging.info("[PLACETOPAY] Trying to decode with base64")
                response = json.loads(base64.b64decode(body.get("response")).decode('utf-8'))
                logging.info("[PLACETOPAY] response decoded second try: %s", response)
        else:
            response = body.get('response')
        if response.get("CODE") == "PENDING":
            logging.info(f"[PLACETOPAY][{response.get('GATEWAY_ORDER_ID')}] We are processing a recursive pending response from cobrador, happy flow, don`t worry!")
            return response
        # response = response.json()
        config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
        sid = request.args.get("sid") or response.get("sid")

        identifier = response.get("reference")

        try:
            gateway_type = "PLACE_TO_PAY"
            audit_response(hotel_code, gateway_type, identifier, sid, json.dumps(response))

        except Exception as e:
            logging.error("Error auditing response for in PLACE_TO_PAY Process Response")
            logging.error("Error auditing: %s", e)


        # amount is not always received.
        # In hotel webs we have to check that pending reservation doesn't matter to receive a null
        # SO NEVER return a 0 because we will remove the payed amount in pending reservation
        amount = response.get("amount")
        payment_order_id = response.get("reference")
        request_id = response.get("requestId")
        status_structure = response.get('status')
        status = status_structure.get("status")
        signature = response.get("signature", None)
        new_extraInfo_key = "placetopay_transaction_" + payment_order_id
        to_extra_info = {
            "payment_gateway": gateway_type
        }
        cobrador_response = {
            "GATEWAY_ORDER_ID" : identifier,
			"GATEWAY_PAID_AMOUNT": amount
		}
        if response.get("external_id"):
            cobrador_response["GATEWAY_EXTERNAL_ID"] = response.get("external_id")
        reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",[("identifier", "=", str(identifier), ("hotel_code", "=", hotel_code))], hotel_code="payment-seeker:")
        if reservation_metadata:
            reservation_metadata = reservation_metadata[0]
            try:
                extra_info = json.loads(reservation_metadata.get("extraInfo", "{}"))
            except Exception as e:
                extra_info = {}
            if not amount:
                amount = extra_info.get("amount_sent_to_gateway", 0)
            if request_id == extra_info.get("request_id") and identifier == str(reservation_metadata.get("identifier")):
                to_extra_info[new_extraInfo_key] = {
					"request_id": request_id,
					"sid": sid,
					"placetopay_order_id": response.get("external_id"),
					"no_redirect": True
				}
                #TODO: check if incomming signature matches with our generated signature
                valid_signature = True
                if signature:
                    valid_signature = self._check_signature(signature, request_id, status, status_structure.get("date"), config.get("merchant_code"))
                if not valid_signature or status == "REJECTED":
                    logging.info("[PLACETOPAY][%s] Error processing the payment. Maybe we have receive a REJECTED payment or the signature is not authentic",payment_order_id)
                    cobrador_response["CODE"] = GATEWAY_ERROR_CODE_RETURNED
                    cobrador_response["GATEWAY_ERROR_MESSAGE"] = status_structure.get("message", "Error")

                if status == "APPROVED":
                    logging.info("[PLACETOPAY][%s] Succesfull payment received from TPV",payment_order_id)
                    cobrador_response["CODE"] = GATEWAY_SUCESS_RETURNED

                cobrador_response["GATEWAY_ORDER_ID"] = payment_order_id
                cobrador_response["GATEWAY_PAID_AMOUNT"] = amount
                cobrador_response["GATEWAY_EXTRA_INFO"] = to_extra_info
                cobrador_response["IDENTIFIER"] = payment_order_id
        return cobrador_response

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT



@app.route("/placetopay/redirect", methods=["POST"])
def redirect_to_placetopay():
    form_data = request.form
    url_to_redirect = None
    logging.info("[PLACETOPAY][%s] Redirecting user to session url" % form_data.get("Ds_Merchant_Order"))
    hotel_code = form_data.get("hotel_code")
    hotel = get_hotel_by_application_id(hotel_code)
    payment_order_id = form_data.get("Ds_Merchant_Order",0)

    payment_url = form_data.get("payment_url")

    gateway_type = form_data.get("gateway_type")

    config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)

    hotel_name = get_hotel_advance_config_item(hotel, "Email sender")
    if hotel_name:
        hotel_name = hotel_name[0].get("value", "")
    else:
        hotel_name = ""

    translations = language_utils.get_web_dictionary(form_data.get("language", SPANISH))
    auth_structure, seed, nonce, encoded_tranKey = _generate_auth(config)

    request_payload = {
		"auth": auth_structure,
		"locale": form_data.get("Ds_Merchant_ConsumerLanguage"),
		"payment": {
			"reference": form_data.get("Ds_Merchant_Order"),
			"description": "",
			"amount": {
				"currency": form_data.get("Ds_Merchant_Currency"),
				"total": float(form_data.get("Ds_Merchant_Amount"))
			},
		},
		"fields": {
			"keyword": "sid",
			"value": form_data.get("sid")
		},
		"expiration": form_data.get("Ds_Merchant_Expiration"),
		"returnUrl": form_data.get("Ds_Merchant_UrlOK"),
		"cancelUrl": form_data.get("Ds_Merchant_UrlKO"),
		"userAgent": form_data.get("Ds_Merchant_UserAgent"),
		"ipAddress": form_data.get("Ds_Merchant_ipAddress"),
		"notificationUrl": form_data.get("Ds_Merchant_MerchantURL")
	}

    response = get_payment_info(payment_url, request_payload, hotel_code, form_data)

    if not response.get("error"):
        logging.info("[PLACETOPAY][%s] We have a succesfull response from TPV to redirect user to gateway" % payment_order_id)
        request_id = response.get("request_id")
        url_to_redirect = response.get("process_url")
        reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata", [("identifier", "=", str(payment_order_id), ("hotel_code", "=", hotel_code))], hotel_code="payment-seeker:")
        if not reservation_metadata:
            amount_sent_to_gateway = float(form_data.get("Ds_Merchant_Amount"))
            extra_info = {
                "request_id": request_id,
                "amount_sent_to_gateway": amount_sent_to_gateway
            }
            properties = {
                "sid": form_data.get("sid", ""),
                "identifier": str(payment_order_id),
                "hotel_code": form_data.get("hotel_code"),
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "extraInfo": json.dumps(extra_info)
            }
            datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")
    else:
        logging.info("[PLACETOPAY] Cannot send user to gateway due to an error in TPV")
        real_urlko = form_data.get("Ds_Merchant_UrlKO", "")
        if real_urlko:
            error_message = base64.urlsafe_b64encode(response.get("message",translations.get("T_ERROR_TPV_REDIRECT")).encode()).decode()
            real_urlko += "&errorCode=PAYMENT&errorForcedMessage=%s" % (error_message)
            if "?" not in real_urlko:
                real_urlko = real_urlko.replace("&", "?", 1)
            return redirect(real_urlko, 302)
    if url_to_redirect:
        logging.info("[PLACETOPAY][%s] Redirecting user to gateway" % payment_order_id)
        header = {
            'Content-Type': "text/plain"
        }
        merchant_url = form_data.get("Ds_Merchant_MerchantURL")
        simulated_gateway_response = {
            "CODE": GATEWAY_PENDING_RETURNED,
            "GATEWAY_ORDER_ID":  form_data.get("Ds_Merchant_Order"),
            "GATEWAY_PAID_AMOUNT": float(form_data.get("Ds_Merchant_Amount")),
            "IDENTIFIER": form_data.get("Ds_Merchant_Order"),
            "GATEWAY_EXTRA_INFO": {}
		}
        logging.info("[PLACETOPAY][%s] Payload to create a pending reservation: %s" % (payment_order_id, simulated_gateway_response))
        simulated_gateway_response = base64.b64encode(json.dumps(simulated_gateway_response).encode('utf-8'))
        # We say to HW do pending reservation
        requests.post(merchant_url, data=simulated_gateway_response, headers=header)
        payload = {
            "body" : {
                "hotel_code": hotel_code,
                "num_retry": 0,
                "payment_order_id": form_data.get("Ds_Merchant_Order"),
                "identifier": form_data.get("Ds_Merchant_Order"),
                "request_id": response.get("request_id"),
                "gateway_type": gateway_type,
                "amount": float(form_data.get("Ds_Merchant_Amount")),
                "sid": form_data.get("sid")
            },
            "merchant_url": PlaceToPayFormController.check_payment_url
        }

        data = {
            "task_id": str(uuid.uuid4()),
            "data": base64.urlsafe_b64encode(json.dumps(payload).encode("utf-8")).decode("utf-8"),
        }
        logging.info("[PLACETOPAY][%s] Creating task to ask about the payment with this payload: %s" % (payment_order_id, data))
        logging.info("[PLACETOPAY][%s] Creating task to ask about the payment with this data: %s" % (payment_order_id, payload))
        #First call in 10 minutes. We give the customer time to complete the payment
        queue_utils.create_task('execute_fallback_task', json.dumps(data), queue_name=COBRADOR_QUEUE, task_name=f'check_placetopay_payment_task__{int(datetime.datetime.now().timestamp() * 1000)}_{hotel_code}', in_seconds=300)
        return redirect(url_to_redirect, 302)


@app.route("/placetopay/check_payment", methods=["POST"])
def check_placetopay_payment():
    logging.info("[PLACETOPAY] Checking if payment has been processed")
    body = json.loads(request.data)
    if not isinstance(body, dict):
        body = json.loads(body)
    hotel_code = body.get("hotel_code")
    gateway_type = body.get("gateway_type")
    sid = body.get("sid")
    config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
    payment_order_id = body.get("payment_order_id")
    reservation = datastore_communicator.get_using_entity_and_params("Reservation", search_params=[("identifier", "=", payment_order_id)], hotel_code=hotel_code)
    if not reservation:
        logging.info("[PLACETOPAY][%s] Alternative way to get reservation" % payment_order_id)
        from_date = datetime.datetime.now() - datetime.timedelta(days=2)
        from_date = from_date.strftime("%Y-%m-%d 00:00:00")
        candidated_reservation_list = datastore_communicator.get_using_entity_and_params("Reservation", search_params=[("timestamp", ">=", from_date)],hotel_code=hotel_code)
        candidated_reservation = [x for x in candidated_reservation_list if payment_order_id in x.get("identifier")]
        if candidated_reservation:
            extra_info = json.loads(candidated_reservation[0].get("extraInfo"))
            if payment_order_id == extra_info.get("paymentOrderId"):
                reservation = candidated_reservation
    if len(reservation) > 0:
        reservation = reservation[0]
        extra_info = json.loads(reservation.get("extraInfo", "{}"))
        if extra_info.get("status_reservation") != "pending":
            logging.info("[PLACETOPAY][%s] Status reservation has changed, please check it at first" % payment_order_id)
            return "OK"
    else:
        logging.error("[PLACETOPAY][%s] Something went wrong while recoverying reservation from hotel. Check if it has been created previously or that it is not too old")
        return "KO"

    logging.info("[PLACETOPAY] Checking if payment %s has been processed" % payment_order_id)
    logging.info("[PLACETOPAY][%s] Body received: %s" % (payment_order_id, body))
    num_retry = body.get("num_retry", "")
    logging.info("[PLACETOPAY][%s] num_retry received: %s" % (payment_order_id, num_retry))
    if not num_retry:
        num_retry = "0"
        body["num_retry"] = 0
    num_retry = int(num_retry)

    logging.info("[PLACETOPAY][%s] Checking if we have an APPROVED payment. Num retry: %s" % (payment_order_id, body.get("num_retry")))

    in_seconds = 120 #by default check again in 2 mnutes
    if ("num_retry" in body and int(body.get("num_retry")) == 0):
        in_seconds = 300 #next try: 5 minutes
        num_retry += 1
    if "num_retry" in body and int(body.get("num_retry")) == 1:
        in_seconds = 3600 #next try: 1 hour
        num_retry += 1
    if "num_retry" in body and int(body.get("num_retry")) == 2:
        in_seconds = 18000  #next try: 5 hour
        num_retry += 1
    if "num_retry" in body and int(body.get("num_retry")) == 3:
        in_seconds = 28800 #next try: 8 hours
        num_retry += 1
    if "num_retry" in body and int(body.get("num_retry")) == 4:
        in_seconds = 57600 #Next try: 16 hours
        num_retry += 1
    if "num_retry" in body and int(body.get("num_retry")) == 5:
        # Last retry: we don't create a new task
        logging.info("[PLACETOPAY][%s] MAX TRIES!!: %s" % (payment_order_id, body.get("num_retry")))
        body["num_retry"] = "MAX"

    logging.info("[PLACETOPAY][%s] Next num_try calculated: %s in_seconds: %s" % (payment_order_id, num_retry, in_seconds))

    merchant_url = PlaceToPayFormController.addSidToUrlOKandKO(self=None, url=config.get("merchant_url"), sid=sid)
    logging.info("[PLACETOPAY][%s] merchant_url to create task: %s" % (payment_order_id, merchant_url))
    body["merchant_url"] = PlaceToPayFormController.check_payment_url
    if body.get("request_id"):
        request_id = body.get("request_id")
        logging.info("[PLACETOPAY][%s] Signature Correct!  request id: %s" % (payment_order_id, request_id))
        try:
            response = request_result(config, request_id)
            logging.info("[PLACETOPAY][%s] RESPONSE received: %s" % (payment_order_id, response))
        except Exception as e:
            logging.error(e)
            logging.error("[PLACETOPAY][%s] Oh no, something went wrong checking the status of payment!!!" % payment_order_id)
            response = None

        if not isinstance(response, dict):
            logging.error(
                "[PLACETOPAY][%s] Oh no!! BAD RESPONSE RECEIVED: %s" % (payment_order_id, response))
            response = None
        if response:
            status = response.get('status')
            stop_because_i_know_whats_happening = status.get('status') == "APPROVED" or status.get('status') == "REJECTED" or status.get('status') == "PENDING"
            if stop_because_i_know_whats_happening:
                payment = response.get('payment')
                if payment and len(payment) > 0:
                    payment = payment[0]
                else:
                    payment = response.get("request", {}).get("payment", {})
                gateway_response = {
                    'status': {
                        "status": status.get("status"),
                        "reason": status.get("reason"),
						"message": status.get("message"),
						"date": status.get("date"),
                    },
                    "requestId": response.get("requestId"),
					"reference": payment.get("reference"),
					"amount": payment.get("amount", {}).get("total", 0) or body.get("amount"),
					"external_id": payment.get("internalReference",""),
					"sid": sid
                }
                if not status.get('status') == "PENDING" or (status.get('status') == "PENDING" and body.get("num_retry") == "MAX"):
                    status = status.get("status")
                    if status == "PENDING":
                        logging.info("[PLACETOPAY][%s] We have spent all the bullets to confirm the reservation. We cancel it!" % (payment_order_id, num_retry))
                        status = "REJECTED"
                        gateway_response["status"]["status"] = status
                        gateway_response["status"]["reason"] = "XN"
                        gateway_response["status"]["message"] = "Rechazado por Paraty. Sin respuesta por la pasarela"

                    logging.info("[PLACETOPAY][%s] Final response status: %s" % (payment_order_id, status))
                    send_response_to_merchant_url(gateway_response, merchant_url, payment_order_id)
                    return "OK"
                elif in_seconds:
                    body["num_retry"] = num_retry
                    logging.info("[PLACETOPAY][%s] Craeting new task for check place to pay. Retry number %s in_seconds: %s" % (payment_order_id, num_retry, in_seconds))
                    #If we are here we dont have a final status from place to place. We still asking.
                    create_deferred_check_payment_task(body, num_retry, payment_order_id, hotel_code, in_seconds)
                    return "KO"

        logging.error("[PLACETOPAY][%s] No response received from checking payment" % payment_order_id)
        if not response and body.get("num_retry") != "MAX" and in_seconds:
            logging.error("[PLACETOPAY][%s] No response received from checking payment... retrying " % payment_order_id)
            create_deferred_check_payment_task(body, num_retry, payment_order_id, hotel_code, in_seconds)
        else:
            logging.info("[PLACETOPAY][%s] We haven't had response from Place2Pay and we have already asked as many times as we can." % payment_order_id)
            gateway_response = {
                'status': {
                    "status": "REJECTED",
                    "reason": "XN",
                    "message": "Gateway has not response us in any way",
                    "date": datetime.datetime.now(datetime.timezone.utc).isoformat(),
                },
                "requestId": body.get("request_id"),
                "reference": payment_order_id,
                "amount": body.get("amount"),
                "external_id": "",
                "sid": sid
            }
            send_response_to_merchant_url(gateway_response, merchant_url, payment_order_id)




def send_response_to_merchant_url(gateway_response, merchant_url, payment_order_id):
    gateway_response_encode = base64.b64encode(json.dumps(gateway_response).encode('utf-8'))
    header = {
        'Content-Type': "text/plain"
    }
    logging.info("[PLACETOPAY][%s] Formatted response from TPV to send to merchant_url: %s" % (payment_order_id, gateway_response))
    merchant_url_response = requests.post(merchant_url, data=gateway_response_encode, headers=header)

def create_deferred_check_payment_task(body, num_retry, payment_order_id, hotel_code, in_seconds):
    body["num_retry"] = num_retry
    # If we are here we dont have a final status from place to place. We still asking.
    # Looks strange but it will work, trust the process
    body = transform_data(body)
    data = {
        "task_id": str(uuid.uuid4()),
        "data": base64.urlsafe_b64encode(json.dumps(body).encode("utf-8")).decode("utf-8"),
    }
    logging.info("[PLACETOPAY][%s] create_task for check place to pay. PAYLOAD %s" % (payment_order_id, data))
    logging.info("[PLACETOPAY][%s] create_task for check place to pay. DATA %s" % (payment_order_id, body))

    queue_utils.create_task('execute_fallback_task', json.dumps(data), queue_name=COBRADOR_QUEUE,
                            task_name=f'check_placetopay_payment_task__{int(datetime.datetime.now().timestamp() * 1000)}_{hotel_code}',
                            in_seconds=in_seconds)


def transform_data(data):
    return {
        "body": {key: value for key, value in data.items() if key != "merchant_url"},
        "merchant_url": data.get("merchant_url", "")
    }
