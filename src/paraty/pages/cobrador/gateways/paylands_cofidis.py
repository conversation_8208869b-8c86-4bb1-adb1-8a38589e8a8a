import base64
import json
import logging
from flask import render_template
import requests

from paraty import app
from paraty.pages.cobrador.cobrador_constants import SID_FROM_COBRADOR, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, get_integration_name, audit_response
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface


class PaylandsController(GatewayInterface):

	def translate_error(self, error):
		return error

	def get_currency(self):
		return "978"

	def get_sign(self):
		return ""

	def get_url_post(self):
		return "/pages/cobrador/save_payment"

	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def get_card_user(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("payland_credencials", ""):
				credencials = extra_info.get("payland_credencials", "")
				if credencials.get("customer", ""):
					return credencials.get("customer", "")

		return ""

	def get_token(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("payland_credencials", ""):
				credencials = extra_info.get("payland_credencials", "")
				if credencials.get("token_card", ""):
					return credencials.get("token_card", "")

		return ""

	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		try:

			config = self.get_configuration(hotel_code)

			currency = self.get_currency()
			url_post = self.get_url_post()

			service = config.get("TEST_service")
			signature = config.get("TEST_signature")

			customer = self.get_card_user(reservation)

			amount = ("%.2f" % float(amount)).replace('.', '')

			payload = {
				"amount": amount,
				"operative": "AUTHORIZATION",
				"currency": currency,
				"signature": signature,
				"customer_ext_id": customer,
				"additional": "123456",
				"service": service,
				"url_post": url_post,
				"url_ok": "",
				"url_ko": "",
				"description": "Order for booking"
			}

			payload = json.dumps(payload)

			headers = {}
			headers['Content-Type'] = "application/json"
			api_key = config.get("TEST_api_key").encode("utf-8")
			headers['Authorization'] = "Basic %s" % str(base64.b64encode(api_key).decode("utf-8"))

			url_checkout = config.get("TEST_url_checkout")
			response = requests.request("POST", url_checkout, data=payload, headers=headers)

			logging.info("[PAYLANDS] Response Getting Token: %s", response)
			response.raise_for_status()

			response_json = json.loads(response.text)

			uuid_order = response_json.get('order').get("uuid")
			uuid_card = self.get_token(reservation)

			payload = {
				"order_uuid": uuid_order,
				"card_uuid": uuid_card,
				"signature": signature
			}


			try:
				gateway_type = "paylands"
				audit_response(hotel_code, gateway_type, uuid_order, SID_FROM_COBRADOR, response.text)

			except Exception as e:
				logging.error("Error auditing response for in PAYLANDS")
				logging.error("Error auditing: %s", e)



			payload_json = json.dumps(payload)

			url_payment_direct = config.get("TEST_url_payment_direct")
			response = requests.request("POST", url_payment_direct, data=payload_json, headers=headers)
			response.raise_for_status()

			return uuid_order

		except Exception as e:
			logging.warning("Exception at get_token [PAYLANDS]: %s", e)

		return "ERROR: Payment not done"


	def gateway_has_token(self):
		return True

	def reservation_has_token(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("payland_credencials", ""):
				credencials = extra_info.get("payland_credencials", "")
				if credencials.get("token_card", ""):
					return True

		return False


class PaylandsCofidisFormController(FormGatewayInterface):

	def get_currency(self):
		return "978"

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		#TODO: reacerlo todo cuando comience la integración
		config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)

		currency = self.get_currency()
		url_post = "/pages/cobrador/save_payment"

		service = config.get("service")
		signature = config.get("signature")

		url_ok = config.get("url_ok") + "&sid=%s" % sid
		url_ko = config.get("url_ko") + "&sid=%s" % sid

		amount_to_show = "%.2f" % (float(amount)/4)

		amount = ("%.2f" % float(amount)).replace('.', '')

		context = {
			"4xcard": config.get("service 4xcard"),
			"pay": config.get("service pay"),
			"paylands": config.get("service"),
			"dev": app.config.get("DEV")
		}

		if config.get("service"):
			payload = {
				"amount": amount,
				"secure": False,
				"operative": "AUTHORIZATION",
				"currency": currency,
				"signature": signature,
				"customer_ext_id": payment_order_id,
				"additional": "123456",
				"service": config.get("service"),
				"url_post": url_post,
				"url_ok": url_ok,
				"url_ko": url_ko,
				"description": "Order for booking"
			}

			payload = json.dumps(payload)

			headers = {}
			headers['Content-Type'] = "application/json"
			api_key = config.get("api_key").encode("utf-8")
			headers['Authorization'] = "Basic %s" % str(base64.b64encode(api_key).decode("utf-8"))

			url_checkout = config.get("url_checkout")
			response = requests.request("POST", url_checkout, data=payload, headers=headers)

			logging.info("[PAYLANDS] Response Getting Token: %s", response)
			# response.raise_for_status()
			audit_response(hotel_code, "PAYLANDS", payment_order_id, "", response.text, payload=json.dumps(payload))

			response_json = json.loads(response.text)

			context["token"] = response_json.get("order").get("token")

		if config.get("service 4xcard"):
			payload = {
				"amount": amount,
				"secure": False,
				"operative": "AUTHORIZATION",
				"currency": currency,
				"signature": signature,
				"customer_ext_id": payment_order_id,
				"additional": "123456",
				"service": config.get("service 4xcard"),
				"url_post": url_post,
				"url_ok": url_ok,
				"url_ko": url_ko,
				"description": "Order for booking",
				"extra_data": {
					"profile": {
						"first_name": "",
						"last_name": "",
						"email": "<EMAIL>"
					}
				}
			}

			payload = json.dumps(payload)

			headers = {}
			headers['Content-Type'] = "application/json"
			api_key = config.get("api_key").encode("utf-8")
			headers['Authorization'] = "Basic %s" % str(base64.b64encode(api_key).decode("utf-8"))

			url_checkout = config.get("url_checkout")
			response = requests.request("POST", url_checkout, data=payload, headers=headers)

			logging.info("[PAYLANDS] Response Getting Token: %s", response)
			# response.raise_for_status()
			audit_response(hotel_code, "PAYLANDS", payment_order_id, "", response.text, payload=json.dumps(payload))

			response_json = json.loads(response.text)

			context["token_4xcard"] = response_json.get("order").get("token")
			context["amount_to_show_4xcard"] = amount_to_show

		if config.get("service pay"):
			payload = {
				"amount": amount,
				"secure": False,
				"operative": "AUTHORIZATION",
				"currency": currency,
				"signature": signature,
				"customer_ext_id": payment_order_id,
				"additional": "123456",
				"service": config.get("service pay"),
				"url_post": url_post,
				"url_ok": url_ok,
				"url_ko": url_ko,
				"description": "Order for booking",
				"extra_data": {
					"profile": {
						"first_name": "",
						"last_name": "",
						"email": "<EMAIL>"
					}
				}
			}

			payload = json.dumps(payload)

			headers = {}
			headers['Content-Type'] = "application/json"
			api_key = config.get("api_key").encode("utf-8")
			headers['Authorization'] = "Basic %s" % str(base64.b64encode(api_key).decode("utf-8"))

			url_checkout = config.get("url_checkout")
			response = requests.request("POST", url_checkout, data=payload, headers=headers)

			logging.info("[PAYLANDS] Response Getting Token: %s", response)
			#response.raise_for_status()
			audit_response(hotel_code, "PAYLANDS", payment_order_id, "", response.text, payload=json.dumps(payload))

			response_json = json.loads(response.text)

			context["token_pay"] = response_json.get("order").get("token")
			context["amount_to_show"] = amount_to_show


		return render_template("/pages/cobrador/gateways/paylands_insite_form.html", **context)


	def process_gateway_response(self, hotel_code, gateway_type, response):
		pass

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT
