import hashlib
import json
import logging
import random
import xml.etree.ElementTree as xml
from datetime import datetime
from urllib.parse import parse_qs

import requests
from paraty.pages.cobrador.cobrador_constants import SID_FROM_COBRADOR, GATEWAY_ERROR_RETURNED, \
	SEPARATOR_INFO_ERROR_ORDER, GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_integration_name, get_payment_gateway_configuration, audit_response
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore
from flask import request as r

class CecaData():

	def get_formated_price(self, price):
		return ("%.2f" % price).replace(".", "")

	def get_uuid(self):
		selectedUUID = '%d' % random.randint(10000000, 99999999)
		return selectedUUID

	def calculate_sign(self):

		sign_target = "%s%s%s%s%s%s%s%s%s%s%s%s%s" % (self.key_secret,
		                                          self.merchant_id,
		                                          self.acquirer_bin,
		                                          self.terminal_id,
		                                          self.num_operation,
		                                          self.amount,
		                                          self.currency,
		                                          self.exponent,
		                                          "SHA2",
		                                          self.url_ok,
		                                          self.url_ko,
		                                          'IN',
		                                          self.mmpp)

		result = hashlib.sha256(sign_target.encode()).hexdigest()
		return result

	def calculate_sign_form(self):
		sign = "%s%s%s%s%s%s%s%s%s%s%s" % (self.key_secret,
		                                   self.merchant_id,
		                                   self.acquirer_bin,
		                                   self.terminal_id,
		                                   self.num_operation,
		                                   self.amount,
		                                   self.currency,
		                                   self.exponent,
		                                   "SHA2",
		                                   self.url_ok,
		                                   self.url_ko)

		result = hashlib.sha256(sign.encode()).hexdigest()

		self.sign = result

		return result

	def __init__(self, config):

		self.acquirer_bin = config.get("acquirer_bin")
		self.merchant_id = config.get("merchant_id")
		self.terminal_id = config.get("terminal_id")
		self.key_secret = config.get("key_secret")

		self.url_merchant = config.get("url_merchant")
		self.url_payment = config.get("url_payment")
		self.url_ok = config.get("url_ok")
		self.url_ko = config.get("url_ko")
		self.watcher_url = config.get("url_watcher")

		self.tokenizer = False
		self.id_user = ""
		self.token = ""

		self.url_payment_token = config.get("url_payment_token")

		self.currencies = {}
		currencies = config.get("currencies")
		for currency in currencies.split(","):
			key = currency.split(":")[0]
			value = currency.split(":")[1]
			self.currencies[key] = value

		self.status = config.get("status")

		self.amount = ""
		self.currency = ""
		self.exponent = "2"
		self.sign = ""
		self.mmpp = ""

		self.num_operation = ""
		self.description = ""


class CecaDataTest(CecaData):

	def __init__(self, config):

		self.acquirer_bin = config.get("TEST_acquirer_bin")
		self.merchant_id = config.get("TEST_merchant_id")
		self.terminal_id = config.get("TEST_terminal_id")
		self.key_secret = config.get("TEST_key_secret")

		self.url_merchant = config.get("TEST_url_merchant")
		self.url_payment = config.get("TEST_url_payment")
		self.url_ok = config.get("TEST_url_ok")
		self.url_ko = config.get("TEST_url_ko")
		self.watcher_url = config.get("TEST_url_watcher")

		self.tokenizer = False
		self.id_user = ""
		self.token = ""

		self.url_payment_token = config.get("TEST_url_payment_token")

		self.currencies = {}
		currencies = config.get("TEST_currencies")
		for currency in currencies.split(","):
			key = currency.split(":")[0]
			value = currency.split(":")[1]
			self.currencies[key] = value

		self.status = config.get("status")

		self.amount = ""
		self.currency = ""
		self.exponent = "2"
		self.sign = ""
		self.mmpp = ""

		self.num_operation = ""
		self.description = ""


class CecaController(GatewayInterface):

	def init(self, config):
		if config.get('status', '') == 'TEST':
			self.data = CecaDataTest(config)
		else:
			self.data = CecaData(config)


	def translate_error(self, error):
		return error

	def get_currency(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("ceca_credencials", ""):
				credencials = extra_info.get("ceca_credencials", "")
				if credencials.get("currency_code", ""):
					return credencials.get("currency_code", "")

		return "978"


	def get_sign(self):
		return ""

	def get_url_post(self):
		return "/pages/cobrador/save_payment"

	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def get_card_user(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("ceca_credencials", ""):
				credencials = extra_info.get("ceca_credencials", "")
				if credencials.get("user_id", ""):
					return credencials.get("user_id", "")

		return ""

	def get_token(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("ceca_credencials", ""):
				credencials = extra_info.get("ceca_credencials", "")
				if credencials.get("token_card", ""):
					return credencials.get("token_card", "")

		return ""

	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		try:

			config = self.get_configuration(hotel_code)

			self.init(config)

			self.data.currency = self.get_currency(reservation)
			self.data.amount = self.data.get_formated_price(amount)
			self.data.num_operation = "PEP-"+self.data.get_uuid()
			self.data.id_user = self.get_card_user(reservation)
			self.data.token = self.get_token(reservation)
			self.data.mmpp = reservation.get("identifier")

			payload = {
				"MerchantID": self.data.merchant_id,
				"AcquirerBIN": self.data.acquirer_bin,
				"TerminalID": self.data.terminal_id,
				"Num_operacion": self.data.num_operation,
				"Importe": self.data.amount,
				"TipoMoneda": self.data.currency,
				"Exponente": self.data.exponent,
				"Cifrado": "SHA2",
				"URL_OK": self.data.url_ok,
				"URL_NOK": self.data.url_ko,
				"idusuario": self.data.id_user,
				"Token": self.data.token,
				"Idioma": "XML",
				"Inicio_Rec": "N",
				"Tipo_COF": "I",
				"MMPP_TX_ID": self.data.mmpp,
				"Firma": self.data.calculate_sign()
			}

			headers = {}
			headers['Content-Type'] = "application/x-www-form-urlencoded"

			url_checkout = self.data.url_payment_token
			response = requests.post(url_checkout, data=payload, headers=headers)

			try:
				gateway_type = "ceca"
				audit_response(hotel_code, gateway_type, self.data.num_operation, SID_FROM_COBRADOR, response.text, payload=json.dumps(payload))

			except Exception as e:
				logging.error("Error auditing response for in CECA")
				logging.error("Error auditing: %s", e)


			response_xml = xml.ElementTree(xml.fromstring(response.text))
			if response_xml.find('ERROR'):
				return "ERROR: Payment not done"

			operacion = response_xml.find('OPERACION')
			result = operacion.attrib.get('tipo')
			referencia = operacion.find('numeroAutorizacion').text

			if result != '000' or not referencia:
				return GATEWAY_ERROR_RETURNED + ":" + result + SEPARATOR_INFO_ERROR_ORDER + referencia

			return referencia

		except Exception as e:
			logging.warning("Exception at get_token [CECA]: %s", e)

		return "ERROR: Payment not done"

	def reservation_has_token(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("ceca_credencials", ""):
				credencials = extra_info.get("ceca_credencials", "")
				if credencials.get("token_card", ""):
					return True

		return False

	def gateway_has_token(self):
		return True


class CecaFormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		gateway_type = [x for x in gateway_type.split(";") if "CECA" in x][0]
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		ceca_controller = CecaData(gateway_configuration)
		if gateway_configuration.get('status', '') == 'TEST':
			ceca_controller = CecaDataTest(gateway_configuration)

		ceca_controller.currency = "978"
		ceca_controller.num_operation = payment_order_id
		ceca_controller.amount = ("%.2f" % float(amount)).replace(".", "")
		if extra_data.get("from_callcenter"):
			ceca_controller.callseeker = True

		if gateway_configuration.get("status_token") and ceca_controller.amount == "000":
			ceca_controller.tokenizer = True
			ceca_controller.id_user = sid
			ceca_controller.token = ""
			ceca_controller.amount = "000"

			if gateway_configuration.get("url_register_token"):
				ceca_controller.url_payment = gateway_configuration["url_register_token"]

		ceca_controller.sign = ceca_controller.calculate_sign_form()

		context = vars(ceca_controller)

		properties = {
			"sid": sid,
			"identifier": payment_order_id,
			"hotel_code": hotel_code,
			"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
			"extraInfo": json.dumps({})
		}
		save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")

		return build_template("pages/cobrador/gateways/ceca/ceca_form.html", context)

	def process_gateway_response(self, hotel_code, gateway_type, response):
		gateway_type = [x for x in gateway_type.split(";") if "CECA" in x][0]
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		ceca_data = CecaData(gateway_configuration)
		ceca_data.currency = "978"
		if not isinstance(response, dict):
			response_json = parse_qs(response)
		else:
			response_json = response

		try:
			sid = r.args.get("sid")
			if not sid:
				sid = "not received correctly"
			gateway_type = "ceca"
			audit_response(hotel_code, gateway_type, response_json.get("Num_operacion")[0], sid, json.dumps(response_json))

		except Exception as e:
			logging.warning("Error auditing response for in CECA Process Response")
			logging.warning("Error auditing: %s", e)

		cobrador_response = {
			'GATEWAY_ORDER_ID': response_json.get("Num_operacion")[0],
			'GATEWAY_PAID_AMOUNT': response_json.get('Importe')[0],
			'CODE': GATEWAY_SUCESS_RETURNED,
			'PAYMENT_GATEWAY_NAME': "CECA",
			"CUSTOM_RESPONSE": "<HTML><HEAD><TITLE>Respuesta correcta a la comunicación ON-LINE</TITLE></HEAD><BODY>$*$OKY$*$</BODY></HTML>",
			"GATEWAY_EXTRA_INFO": {}
		}
		cobrador_response["GATEWAY_PAID_AMOUNT"] = float(cobrador_response["GATEWAY_PAID_AMOUNT"]) / 100.0

		if not self._check_sign(ceca_data.key_secret, response_json):
			cobrador_response["CODE"] = GATEWAY_ERROR_RETURNED
			cobrador_response["CUSTOM_RESPONSE"] = "<HTML><HEAD><TITLE>Respuesta correcta a la comunicación ON-LINE</TITLE></HEAD><BODY>$*$NOK$*$</BODY></HTML>"

		if response_json.get("Idusuario"):
			cobrador_response["GATEWAY_EXTRA_INFO"]["card_token"] = response_json.get('Token')[0]
			cobrador_response["GATEWAY_EXTRA_INFO"]["ceca_credencials"] = {
				"user_id": response_json.get('Idusuario')[0],
				"currency_code": response_json.get('TipoMoneda')[0],
				"token_card": response_json.get('Token')[0]
			}

		return cobrador_response

	def _check_sign(self, key, response_json):

		sign = response_json.get("Firma")[0]

		merchant_id = response_json.get("MerchantID")[0]
		acquirer_bin = response_json.get("AcquirerBIN")[0]
		terminal_id = response_json.get("TerminalID")[0]
		num_operacion = response_json.get("Num_operacion")[0]
		amount = response_json.get("Importe")[0]
		currency = response_json.get("TipoMoneda")[0]
		exponente = response_json.get("Exponente")[0]
		referencia = response_json.get("Referencia")[0]

		sign_target = "%s%s%s%s%s%s%s%s%s" % (key, merchant_id, acquirer_bin, terminal_id, num_operacion, amount, currency, exponente, referencia)

		result = hashlib.sha256(sign_target.encode()).hexdigest()

		if sign == result:
			return True

		logging.info("Sign not valid: %s vs %s" % (sign, sign_target))

		return False

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT
