import datetime
import string
import random

from random import randint

import openpay
import requests

from paraty import Config
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, GATEWAY_ERROR_CODE_RETURNED, \
    GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, get_integration_name, audit_response, get_amount_to_pay
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface

from paraty.utilities.manager_utils import get_configuration_property_value
from paraty.utilities.templates.templates_processor import build_template
import json
from flask import request
import re

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.logging.my_gae_logging import logging
from paraty.utilities.languages import language_utils


PAYMENT_SUBDOMAIN = "https://payment."
PRODUCTION_DOMAIN = "https://api.openpay.mx"
SANDBOX_DOMAIN = "https://sandbox-api.openpay.mx/v1/%s"

#Endpoint to get customers with params
CUSTOMERS_ENDPOINT_WITH_PARAMS = "/customers?%s"

#General endpoints without filter
GENERAL_CUSTOMERS_ENDPOINT = "/customers/"
GENERAL_CHARGES_ENDPOINT = "/charges/%s"
GENERAL_REFUND_ENDPOINT = "/charges/%s/refund"

#Endpoint to do a cahrge in a customer
CREATE_CUSTOMERS_CHARGES_ENDPOINT = "/customers/%s/charges/"

#Endpoint to get a customer cahrge
GET_CUSTOMERS_CHARGES_ENDPOINT = "/customers/%s/charges/%s"

#Endpoint to get a customer cahrge
CREATE_CARD_TOKEN_ENDPOINT = "/customers/%s/cards"

#Entpoint to do a refund of a charge in a customer
DO_CUSTOMERS_REFUND_ENDPOINT = "/customers/%s/charges/%s/refund/"


def get_endpoint_openpay(integration_config):
    final_endpoint = SANDBOX_DOMAIN if (
                Config.DEV or integration_config.get("sandbox", "").lower() == "true") else PRODUCTION_DOMAIN
    logging.info("USING OPENPAY endpoint: %s", final_endpoint)
    return final_endpoint

def get_trade_keys(integration):
    openpay.api_key = integration.get("test_private_key") if Config.DEV else integration.get("private_key")
    openpay.verify_ssl_certs = False
    openpay.merchant_id = integration.get("test_merchant_id") if Config.DEV else integration.get("merchant_id")
    openpay.country = 'mx'
    openpay.api_version = "v1"

def get_openpay_transaction_info(extraInfo, identifier):
    openpay_transaction_key = "openpay_transaction_" + identifier
    openpay_transaction = extraInfo.get(openpay_transaction_key)
    openpay_internal_payment_id = identifier
    openpay_internal_customer_id = ""
    openpay_order_id = ""
    token_id = ""
    if openpay_transaction:
        openpay_internal_payment_id = openpay_transaction.get('openpay_internal_payment_id')
        openpay_internal_customer_id = openpay_transaction.get('openpay_internal_customer_id')
        openpay_order_id = openpay_transaction.get('openpay_order_id')
        token_id = openpay_transaction.get('token_id')

    logging.info("[OPENPAY] Transaction info obtained: %s", openpay_transaction)
    return openpay_internal_payment_id, openpay_internal_customer_id, openpay_order_id, token_id


class OpenpayController(GatewayInterface):
    def get_configuration(self, hotel_code):

        integration_name = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

        return gateway_configuration




    def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
        extra_info = reservation.get("extraInfo", "{}")
        if extra_info:
            try:
                extra_info = json.loads(extra_info)
            except:
                logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
                          reservation.get("identifier"))

                return ""

            reservation_id = int(reservation.key.id)
            extra_info["last_merchant_order_used"] = merchant_order
            reservation["extraInfo"] = json.dumps(extra_info)

            reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
                                                                              reservation, hotel_code=hotel_code)
            logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
                     reservation_id)

            return reservation_updated_id

    def update_extraInfo_in_reservation(self, hotel_code, reservation, openpay_internal_customer_id, openpay_order_id, token_id, response_charge_from_openpay):
        extra_info = reservation.get("extraInfo", "{}")
        openpay_order_id = openpay_order_id
        if extra_info:
            try:
                extra_info = json.loads(extra_info)
            except:
                logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
                              reservation.get("identifier"))

                return ""

            reservation_id = int(reservation.key.id)
            new_extraInfo_key = "openpay_transaction_" + str(openpay_order_id)
            extra_info[new_extraInfo_key] = {
                "openpay_internal_customer_id": openpay_internal_customer_id,
                "openpay_order_id": openpay_order_id,
                "openpay_internal_payment_id": response_charge_from_openpay.get("id"),
                "token_id": token_id,
            }
            reservation["extraInfo"] = json.dumps(extra_info)

            reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
                                                                              reservation, hotel_code=hotel_code)
            logging.info("ExtraInfo update for reservation %s", reservation.get("identifier"))

            return reservation_updated_id
    def get_next_openpay_order(self, hotel_code, reservation):

        last_merchant_order_used = ""
        if reservation.get("extraInfo"):
            extra_info = json.loads(reservation.get("extraInfo"))
            last_merchant_order_used = extra_info.get("last_merchant_order_used")

        if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
            last_merchant_order_used = last_merchant_order_used[0:8]
            numerical_order = int(last_merchant_order_used)
            numerical_order += 1

            merchant_order = str(numerical_order)

            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
        else:

            merchant_order = reservation.get("identifier")
            # maybe this reservation was done before gateway was configured. So it could be an alphanumeric
            if not merchant_order.isnumeric():
                merchant_order = randint(10000000, 99999000)

            numerical_order = int(merchant_order)
            numerical_order += 1
            merchant_order = str(numerical_order)
            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

        # IMPORTANT: Even redsys return us an erro, they save ALWAYS the order.
        # so we have to save it in reservation for not received a duplicate order error in next payment!
        self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)

        return merchant_order

    def execute_payment_in_gateway(self, hotel_code, reservation, amount):
        identifier = reservation.get("identifier", "")
        integration_name = get_integration_name(hotel_code)
        integration = get_payment_gateway_configuration(integration_name, hotel_code)
        translations = language_utils.get_web_dictionary(reservation.get("language", "SPANISH"))

        if re.match("^.+-(.*)$", identifier):
            parts = re.search("^.+-(.*)$", identifier)
            identifier = parts.groups()[0]

        get_trade_keys(integration)

        try:
            extra_info = json.loads(reservation.get("extraInfo", {}))
        except:
            extra_info = {}

        currency = extra_info.get("currency", "")
        if not currency:
            currency = get_configuration_property_value("Base Price Currency")
        if not currency:
            currency = "USD"

        try:
            openpay_internal_payment_id, openpay_internal_customer_id, openpay_order_id, token_id = get_openpay_transaction_info(extra_info, identifier)

            customer = openpay.Customer.retrieve(openpay_internal_customer_id)

            internal_openpay_order = self.get_openpay_order(extra_info, customer=customer, integration=integration, identifier=identifier)

            credit_card_customer = customer.cards.all().get("data")
            credit_card_id = ""
            if credit_card_customer:
                current_credit_card = credit_card_customer[0]
                credit_card_id = current_credit_card.id
            if internal_openpay_order.json():
                order = internal_openpay_order.json()

                next_transaction_id = self.get_next_openpay_order(hotel_code, reservation)


                response_charge_from_openpay = OpenpayFormController.create_charge(self=None, request_data=None, payment_order_id=next_transaction_id, translations=translations, customer=customer, integration=integration, token_id=token_id, current_order=order, amount=amount, currency=currency)

                response_charge_from_openpay = response_charge_from_openpay.json()

                try:
                    gateway_type = "OPENPAY"
                    audit_response(hotel_code, gateway_type, next_transaction_id, "FROM_COBRADOR", json.dumps(response_charge_from_openpay))

                except Exception as e:
                    logging.error("Error auditing response for in OPENPAY in EXECUTE")
                    logging.error("Error auditing: %s", e)

                if response_charge_from_openpay.get("status") == "completed":
                    self.update_extraInfo_in_reservation(hotel_code, reservation, openpay_internal_customer_id, next_transaction_id, token_id, response_charge_from_openpay)
                    logging.info(
                        "[OPENPAY][%s][%s][PAYMENT_RESPONSE]: %s" % (hotel_code, identifier, response_charge_from_openpay))
                    return next_transaction_id
                logging.error(
                    "[OPENPAY][%s][%s][PAYMENT_RESPONSE]: %s" % (hotel_code, identifier, response_charge_from_openpay))
        except Exception as e:
            logging.error("Exception from openpay: %s" % e)

        return GATEWAY_ERROR_RETURNED

    def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
        integration_name = get_integration_name(hotel_code)
        integration = get_payment_gateway_configuration(integration_name, hotel_code)
        get_trade_keys(integration)

        extra_info = json.loads(reservation.get("extraInfo"))

        openpay_internal_payment_id, openpay_internal_customer_id, openpay_order_id, token_id = get_openpay_transaction_info(extra_info, order_to_refund)

        order = self.get_openpay_order(extra_info, customer=None, integration=integration, order_to_refund=openpay_internal_payment_id)

        if order.json():
            order_dict = order.json()
            logging.info("[OPENPAY] Charge obtained from openpay %s : %s" % (order_to_refund, order_dict))
            base_url = get_endpoint_openpay(integration)
            base_url = base_url % openpay.merchant_id

            customer_id = openpay_internal_customer_id if openpay_internal_customer_id else order_dict.get("customer_id")
            order_id = openpay_internal_payment_id if openpay_internal_payment_id else order_dict.get("id")
            base_url = base_url + DO_CUSTOMERS_REFUND_ENDPOINT
            final_url = base_url % (customer_id, order_id)
            data = {
                "description": "Paraty refund",
            }
            # "amount": amount

            response_charge_from_openpay = requests.post(final_url, headers={'Content-type': 'application/json'},
                                                         auth=(openpay.api_key, ''), json=data)
            response_charge_from_openpay = response_charge_from_openpay.json()
            if response_charge_from_openpay.get("status") == "completed":
                logging.info(
                    "[OPENPAY][%s][%s][REFUND_RESPONSE]: %s" % (hotel_code, order_to_refund, response_charge_from_openpay))
                return order_to_refund
            logging.error(
                "[OPENPAY][%s][%s][REFUND_RESPONSE]: %s" % (hotel_code, order_to_refund, response_charge_from_openpay))

        return GATEWAY_ERROR_RETURNED

    def translate_error(self, error):
        return error

    def get_openpay_order(self, extra_info, customer, integration, order_to_refund=None, identifier=None):
        base_url = get_endpoint_openpay(integration)
        base_url = base_url % (openpay.merchant_id)

        if order_to_refund:
            openpay_internal_payment_id, openpay_internal_customer_id, openpay_order_id, token_id = get_openpay_transaction_info(extra_info, order_to_refund)
        else:
            openpay_internal_payment_id, openpay_internal_customer_id, openpay_order_id, token_id = get_openpay_transaction_info(extra_info, identifier)


        headers = {
                'Content-type': 'application/json'
            }
        if not customer:
            get_charge_url = GENERAL_CHARGES_ENDPOINT % (openpay_internal_payment_id)
            final_url = base_url + get_charge_url

            order = requests.get(url=final_url, auth=(openpay.api_key, ''), headers=headers)
        else:
            get_charge_url = GET_CUSTOMERS_CHARGES_ENDPOINT % (customer.get("id"), openpay_internal_payment_id)
            final_url = base_url + get_charge_url
            order = requests.get(final_url, headers=headers, auth=(openpay.api_key, ''))

        return order

    def reservation_has_token(self, reservation):
        try:
            extra_info = json.loads(reservation.get("extraInfo"))
            identifier = reservation.get("identifier")
            openpay_internal_payment_id, openpay_internal_customer_id, openpay_order_id, token_id = get_openpay_transaction_info(extra_info, identifier)
            if extra_info.get("openpay_internal_payment_id") or openpay_internal_payment_id or token_id:
                return True
        except:
            return False

    def gateway_has_token(self):
        return True

    def get_initial_order_id_from_extra_info(self, reservation_extra_info):
        return reservation_extra_info.get("paymentOrderId")

class OpenpayFormController(FormGatewayInterface):

    def extract_customer_data(self, request_data, translations, customer):
        if customer:
            extracted_data = {
                "name": customer.get("name", ""),
                "email": customer.get("email").replace("%40", "@"),
                "addres": {
                    "city": customer["address"].get("city", translations.get("T_no_data_provided")),
                    "state": customer["address"].get("state", translations.get("T_no_data_provided")),
                    "line1": customer["address"].get("line1", translations.get("T_no_data_provided")),
                    "postal_code": customer["address"].get("postal_code", "0"),
                    "country_code": customer["address"].get("country_code", "MX")
                },
                "last_name": customer.get("lastName1", ""),
                "phone_number": customer.get("telephone", ""),
            }
        else:
            extracted_data = {
                "name": request_data.get("firstName", ""),
                "email": request_data.get("email").replace("%40", "@"),
                "addres": {
                    "city": request_data.get("city", translations.get("T_no_data_provided")),
                    "state": request_data.get("province", translations.get("T_no_data_provided")),
                    "line1": request_data.get("address", translations.get("T_no_data_provided")),
                    "postal_code": request_data.get("postalCode", "0"),
                    "country_code": request_data.get("country", "MX")
                },
                "last_name": request_data.get("lastName1", ""),
                "phone_number": request_data.get("telephone", ""),
            }

        return extracted_data

    def create_openpay_customer(self, request_data, translations, payment_order_id):

        try:
            logging.info("Creating customer in openpay with ID %s", payment_order_id)
            customer_body = {
                "name": request_data.get("name",""),
                "email": request_data.get("email").replace("%40","@"),
                "address": {
                    "city": request_data.get("city", translations.get("T_no_data_provided")) or translations.get("T_no_data_provided"),
                    "state": request_data.get("province", translations.get("T_no_data_provided")) or translations.get("T_no_data_provided"),
                    "line1": request_data.get("address", translations.get("T_no_data_provided")) or translations.get("T_no_data_provided"),
                    "postal_code": request_data.get("postalCode", "10001") or "10001",
                    "country_code": request_data.get("country","MX")
                },
                "last_name": request_data.get("lastName1", ""),
                "phone_number": request_data.get("telephone", ""),
                "external_id": payment_order_id
            }
            customer = openpay.Customer.create(**customer_body)
        except Exception as e:
            logging.warning("Error creating the new customer in Openpay")
            logging.error(e)
            return None

        return customer

    def create_charge(self, request_data, payment_order_id, translations, customer, integration, token_id=None, current_order=None, amount=None, currency=None, config=None):
        customer_id = customer.openapay_id
        try:
            logging.info("[OPENPAY] Creating a new charge with order id %s for customer %s" %(payment_order_id, customer.get("external_id")))
            token_id = token_id if token_id else request_data.get("token_id")
            device_session_id = current_order.get("device_session_id") if current_order else request_data.get("device_id")
            amount = amount if amount else (float(request_data.get("amount")) / 100)
            currency = currency if currency else request_data.get("currency")
            description = request_data.get("description", translations.get("T_paraty_charge")) if request_data else translations.get("T_paraty_charge")
            headers = {
                'Content-type': 'application/json'
            }
            auth = (openpay.api_key, '')

            if not device_session_id:
                caracteres = string.ascii_letters + string.digits  # letras mayúsculas, minúsculas y números
                device_session_id = ''.join(random.choice(caracteres) for _ in range(32))


            data = {
                "source_id": token_id,
                "method": "card",
                "amount": amount,
                "currency": currency,
                "description": description,
                "order_id": payment_order_id,
                "device_session_id": device_session_id,
            }

            base_url = get_endpoint_openpay(integration)
            base_url = base_url % (openpay.merchant_id)
            base_url = base_url + CREATE_CUSTOMERS_CHARGES_ENDPOINT
            final_url = base_url % customer_id
            response_charge_from_openpay = requests.post(final_url, headers=headers, auth=auth, json=data)
            return response_charge_from_openpay
        except Exception as e:
            logging.error("[OPENPAY] Error creating charge with order id %s", payment_order_id)
            logging.error(e)
            return None

    def create_token(self, customer, request_data):
        customer_id = customer.openapay_id
        token_id = request_data.get("token_id")
        device_session_id = request_data.get("device_id")
        logging.info("Params to create a new token (customer_id: %s, token_id: %s, device_session_id: %s" %(customer_id, token_id,device_session_id))
        return openpay.Customer.retrieve(customer_id).cards.create(token_id=token_id, device_session_id=device_session_id)

    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        integration = get_payment_gateway_configuration(gateway_type, hotel_code)

        translations = language_utils.get_web_dictionary(extra_data.get("language", "SPANISH"))

        get_trade_keys(integration)

        merchant_url = integration.get("url_merchant")
        if Config.DEV:
            merchant_url = integration.get("test_url_merchant")

        if "?" in merchant_url:
            merchant_url = merchant_url + "&sid=" + str(sid) + "&reservation=" + str(payment_order_id)
        else:
            merchant_url = merchant_url + "?sid=" + str(sid) + "&reservation=" + str(payment_order_id)

        url_ko = integration.get("url_ko")
        url_ko = url_ko + "?sid=" + str(sid) + "&errorCode=CC_DENIED_BUT_REQUIRED"

        charge_description = translations.get("T_charge_description") %(payment_order_id)
        amount = gateways_format_price(amount)
        extra_info = {
            "amount": amount,
            "currency": extra_data.get("currency")
        }

        properties = {
            "sid": sid,
            "identifier": str(payment_order_id),
            "hotel_code": hotel_code,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "extraInfo": json.dumps(extra_info)
        }
        datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")

        openpay_template = "pages/cobrador/gateways/openpay_insite_form.html"
        context = {
            "merchant_id": openpay.merchant_id,
            "public_key": integration.get("public_key"),
            "amount": amount,
            "charge_description": charge_description,
            "merchant_url": merchant_url,
            "sid": sid,
            "currency": extra_data.get("currency"),
            'url_ko': url_ko
        }

        form_gateway = build_template(openpay_template, context, False)

        return form_gateway


    def process_gateway_response(self, hotel_code, gateway_type, response):
        translations = language_utils.get_web_dictionary("SPANISH")

        integration = get_payment_gateway_configuration(gateway_type, hotel_code)
        payment_order_id = request.args.get("reservation", "")
        request_json = request.json
        request_data = request_json.get("data_session_form", {})
        request_data["token_id"] = request.args.get("token_id")
        request_data["device_id"] = request.args.get("device_session_id")
        request_data["description"] = translations.get("T_charge_description") %(payment_order_id)
        reservation = request.args.get("reservation", "")
        reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
                                                                                  [("identifier", "=", reservation),
                                                                                   ("hotel_code", "=", hotel_code)])
        if reservation_metadata:
            reservation_metadata = reservation_metadata[0]
            extra_info = json.loads(reservation_metadata.get("extraInfo"))
            request_data["currency"] = extra_info.get("currency")
            request_data["amount"] = extra_info.get("amount")

        get_trade_keys(integration)

        amount = float(request_data.get("amount", request.args.get("amount"))) / 100

        translations = language_utils.get_web_dictionary(request_data.get("language", "SPANISH"))
        # response_token_from_openpay = self.create_token(request_data, payment_order_id, translations, customer=None)

        #TODO: try to associate the charge with a previously created customer
        customer = self.create_openpay_customer(request_data, translations, payment_order_id)

        response_token_from_openpay = {}
        try:
            logging.info("[OPENPAY] Creating new token for payment order id %s", payment_order_id)
            response_token_from_openpay = self.create_token(customer, request_data)
            response_token_from_openpay = response_token_from_openpay.json()
        except Exception as e:
            logging.warning("[OPENPAY] Be careful!!! Token hasen't been created for %s, please check payment process!", payment_order_id)
            logging.error(e)

        response_charge_from_openpay = {}
        if amount > 0:
            response_charge_from_openpay = self.create_charge(request_data, payment_order_id, translations, customer=customer, integration=integration, amount=amount)
            response_charge_from_openpay = response_charge_from_openpay.json()
            logging.info("[OPENPAY][PROCESS_RESPONSE][%s][%s] response for payment intent: %s" % (
                hotel_code, str(payment_order_id), json.dumps(response_charge_from_openpay)))

            audit_response(hotel_code, "openpay", payment_order_id, "", json.dumps(response_charge_from_openpay),
                           payload=json.dumps({"id": None}))
        metadata = response_charge_from_openpay.get("order_id", payment_order_id)

        amount = str(response_charge_from_openpay.get("amount", 0))
        amount = float(amount)

        to_extra_info = {
            "payment_gateway": gateway_type
        }

        if response_charge_from_openpay and response_charge_from_openpay.get("status", "") == "completed":
            logging.info("[OPENPAY]: Successful created charge in openpay: %s", payment_order_id)
            new_extraInfo_key = "openpay_transaction_"+payment_order_id
            to_extra_info[new_extraInfo_key] = {
                "openpay_internal_customer_id": response_charge_from_openpay.get("customer_id"),
                "openpay_order_id": response_charge_from_openpay.get("order_id"),
                "openpay_internal_payment_id": response_charge_from_openpay.get("id"),
                "token_id": request_data.get("token_id")
            }

            code = GATEWAY_SUCESS_RETURNED
        elif response_token_from_openpay.get("id"):
            logging.info("[OPENPAY]: Amount 0!!! Successful created token in openpay: %s", payment_order_id)
            new_extraInfo_key = "openpay_transaction_" + payment_order_id
            to_extra_info[new_extraInfo_key] = {
                "openpay_internal_customer_id": response_token_from_openpay.get("customer_id"),
                "openpay_order_id": payment_order_id,
                "token_id": response_token_from_openpay.get("id"),
                "openpay_internal_payment_id": "is token",
            }
            code = GATEWAY_SUCESS_RETURNED
        else:
            code = GATEWAY_ERROR_CODE_RETURNED

        cobrador_response = {
            "CODE": code,
            "GATEWAY_ORDER_ID": metadata,
            "GATEWAY_PAID_AMOUNT": amount,
            "GATEWAY_EXTRA_INFO": to_extra_info
        }

        logging.info("Cobrador response: %s", cobrador_response)


        return cobrador_response

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT







