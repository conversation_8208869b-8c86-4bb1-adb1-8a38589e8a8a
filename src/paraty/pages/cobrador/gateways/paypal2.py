import json
import re
import uuid
from datetime import datetime
from urllib.parse import urlencode
from flask import request, make_response
from flask_cors import cross_origin

from paraty import app
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED, \
	CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, get_amount_to_pay, audit_response
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.pages.cobrador.gateway_logger import build_logger_context
from paraty.utilities.languages.language_utils import SPANISH
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty.utilities.templates.templates_processor import build_template

import requests

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_value, get_reservations_of_hotel, \
	get_web_section
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.language_utils import get_language_locale
from paraty_commons_3.logging.my_gae_logging import logging
from base64 import urlsafe_b64encode, urlsafe_b64decode
from Cryptodome.Cipher import AES
from paraty.utilities.languages import language_utils
from paraty_commons_3.session.session_utils import get_session_from_hotel

KEY = b")X@XcRfUjXn2r2u7"

def get_integration_config_by_status(integration_config):
	status = integration_config.get("status", "").strip().upper()
	base_configs = {x: integration_config[x] for x in integration_config if "TEST_" not in x}
	base_configs = {x: integration_config[x] for x in base_configs if "DEV_" not in x}
	if not integration_config.get("endpoint"):
		base_configs["endpoint"] = "https://api.sandbox.paypal.com"
	if status == "TEST":
		test_configs = {x.replace("TEST_", ""): integration_config[x] for x in integration_config if "TEST_" in x}
		if not integration_config.get("TEST_endpoint"):
			base_configs["endpoint"] = "https://api.sandbox.paypal.com"
		base_configs.update(test_configs)


	if app.config.get("DEV"):
		test_configs = {x.replace("DEV_", ""): integration_config[x] for x in integration_config if "DEV_" in x}
		base_configs.update(test_configs)

	return base_configs



class PaypalV2FormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):

		only_show_one_payment_method = get_hotel_advance_config_value(hotel_code, "Split paypal payment options")
		show_card = False
		if "ONLY_CARD" in gateway_type:
			show_card = True
			gateway_type = gateway_type.replace("ONLY_CARD", "").strip()

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		integration = get_integration_config_by_status(integration)
		hotel = get_hotel_by_application_id(hotel_code)

		language = extra_data.get("language", SPANISH)
		translations = language_utils.get_web_dictionary(language)
		reservation_amount = amount
		rate_condition = "nrf" if extra_data.get("nrf") else "flex"

		gotrip_dis_perc = False
		gotrip_dis_msi = False
		if extra_data.get("gotrip"):
			if integration.get("disable msi payments for gotrip users"):
				gotrip_dis_msi = True
			if integration.get("disable percent payments for gotrip users"):
				gotrip_dis_perc = True


		currency = "eur"
		if integration.get("currency"):
			currency = integration.get("currency")
		elif extra_data.get("currency"):
			currency = extra_data.get("currency")
		currency = currency.upper()
		region_block = False
		if integration.get("msi_only_for_mexico"):
			if extra_data.get("country", "ES").upper() != "MX":
				region_block = True

		valid_installments = False
		if integration.get("msi payment") and not region_block and not gotrip_dis_msi:
			valid_installments = True

		section_name = get_configuration_property_value(hotel_code, "Texto a reemplazar")
		translations_section = get_web_section(hotel, section_name, extra_data.get("language", "SPANISH"),
											   set_languages=True)

		translations._dict.update(translations_section or {})
		amounts = ['100']
		if integration.get("flex_amounts") and not extra_data.get("nrf") and not extra_data.get("from_tpv_link") and not gotrip_dis_perc:
			amounts.extend(integration.get("flex_amounts").strip(";").split(";"))
			amounts = [str(i) for i in amounts]

		custom_text = {}
		custom_amounts = {}
		b3_custom_text_from_pep = get_hotel_advance_config_value(hotel_code,"b3 custom text from pep")
		if b3_custom_text_from_pep and b3_custom_text_from_pep.lower() == "true":
			currency_value = translations.get("T_currency_value")
			for option_amount in amounts:
				if "-day" in option_amount:
					total_reservation_nights = extra_data.get("num_nights")
					num_nights_to_pay = option_amount.split("-day")[0]
					amount_to_pay = str(get_amount_to_pay(reservation_amount, total_reservation_nights=total_reservation_nights, num_nights_to_pay=num_nights_to_pay, prices_per_day= extra_data.get("prices_per_day")))
					current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace("@@amount@@", amount_to_pay).replace("@@currency@@", extra_data.get("currency"))
					translate = translations.get("T_paypal_n_day").replace("@@currency@@", extra_data.get("currency")).replace("@@amount@@",amount_to_pay).replace("@@currency_value@@", current_currency_value)
					num_days = option_amount.split("-day")[0]
					final_translate = translate.replace("@@night@@", num_days)
					custom_text[option_amount] = final_translate
					select_option_translation = translations.get("T_select_option_paypal_n_day")
					if select_option_translation:
						select_option_translation = select_option_translation.replace("@@currency@@", extra_data.get("currency")).replace("@@amount@@",amount_to_pay).replace("@@currency_value@@", current_currency_value).replace("@@night@@", num_days)
						custom_text["select_option_button_n_day"] = select_option_translation
				else:
					current_currency_value = currency_value.replace("@@currency_value@@", reservation_amount).replace("@@amount@@", reservation_amount).replace("@@currency@@", extra_data.get("currency"))
					amount_to_pay = str(get_amount_to_pay(reservation_amount, percent=option_amount))
					if option_amount == "100":
						final_translate = translations.get("T_paypal_100").replace("@@currency@@", extra_data.get("currency")).replace("@@amount@@", amount_to_pay).replace("@@currency_value@@", current_currency_value)
						select_option_translation = translations.get("T_select_option_paypal_100")
						if select_option_translation:
							select_option_translation = select_option_translation.replace("@@currency@@", extra_data.get("currency")).replace("@@amount@@", amount_to_pay).replace("@@currency_value@@", current_currency_value)
							custom_text["select_option_button_n_day_100"] = select_option_translation
					else:
						final_translate = translations.get("T_paypal_percent").replace("@@percent@@", option_amount).replace("@@currency@@", extra_data.get("currency")).replace("@@currency_value@@", current_currency_value)
				custom_text[option_amount] = final_translate
				custom_amounts[option_amount] = amount_to_pay

		merchant_url = integration.get("merchant_url", "")
		merchant_url_params = {
			"sid": sid,
			"reservation": payment_order_id
		}

		merchant_url += "&%s" % urlencode(merchant_url_params)
		if "?" not in merchant_url:
			merchant_url = merchant_url.replace("&", "?", 1)

		fraudnet_token = str(uuid.uuid4()).replace("-", "")

		extra_info = {
			"fraudnet_token": fraudnet_token,
			"gateway_type": gateway_type
		}

		properties = {
			"sid": sid,
			"identifier": str(payment_order_id),
			"hotel_code": hotel_code,
			"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
			"extraInfo": json.dumps(extra_info)
		}
		datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")

		sensible_data = {
			"num_nights": extra_data.get("num_nights"),
			"amount": amount,
			"currency": currency,
			"room_info": extra_data.get("room_info"),
			"prices_per_day": extra_data.get("prices_per_day"),
			"country": extra_data.get("country", "ES").upper()
		}
		sensible_data.update(extra_info)

		payment_info_string = json.dumps(sensible_data)
		encryptor = AES.new(KEY, AES.MODE_EAX)
		payload = encryptor.encrypt(payment_info_string.encode("utf-8"))
		payload = urlsafe_b64encode(encryptor.nonce + payload).decode("utf-8")

		context = {
			"client_id": integration.get("client_id", ""),
			"dev": app.config.get("DEV"),
			"merchant_url": merchant_url,
			"hotel_name": hotel.get("name", ""),
			"hotel_code": hotel_code,
			"valid_installments": valid_installments,
			"fraudnet_token": fraudnet_token,
			"payment_order_id": payment_order_id,
			"payload": payload,
			"merchant_name": integration.get("merchant_name", ""),
			"amounts": amounts,
			"num_nights": extra_data.get("num_nights"),
			"currency": currency,
			"test": True if integration.get("status", "").upper() == "TEST" else False,
			"only_show_one_payment_method": only_show_one_payment_method,
			"show_card": show_card,
			"custom_text": custom_text,
			"custom_amounts": custom_amounts,
			"rate_condition": rate_condition,
			"logging_session": build_logger_context(),
			"sid": sid
		}
		if extra_data.get("prices_per_day"):
			context["prices_per_day"] = extra_data.get("prices_per_day")
		context["locale"] = get_language_locale(language)
		template_path = "pages/cobrador/gateways/paypal_v2_insite_form.html"
		form_gateway = build_template(template_path, context, False)
		return form_gateway

	def get_auth_token(self, hotel_code, integration, payment_order_id):
		post_data = {
			"grant_type": "client_credentials"
		}
		response = requests.post("%s/v1/oauth2/token" % integration.get("endpoint"), data=post_data, auth=(integration.get("client_id"), integration.get("secret")))
		token = ""
		if response.status_code in [200, 201]:
			logging.info("[PAYPAL][%s][%s][auth_token]Response: %s" % (hotel_code, payment_order_id, response.text.replace("\n", "")))
			response_json = response.json()
			token = response_json.get("access_token")
		else:
			logging.error("[PAYPAL][%s][%s][auth_token]Response: %s" % (hotel_code, payment_order_id, response.text.replace("\n", "")))
		return token

	def process_gateway_response(self, hotel_code, gateway_type, response):
		if "ONLY_CARD" in gateway_type:
			gateway_type = gateway_type.replace("ONLY_CARD", "").strip()

		integration = get_payment_gateway_configuration(gateway_type, hotel_code)
		integration = get_integration_config_by_status(integration)

		payer_id = request.args.get("PayerID", "")
		order_id = request.args.get("orderID", "")
		reservation = request.args.get("reservation", "")
		extra_info = {}
		reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
																				  [("identifier", "=", reservation),
																				   ("hotel_code", "=", hotel_code)])
		if reservation_metadata:
			reservation_metadata = reservation_metadata[0]
			extra_info = json.loads(reservation_metadata.get("extraInfo"))

		if not extra_info:
			return {
				"CODE": GATEWAY_ERROR_CODE_RETURNED,
				"GATEWAY_ORDER_ID": reservation
			}

		auth_token = self.get_auth_token(hotel_code, integration, reservation)

		headers = {
			"Authorization": "Bearer %s" % auth_token,
			"Content-Type": "application/json",
			"PAYPAL-CLIENT-METADATA-ID": extra_info.get("fraudnet_token")
		}

		error_message = ""


		response = requests.get("%s/v2/checkout/orders/%s" % (integration.get("endpoint"), order_id), headers=headers)

		try:
			sid = request.values.get('sid', '')
			audit_response(hotel_code, "PAYPALV2", reservation, sid, response.text)

		except Exception as e:
			logging.warning("error creating payment audit")

		if response.status_code in [200, 201]:
			response_json = response.json()
			amount = 0.0
			purchases = response_json.get("purchase_units", [])
			if purchases:
				purchase = purchases[0]
				amount = float(purchase.get("amount", {}).get("value", "0.0"))

			payment_status = response_json.get("status")
			if payment_status == "APPROVED":
				capture_response = requests.post("%s/v2/checkout/orders/%s/capture" % (integration.get("endpoint"), order_id), json={}, headers=headers)
				try:
					sid = request.values.get('sid', '')
					audit_response(hotel_code, "PAYPALV2", reservation, sid, capture_response.text)

				except Exception as e:
					logging.warning("error creating payment audit")
				if capture_response.status_code in [200, 201]:
					capture_response_json = capture_response.json()
					capture_status = capture_response_json.get("status")
					if capture_status in ["COMPLETE", "COMPLETED"]:
						logging.info("[PAYPAL][%s][%s][capture][%d]Response: %s" % (
						hotel_code, reservation, capture_response.status_code, capture_response.text.replace("\n", "")))
						return {
							"CODE": GATEWAY_SUCESS_RETURNED,
							"GATEWAY_ORDER_ID": reservation,
							"PAYMENT_GATEWAY_NAME": "Paypal",
							"GATEWAY_EXTRA_INFO": {
								"paypal_token": order_id,
								"paypal_payer": payer_id
							},
							"GATEWAY_PAID_AMOUNT": amount
						}
					else:
						logging.warning("[PAYPAL][%s][%s][capture][%d]Response: %s" % (
							hotel_code, reservation, capture_response.status_code,
							capture_response.text.replace("\n", "")))

				else:
					try:
						capture_response_json = capture_response.json()
					except Exception as e:
						capture_response_json = {}

					if capture_response_json.get("message"):
						error_message = capture_response_json.get("message")
					logging.warning("[PAYPAL][%s][%s][capture][%d]Response: %s" % (hotel_code, reservation, capture_response.status_code, capture_response.text.replace("\n", "")))
			elif payment_status in ["COMPLETED"]:
				return {
					"CODE": GATEWAY_SUCESS_RETURNED,
					"PAYMENT_GATEWAY_NAME": "Paypal",
					"GATEWAY_ORDER_ID": reservation,
					"GATEWAY_EXTRA_INFO": {
						"paypal_token": order_id,
						"paypal_payer": payer_id
					},
					"GATEWAY_PAID_AMOUNT": amount
				}

		logging.warning("[PAYPAL][%s][%s][get_order][%d]Response: %s" % (hotel_code, reservation, response.status_code, response.text.replace("\n", "")))

		error_response = {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"GATEWAY_ORDER_ID": reservation,
			"GATEWAY_EXTRA_INFO": {
				"paypal_token": order_id,
				"paypal_error_reason": response.text
			}
		}

		if error_message:
			error_response["GATEWAY_ERROR_MESSAGE"] = error_message

		return error_response

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


def string_limit(string):
	if len(string) > 127:
		string = string[:124] + "..."
	return string



@app.route("/paypal/order/create/", methods=["POST"])
@cross_origin(origin='*')
def create_order():
	if request.is_json:
		request_json = request.json
	else:
		return {}, 400
	hotel_code = request_json.get("hotel_code")
	sid = request_json.get("sid")
	payment_order_id = request_json.get("payment_order_id")
	selected_payment_option = request_json.get("selected_payment_option", "").lower()
	payload = request_json.get("payload")
	payload = urlsafe_b64decode(payload)
	nounce = payload[:16]
	payload = payload[16:]
	encryptor = AES.new(KEY, AES.MODE_EAX, nonce=nounce)
	payload = encryptor.decrypt(payload)
	extra_info = json.loads(payload)

	gateway = extra_info.get("gateway_type", "")
	integration = get_payment_gateway_configuration(gateway, hotel_code)
	integration = get_integration_config_by_status(integration)

	amount = round(float(extra_info.get("amount")), 2)
	amount_percent = request_json.get("percent_to_pay")
	if amount_percent:
		amounts = []
		amounts.extend(integration.get("flex_amounts", "100").strip(";").split(";"))
		amounts = [str(x) for x in amounts]

		if amount_percent in amounts:

			if amount_percent.endswith("-day"):

				nights_to_pay = int(amount_percent.replace("-day", ""))
				num_nights = int(extra_info.get("num_nights"))
				if nights_to_pay < num_nights:
					if extra_info.get("prices_per_day"):
						amount = get_amount_to_pay(amount, total_reservation_nights=num_nights,
												   num_nights_to_pay=nights_to_pay,
												   prices_per_day=extra_info.get("prices_per_day"))
					else:
						amount = round((amount / num_nights) * nights_to_pay, 2)

			else:
				amount_percent = float(amount_percent)

				amount = round((amount_percent * amount)/100, 2)


	if extra_info.get("room_info"):
		product_items = []
		original_rooms_amount = sum([i.get("amount", 0) for i in extra_info.get("room_info")])
		total_amount = 0
		for room in extra_info.get("room_info"):
			room_price_percent = room.get("amount", 0)/(original_rooms_amount/100)
			room_price = round((amount/100) * room_price_percent, 2)

			item_name = "%s, %s %s" % (request_json.get("concept_name"), request_json.get("name"), request_json.get("surname"))
			item_name = string_limit(item_name)
			description = "%s, %s" % (room.get("rate_name"), room.get("room_name"))
			description = string_limit(description)

			product_items.append(
				{
					"sku": "Reserva número %s" % str(payment_order_id),
					"name": item_name,
					"description": description,
					"quantity": "1",
					"unit_amount": {
						"currency_code": room.get("currency"),
						"value": room_price
					}
				}
			)

			total_amount += room_price

		total_amount = round(total_amount, 2)
		if total_amount != amount:
			# adjustment = (total_amount - amount) * -1
			diff = abs(total_amount - amount)
			adjustment = -diff if total_amount > amount else diff
			first_amount = round(float(product_items[0]["unit_amount"]["value"]), 2)

			product_items[0]["unit_amount"]["value"] = round(first_amount + adjustment, 2)

	else:
		product_items = [
			{
				"sku": "Reserva número %s" % str(payment_order_id),
				"name": request_json.get("concept_name"),
				"quantity": "1",
				"unit_amount": {
					"currency_code": extra_info.get("currency"),
					"value": amount
				}
			}
		]

	full_name = request_json.get("name") or "" + " " + request_json.get("surname") or ""

	context = {
		"intent": "CAPTURE",
		"purchase_units": [
			{
				"reference_id": payment_order_id,
				"amount": {
					"value": amount,
					"currency_code": extra_info.get("currency"),
					"breakdown": {
						"item_total": {
							"currency_code": extra_info.get("currency"),
							"value": amount
						}
					}
				},
				"description": request_json.get("hotel_name"),
				"custom_id": str(payment_order_id),
				"items": product_items
			}
		],
		"payment_source": {}
	}

	if selected_payment_option == "card":
		context["payment_source"]["card"] = {}
	# 		"name": full_name,
	# 		"billing_address": {
	# 			"admin_area_2": request_json.get("city") or "Torremolinos",
	# 			"country_code": extra_info.get("country") or "ES",
	# 			"postal_code": request_json.get("postal_code") or "99999"
	# 		}
	# 	}
	#
	elif selected_payment_option == "paypal":
		context["payment_source"]["paypal"] = {
			"name": {
				"given_name": request_json.get("name"),
				"surname": request_json.get("surname")
			},
			"experience_context": {
				"brand_name": request_json.get("hotel_name"),
				"shipping_preference": "NO_SHIPPING"
			}
		}

	if request_json.get("msi"):
		context["style"] = {
			"label": "installment",
			"period": "MX"
		}

	post_data = {
		"grant_type": "client_credentials"
	}
	response = requests.post("%s/v1/oauth2/token" % integration.get("endpoint"), data=post_data,
							 auth=(integration.get("client_id"), integration.get("secret")))
	auth_token = ""
	if response.status_code in [200, 201]:
		logging.info("[PAYPAL][%s][%s][auth_token]Response: %s" % (
		hotel_code, payment_order_id, response.text.replace("\n", "")))
		response_json = response.json()
		auth_token = response_json.get("access_token")

	headers = {
		"Authorization": "Bearer %s" % auth_token,
		"Content-Type": "application/json",
		"PAYPAL-CLIENT-METADATA-ID": extra_info.get("fraudnet_token")
	}

	if integration.get("merchant_name"):

		paypal_merchant_name = re.findall("PP_(.+)_PYMNT", integration.get("merchant_name"))
		if paypal_merchant_name:
			paypal_merchant_name = paypal_merchant_name[0]
		else:
			paypal_merchant_name = integration.get("merchant_name")

		fraudnet_url = "%s/v1/risk/transaction-contexts/%s/%s" % (
		integration.get("endpoint"), paypal_merchant_name, extra_info.get("fraudnet_token"))

		anti_fraud_payload = {
			"additional_data": [
				{
					"key": "sender_first_name",
					"value": request_json.get("name")
				},
				{
					"key": "sender_last_name",
					"value": request_json.get("surname")
				},
				{
					"key": "sender_email",
					"value": request_json.get("email")
				},
				{
					"key": "sender_phone",
					"value": request_json.get("phone")
				},
				{
					"key": "sender_country_code",
					"value": request_json.get("country")
				},
				{
					"key": "sender_create_date",
					"value": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
				},
				{
					"key": "highrisk_txn_flag",
					"value": 0
				},
				{
					"key": "vertical",
					"value": "Hotel"
				}
			]
		}

		if request_json.get("city"):
			anti_fraud_payload["additional_data"].append({
				"key": "ota_start_city",
				"value": request_json.get("city")
			})

		if request_json.get("postal_code"):
			anti_fraud_payload["additional_data"].append({
				"key": "ota_start_zipcode",
				"value": request_json.get("postal_code")
			})

		if extra_info.get("country"):
			anti_fraud_payload["additional_data"].append({
				"key": "ota_start_country",
				"value": extra_info.get("country", "ES").upper()
			})

		session_content = get_session_from_hotel(hotel_code, sid)

		if session_content and session_content.get("currentSearch", {}).get("currentSearch", {}):
			original_start_date_str = session_content.get("currentSearch", {}).get("currentSearch", {}).get("startDate")
			original_end_date_str = session_content.get("currentSearch", {}).get("currentSearch", {}).get("endDate")

			original_start_date = datetime.strptime(original_start_date_str, "%Y-%m-%d")
			original_end_date = datetime.strptime(original_end_date_str, "%Y-%m-%d")

			anti_fraud_payload["additional_data"].extend([
				{
					"key": "ota_service_start_date",
					"value": original_start_date.strftime("%Y-%m-%dT%H:%M:%SZ")
				},{
					"key": "ota_service_end_date",
					"value": original_end_date.strftime("%Y-%m-%dT%H:%M:%SZ")
				}
			])

		logging.info("[PAYPAL][%s][%s][fraud_net]Request: %s" % (
			hotel_code, payment_order_id, json.dumps(anti_fraud_payload)))

		response = requests.put(fraudnet_url, json=anti_fraud_payload, headers=headers)

		logging.info("[PAYPAL][%s][%s][fraud_net][%d]Response: %s" % (
			hotel_code, payment_order_id, response.status_code, response.text.replace("\n", "")))

	logging.info("[PAYPAL][%s][%s][create_order]Request: %s" % (
		hotel_code, payment_order_id, json.dumps(context)))

	response = requests.post("%s/v2/checkout/orders" % (integration.get("endpoint")), json=context,headers=headers)

	logging.info("[PAYPAL][%s][%s][create_order][%d]Response: %s" % (
		hotel_code, payment_order_id, response.status_code, response.text.replace("\n", "")))

	return response.json(), response.status_code