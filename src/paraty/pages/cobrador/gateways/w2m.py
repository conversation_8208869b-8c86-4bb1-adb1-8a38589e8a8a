import json
import requests
import logging
from flask import request

from datetime import datetime

from paraty import app
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.pages.cobrador.gateways.trust import add_parameter_to_url
from paraty.pages.cobrador.cobrador_utils import get_integration_name, get_payment_gateway_configuration, \
	complete_payment, get_reservations_total_price, \
	execution_locked, get_lock_key, lock_execution, send_payment_confirmation_emails, save_secure_payment_reservation, \
	audit_response, filter_reservation_metadata_by_hotel_code, initiate_cobrador_response, change_refund_status
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, \
	W2M, GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.utilities.templates.templates_processor import build_template

from paraty_commons_3.datastore import datastore_communicator

URL_CHECKOUT_TOKEN = "https://%sauth.w2m.com/auth/realms/integration/protocol/openid-connect/token"
URL_CREATE_CHECKOUT = "https://%sapi.w2m.com/integration/checkout/api/v1/checkout"
URL_GET_CHECKOUT = "https://%sapi.w2m.com/integration/checkout/api/v1/checkout/%s"
URL_PAYMENT = "https://%sapi.w2m.com/integration/checkout/api/v1/checkout/%s/installment/charge"
URL_PAYMENT_FAILED = "https://checkout-core-api.%sinternal.w2m.com/api/v1/checkout/%s/installment/charge"
URL_REFUND = "https://%sapi.w2m.com/integration/checkout/api/v1/checkout/%s/installment/%s/refund" # checkoutid - orderid




class W2MFormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		logging.info(f"Building W2M form for {payment_order_id}")
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		gateway_configuration['test'] = "false"
		gateway_configuration['commerce'] = "O7HOTELS"
		gateway_configuration['market'] = "VISTABLANES"
		extra_data['amount'] = ("%.2f" % float(amount)).replace('.', '')
		currency = extra_data.get("currency", "EUR")
		checkout_token = get_checkout_token(gateway_configuration)
		checkout_id, checkout_url = get_redirection_checkout(payment_order_id, amount, currency, sid, gateway_configuration, checkout_token, extra_data)
		logging.info(f"checkout_url: {checkout_url}")
		save_metadata(hotel_code, sid, payment_order_id, checkout_id, amount, currency)

		context = {
			"checkout_url": checkout_url,
		}

		return build_template("pages/cobrador/gateways/_w2m.html", context)

	def process_gateway_response(self, hotel_code, gateway_type, response):
		logging.info(f"Processing W2M response for {hotel_code}")
		logging.info(f"Response: {response}")
		if not response:
			response = request.get_json()
		checkout_id = response.get('checkoutId')
		order_id = response.get('orderId')

		cobrador_response = initiate_cobrador_response(W2M)
		
		function_name = "w2m_process_gateway_response"
		lock_key = get_lock_key(hotel_code, f"{function_name}_{checkout_id}")
				
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		checkout = get_checkout(checkout_id, gateway_configuration)

		installments = checkout.get('payment', {}).get('installments', [])
		if installments:
			identifier = order_id.split('-')[0]
			if order_id:
				installments = [x for x in installments if x.get('orderCode') == order_id]
			installment = installments[0]
			logging.info(f"Installment: {installment}")

			try:
				sid = request.args.get("sid")
				if not sid:
					sid = "not received correctly"

				gateway_type = "W2M"
				audit_response(hotel_code, gateway_type, identifier, sid,
							   json.dumps(installment), type_audit="INSTALLMENT")

			except Exception as e:
				logging.error("Error auditing response for in W2M Process Response INSTALLMENT")
				logging.error("Error auditing: %s", e)

			if installment.get('status') == 'COMPLETED':
				lock_key += "_payment"
				if not app.config.get("DEV") and execution_locked(hotel_code, lock_key):
					logging.info(f"Execution locked for payment processing for checkout {checkout_id} (We already returned a response)")
					cobrador_response['CODE'] = GATEWAY_ERROR_CODE_RETURNED
					cobrador_response['ERROR_MESSAGE'] = f"Payment for checkout {checkout_id} is already being processed"
					return cobrador_response
				logging.info(f"Installment status: {installment.get('status')}")
				payment_info = installment.get('amount', {})
				amount = payment_info.get('value', 0)
				logging.info(f"Payment amount: {amount} from installment")
				cobrador_response['GATEWAY_EXTRA_INFO']['W2M_credentials'] = response
				cobrador_response['GATEWAY_EXTRA_INFO']['sid'] = get_sid_from_checkout(checkout)
				cobrador_response['GATEWAY_PAID_AMOUNT'] = amount
				cobrador_response['IDENTIFIER'] = identifier
				if not amount:
					# Esto significa que en el formulario detectamos que no se iba a pagar el 100%, por lo que mandamos cantidad 0 para poder tokenizar
					# y ahora hacemos el cobro necesario basándonos en lo guardado en metadata de la reserva
					amount_to_send_to_gateway, currency = get_amount_currency_from_metadata(identifier, hotel_code)
					if amount_to_send_to_gateway and float(amount_to_send_to_gateway):
						payment_order_id = identifier
						payment_response = do_payment_by_checkout_id(checkout_id, payment_order_id, amount_to_send_to_gateway, currency, gateway_configuration)

						try:
							sid = request.args.get("sid")
							if not sid:
								sid = "not received correctly"

							gateway_type = "W2M"
							audit_response(hotel_code, gateway_type, identifier, sid,
										   json.dumps(payment_response), type_audit="PAYMENT")

						except Exception as e:
							logging.error("Error auditing response for in W2M Process Response PAYMENT")
							logging.error("Error auditing: %s", e)


						if payment_response.get('status') == 'COMPLETED':
							cobrador_response['CODE'] = GATEWAY_SUCESS_RETURNED
							cobrador_response['GATEWAY_PAID_AMOUNT'] = amount_to_send_to_gateway
							cobrador_response['GATEWAY_EXTRA_INFO']['W2M_credentials']['initiation_order_id'] = payment_response.get('orderCode')
							cobrador_response['GATEWAY_ORDER_ID'] = payment_response.get('orderCode')
						else:
							cobrador_response['ERROR_MESSAGE'] = payment_response.get('message', '')
					else:
						logging.info(f"No amount to send to gateway for {identifier} because it's just a tokenization")
						cobrador_response['CODE'] = GATEWAY_SUCESS_RETURNED
						cobrador_response['GATEWAY_ORDER_ID'] = installment.get('orderCode')

				else:
					logging.info(f"Payment completed")
					cobrador_response['GATEWAY_EXTRA_INFO']['W2M_credentials']['initiation_order_id'] = installment.get('orderCode')
					cobrador_response['CODE'] = GATEWAY_SUCESS_RETURNED
					cobrador_response['GATEWAY_ORDER_ID'] = installment.get('orderCode')
			elif installment.get('status') == 'REFUNDED':
				# Handle refund logic here
				lock_key += "_refund"
				if not app.config.get("DEV") and execution_locked(hotel_code, lock_key):
					logging.info(f"Execution locked for payment processing for checkout {checkout_id} (We already returned a response)")
					cobrador_response['CODE'] = GATEWAY_ERROR_CODE_RETURNED
					cobrador_response['ERROR_MESSAGE'] = f"Payment for checkout {checkout_id} is already being processed"
					return cobrador_response
				change_refund_status(hotel_code, identifier, order_id, 'devolution')
				payed_in_this_transaction = installment.get('amount', {}).get('value', 0) * -1
				send_payment_confirmation_emails(hotel_code, identifier, "", True, True, False, payed_in_this_transaction, order_id, False, True, False, {}, reservation=None)
				cobrador_response['CODE'] = GATEWAY_ERROR_CODE_RETURNED
				cobrador_response['ERROR_MESSAGE'] = f"Refund procesed correctly {checkout_id} identifier {identifier} order_id {order_id}"
				lock_execution(hotel_code, lock_key)
			else:
				logging.info(f"Installment status: {installment.get('status')}, we are waiting for a proper response")
		if cobrador_response.get('CODE') == GATEWAY_SUCESS_RETURNED and lock_execution(hotel_code, lock_key):
			logging.info(f"Execution locked for payment processing for checkout {checkout_id}")
		return cobrador_response

	def aux_decrypt_gateway_response(self, hotel_code, response, gateway_configuration):
		identifier = response.get("orderId").split("-")[0]
		reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",[("identifier", "=", identifier)])
		reservation_metadata = filter_reservation_metadata_by_hotel_code(reservation_metadata, hotel_code)
		response = {}
		if reservation_metadata:
			reservation_metadata = reservation_metadata[0]
			response = {
				"original_hotel_code": reservation_metadata.get("hotel_code"),
				"sid": reservation_metadata.get("sid")
			}
		return response

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


# build_form_html
def get_checkout_token(gateway_configuration):
	url_checkout_token = get_url_checkout_token(gateway_configuration)
	payload = get_payload_checkout_token(gateway_configuration)

	headers = {'Content-Type': 'application/x-www-form-urlencoded'}

	response = requests.post(url_checkout_token, headers=headers, data=payload)
	checkout_token = response.json().get('access_token')
	return checkout_token


def get_payload_checkout_token(gateway_configuration):
	# return f'grant_type=client_credentials&client_id={gateway_configuration.get("client_id")}&client_secret={gateway_configuration.get("client_secret")}'
	return f'grant_type=client_credentials&client_id={gateway_configuration.get("client_id")}&client_secret=HrDVt1KW9MZvTPzDHhGJkL1bYkHWwAB6'


def get_amount_currency_from_metadata(identifier, hotel_code):
	reservation_metadata_list = datastore_communicator.get_using_entity_and_params("ReservationMetadata",[("identifier", "=", identifier), ("hotel_code", "=", hotel_code)])
	amount, currency = 0, 'EUR'
	if reservation_metadata_list:
		# Find the record with the most recent timestamp
		most_recent_metadata = max(reservation_metadata_list, key=lambda x: x.get('timestamp', ''))
		reservation_metadata_extraInfo = json.loads(most_recent_metadata.get('extraInfo'))
		amount = reservation_metadata_extraInfo.get('amount_to_send_to_gateway', 0)
		currency = reservation_metadata_extraInfo.get('currency', 'EUR')
		logging.info(f"Payment for {identifier} with amount {amount} and currency {currency} from metadata (timestamp: {most_recent_metadata.get('timestamp')})")
	return amount, currency


# process_gateway_response
def get_checkout(checkout_id, gateway_configuration):
	logging.info(f"checkout_id {checkout_id}")
	token = get_checkout_token(gateway_configuration)
	headers = get_redirection_checkout_headers(token, gateway_configuration)
	url = get_checkout_url(checkout_id, gateway_configuration)
	response = requests.get(url, headers=headers)
	checkout = json.loads(response.text)
	logging.info(f"checkout info {checkout}")
	return checkout


def do_payment_by_checkout_id(checkout_id, payment_order_id, amount, currency, gateway_configuration):
	logging.info(f"Payment for payment_order_id {payment_order_id} with amount {amount} and currency {currency}")

	url = get_payment_url(checkout_id, gateway_configuration, failed=False)
	payload = get_payment_payload(payment_order_id, amount, currency)

	headers = get_redirection_checkout_headers(get_checkout_token(gateway_configuration), gateway_configuration)
	try:
		response = requests.post(url, headers=headers, data=payload)
	except:
		url = get_payment_url(checkout_id, gateway_configuration, failed=True)
		response = requests.post(url, headers=headers, data=payload)
	return json.loads(response.text)


def get_payment_payload(identifier, amount, currency):
	return json.dumps({
		"amount": {
			"value": amount,
			"currency": currency
		},
		"orderCode": identifier,
		"dueDate": datetime.now().strftime("%Y-%m-%d")
	})


def do_refund(checkout_id, order_id, amount, currency, gateway_configuration):
	url = get_refund_url(checkout_id, order_id ,gateway_configuration)
	headers = get_redirection_checkout_headers(get_checkout_token(gateway_configuration), gateway_configuration)
	refund_payload = get_refund_payload(amount, currency)
	response = requests.post(url, headers=headers, data=refund_payload)
	return json.loads(response.text)


def get_refund_payload(amount, currency):
	return json.dumps({
		"amount": {
			"value": float(amount),
			"currency": currency
		}
	})


def get_sid_from_checkout(checkout):
	try:
		return checkout.get('booking', {}).get('okURL').split('sid=')[1].split('&')[0]
	except:
		return ""


def save_metadata(hotel_code, sid, payment_order_id, checkout_id, amount, currency):
	logging.info(f"Saving metadata for {payment_order_id} with checkout_id {checkout_id}")
	payload = {
		"checkout_id": checkout_id,
		"amount_to_send_to_gateway": amount,
		"currency": currency
	}

	properties = {
		"sid": sid,
		"identifier": payment_order_id,
		"hotel_code": hotel_code,
		"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
		"extraInfo": json.dumps(payload)
	}

	datastore_communicator.save_to_datastore("ReservationMetadata", None, properties,
												hotel_code="payment-seeker:")


def get_redirection_checkout(payment_order_id, amount, currency, sid, gateway_configuration, checkout_token, extra_data):
	url = get_redirection_checkout_url(gateway_configuration)
	payload = get_redirection_checkout_payload(payment_order_id, amount, currency, sid, gateway_configuration, extra_data)
	headers = get_redirection_checkout_headers(checkout_token, gateway_configuration)

	response = requests.post(url, headers=headers, data=payload)
	checkout_id, checkout_url = process_get_redirection_checkout_response(response)
	return checkout_id, checkout_url


def process_get_redirection_checkout_response(response):
	response_dict = response.json()
	checkout_id = response_dict.get("checkoutId")
	checkout_url = response_dict.get("checkoutURL")
	return checkout_id, checkout_url


def get_redirection_checkout_headers(checkout_token, gateway_configuration):
	return {
		'Accept-Language': 'es_ES',
		'commerce': f'{gateway_configuration.get("commerce")}',
		'market': f'{gateway_configuration.get("market")}',
		'Authorization': f'Bearer {checkout_token}',
		'Content-Type': 'application/json'
	}


def get_redirection_checkout_payload(payment_order_id, amount, currency, sid, gateway_configuration, extra_data):
	url_ok = add_parameter_to_url(gateway_configuration.get('url_ok'), "sid", sid)
	url_ko = add_parameter_to_url(gateway_configuration.get('url_ko'), "sid", sid)
	is_complete_payment, allowedPaymentMethods = get_allowed_payment_methods(gateway_configuration, extra_data)
	description = ''
	if not is_complete_payment:
		amount = 0
		logging.info(f"Setting amount to 0 as it is not a full payment (W2M doesn't allow us to pay and tokenize at once)")
	logging.info(f"amount sent to W2M : {amount}{currency}")
	payload = {
		"booking": {
			"bookingId": payment_order_id,
			"koURL": url_ko,
			"okURL": url_ok,
			"backURL": url_ko,
			"description": description,
			"startDate": extra_data.get('start_date'),
			"endDate": extra_data.get('end_date'),
			"amount": {
				"value": amount,
				"currency": currency
				}
			}
		}
	payload['booking']["payment"] = {"allowedPaymentMethods": allowedPaymentMethods}
	return json.dumps(payload)


def get_allowed_payment_methods(gateway_configuration, extra_data):
	allowed_payment_methods = ["CARD"]
	extra_data["reservations_total_price"] = get_reservations_total_price(extra_data)
	is_complete_payment = complete_payment(extra_data)
	if is_complete_payment:
		for payment_method in gateway_configuration.get('allowed payment methods').split(','):
			if payment_method.strip() not in allowed_payment_methods:
				allowed_payment_methods.append(payment_method.strip())
	return is_complete_payment, allowed_payment_methods


def get_url_checkout_token(gateway_configuration):
	return URL_CHECKOUT_TOKEN % ("pre-" if gateway_configuration.get('test', 'false').lower() == 'true' else "")


def get_redirection_checkout_url(gateway_configuration):
	return URL_CREATE_CHECKOUT % ("pre-" if gateway_configuration.get('test', 'false').lower() == 'true' else "")


def get_checkout_url(checkout_id, gateway_configuration):
	return URL_GET_CHECKOUT % (("pre-" if gateway_configuration.get('test', 'false').lower() == 'true' else ""), checkout_id)


def get_payment_url(checkout_id, gateway_configuration, failed=False):
	return URL_PAYMENT % (("pre-" if gateway_configuration.get('test', 'false').lower() == 'true' else ""), checkout_id) if not failed else URL_PAYMENT_FAILED % (("pre." if gateway_configuration.get('test', 'false').lower() == 'true' else ""), checkout_id)


def get_refund_url(checkout_id, order_id, gateway_configuration):
	return URL_REFUND % (("pre-" if gateway_configuration.get('test', 'false').lower() == 'true' else ""), checkout_id, order_id)


class W2MController(GatewayInterface):
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		logging.info(f"[W2M] Execute payment in gateway. Reservation: {reservation.get('identifier')}, Amount: {amount}")

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)
		error_code = ''
		if gateway_configuration:
			logging.info(f"[W2M] Gateway configuration: {gateway_configuration}")
			checkout_id = self.reservation_has_token(reservation)
			identifier = reservation.get('identifier')
			_, currency = get_amount_currency_from_metadata(identifier, hotel_code)
			payment_order_id = identifier
			payment_response = do_payment_by_checkout_id(checkout_id, payment_order_id, amount, currency, gateway_configuration)
			try:

				gateway_type = "W2M"
				audit_response(hotel_code, gateway_type, payment_response.get('orderCode', identifier), "FROM_COBRADOR",
							   json.dumps(payment_response), type_audit="PAYMENT")

			except Exception as e:
				logging.error("Error auditing response for in W2M Process Response PAYMENT")
				logging.error("Error auditing: %s", e)


			if payment_response.get('status') == 'COMPLETED':
				return payment_response.get('orderCode')
			error_code = payment_response.get('message', '')
		return f"ERROR: {error_code}"

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		logging.info(f"[W2M] Execute refund in gateway. Reservation: {reservation.get('identifier')}, Amount: {amount}")

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)
		error_code = ""
		if gateway_configuration:
			logging.info(f"[W2M] Gateway configuration: {gateway_configuration}")
			order_id = order_to_refund if order_to_refund != reservation.get('identifier') else get_initial_order_id(reservation)

			if not order_id:
				logging.info("Reservation doesn't have order_id (needed to execute a refund)")
				return GATEWAY_ERROR_RETURNED

			checkout_id = self.reservation_has_token(reservation)
			_, currency = get_amount_currency_from_metadata(reservation.get('identifier'), hotel_code)
			refund_response = do_refund(checkout_id, order_id, amount, currency, gateway_configuration)
			refund_status = refund_response.get('installment', {}).get('status', '')
			if refund_status in ['COMPLETED', 'REFUND_PENDING']:
				return {
					"order_id": order_id,
					"extra_params": {
						"refund_pending": refund_status == 'REFUND_PENDING',
						"checkout_id": checkout_id,
						"create_task": True
					}
				}
			elif refund_status == 'REFUNDED':
				return order_id
			error_code = refund_response.get('message', '')
		return f"ERROR: {error_code}"

	def reservation_has_token(self, reservation):
		extra_info = reservation.get('extraInfo', "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning(f"[W2M] Reservation extra info was not JSON")
				extra_info = {}
			checkout_id = extra_info.get('W2M_credentials', {}).get('checkoutId')
			if checkout_id:
				return checkout_id
		return False
	
	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration
	def translate_error(self, error):
		return error

	def get_initial_order_id_from_extra_info(self, extra_info):
		return extra_info.get("paymentOrderId")


class W2MWebhookController(GatewayInterface):
	def refund_webhook(self):
		hotel_code = request.args.get("hotel_code")
		checkout_id = request.args.get("checkout_id")
		order_id = request.args.get("order_id")

		identifier = request.args.get("identifier")
		logging.info(
			f"Refund status check: order_id={order_id}, hotel_code={hotel_code}, checkout_id={checkout_id}, identifier={identifier}")

		gateway_type = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		checkout = get_checkout(checkout_id, gateway_configuration)
		installments = checkout.get('payment', {}).get('installments', [])
		if installments:
			this_transaction = [x for x in installments if x.get('orderCode') == order_id]
			if this_transaction:
				installment = this_transaction[0]
				logging.info(f"Installment: {installment}")
				installment_status = installment.get('status')
				if installment_status != 'REFUNDED':
					change_refund_status(hotel_code, identifier, order_id, 'failed_devolution')
		return "OK"


def get_initial_order_id(reservation):
	extra_info = reservation.get('extraInfo', "{}")
	if extra_info:
		try:
			extra_info = json.loads(extra_info)
		except:
			extra_info = {}
		initiation_order_id = extra_info.get('W2M_credentials', {}).get('initiation_order_id')
		if initiation_order_id:
			return initiation_order_id
	return False


