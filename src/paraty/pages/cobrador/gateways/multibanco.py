from flask import request, make_response
from flask.views import MethodView

from paraty.pages.cobrador.cobrador_gateway_form import get_form_interface_controller
from paraty.pages.cobrador.cobrador_utils import get_integration_name, extract_params_from_request
from paraty.pages.cobrador.gateways.sibs import SIBSFormControler
from paraty.pages.cobrador.gateways.sibs2 import SIBS2WebhookController

from paraty.pages.cobrador.gateways.trust import TrustWebhookController
from paraty_commons_3.logging.my_gae_logging import logging


class MultibancoWebhookController(MethodView):

    def get(self):
        return self.post()

    def post(self):
        hotel_code = request.args.get("hotel_code")
        gateway = get_integration_name(hotel_code)

        if request.args.get("force_gateway"):
            gateway = request.args["force_gateway"]

        response = ""
        if "sibs" in gateway.lower():
            multibanco_sibs = SIBS2WebhookController()
            response = multibanco_sibs.decrypt(hotel_code)

        elif "trust" in gateway.lower():
            multibanco_trust = TrustWebhookController()
            response = multibanco_trust.trust_webhook(hotel_code)

        return response


class MultibancoCheckController(MethodView):

    def get(self):
        return self.post()

    def post(self):
        hotel_code = request.args.get("hotel_code")
        gateway = request.args.get("gateway")
        identifier = request.args.get("identifier")
        logging.info(f"Multibanco controller: {hotel_code} - {gateway} - {identifier}")

        response = ""
        if "old_sibs" in gateway.lower():
            logging.info("Multibanco SIBS")
            multibanco_old_sibs = SIBSFormControler()
            response = multibanco_old_sibs.check(hotel_code, identifier)

        elif "sibs" in gateway.lower():
            logging.info("Multibanco SIBS2")
            multibanco_sibs = SIBS2WebhookController()
            response = multibanco_sibs.check(hotel_code, identifier)

        elif "trust" in gateway.lower():
            multibanco_trust = TrustWebhookController()
            response = multibanco_trust.check(hotel_code, identifier)
        logging.info(response)

        return response

class GenericWebhookController(MethodView):
    def get(self):
        return self.post()

    def post(self):
        try:
            hotel_code = request.args.get("hotel_code")
            response_to_process = extract_params_from_request(request)
            if not response_to_process or not hotel_code:
                return make_response("Bad params request or empty json body", 400)

            gateway_type = get_integration_name(hotel_code)
            form_controler = get_form_interface_controller(gateway_type)
            if form_controler:
                logging.info("Processing webhook response for %s" % hotel_code)
                logging.info("[%s] Webhook response to process %s" % (gateway_type, response_to_process))
                return form_controler.process_webhook_response(hotel_code, gateway_type, response_to_process)
            else:
                logging.warning("No form controller found for hotel code %s", hotel_code)
                return make_response("Internal server error", 500)
        except Exception as e:
            logging.error(e)
            logging.warning("Cannot process webhook response, please check it!!!")
            return make_response("Internal server error", 500)
