import json
import uuid
import requests
from flask import request

from paraty import Config
from paraty.pages.cobrador.cobrador_constants import GATEWAY_SUCESS_RETURNED, GATEWAY_ERROR_RETURNED, \
    GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, add_sid_to_url, \
    gateways_format_price, audit_response
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.logging.my_gae_logging import logging


class NexiFormControler(FormGatewayInterface):
    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        logging.info("Generating Payment Form")
        currency = extra_data.get('currency', '')

        gateway_type = [x for x in gateway_type.split(";") if "NEXI" in x][0]
        nexi_data = get_payment_gateway_configuration(gateway_type, hotel_code)

        nexi_data['currency'] = currency if currency else "EUR"
        if Config.DEV:
            nexi_data['merchant_url'] = "http://localhost:8090/cobrador/proxy/merchant_url"

        nexi_data['id'] = payment_order_id
        nexi_data['amount'] = amount
        nexi_data['hotel_code'] = hotel_code
        nexi_data['sid'] = sid

        language = extra_data.get("language")
        merchant_url = add_sid_to_url(nexi_data['merchant_url'], sid)
        merchant_url += f"&identifier={nexi_data['id']}"
        nexi_data['merchant_url'] = merchant_url

        context = {
            "hosted_page": self._get_hosted_page(nexi_data)
        }

        logging.info("Context: %s", context)

        template_path = "pages/cobrador/gateways/nexi/_nexi_form.html"
        return build_template(template_path, context, language)

    def process_gateway_response(self, hotel_code, gateway_type, response):
        identifier = request.args.get("identifier")
        payment_id = request.args.get("paymentid")
        gateway_extra_info = {
            "NEXI": {
                "payment_id": payment_id
            }
        }
        try:

            gateway_type = [x for x in gateway_type.split(";") if "NEXI" in x][0]
            nexi_data = get_payment_gateway_configuration(gateway_type, hotel_code)
            nexi_data['hotel_code'] = hotel_code
            nexi_data['sid'] = request.args.get("sid")
            nexi_data['id'] = identifier
            order_status = self._get_order_status(nexi_data, identifier)

            self._audit(nexi_data, json.dumps(order_status.json()), f"{nexi_data['url']}/orders/{identifier}")
            if order_status.status_code != 200:
                raise Exception

            order_status_json = order_status.json()
            amount_to_pay = order_status_json.get("orderStatus").get("capturedAmount")
            amount_to_pay = float(f"{str(amount_to_pay)[:-2]}.{str(amount_to_pay)[-2:]}")
            current_operation = [x for x in order_status_json.get("operations") if x.get("operationId") == payment_id]

            if not current_operation:
                raise Exception("Not operation found: %s - %s", identifier, payment_id)

            current_operation = current_operation[0]
            status_payment = current_operation.get("operationResult")
            result_payment = GATEWAY_ERROR_RETURNED
            gateway_extra_info["NEXI"]['status_payment'] = status_payment

            if current_operation.get("additionalData") and current_operation['additionalData'].get("cardId"):
                gateway_extra_info["NEXI"]['card_id'] = current_operation['additionalData']['cardId']

            if status_payment in ["AUTHORIZED", "EXECUTED"]:
                result_payment = GATEWAY_SUCESS_RETURNED

            return {
                "CODE": result_payment,
                "GATEWAY_ORDER_ID": identifier,
                "GATEWAY_PAID_AMOUNT": amount_to_pay,
                "GATEWAY_EXTRA_INFO": gateway_extra_info
            }

        except Exception as e:
            logging.error("Something went wrong: %s", e)
            return {
                "CODE": GATEWAY_ERROR_CODE_RETURNED,
                "GATEWAY_ORDER_ID": identifier,
                "GATEWAY_PAID_AMOUNT": "",
                "GATEWAY_EXTRA_INFO": gateway_extra_info
            }

    def _build_header(self, x_api_key):
        return {
            'X-Api-Key': x_api_key,
            'Correlation-Id': str(uuid.uuid4()),
            "Content-Type": "application/json"
        }

    def _get_hosted_page(self, nexi_data):
        headers = self._build_header(nexi_data['x_api_key'])

        amount = gateways_format_price(nexi_data['amount'])
        payload = {
            "order": {
                "orderId": nexi_data['id'],
                "amount": amount,
                "currency": "EUR",
            },
            "transactionSummary": {
                "language": "eng"
            },
            "paymentSession": {
                "actionType": "PAY",
                "amount": amount,
                "language": "eng",
                "resultUrl": nexi_data['merchant_url'],
                "cancelUrl": nexi_data['merchant_url'],
            },
            "recurrence": {
                "action": "CONTRACT_CREATION",
                "contractId": nexi_data['id'],
                "contractType": "MIT_UNSCHEDULED"
            }
        }

        logging.info("Payload to nexi: %s", json.dumps(payload))
        url_post = f"{nexi_data['url']}/orders/hpp"
        response = requests.post(url_post, json.dumps(payload), headers=headers)
        self._audit(nexi_data, json.dumps(response.json()), json.dumps(payload), type_audit="Form Payment")

        return response.json().get("hostedPage")

    def _get_order_status(self, nexi_data, identifier):
        headers = self._build_header(nexi_data['x_api_key'])
        return requests.get(f"{nexi_data['url']}/orders/{identifier}", headers=headers)

    def _audit(self, nexi_data, response, payload, type_audit="Process payment"):
        try:
            audit_response(nexi_data['hotel_code'], "NEXI", nexi_data['id'], nexi_data['sid'], response, payload=payload, type_audit=type_audit)

        except Exception as e:
            logging.warning("Error auditing response for NEXI")

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT
