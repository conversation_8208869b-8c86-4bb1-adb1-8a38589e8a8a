import json
from base64 import b64encode

import requests
from flask import request as r

from paraty import Config
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED, \
    CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, \
    audit_response
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.utilities.templates.templates_processor import build_template


class DatatransTPVFormControler(FormGatewayInterface):
    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        url_transaction_id = "https://api.sandbox.datatrans.com/v1/transactions/secureFields"
        gateway_type = [x for x in gateway_type.split(";") if "DATATRANS_TPV" in x][0]
        datatrans_data = get_payment_gateway_configuration(gateway_type, hotel_code)

        in_production = False
        if datatrans_data.get("production", "").lower() == "true":
            in_production = True
            url_transaction_id = url_transaction_id.replace("sandbox.", "")


        datatrans_data['url_merchant'] = self.build_url_merchant(datatrans_data, sid, payment_order_id)

        currency = extra_data.get('currency', 'EUR')
        payload = {
            "amount": gateways_format_price(amount),
            "currency": currency,
            "returnUrl": datatrans_data['url_merchant']
        }

        header = self.build_header(datatrans_data)
        response_transaction = requests.post(url_transaction_id, data=json.dumps(payload), headers=header)

        audit_response(hotel_code, "DATATRANS_TPV", payment_order_id, sid, response_transaction.text, payload=json.dumps(payload), type_audit="Form Payment")

        if response_transaction.status_code == 201:
            transaction_id = response_transaction.json().get("transactionId")

            context = {
                "transaction_id": transaction_id,
                "payment_order_id": payment_order_id,
                "sid": sid,
                "url_redirect": datatrans_data['url_merchant'],
                "base_url": r.host_url,
                "in_production": in_production
            }

            if not Config.DEV:
                context['base_url'] = context['base_url'].replace("http", "https")

            template_path = "pages/cobrador/gateways/datatrans/_datatrans_tpv.html"
            language = extra_data.get("language")
            form_gateway = build_template(template_path, context, language)

            return form_gateway

    def process_gateway_response(self, hotel_code, gateway_type, response):

        try:
            transaction_id = response.get("uppTransactionId")
        except Exception as e:
            response_format = dict([x.split("=") for x in response.split("&")])
            transaction_id = response_format.get("uppTransactionId")

        url_check_status = "https://api.sandbox.datatrans.com/v1/transactions/%s" % transaction_id

        gateway_type = [x for x in gateway_type.split(";") if "DATATRANS_TPV" in x][0]
        datatrans_data = get_payment_gateway_configuration(gateway_type, hotel_code)

        if datatrans_data.get("production", "").lower() == "true":
            url_check_status = url_check_status.replace("sandbox.", "")

        header = self.build_header(datatrans_data)
        response_status = requests.get(url_check_status, headers=header)

        audit_response(hotel_code, "DATATRANS_TPV", r.values.get("identifier"), r.values.get("sid"), response_status.text)

        if response_status.status_code == 200:
            response_status_json = response_status.json()

            if response_status_json.get("detail") and response_status_json['detail'].get("authorize", {}).get("amount"):
                url_settle_payment = "https://api.sandbox.datatrans.com/v1/transactions/%s/authorize" % transaction_id


                if datatrans_data.get("production", "").lower() == "true":
                    url_settle_payment = url_settle_payment.replace("sandbox.", "")

                amount_to_pay = response_status_json['detail'].get("authorize", {}).get("amount")
                payload = {
                    "refno": r.values.get("identifier"),
                    "amount": amount_to_pay,
                    "autoSettle": True
                }

                response_settle_payment = requests.post(url_settle_payment, data=json.dumps(payload), headers=header)

                audit_response(hotel_code, "DATATRANS_TPV", r.values.get("identifier"), r.values.get("sid"),
                               response_settle_payment.text, payload=json.dumps(payload))

                if response_settle_payment.status_code == 200:
                    amount_to_pay = float("%s.%s" % (str(amount_to_pay)[:-2], str(amount_to_pay)[-2:]))
                    return {
                        "CODE": GATEWAY_SUCESS_RETURNED,
                        "GATEWAY_ORDER_ID": r.values.get("identifier"),
                        "GATEWAY_PAID_AMOUNT": amount_to_pay,
                    }

        return {
            "CODE": GATEWAY_ERROR_CODE_RETURNED,
            "GATEWAY_ORDER_ID": r.values.get("identifier"),
            "GATEWAY_PAID_AMOUNT": "",
        }

    def basic_auth(self, username, password):
        token = b64encode(f"{username}:{password}".encode('utf-8')).decode("ascii")
        return f'Basic {token}'

    def build_header(self, datatrans_data):
        header = {
            'Authorization': self.basic_auth(datatrans_data['merchant_id'], datatrans_data['password']),
            'Content-Type': 'application/json'
        }

        return header

    def build_url_merchant(self, datatrans_data, sid, payment_order_id):
        if Config.DEV:
            datatrans_data['url_merchant'] = "http://localhost:9090/cobrador/proxy/merchant_url"

        if "?" in datatrans_data['url_merchant']:
            datatrans_data['url_merchant'] += "&sid=%s" % sid

        else:
            datatrans_data['url_merchant'] += "?sid=%s" % sid

        datatrans_data['url_merchant'] += "&identifier=%s" % payment_order_id

        return datatrans_data['url_merchant']

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT
