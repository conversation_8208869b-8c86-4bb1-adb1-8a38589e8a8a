import datetime
import traceback
import uuid
from base64 import b64decode, b64encode, urlsafe_b64encode
from random import randint

from paraty import app
from paraty.config import Config
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, COBRADOR_QUEUE, \
    GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, \
    limit_amount_excedeed, get_limit_error_message, get_integration_name, audit_response, audit_error_web_payment, \
    get_config_property_value
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.utilities.languages.language_utils import SPANISH, get_language_code
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty.utilities.templates.templates_processor import build_template
import json
from flask import request, redirect
import re
from Cryptodome.Cipher import AES

from paraty_commons_3 import queue_utils
from paraty_commons_3.common_data.common_data_provider import get_web_section, get_integration_configuration_of_hotel, \
    get_rates_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty.utilities.languages import language_utils
from paraty_commons_3.session.session_utils import get_session_from_hotel

KEY = b")J@N1RfUeXn2a4u4"

ASK_BILLING_INFO = "Ask billing info"

class StripeController(GatewayInterface):
    def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
        extra_info = reservation.get("extraInfo", "{}")
        if extra_info:
            try:
                extra_info = json.loads(extra_info)
            except:
                logging.info("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
                          reservation.get("identifier"))

                return ""

            reservation_id = int(reservation.key.id)
            extra_info["last_merchant_order_used"] = merchant_order
            reservation["extraInfo"] = json.dumps(extra_info)

            reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
                                                                              reservation, hotel_code=hotel_code)
            logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
                     reservation_id)

            return reservation_updated_id

    def get_next_stripe_order(self, hotel_code, reservation):

        last_merchant_order_used = ""
        if reservation.get("extraInfo"):
            extra_info = json.loads(reservation.get("extraInfo"))
            last_merchant_order_used = extra_info.get("last_merchant_order_used")

        if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
            last_merchant_order_used = last_merchant_order_used[0:8]
            numerical_order = int(last_merchant_order_used)
            numerical_order += 1

            merchant_order = str(numerical_order)

            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
        else:

            merchant_order = reservation.get("identifier")
            # maybe this reservation was done before gateway was configured. So it could be an alphanumeric
            if not merchant_order.isnumeric():
                merchant_order = randint(10000000, 99999000)

            numerical_order = int(merchant_order)
            numerical_order += 1
            merchant_order = str(numerical_order)
            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

        # IMPORTANT: Even redsys return us an erro, they save ALWAYS the order.
        # so we have to save it in reservation for not received a duplicate order error in next payment!
        self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)

        return merchant_order

    def execute_payment_in_gateway(self, hotel_code, reservation, amount, extra_data={}):
        # Never change this import to global
        import stripe
        identifier = reservation.get("identifier", "")
        hotel = get_hotel_by_application_id(hotel_code)
        integration_name = get_integration_name(hotel_code)
        integration = get_payment_gateway_configuration(integration_name, hotel_code)

        if re.match("^.+-(.*)$", identifier):
            parts = re.search("^.+-(.*)$", identifier)
            identifier = parts.groups()[0]

        stripe.api_key = integration.get("private_key")
        stripe.api_version = "2020-08-27"

        try:
            extra_info = \
                json.loads(reservation.get("extraInfo", {}))
        except:
            extra_info = {}

        currency = extra_info.get("currency", "")
        if not currency:
            currency = get_configuration_property_value("Base Price Currency")
        if not currency:
            currency = "EUR"

        try:
            customer = stripe.Customer.retrieve(extra_info.get("stripe_customer_id"))

            payment_methods= stripe.PaymentMethod.list(customer=customer.stripe_id)
            if payment_methods.get("data"):
                payment_method_id = payment_methods["data"][0].stripe_id

                next_transaction_id = self.get_next_stripe_order(hotel_code, reservation)

                metadata = {
                    "Paraty Order Id": str(next_transaction_id)
                }

                if integration.get("add more metadata"):
                    payment_type = "PROGRAMMED"

                    try:
                        stack = traceback.extract_stack()

                        if stack and len(stack) >= 3:
                            caller = stack[-3]
                            if caller.name == "do_payment_in_reservation":
                                payment_type = "MANUAL"
                    except Exception as e:
                        logging.info("Error getting payment source %s" % e)

                    extra_description = ""

                    if extra_data.get("comments"):
                        extra_description = extra_data.get("comments")

                    is_extra = "false"
                    if extra_data.get("extra_payment"):
                        is_extra = "true"


                    metadata.update({
                        "property_id": hotel.get("id"),
                        "payment_type": payment_type,
                        "is_extra": is_extra,
                        "extra_description": extra_description
                    })

                    if reservation.get("startDate") and reservation.get("endDate"):
                        start_date = datetime.datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
                        end_date = datetime.datetime.strptime(reservation.get("endDate"), "%Y-%m-%d")

                        metadata.update({
                            "checkin_date": start_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                            "checkout_date": end_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                        })

                paymentintent = stripe.PaymentIntent.create(
                    customer=customer.stripe_id,
                    currency=currency,
                    amount=gateways_format_price(amount),
                    payment_method=payment_method_id,
                    off_session=True,
                    confirm=True,
                    metadata=metadata
                )

                try:
                    gateway_type = "STRIPE"
                    audit_response(hotel_code, gateway_type, next_transaction_id, "FROM_COBRADOR", json.dumps(paymentintent), type_audit="EXECUTE_PAYMENT")

                except Exception as e:
                    logging.error("Error auditing response for in STRIPE EXECUTE PAYMNET")
                    logging.error("Error auditing: %s", e)

                if paymentintent.get("status") == "succeeded":
                    logging.info("[STRIPE][%s][%s][PAYMENT_RESPONSE]: %s" % (hotel_code, identifier, json.dumps(paymentintent)))
                    return next_transaction_id
                else:
                    logging.info("[STRIPE][%s][%s][PAYMENT_RESPONSE]: %s" % (hotel_code, identifier, json.dumps(paymentintent)))
                    error_message = GATEWAY_ERROR_RETURNED
                    if paymentintent.get("last_payment_error"):
                        error_message = paymentintent.get("last_payment_error", {}).get("message", "ERROR")

                    return "%s%s%s" % (GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, error_message)
        except Exception as e:
            logging.info("Exception from stripe: %s" % e)

        return GATEWAY_ERROR_RETURNED

    def reservation_has_token(self, reservation):

        try:
            extra_info = json.loads(reservation.get("extraInfo"))
        except:
            extra_info = {}

        if extra_info.get("stripe_customer_id"):
            return True
        else:
            return False

    def gateway_has_token(self):
        return True

    def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
        # Never change this import to global
        import stripe
        integration_name = get_integration_name(hotel_code)
        integration = get_payment_gateway_configuration(integration_name, hotel_code)

        stripe.api_key = integration.get("private_key")
        stripe.api_version = "2020-08-27"


        order = stripe.PaymentIntent.search(query="metadata['Paraty Order Id']:'%s'" % order_to_refund)

        order = [i for i in order["data"] if i.status == "succeeded"]
        if order:
            order_dict = order[0]

            refundintent = stripe.Refund.create(
                amount=gateways_format_price(amount),
                payment_intent=order_dict.stripe_id,
                metadata={
                    "Paraty Order Id": "r" + str(order_to_refund)
                }
            )

            if refundintent.get("status") == "succeeded":
                logging.info("[STRIPE][%s][%s][REFUND_RESPONSE]: %s" % (hotel_code, order_to_refund, json.dumps(refundintent)))
                return order_to_refund
            logging.info("[STRIPE][%s][%s][REFUND_RESPONSE]: %s" % (hotel_code, order_to_refund, json.dumps(refundintent)))

        return GATEWAY_ERROR_RETURNED

    def translate_error(self, error):
        error_message = error.split(SEPARATOR_INFO_ERROR_ORDER)
        if len(error_message) > 1:
            return error_message[1]
        return error

    def get_configuration(self, hotel_code):
        integration_name = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

        return gateway_configuration

    def get_initial_order_id_from_extra_info(self, reservation_extra_info):
        return reservation_extra_info.get("paymentOrderId")

class StripeFormController(FormGatewayInterface):
    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        # Never change this import to global
        import stripe

        integration = get_payment_gateway_configuration(gateway_type, hotel_code)
        hotel = get_hotel_by_application_id(hotel_code)

        if extra_data.get("custom_payment_credentials"):
            integration["private_key"] = "sk_test_51KppvjAy5oBXvi38TjWqW8wiejvGUzdBnmfnCdxZQ4L5hIRQW8CrxIkft18OVV210VW3AQgrmWsFB54kvQdBnyQn00c2skNa1F"

        origin_integration = {}
        is_spa = False
        if "SPA" in gateway_type:
            is_spa = True

            if extra_data.get("spa_cart", {}).get("ticket"):
                main_ticket = extra_data["spa_cart"]["ticket"][0].get("ticket")
                origin_integration = get_remote_hotel_config(hotel, gateway_type, main_ticket)
                logging.info("change credentials to: %s" % json.dumps(origin_integration))


        language = extra_data.get("language", SPANISH)
        language_code = get_language_code(language)
        translations = language_utils.get_web_dictionary(extra_data.get("language", "SPANISH"))

        stripe.api_key = integration.get("private_key")
        if is_spa and origin_integration:
            stripe.api_key = origin_integration.get("private_key")
        stripe.api_version = "2020-08-27"

        url_merchant = integration.get("url_merchant", "")
        url_merchant += "&sid=" + str(sid) + "&reservation=" + str(payment_order_id)
        if is_spa and origin_integration:
            url_merchant += "&origin_hotel=" + origin_integration.get("hotel_code")
        if "?" not in url_merchant:
            url_merchant = url_merchant.replace("&", "?", 1)

        webhook_merchant = url_merchant

        if is_spa:
            webhook_merchant += "&from_backup_webhook=true"

        payment_order_metadata = {
            "Paraty Order Id": payment_order_id,
            "url_merchant": webhook_merchant
        }

        additional_services_amount = extra_data.get("additional_services_amount", 0)

        customer = None
        customer_name = payment_order_id
        if extra_data.get("location_modification"):
            customer_name = extra_data.get("location_modification")
        customers = stripe.Customer.search(query="name:'%s'" % customer_name)
        if customers.get("data"):
            customer = customers.data[0]
        if not customer:
            customer = stripe.Customer.create(name=customer_name)

        if integration.get("add more metadata"):

            payment_type = "IN_WEB"
            if extra_data.get("from_tpv_link"):
                payment_type = "PAID_BY_LINK"

            rate_name = ""
            rate_identifier = ""

            if extra_data.get("room_info"):
                rate_name = extra_data["room_info"][0].get("rate_name") or ""
                rate_identifier = extra_data["room_info"][0].get("rate_identifier") or ""

            payment_order_metadata.update({
                "property_id": hotel.get("id"),
                "payment_type": payment_type,
                "is_day_pass": is_spa,
                "price_supplements": float(additional_services_amount or 0),
                "rate_name": rate_name,
                "rate_id": get_rate_id_by_local_name(hotel, rate_identifier),
                "grouping_id": customer_name
            })

            if extra_data.get("start_date") and extra_data.get("end_date"):
                start_date = datetime.datetime.strptime(extra_data.get("start_date"), "%Y-%m-%d")
                end_date = datetime.datetime.strptime(extra_data.get("end_date"), "%Y-%m-%d")

                payment_order_metadata.update({
                    "checkin_date": start_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    "checkout_date": end_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                })

        currency = "eur"

        new_webhook = "https://payment-seeker.appspot.com/stripe/webhook?hotel_code=%s" % hotel_code
        webhooks = stripe.WebhookEndpoint.list()
        webhooks = [webhook.get("url") for webhook in webhooks.get("data", [])]
        if new_webhook not in webhooks:
            data = {
                "url": new_webhook,
                "enabled_events": [
                    "payment_intent.payment_failed",
                    "payment_intent.succeeded",
                    "charge.failed"
                ]
            }
            try:
                stripe.WebhookEndpoint.create(**data)
            except Exception as e:
                logging.warning(e)

        if integration.get("currency"):
            currency = integration.get("currency")
        elif extra_data.get("currency"):
            currency = extra_data.get("currency")

        currency = currency.lower()

        #be careful with gateways that currency comes from configs (not from request)
        num_nights = extra_data.get("num_nights")
        if limit_amount_excedeed(hotel_code, currency, num_nights, amount, sid=sid):
            return get_limit_error_message(language)

        if (currency != "mxn" or float(amount or 0) < 600) or extra_data.get("country", "ES").upper() != "MX":
            integration["msi payment"] = False
        else:
            if integration.get("msi only for callcenter"):
                if not extra_data.get("from_callcenter") and not extra_data.get("from_tpv_link"):
                    integration["msi payment"] = False

        section_name = get_configuration_property_value(hotel_code, "Texto a reemplazar")
        translations_section = get_web_section(hotel, section_name, extra_data.get("language", "SPANISH"), set_languages=True) or {}

        if integration.get("flex_amounts") or integration.get("msi payment") or integration.get("force checkout"):
            context = build_multiple_amount_checkout(hotel_code, integration, amount, currency, url_merchant, customer, sid, payment_order_id, additional_services_amount, extra_data.get("from_tpv_link"), extra_data, origin_integration, is_spa)
            custom_msi_option_description = translations_section.get("T_payment_msi_t", "")
            context["custom_msi_option_description"] = custom_msi_option_description if custom_msi_option_description else ""
            if integration.get("msi filter by tw and bw") and integration.get("msi travel window") and integration.get("msi booking window"):
                context["tw_bw_installments"] = check_installments_by_tw_and_bw(integration, extra_data, amount, translations_section)
            if integration.get("form_style", "") == "v2" or Config.DEV:
                context["form_style"] = integration["form_style"]

            if Config.DEV:
                amount = float(amount)
                installments = "3;6;9;12@@10;15@@20"
                context["installments"] = []
                for x in installments.split(";"):
                    if "@@" in x:
                        flex, extra = x.split("@@")
                        context["installments"].append({
                            "amount": "%.2f" % ((amount / int(flex)) * (1 + (int(extra) / 100))),
                            "flex": flex,
                            "extra": True
                        })
                    else:
                        context["installments"].append({
                            "amount": "%.2f" % (amount / int(x)),
                            "flex": x
                        })

                context["add_google_pay"] = True
                context["add_apple_pay"] = True
                context["add_amazon_pay"] = True
            
            form_gateway = build_template("pages/cobrador/gateways/stripe_flex_amounts_form.html", context,extra_data.get("language", "SPANISH"), hotel={"applicationId": hotel_code})
            return form_gateway
        normal_payment = True
        customer_id = customer.stripe_id
        if float(amount) == 0:
            normal_payment = False
            params = {
                "payment_method_types": ["card"],
                "customer": customer_id,
                "metadata": payment_order_metadata
            }
            logging.info("[STRIPE][BUILD_FORM][%s][%s] params for amount 0 setup intent: %s" % (hotel_code, str(payment_order_id), json.dumps(params)))

            setupIntent = stripe.SetupIntent.create(**params)


            audit_response(hotel_code, "STRIPE", payment_order_id, sid, json.dumps(setupIntent), payload=json.dumps(params))
            logging.info("[STRIPE][BUILD_FORM][%s][%s] response for payment intent: %s" % (
                hotel_code, str(payment_order_id), json.dumps(setupIntent)))
            client_secret = setupIntent.get("client_secret", "")
        else:
            params = {
                "customer": customer_id,
                "currency": currency,
                "amount": gateways_format_price(amount),
                "payment_method_types": ["card"],
                "setup_future_usage": "off_session",
                "metadata": payment_order_metadata
            }

            logging.info("[STRIPE][BUILD_FORM][%s][%s] params for payment intent: %s" % (
            hotel_code, str(payment_order_id), json.dumps(params)))

            paymentintent = stripe.PaymentIntent.create(**params)

            audit_response(hotel_code, "STRIPE", payment_order_id, sid, json.dumps(paymentintent),
                           payload=json.dumps(params))

            logging.info("[STRIPE][BUILD_FORM][%s][%s] response for payment intent: %s" % (
                hotel_code, str(payment_order_id), json.dumps(paymentintent)))

            client_secret = paymentintent.get("client_secret", "")

        public_key = integration.get("public_key", "")
        if is_spa and origin_integration:
            public_key = origin_integration.get("public_key", "")

        context = {
            "public_key": public_key,
            "url_merchant": url_merchant,
            "normal_payment": normal_payment,
            "client_secret": client_secret,
            "lang": language_code,
            "sid": sid,
            "is_spa": is_spa,
            "ask_billing_info": integration.get(ASK_BILLING_INFO) or False,
            "billing_name_label": translations.get("T_BILLING_NAME"),
            "billing_name_placeholder": translations.get("T_BILLING_NAME_PLACEHOLDER")
        }

        if extra_data.get("custom_payment_credentials"):
            if extra_data["custom_payment_credentials"].get("public_key"):
                context["public_key"] = extra_data["custom_payment_credentials"]["public_key"]
            if extra_data["custom_payment_credentials"].get("client_secret"):
                context["client_secret"] = extra_data["custom_payment_credentials"]["client_secret"]

        logging.info(f"Context: {context}")

        template_path = "pages/cobrador/gateways/stripe_insite_form.html"
        form_gateway = build_template(template_path, context, extra_data.get("language", "SPANISH"))
        return form_gateway

    def process_gateway_response(self, hotel_code, gateway_type, response):
        # Never change this import to global
        import stripe
        integration = get_payment_gateway_configuration(gateway_type, hotel_code)
        origin_hotel = request.args.get("origin_hotel", "")
        has_public_key = request.values.get("public_key")

        stripe.api_key = integration.get("private_key")

        if origin_hotel:
            origin_integration = get_activated_stripe_integration_config(origin_hotel)
            if origin_integration:
                stripe.api_key = origin_integration.get("private_key")

        if has_public_key:
            stripe.api_key = has_public_key

        stripe.api_version = "2020-08-27"

        payment_intent_id = request.args.get("payment_intent", "")
        setup_intent = request.args.get("setup_intent", "")
        stripe_sid = request.args.get("stripe_sid", "")
        payment_order_id = request.args.get("reservation", "")
        sid = request.args.get("sid", "")


        if (not setup_intent and not payment_intent_id) and stripe_sid:
            reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
                                                                                      [("sid", "=", stripe_sid)])
            if reservation_metadata:
                reservation_metadata = reservation_metadata[0]
                reservation_metadata_extra_info = json.loads(reservation_metadata.get("extraInfo", "{}"))
                checkout_id = reservation_metadata_extra_info.get("checkout_id")
                checkout = stripe.checkout.Session.retrieve(id=checkout_id)
                setup_intent = checkout.get("setup_intent", None)
                payment_intent_id = checkout.get("payment_intent", None)

        if has_public_key:
            response_from_stripe = stripe.PaymentIntent.retrieve(id=payment_intent_id, client_secret=response.get("payment_intent_client_secret"))
        elif payment_intent_id:
            response_from_stripe = stripe.PaymentIntent.retrieve(id=payment_intent_id)
        else:
            response_from_stripe = stripe.SetupIntent.retrieve(id=setup_intent)

        if not has_public_key:
            stripe.Customer.modify(response_from_stripe.customer, email="")

        if response_from_stripe.object == "payment_intent":
            if response_from_stripe.status == "requires_capture":
                stripe.PaymentIntent.capture(response_from_stripe.id)
                response_from_stripe = stripe.PaymentIntent.retrieve(id=response_from_stripe.id)

        user_details_for_metadata = {}

        session = get_session_from_hotel(hotel_code, sid)
        personal_details = {}
        if session:
            personal_details = session.get('personal_details_')

        if personal_details:
            user_details_for_metadata["reservation_name"] = "%s %s" % (
                personal_details.get("name") or "", personal_details.get("lastName1") or "")

            user_details_for_metadata["guest_name"] = user_details_for_metadata["reservation_name"]


        if user_details_for_metadata and not has_public_key:
            if response_from_stripe.id.startswith("pi_"):
                stripe.PaymentIntent.modify(
                    response_from_stripe.id,
                    metadata=user_details_for_metadata,
                )
            else:
                stripe.SetupIntent.modify(
                    response_from_stripe.id,
                    metadata=user_details_for_metadata,
                )

        logging.info("[STRIPE][PROCESS_RESPONSE][%s][%s] response for payment intent: %s" % (
            hotel_code, str(payment_order_id), json.dumps(response_from_stripe)))

        audit_response(hotel_code, "STRIPE", payment_order_id, "", json.dumps(response_from_stripe), payload=json.dumps({"id": payment_intent_id}))
        metadata = response_from_stripe.get("metadata", {})

        amount = str(response_from_stripe.get("amount", 0))

        if len(amount) > 2:
            price_euros = amount[:-2]
            price_cents = amount[-2:]
            amount = price_euros + "." + price_cents

        amount = float(amount)

        to_extra_info = {}

        if response_from_stripe.get("status", "") == "succeeded":
            to_extra_info["stripe_customer_id"] = response_from_stripe.get("customer", "")
            if origin_hotel:
                to_extra_info["stripe_origin"] = origin_hotel
            code = GATEWAY_SUCESS_RETURNED
        else:
            code = GATEWAY_ERROR_CODE_RETURNED

        cobrador_response = {
            "CODE": code,
            "GATEWAY_ORDER_ID": metadata.get("Paraty Order Id", request.values.get("reservation")),
            "GATEWAY_PAID_AMOUNT": amount,
            "GATEWAY_EXTRA_INFO": to_extra_info,
            "GATEWAY_EXTERNAL_ID": response_from_stripe.id
        }

        if code == "KO":

            cobrador_response["SKIP_ERROR_AUDIT"] = True

        return cobrador_response

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT


def build_multiple_amount_checkout(hotel_code, integration, amount, currency, url_merchant, customer, sid, payment_order_id, additional_services_amount, from_tpv_link, extra_data, origin_integration, is_spa):
    rate_name = ""
    rate_identifier = ""

    if extra_data.get("room_info"):
        rate_name = extra_data["room_info"][0].get("rate_name") or ""
        rate_identifier = extra_data["room_info"][0].get("rate_identifier") or ""


    payment_info = {
        "integration": integration,
        "origin_integration": origin_integration,
        "hotel_code": hotel_code,
        "sid": sid,
        "amount": amount,
        "currency": currency,
        "url_merchant": url_merchant,
        "customer_id": customer.stripe_id,
        "customer_name": customer.name,
        "payment_order_id": payment_order_id,
        "additional_services_amount": additional_services_amount,
        "is_spa": is_spa,
        "start_date": extra_data.get("start_date"),
        "end_date": extra_data.get("end_date"),
        "from_tpv_link": extra_data.get("from_tpv_link"),
        "rate_name": rate_name,
        "rate_identifier": rate_identifier
    }

    payment_info_string = json.dumps(payment_info)

    encryptor = AES.new(KEY, AES.MODE_EAX)

    payload = encryptor.encrypt(payment_info_string.encode("utf-8"))

    payload = b64encode(encryptor.nonce + payload).decode("utf-8")

    amounts = []
    if integration.get("flex_amounts"):
        amounts.extend(integration.get("flex_amounts").strip(";").split(";"))
        amounts = [float(x) for x in amounts]

    if amounts and from_tpv_link:
        amounts = [100]

    msi_payment = False

    if integration.get("msi payment"):
        if integration.get("msi filter by rate name") and extra_data.get("room_info") and extra_data["room_info"][0].get("rate_identifier"):
            rate_identifier = extra_data["room_info"][0]["rate_identifier"]
            keywords = integration.get("msi filter by rate name").strip(";").split(";")

            for keyword in keywords:
                if keyword.lower() in rate_identifier.lower():
                    msi_payment = True
                    amounts = [100]
        else:
            msi_payment = True
            amounts = [100]

    if not amounts:
         amounts = [100]

    context = {
        "payload": payload,
        "amounts": amounts,
        "msi_payment": msi_payment,
        "dev": app.config.get("DEV")
    }

    return context


@app.route("/stripe/redirect", methods=["POST"])
def redirect_url():
    form_data = request.form

    payload = form_data.get("payload")
    payload = b64decode(payload)

    nounce = payload[:16]

    payload = payload[16:]

    encryptor = AES.new(KEY, AES.MODE_EAX, nonce=nounce)

    payload = encryptor.decrypt(payload)

    payment_data = json.loads(payload)

    integration = payment_data.get("integration")
    origin_integration = payment_data.get("origin_integration")
    hotel_code = payment_data.get("hotel_code")
    hotel = get_hotel_by_application_id(hotel_code)
    sid = payment_data.get("sid")
    amount = float(payment_data.get("amount"))
    payment_amount = form_data.get("payment-amount", 100)
    msi_payment = False
    # if "forcemsi@@" in payment_amount:
    #     total_amount_to_pay, installment = payment_amount.split("forcemsi@@")[1].split("@@")
    #     total_amount_to_pay = float(total_amount_to_pay)
    #     installment = float(installment)
    #     msi_payment = True
    # else:
    percent_to_pay = float(form_data.get("payment-amount", 100))
    url_merchant = payment_data.get("url_merchant")
    customer_id = payment_data.get("customer_id")
    currency = payment_data.get("currency")
    payment_order_id = payment_data.get("payment_order_id")
    is_spa = payment_data.get("is_spa")
    rate_identifier = payment_data.get("rate_identifier")
    rate_name = payment_data.get("rate_name")
    from_tpv_link = payment_data.get("from_tpv_link")
    customer_name = payment_data.get("customer_name")
    additional_services_amount = float(payment_data.get("additional_services_amount", 0))

    url_ko = integration.get("url_ko", "")
    url_ko += "&sid=" + str(sid)
    if "?" not in url_ko:
        url_ko = url_ko.replace("&", "?", 1)
    if not msi_payment:
        total_amount_to_pay = (amount-additional_services_amount) * percent_to_pay/100

    body = {
        "cancel_url": url_ko,
        "customer": customer_id,
        "currency": currency,
        "payment_method_types": ["card"]
    }

    session_expiration = integration.get("session expiration", "")

    if session_expiration and session_expiration.isnumeric():
        session_expiration = int(session_expiration)
        expire = datetime.datetime.utcnow()
        expire += datetime.timedelta(minutes=session_expiration)
        session_expiration = int(expire.strftime("%s"))
    else:
        expire = datetime.datetime.utcnow()
        expire += datetime.timedelta(hours=2)
        session_expiration = int(expire.strftime("%s"))

    temporal_sid = str(uuid.uuid4())

    url_merchant += "&%s=%s" % ("stripe_sid", temporal_sid)
    if "?" not in url_merchant:
        url_merchant = url_merchant.replace("&", "?", 1)

    webhook_merchant = url_merchant
    if is_spa:
        webhook_merchant += "&from_backup_webhook=true"

    metadata = {
        "Paraty Order Id": payment_order_id,
        "url_merchant": webhook_merchant
    }
    if integration.get("add more metadata"):

        payment_type = "IN_WEB"
        if from_tpv_link:
            payment_type = "PAID_BY_LINK"

        metadata.update({
            "property_id": hotel.get("id"),
            "payment_type": payment_type,
            "is_day_pass": is_spa,
            "rate_name": rate_name,
            "rate_identifier": get_rate_id_by_local_name(hotel, rate_identifier),
            "price_supplements": float(additional_services_amount or 0),
            "grouping_id": customer_name
        })

        if payment_data.get("start_date") and payment_data.get("end_date"):
            start_date = datetime.datetime.strptime(payment_data.get("start_date"), "%Y-%m-%d")
            end_date = datetime.datetime.strptime(payment_data.get("end_date"), "%Y-%m-%d")

            metadata.update({
                "checkin_date": start_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "checkout_date": end_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            })

    session = get_session_from_hotel(hotel_code, sid)
    if session:
        personal_details = session.get('personal_details_') or {}

        if personal_details:
            metadata["reservation_name"] = "%s %s" % (
                personal_details.get("name") or "", personal_details.get("lastName1") or "")

            metadata["guest_name"] = metadata["reservation_name"]

    body["expires_at"] = session_expiration

    if total_amount_to_pay + additional_services_amount == 0:
        body["mode"] = "setup"
        body["setup_intent_data"] = {
            "metadata": metadata
        }
        if origin_integration:
            body["setup_intent_data"]["metadata"]["origin_hotel"] = origin_integration
    else:
        body["mode"] = "payment"
        body["payment_intent_data"] = {
            "setup_future_usage": "off_session",
            "metadata": metadata
        }
        if origin_integration:
            body["payment_intent_data"]["metadata"]["origin_hotel"] = origin_integration

        body["line_items"]= [
            {
                "price_data": {
                    "currency": currency,
                    "product_data": {
                        "name": "Habitaciones %.2f %%" % (percent_to_pay if not msi_payment else installment)
                    },
                    "unit_amount": gateways_format_price(total_amount_to_pay)
                },
                "quantity": 1
            }
        ]

        if additional_services_amount:
            body["line_items"].append({
                "price_data": {
                    "currency": currency,
                    "product_data": {
                        "name": "Servicios adicionales"
                    },
                    "unit_amount": gateways_format_price(additional_services_amount)
                },
                "quantity": 1
            })
        if form_data.get("payment-method") == "msi-payment":
            body["payment_method_options"] = {
                "card": {
                    "installments": {
                        "enabled": True
                    }
                }
            }

            if msi_payment:
                body["payment_method_options"] = {
                "card": {
                    "installments": {
                        "plan": {
                            "type": "fixed_count",
                            "count": installment,
                            "interval": "month"
                        }
                    }
                }
            }


    body["success_url"] = url_merchant
    # Never change this import to global
    import stripe
    stripe.api_key = integration.get("private_key")
    if origin_integration:
        stripe.api_key = origin_integration.get("private_key")

    stripe.api_version = "2020-08-27"

    checkout_element = stripe.checkout.Session.create(**body)

    extra_info = {
        "checkout_id": checkout_element.stripe_id
    }
    properties = {
        "sid": temporal_sid,
        "identifier": payment_order_id,
        "hotel_code": hotel_code,
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "extraInfo": json.dumps(extra_info)
    }

    datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")


    return redirect(checkout_element.get("url"))


@app.route("/stripe/webhook", methods=["POST"])
def stripe_webhook():
    body = request.json
    hotel_code = request.args.get("hotel_code")
    if hotel_code == "backend-spa3":
        return "KO"
    origin_hotel = body.get("data", {}).get("object", {}).get("metadata", {}).get("origin_hotel", "")
    logging.info("[stripe][webhook] %s" % json.dumps(body, indent=4))
    if body.get("type") == "payment_intent.payment_failed":
        gateway_type = get_integration_name(hotel_code)
        integration = get_payment_gateway_configuration(gateway_type, hotel_code)

        # Never change this import to global
        import stripe
        stripe.api_key = integration.get("private_key")
        if origin_hotel:
            origin_integration = get_activated_stripe_integration_config(origin_hotel)
            if origin_integration:
                stripe.api_key = origin_integration.get("private_key")

        stripe.api_version = "2020-08-27"

        customer_id = body.get("data", {}).get("object", {}).get("customer")
        if customer_id:
            payment_object = body.get("data", {}).get("object", {})
            customer = stripe.Customer.retrieve(id=customer_id)
            charges = payment_object.get("charges", {}).get("data", [])
            charges = [i for i in charges if i.get("status") == "failed"]
            charges.sort(key=lambda x: x.get("created"), reverse=True)

            error_message = None
            if charges:
                charge = charges[0]
                error_message = charge.get("failure_message")

            if not error_message:
                last_error = payment_object.get("last_payment_error", {})

                if last_error:
                    error_message = last_error.get("message")

            if error_message:
                order_id = None
                if payment_object.get("object") == "payment_intent":
                    order_id = payment_object.get("metadata", {}).get("Paraty Order Id") or None

                audit_error_web_payment(hotel_code, customer.get("name"),
                                        body.get("data", {}).get("object", {}).get("amount", 0) / 100, error_message,
                                        order_id=order_id)
    elif body.get("type") == "payment_intent.succeeded":
        intent = body.get("data", {}).get("object")
        if intent:
            object_type = intent.get("object", "")
            object_id = intent.get("id", "")
            merchant_url = intent.get("metadata", {}).get("url_merchant", "")
            if merchant_url and (hotel_code in merchant_url or (origin_hotel and origin_hotel in merchant_url)):
                merchant_url = "%s&%s=%s" % (merchant_url, object_type, object_id)
                if "?" not in merchant_url:
                    merchant_url = merchant_url.replace("&", "?", 1)

                payload = {
                    "merchant_url": merchant_url
                }

                data = {
                    "task_id": str(uuid.uuid4()),
                    "data": urlsafe_b64encode(json.dumps(payload).encode("utf-8")).decode("utf-8")
                }

                try:
                    queue_utils.create_task('execute_fallback_task', json.dumps(data),
                                            queue_name=COBRADOR_QUEUE,
                                            task_name='fallback_task__%s' % object_id, in_seconds=120)
                except Exception as e:
                    logging.warning("Error controlled. Payment already differed: %s", object_id)
                    logging.warning("%s" % e)
                    
    elif body.get("type") == "charge.failed":
        intent = body.get("data", {}).get("object")
        if intent:
            print(intent)
            payment_intent = intent.get("payment_intent")
            if payment_intent:
                hotel_code = request.args.get("hotel_code")
                gateway_type = get_integration_name(hotel_code)
                integration = get_payment_gateway_configuration(gateway_type, hotel_code)
                # Never change this import to global
                import stripe
                stripe.api_key = integration.get("private_key")
                if origin_hotel:
                    origin_integration = get_activated_stripe_integration_config(origin_hotel)
                    if origin_integration:
                        stripe.api_key = origin_integration.get("private_key")
                stripe.api_version = "2020-08-27"
                session_checkouts = stripe.checkout.Session.list(payment_intent=payment_intent, limit=3)
                if session_checkouts.data:
                    for session in session_checkouts:
                        if session.customer:
                            stripe.Customer.modify(session.customer, email="")
                        stripe.checkout.Session.expire(session.id)
                else:
                    stripe.PaymentIntent.cancel(payment_intent)
    return "OK"




def get_remote_hotel_config(hotel, gateway_name, ticket_key):
    integration_config = get_integration_configuration_of_hotel(hotel, gateway_name)
    result = {}
    if integration_config:
        integration_config = integration_config[0]
        room_map = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in integration_config.get("roomMap", [])}

        origin_hotel_code = room_map.get(ticket_key, "")
        result = get_activated_stripe_integration_config(origin_hotel_code)
        if result:
            result["hotel_code"] = origin_hotel_code
    return result


def get_activated_stripe_integration_config(hotel_code):
    gateway_name = get_config_property_value(hotel_code, "Use Payment Gateway")
    result = {}
    if gateway_name:
        gateway_name = gateway_name.split(";")
        gateway_name = [i for i in gateway_name if "STRIPE" in i]

        if gateway_name:
            gateway_name = gateway_name[0]
            result = get_payment_gateway_configuration(gateway_name, hotel_code)

    return result

def check_installments_by_tw_and_bw(integration, extra_data, amount, translations_section):
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    start_date_formatted = extra_data.get("start_date")
    window_installments = integration.get("msi filter by tw and bw")

    booking_window = integration.get("msi booking window").split("@@")
    if len(booking_window) > 1:
        apply_booking_window = current_date >= booking_window[0] and current_date <= booking_window[1]
    else:
        apply_booking_window = current_date == booking_window[0]

    travel_window = integration.get("msi travel window").split("@@")
    if len(travel_window) > 1:
        apply_travel_window = start_date_formatted >= travel_window[0] and start_date_formatted <= travel_window[1]
    else:
        apply_travel_window = start_date_formatted == travel_window[0]

    tw_bw_installments = []
    T_payment_msi_bw_tw = translations_section.get("T_payment_msi_bw_tw")
    if apply_travel_window and apply_booking_window and T_payment_msi_bw_tw:
        for installment in window_installments.split(";"):
            tw_bw_installments.append({
                "price": float(amount) / float(installment),
                "installment": installment,
                "description": T_payment_msi_bw_tw.replace("@@INSTALLMENTS@@", installment)
            })

    return tw_bw_installments


@timed_cache(hours=24)
def get_rate_id_by_local_name(hotel, rate_name):
    rate_id = ""
    rates = get_rates_of_hotel(hotel, only_enabled=False, include_removed=True)
    for rate in rates:
        if rate.get("localName") == rate_name:
            rate_id = rate.get("id")
            break

    return rate_id
