from random import randint

import json
import logging
import requests
import urllib.parse
from paraty_commons_3.datastore import datastore_communicator


from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface

from paraty.pages.cobrador.cobrador_utils import build_common_iframe_html, get_payment_gateway_configuration, \
	send_cobrador_response_to_client_server, decrypt_paraty_own_response_from_api, gateways_format_price, \
	get_integration_name, audit_response

from paraty.pages.cobrador.gateways.payu import generate_signature

from paraty.pages.cobrador.cobrador_constants import CONTENT_TYPE_TEXT, GATEWAY_ERROR_RETURNED, GATEWAY_SUCESS_RETURNED

APPROVED = "APPROVED"
class PayuInsiteFormControler(FormGatewayInterface):

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		# this method has to return ALWAYS an iframe, we are going to use PEP common solution
		form_gateway = build_common_iframe_html(hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data)
		return form_gateway

	def execute_payment_in_gateway_by_api(self, gateway_type, hotel_code, datas, res_client_server=''):
		payment_order_id = datas.get('payment_order_id')
		logging.info(f'[PAYU - {payment_order_id}] Executing execute_payment_in_gateway_by_api')
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		logging.info(f'[PAYU - {payment_order_id}] Request to tokenize')
		currency = datas.get('currency', gateway_configuration.get('currency_code'))

		url = gateway_configuration.get('payment_url')
		request_to_tokenize = get_token_json(datas, gateway_configuration)
		response_tokenization = do_call_to_payu_api(url, request_to_tokenize)
		response_token_json = response_tokenization.json()
		logging.info(f"[PAYU - {payment_order_id}] Tokenization response: {response_token_json}")

		valid_response = is_valid_response(response_token_json)
		creditCardToken = response_token_json.get('creditCardToken', {})
		if valid_response and creditCardToken:
			extra_params = {
				'creditCardTokenId': creditCardToken.get('creditCardTokenId', ''),
				'identificationNumber': creditCardToken.get('identificationNumber', '')
			}
			amount = datas.get('amount', 0)
			sid = datas.get('sid', '')
			if not float(amount):
				logging.info(f'[PAYU - {payment_order_id}] Amount is zero, calling to cobrador/proxy/merchant_url')
				res_client_server = send_cobrador_response_to_client_server(gateway_configuration, sid, amount, payment_order_id, extra_params)
			else:
				logging.info(f'[PAYU - {payment_order_id}] Amount is {amount}, we have to do a payment')
				extra_info_json = {
					'name': datas.get('holder_name', ''),
					'email': datas.get('customerEmail', '')
				}
				request_payment = get_payment_by_token_json(gateway_configuration, amount, payment_order_id, False, currency, creditCardToken.get('creditCardTokenId', ''), extra_info_json)
				logging.info(f"[PAYU - {payment_order_id}] Payment by token request: {request_payment}")

				response_payment = do_call_to_payu_api(url, request_payment)
				response_payment_json = response_payment.json()
				logging.info(f"[PAYU - {payment_order_id}] Payment by token response: {response_payment_json}")

				try:
					gateway_type = "PAYU_INSITE"
					audit_response(hotel_code, gateway_type, payment_order_id, sid, json.dumps(response_payment_json))

				except Exception as e:
					logging.error("Error auditing response for in PAYU_INSITE in process response")
					logging.error("Error auditing: %s", e)

				if is_approved_payment(response_payment_json):
					logging.info(f"[PAYU - {payment_order_id}] Payment is approved")
					extra_params["orderId"] = response_payment_json.get('transactionResponse', {}).get('orderId', '')
					res_client_server = send_cobrador_response_to_client_server(gateway_configuration, sid, amount, payment_order_id, extra_params)
					logging.info(f"[PAYU - {payment_order_id}] Client server response: {res_client_server}")
			if res_client_server:
				logging.info(f'Payment OK {payment_order_id}')
				return payment_order_id
		return "ERROR "

	def process_gateway_response(self, hotel_code, gateway_type, response):
		if not isinstance(response, dict):
			response = dict(urllib.parse.parse_qsl(response))
		cobrador_parameters = response.get("cobrador_parameters")
		cobrador_signature = response.get("cobrador_signature")
		return decrypt_paraty_own_response_from_api(hotel_code, gateway_type, cobrador_parameters, cobrador_signature)

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT

def is_approved_payment(response):
	transaction = response.get('transactionResponse', {})
	return is_valid_response(response) and transaction.get('state') == APPROVED and transaction.get('responseCode') == APPROVED


def do_call_to_payu_api(url, request):
	headers = {
		"Content-Type": "application/json; charset=utf-8",
		"Accept": "application/json"
	}
	if isinstance(request, dict):
		request = json.dumps(request)
	return requests.post(url, data=request, headers=headers, timeout=20)


class PayuInsiteController(GatewayInterface):
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		logging.info(f"[PAYU] Execute execute_payment_in_gateway. Reservation: {reservation.get('identifier')}, Amount: {amount}")
		# amount = gateways_format_price(amount)
		amount = float(amount)
		logging.info(f"[PAYU] Formated amount: {amount}")
		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)
		if gateway_configuration:
			logging.info(f"[PAYU] Gateway configuration: {gateway_configuration}")
			merchant_order = get_merchant_order(reservation)
			request_payment = get_payment_by_token_json(gateway_configuration, amount, merchant_order, reservation, False, False)
			url = gateway_configuration.get('payment_url')
			response_payment = do_call_to_payu_api(url, request_payment)
			response_payment_json = response_payment.json()
			logging.info(f"[PAYU - {merchant_order}] Payment by token response: {response_payment_json}")

			try:
				gateway_type = "PAYU_INSITE"
				audit_response(hotel_code, gateway_type, merchant_order, "FROM_COBRADOR", json.dumps(response_payment_json))

			except Exception as e:
				logging.error("Error auditing response for in PAYU_INSITE in EXECUTE")
				logging.error("Error auditing: %s", e)

			if is_approved_payment(response_payment_json):
				self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)
				return str(response_payment_json.get('transactionResponse', {}).get('orderId', merchant_order))

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		logging.info(f"[PAYU - {order_to_refund}] Execute refund in gateway. Reservation: {reservation.get('identifier')}, Amount: {amount}")

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		if gateway_configuration:
			logging.info(f"[PAYU - {order_to_refund}] Gateway configuration: {gateway_configuration}")

			url = gateway_configuration.get('payment_url')
			refund_request = get_refund_request(gateway_configuration, order_to_refund, reservation, amount)
			logging.info(f"[PAYU - {order_to_refund}] Refund request: {refund_request}")
			refund_response = do_call_to_payu_api(url, refund_request)
			refund_response_json = refund_response.json()
			logging.info(f"[PAYU - {order_to_refund}] Refund response: {refund_response_json}")
			if is_valid_response(refund_response_json) and refund_response_json.get('transactionResponse', {}).get('state') == APPROVED:
				return order_to_refund

		logging.info(f"[PAYU - {order_to_refund}] Error in refund identifier: {reservation.get('identifier')} - order: {order_to_refund}")
		return GATEWAY_ERROR_RETURNED


	def reservation_has_token(self, reservation):
		extra_info = reservation.get('extraInfo', "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning(f"[TRUST] Reservation extra info was not JSON")
				extra_info = {}
			creditCardTokenId = extra_info.get("payment_by_api_info", {}).get('creditCardTokenId', '')
			if creditCardTokenId:
				return creditCardTokenId
			return False

	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
							  reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			extra_info["last_merchant_order_used"] = merchant_order
			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
																			  reservation, hotel_code=hotel_code)
			logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
						 reservation_id)

			return reservation_updated_id

	def get_initial_order_id_from_extra_info(self, extra_info):
		return extra_info.get("paymentOrderId")

def get_refund_request(gateway_configuration, order_to_refund, reservation, amount):

	orderId = order_to_refund if order_to_refund != reservation.get('identifier') else False
	extra_info = json.loads(reservation.get("extraInfo", "{}"))
	if not orderId:
		orderId = str(extra_info.get('payment_by_api_info', {}).get('orderId'))

	transaction_id, max_amount_to_refund, partial_refund = get_transaction_id(gateway_configuration, orderId)
	currency = extra_info.get('currency', 'MXN')
	# Requests is not the same if it's a partial refund or not
	if float(amount) < max_amount_to_refund or partial_refund:
		refund_request = get_partial_refund_request(gateway_configuration, amount, currency, orderId, transaction_id)
	else:
		refund_request = get_full_refund_request(gateway_configuration, orderId, transaction_id)
	return refund_request


def get_partial_refund_request(gateway_configuration, amount, currency, orderId, transaction_id):
	return {
		"command": "SUBMIT_TRANSACTION",
		"language": "es",
		"merchant": {
			"apiKey": gateway_configuration.get('api_key'),
			"apiLogin": gateway_configuration.get('api_login')
		},
		"transaction": {
			"additionalValues": {
				"TX_VALUE": {
					"value": amount,
					"currency": currency
				}
			},
			"order": {
				"id": orderId
			},
			"parentTransactionId": transaction_id,
			"reason": "Reason for requesting the refund or cancellation of the transaction",
			"type": "PARTIAL_REFUND"
		}
	}


def get_full_refund_request(gateway_configuration, orderId, transaction_id):
	return {
		"language": "es",
		"command": "SUBMIT_TRANSACTION",
		"merchant": {
			"apiKey": gateway_configuration.get('api_key'),
			"apiLogin": gateway_configuration.get('api_login')
		},
		"transaction": {
			"order": {
				"id": orderId
			},
			"type": "REFUND",
			"reason": "Reason for requesting the refund or cancellation of the transaction",
			"parentTransactionId": transaction_id
		},
		"test": True if gateway_configuration.get('test') else False
	}

def get_transaction_id(gateway_configuration, orderId):
	request_query_by_orderId = get_request_query_by_orderId(gateway_configuration, orderId)
	url = gateway_configuration.get('query_url')
	response_query_by_orderId = do_call_to_payu_api(url, request_query_by_orderId)
	response_query_by_orderId_json = response_query_by_orderId.json()
	if is_valid_response(response_query_by_orderId_json):
		transactions = response_query_by_orderId_json.get('result', {}).get('payload', {}).get('transactions', [])
		for transaction in transactions:
			if transaction.get('type', '') == 'AUTHORIZATION_AND_CAPTURE':
				transaction_id = transaction.get('id')
				partial_refund = True if len(transactions) > 1 else False
				max_amount_to_refund = transaction.get('additionalValues').get('CURRENT_TX_VALUE').get('value', 0)
				return transaction_id, partial_refund, max_amount_to_refund
	return '', 0


def get_request_query_by_orderId(gateway_configuration, orderId):
	return {
		"test": True if gateway_configuration.get('test') else False,
		"language": "es",
		"command": "ORDER_DETAIL",
		"merchant": {
			"apiLogin": gateway_configuration.get('api_login'),
			"apiKey": gateway_configuration.get('api_key')
		},
		"details": {
			"orderId": orderId
		}
	}


def get_merchant_order(reservation):
	last_merchant_order_used = ""
	if reservation.get("extraInfo"):
		extra_info = json.loads(reservation.get("extraInfo"))
		last_merchant_order_used = extra_info.get("last_merchant_order_used")

	if last_merchant_order_used and "error" not in last_merchant_order_used.lower():
		last_merchant_order_used = last_merchant_order_used[0:8]
		numerical_order = int(last_merchant_order_used)
		numerical_order += 1

		merchant_order = str(numerical_order)

		logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
	else:

		merchant_order = reservation.get("identifier")
		if not merchant_order.isnumeric():
			merchant_order = randint(10000000, 99999000)

		numerical_order = int(merchant_order)
		numerical_order += 1
		merchant_order = str(numerical_order)
		logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
	return merchant_order


def get_payment_by_token_json(gateway_configuration, amount, merchant_order, reservation, currency=False, creditCardTokenId=False, extra_info_json={}):
	extra_info = {}
	if reservation:
		extra_info = json.loads(reservation.get("extraInfo", "{}"))
		currency = extra_info.get('currency', 'MXN')
	signature = generate_signature(gateway_configuration, amount, merchant_order, currency)
	if not creditCardTokenId and reservation:
		creditCardTokenId = extra_info.get("payment_by_api_info", {}).get(
			'creditCardTokenId', '')

	json_to_return = {
		"language": "es",
		"command": "SUBMIT_TRANSACTION",
		"merchant": {
			"apiKey": gateway_configuration.get('api_key'),
			"apiLogin": gateway_configuration.get('api_login')
		},
		"transaction": {
			"order": {
				"accountId": gateway_configuration.get('account_id'),
				"referenceCode": merchant_order,
				"description": "Payment by token",
				"language": "es",
				"signature": signature,
				"notifyUrl": "",
				"additionalValues": {
					"TX_VALUE": {
						"value": amount,
						"currency": currency
					}
				},
			},
			"payer": {
				"merchantPayerId": "1",
			},
			"creditCardTokenId": creditCardTokenId,
			"creditCard": {
				"processWithoutCvv2": True
			},
			"extraParameters": {"INSTALLMENTS_NUMBER": 1},
			"type": "AUTHORIZATION_AND_CAPTURE",
			"paymentMethod": "VISA",
			"paymentCountry": "MX",
		},
		"test": True if gateway_configuration.get('test') else False,
	}
	# if extra_info_json:
	# 	json_to_return["transaction"]["payer"]["fullName"] = extra_info_json.get("name", '')
	# 	json_to_return["transaction"]["payer"]["emailAdress"] = extra_info_json.get("email", '')
	return json_to_return


def is_valid_response(response_json):
	if response_json:
		if response_json.get('code') == 'SUCCESS':
			return True
	return False


def get_token_json(datas, gateway_configuration):
	expiry_date = get_expiration_date(datas)
	return json.dumps({
		"language": "es",
		"command": "CREATE_TOKEN",
		"merchant": {
			"apiLogin": gateway_configuration.get('api_login'),
			"apiKey": gateway_configuration.get('api_key')
		},
		"creditCardToken": {
			"payerId": "10",
			"name": datas.get('holder_name', ''),
			"identificationNumber": "32144457",
			"paymentMethod": "VISA",
			"number": datas.get("cardNumber"),
			"expirationDate": expiry_date
		}
	})


def get_expiration_date(datas):
	year = datas.get("expiryYear")
	month = datas.get("expiryMonth")
	if len(str(year)) == 2:
		year = f'20{year}'
	if len(str(month)) == 1:
		month = f'0{month}'
	return f'{year}/{month}'

