import base64
import json
import logging
import random
import traceback
from random import randint

import requests
from flask import request

from paraty.pages.cobrador.cobrador_constants import SID_FROM_COBRADOR, GATEWAY_SUCESS_RETURNED, \
	GATEWAY_ERROR_CODE_RETURNED, GATEWAY_ERROR_RETURNED
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, get_integration_name, \
	audit_response, audit_error_web_payment, gateways_format_price, get_reservations_total_price, complete_payment
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.datastore import datastore_communicator


class PaylandsController(GatewayInterface):
	def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
							  reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			extra_info["last_merchant_order_used"] = merchant_order
			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
																			  reservation, hotel_code=hotel_code)
			logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
						 reservation_id)

			return reservation_updated_id

	def update_extraInfo_in_reservation(self, hotel_code, reservation, next_paylands_order, gateway_response, type="payment"):
		logging.info("[%s] Updating extra info with a succesfull payment!!!", next_paylands_order)
		extra_info = reservation.get("extraInfo", "{}")
		paraty_order_id = next_paylands_order
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
							  reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			new_extraInfo_key = "paylands_transaction_" + str(paraty_order_id)
			extra_info[new_extraInfo_key] = {
				"paylands_internal_payment_id": gateway_response.get("uuid"),
				"type": type,
				"paraty_order_id": paraty_order_id
			}

			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
																			  reservation, hotel_code=hotel_code)
			logging.info("ExtraInfo update for reservation %s", reservation.get("identifier"))

			return reservation_updated_id

	def translate_error(self, error):
		return error

	def get_currency(self):
		return "978"
	def get_ISO_currency(self):
		return "EUR"

	def get_sign(self):
		return ""

	def get_url_post(self):
		return "/pages/cobrador/save_payment"

	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def get_card_user(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("payland_credencials", ""):
				credencials = extra_info.get("payland_credencials", "")
				if credencials.get("customer", ""):
					return credencials.get("customer", "")
			if extra_info.get("paylands_internal_customer_id", ""):
					return extra_info.get("paylands_internal_customer_id", "")

		return ""

	def get_token(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("payland_credencials", ""):
				credencials = extra_info.get("payland_credencials", "")
				if credencials.get("token_card", ""):
					return credencials.get("token_card", "")
			if extra_info.get("token_id", ""):
					return extra_info.get("token_id", "")

		return ""

	def get_next_paylands_order(self, hotel_code, reservation):

		last_merchant_order_used = ""
		if reservation.get("extraInfo"):
			extra_info = json.loads(reservation.get("extraInfo"))
			last_merchant_order_used = extra_info.get("last_merchant_order_used")

		if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
			last_merchant_order_used = last_merchant_order_used[0:8]
			numerical_order = int(last_merchant_order_used)
			numerical_order += 1

			merchant_order = str(numerical_order)

			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
		else:

			merchant_order = reservation.get("identifier")
			# maybe this reservation was done before gateway was configured. So it could be an alphanumeric
			if not merchant_order.isnumeric():
				merchant_order = randint(10000000, 99999000)

			numerical_order = int(merchant_order)
			numerical_order += 1
			merchant_order = str(numerical_order)
			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

		self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)

		return merchant_order

	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		try:

			config = self.get_configuration(hotel_code)
			identifier = reservation.get("identifier", "")

			currency = self.get_ISO_currency()
			url_post = self.get_url_post()

			service = config.get("service")
			signature = config.get("signature")

			customer = self.get_card_user(reservation)

			amount = ("%.2f" % float(amount)).replace('.', '')
			next_paylands_order = self.get_next_paylands_order(hotel_code, reservation)


			payload = {
				"amount": amount,
				"operative": "AUTHORIZATION",
				"currency": currency,
				"signature": signature,
				"customer_ext_id": customer,
				"additional": reservation.get("identifier"),
				"service": service,
				"description": "Order for booking",
				"reference": next_paylands_order,
			}

			payload = json.dumps(payload)

			headers = {}
			headers['Content-Type'] = "application/json"
			api_key = config.get("api_key").encode("utf-8")
			headers['Authorization'] = "Basic %s" % str(base64.b64encode(api_key).decode("utf-8"))

			url_checkout = config.get("url_checkout")
			response = requests.request("POST", url_checkout, data=payload, headers=headers)

			logging.info("[PAYLANDS] Response initialating new payment: %s", response)
			response.raise_for_status()
			if response.status_code == 200 and response.text:
				response_json = json.loads(response.text)

				uuid_order = response_json.get('order').get("uuid")
				uuid_card = self.get_token(reservation)

				payload = {
					"order_uuid": uuid_order,
					"card_uuid": uuid_card,
					"signature": signature,
				}

				try:
					gateway_type = "paylands"
					audit_response(hotel_code, gateway_type, uuid_order, SID_FROM_COBRADOR, response.text)

				except Exception as e:
					logging.error("Error auditing response for in PAYLANDS")
					logging.error("Error auditing: %s", e)


				payload_json = json.dumps(payload)

				url_payment_direct = config.get("url_payment_direct")
				response = requests.request("POST", url_payment_direct, data=payload_json, headers=headers)
				response.raise_for_status()
				if response.status_code == 200 and response.text:
					logging.info("[PAYLANDS][%s] Transaction made with order: %s", identifier, next_paylands_order)

					gateway_response = json.loads(response.text).get("order", {})
					gateway_response_status = gateway_response.get("status", "")
					paymentintent = {
						"external_id": gateway_response.get("uuid"),
						"currency": currency,
						"amount": gateways_format_price(amount),
						"metadata": {
							"Paraty Order Id": str(gateway_response.get("reference"))
						}
					}
					if gateway_response_status == "SUCCESS":
						logging.info("[PAYLANDS PAYMENT][%s][%s][PAYMENT_RESPONSE]: %s" % (hotel_code, identifier, json.dumps(paymentintent)))
						self.update_extraInfo_in_reservation(hotel_code, reservation, next_paylands_order, gateway_response)
						return gateway_response.get("reference")
					else:
						error_message = gateway_response_status
						if len(gateway_response.get("transactions")) > 0:
							current_transaction = gateway_response.get("transactions")[0]
							error_message = current_transaction.get("error_details", {}).get("error_description","ERROR")
						logging.warning("[ERROR][PAYLANDS PAYMENT][%s]: %s", next_paylands_order, error_message)
						return GATEWAY_ERROR_RETURNED

		except Exception as e:
			logging.warning("Exception at get_token [PAYLANDS]: %s", e)

		return GATEWAY_ERROR_RETURNED


	def gateway_has_token(self):
		return True

	def get_initial_order_id_from_extra_info(self, extra_info):
		return None

	def reservation_has_token(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("payland_credencials", ""):
				credencials = extra_info.get("payland_credencials", "")
				if credencials.get("token_card", ""):
					return True
			if extra_info.get("token_id", ""):
					return True

		return False


	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		identifier = reservation.get("identifier", "")
		logging.info("[PAYLANDS][%s] trying to refund the transaction with order %s", identifier, order_to_refund)
		try:
			# for payments from cobrador
			order_id = order_to_refund
			next_paylands_order = self.get_next_paylands_order(hotel_code, reservation)


			#for payments from booking3 and payment links
			extra_info = reservation.get("extraInfo", "{}")
			if extra_info:
				try:
					extra_info = json.loads(extra_info)
				except:
					logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
					extra_info = {}

				if extra_info.get("paylands_order_%s" % order_to_refund):
					order_id = extra_info.get("paylands_order_%s" % order_to_refund)
				elif extra_info.get("paylands_transaction_%s" % order_to_refund):
					order_id = extra_info.get("paylands_transaction_%s" % order_to_refund).get("paylands_internal_payment_id")
			currency = extra_info.get("currency", "")


			config = self.get_configuration(hotel_code)
			signature = config.get("signature")
			amount = ("%.2f" % float(amount)).replace('.', '')
			payload = {
					"signature": signature,
					"order_uuid": order_id,
					"amount": amount
				}

			payload = json.dumps(payload)
			headers = {}
			headers['Content-Type'] = "application/json"
			api_key = config.get("api_key").encode("utf-8")
			headers['Authorization'] = "Basic %s" % str(base64.b64encode(api_key).decode("utf-8"))


			url_refunds = config.get("url_refund")
			response = requests.request("POST", url_refunds, data=payload, headers=headers)
			response.raise_for_status()

			if response.status_code == 200 and response.text:
				logging.info("[PAYLANDS][%s] Refund response received: %s", identifier, response.text)
				gateway_response = json.loads(response.text).get("order",{})
				gateway_response_status = gateway_response.get("status", "")
				paymentintent = {
					"external_id": gateway_response.get("uuid"),
					"currency": currency,
					"amount": gateway_response.get("amount")/100,
					"metadata": {
						"Paraty Order Id": next_paylands_order
					}
				}
				if gateway_response_status in ["REFUNDED", "PARTIALLY_REFUNDED"]:
					logging.info("[PAYLANDS REFUND][%s][%s][PAYMENT_RESPONSE]: %s" % (hotel_code, identifier, json.dumps(paymentintent)))
					self.update_extraInfo_in_reservation(hotel_code, reservation, next_paylands_order, gateway_response, type="refund")
					return order_to_refund
				else:
					error_message = gateway_response_status
					if len(gateway_response.get("transactions")) > 0:
						current_transaction = gateway_response.get("transactions")[0]
						error_message = current_transaction.get("error_details", {}).get("error_description", "ERROR")
					logging.warning("[ERROR][PAYLANDS REFUND][%s]: %s", next_paylands_order, error_message)
					return GATEWAY_ERROR_RETURNED
		except Exception as e:
			logging.warning("Exception at get_token [PAYLANDS]: %s", e)
			traceback.print_exc()

		return "ERROR: Payment not done"

class PaylandsFormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		logging.info("[PAYLANDS][%s] Creating Gateway Form", extra_data.get("original_identifier"))
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

		# Initialize base parameters
		params = {
			"sid": sid,
			"payment_order_id": payment_order_id,
			"special_tpv_link": gateway_configuration.get("special_tpv_link", ""),
			"amount": ("%.2f" % float(amount)).replace('.', ''),
			"currency": extra_data.get("currency"),
			"original_identifier": extra_data.get("original_identifier"),
			"add_button": True,
			"operative": "AUTHORIZATION",
			"secure": False,
			"template_uuid": sid,
			"currencies": self._get_currencies_dict(gateway_configuration),
			"reservations_total_price": get_reservations_total_price(extra_data),
		}

		# Build gateway parameters
		self.build_gateway_params(gateway_configuration, params)

		# Generate token and update payment URL
		self._update_payment_url(params)

		# Build and return the form
		return build_template("pages/cobrador/gateways/paylands_redirection_form.html", params, False)

	def _get_currencies_dict(self, gateway_configuration):
		currencies_dict = {}
		if currencies := gateway_configuration.get("currencies"):
			currencies_dict = dict(currency.split(":") for currency in currencies.split(";"))
		return currencies_dict

	def _update_payment_url(self, params):
		if token_response := self.generate_token_payment(params):
			if token_response.status_code == 200 and token_response.text:
				response_json = json.loads(token_response.text)
				token = response_json.get("order", {}).get("token")
				if token:
					params["url_payment"] = f"{params.get('url_payment')}/{token}"
					logging.info("[PAYLANDS] Params for redirect to paylands gateway: %s", params)

	def process_gateway_response(self, hotel_code, gateway_type, response):
		logging.info("[PAYLANDS] Processing gateway response with %s gateway", gateway_type)
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

		body = request.get_json()

		logging.info("Body params: %s", body)

		hotel_code = request.args.get("hotel_code")
		if not hotel_code:
			hotel_code = request.args.get("namespace") or body.get("hotel_code")


		if isinstance(response, str):
			response = json.loads(response)
		paylands_response = response
		cobrador_response = {
			"PAYMENT_GATEWAY_NAME": "PAYLANDS"
		}

		to_extra_info = {
			"no_redirect": True,
			"payment_gateway": gateway_type
		}
		if paylands_response:
			order_response = paylands_response.get("order")
			client_response = paylands_response.get("client")
			identifier = str(order_response.get("reference"))
			amount = order_response.get("amount")/100
			external_id = order_response.get("uuid")

			try:
				sid = request.args.get("sid")
				if not sid:
					sid = "no recibido"
				gateway_type = "paylands"
				audit_response(hotel_code, gateway_type, identifier, sid, json.dumps(paylands_response))

			except Exception as e:
				logging.warning("Error auditing response for in PAYLANDS in process response")
				logging.warning("Error auditing: %s", e)


			reservation_metadata = datastore_communicator.get_using_entity_and_params("ReservationMetadata",
																					  [("identifier", "=", identifier),
																					   ("hotel_code", "=", hotel_code)])

			if len(reservation_metadata) > 0:
				reservation_metadata = reservation_metadata[0]


			current_transaction = order_response.get("transactions")
			if current_transaction:
				current_transaction = current_transaction[0]



			new_extraInfo_key = "paylands_transaction_" + identifier
			if order_response.get("status") == "SUCCESS":
				logging.info("[PAYLANDS][%s]Transaction approved, pending to create!!!", identifier)
				code = GATEWAY_SUCESS_RETURNED

				to_extra_info[new_extraInfo_key] = {
					"paraty_order_id": identifier,
					"paylands_internal_payment_id": order_response.get("uuid")
				}

				to_extra_info["token_id"] = current_transaction.get("source").get("uuid")
				to_extra_info["paylands_internal_customer_id"] = client_response.get("uuid")
				if not sid and reservation_metadata:
					reservation_metadata = reservation_metadata[0]
					sid = reservation_metadata.get('sid')

					logging.info("SID  obteined from reservation_metadata: %s", sid)
				origin = current_transaction.get("source", {}).get("origin")
				if origin:
					to_extra_info[new_extraInfo_key]["payment_source"] = origin

			else:
				logging.info("[PAYLANDS][%s]Transaction declined, pending to create!!!", identifier)
				code = GATEWAY_ERROR_CODE_RETURNED


				error_details = current_transaction.get("error_details") or {}
				error_message = error_details.get("error_description", "")
				cobrador_response["GATEWAY_ERROR_MESSAGE"] = error_message
				audit_error_web_payment(hotel_code, identifier, amount, error_message)

			cobrador_response["CODE"] = code
			cobrador_response["GATEWAY_ORDER_ID"] = identifier
			cobrador_response["GATEWAY_PAID_AMOUNT"] = amount
			cobrador_response["GATEWAY_EXTRA_INFO"] = to_extra_info
			cobrador_response["GATEWAY_EXTERNAL_ID"] = external_id

			# Add expiration date to response
			self.add_expiration_date_to_response(cobrador_response, order_response)

			logging.info("[PAYLANDS][%s] Returning response: %s after processing the gateway response", identifier, cobrador_response)

			return cobrador_response

		else:
			logging.warning("[PAYLANDS] Something went wrong with the payment, here is the paylands responser: %s", paylands_response)
			logging.info("[PAYLANDS] Check if the payment has finished successfully")
			cobrador_response["CODE"] = GATEWAY_ERROR_CODE_RETURNED
			return cobrador_response

	def add_expiration_date_to_response(self, cobrador_response, order_response):
		logging.info("[PAYLANDS] Trying to add expiration date")
		try:
			# We check if there is card information in the response.
			current_transaction = order_response.get("transactions", [])
			if current_transaction and len(current_transaction) > 0:
				source = current_transaction[0].get("source", {})
				expire_month = source.get("expire_month")
				expire_year = source.get("expire_year")

				if expire_month and expire_year:
					if len(str(expire_month)) == 1:
						expire_month = f"0{expire_month}"

					if len(str(expire_year)) == 4:
						expire_year = str(expire_year)[2:4]

					cobrador_response["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{expire_month}/{expire_year}"
					logging.info("[PAYLANDS] Added expiration date to response: %s", cobrador_response["GATEWAY_EXTRA_INFO"]["cc_expire_date"])
		except Exception as e:
			logging.error("[PAYLANDS] Error adding expiration date to response: %s", str(e))



	def build_gateway_params(self, gateway_configuration, params):
		params["api_key"] = gateway_configuration.get("api_key", gateway_configuration.get("TEST_api_key", "43758B6E6D4B4FC1B4A905705538E13B"))
		params["service"] = gateway_configuration.get("service", gateway_configuration.get("TEST_service", "F7D74BA9-D1C9-4DAD-AF56-AA64F7C66674"))
		params["signature"] = gateway_configuration.get("signature", gateway_configuration.get("TEST_signature", "LUkCUx8xULl1Gx01jPt3SS4M"))
		params["url_checkout"] = gateway_configuration.get("url_checkout", gateway_configuration.get("TEST_url_checkout", "https://api.paylands.com/v1/sandbox/payment"))
		params["url_merchant"] = gateway_configuration.get("url_merchant", gateway_configuration.get("TEST_url_merchant","http://localhost:8090/booking4/paylands/merchant_url"))
		params["url_ok"] = gateway_configuration.get("url_ok", gateway_configuration.get("TEST_url_ok","http://localhost:8090/booking4"))
		params["url_ko"] = gateway_configuration.get("url_ko", gateway_configuration.get("TEST_url_ko","http://localhost:8090/booking3"))
		params["url_payment_direct"] = gateway_configuration.get("url_payment_direct", gateway_configuration.get("TEST_url_payment_direct", "https://api.paylands.com/v1/sandbox/payment/direct"))
		params["url_payment"] = gateway_configuration.get("url_payment", gateway_configuration.get("TEST_url_payment", "https://api.paylands.com/v1/sandbox/payment/process/"))
		params["status"] = gateway_configuration.get("status", "TEST")
		params["extra_payment_methods"] = gateway_configuration.get("extra_payment_methods", "")
		params["checkout_id"] = gateway_configuration.get("checkout_id", "")



	def add_param_to_url(self, url, param, value):
		separator = "&" if "?" in url else "?"
		return f"{url}{separator}{param}={value}"

	def generate_token_payment(self, params):
		# Prepare token parameters
		token_params = {
			"customer_ext_id": f"user{params.get('payment_order_id')}",
			"secure": True,
			"currency": params.get("currency"),
			"operative": "AUTHORIZATION", 
			"description": "order's description",
			"amount": str(params.get("amount")),
			"signature": params.get("signature"),
			"additional": params.get("sid"),
			"service": params.get("service"),
			"extra_data": build_extra_data(params)
		}

		token_params['reference'] = get_reference(params)

		# Add sid parameter to URLs
		sid = params.get("sid")
		url_types = {
			"url_post": params.get("url_merchant"),
			"url_ok": params.get("url_ok"),
			"url_ko": params.get("url_ko")
		}
		for url_type, base_url in url_types.items():
			token_params[url_type] = self.add_param_to_url(base_url, "sid", sid)

		# Log request details
		logging.info("[PAYLANDS] Request Getting Token: %s", token_params["url_post"])
		logging.info("[PAYLANDS] Request Getting Token Payload: %s", token_params)

		# Prepare headers with authentication
		headers = {
			'Content-Type': "application/json",
			'Authorization': f"Basic {base64.b64encode(params.get('api_key').encode('utf-8')).decode('utf-8')}"
		}

		# Make request and return response
		response = requests.post(params.get("url_checkout"), data=json.dumps(token_params), headers=headers)

		logging.info("[PAYLANDS] Response Getting Token: %s", response)
		return response


def get_reference(params):
	original_identifier = request.values.get('original_identifier')
	if original_identifier:
		return original_identifier.split('-')[1] if '-' in original_identifier else original_identifier
	return params.get("payment_order_id")


def build_extra_data(params):
	return {
		"cof": {
			"reason": "OTHER"
		},
		"checkout": {
			"uuid": params.get("checkout_id"),
			"payment_methods": get_payment_methods(params),
			"customization": {
				"description": "",
				"payment_details": {
					"Localizador": params.get("payment_order_id")
				}
			}
		}
	}


def get_payment_methods(params):
	payment_methods = ["PAYMENT_CARD"]
	extra_payment_methods = params.get("extra_payment_methods", "")
	if complete_payment(params) and extra_payment_methods:
		payment_methods.extend(payment_method.strip() for payment_method in extra_payment_methods.split(","))
	logging.info("[PAYLANDS] Payment Methods: %s", payment_methods)
	return payment_methods


