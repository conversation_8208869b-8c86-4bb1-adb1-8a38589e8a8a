import Adyen
import base64
import hashlib
import hmac
import uuid
import json
from flask import request
import logging
import urllib.parse
from random import randint
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore, get_using_entity_and_params

from flask_cors import cross_origin
from datetime import datetime, timedelta

from paraty import app, Config
from paraty.pages.cobrador.cobrador_constants import ADYEN, GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, \
    get_ultimate_cobrador_path, audit_response, add_sid_to_url, initiate_cobrador_response, encrypt_message_DES3, \
    decrypt_paraty_own_response_from_api, get_integration_name, change_refund_status, save_pending_refund, \
    get_pending_refunds

from paraty.pages.cobrador.gateways.trust import add_parameter_to_url
from paraty.pages.cobrador.gateways.worldline_eu import get_locale
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

ADYEN_AUTHORISED_PAYMENT = "Authorised"


class AdyenFormController(FormGatewayInterface):
    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
        """Builds Adyen payment form HTML with available payment methods"""
        gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
        adyen = client_setup(gateway_configuration=gateway_configuration)

        context_data = self._get_request_context(extra_data)
        locale = get_locale(context_data["language"])

        formatted_amount = gateways_format_price(amount)
        payment_methods = get_payment_methods(
            gateway_configuration,
            formatted_amount,
            context_data["currency"],
            adyen,
            context_data["country"],
            locale
        )
        payment_seeker_url = get_ultimate_cobrador_path(get_hotel_by_application_id(hotel_code))

        context = self._build_template_context(
            gateway_configuration,
            formatted_amount,
            context_data["currency"],
            payment_methods,
            context_data["country"],
            locale,
            payment_seeker_url,
            payment_order_id,
            gateway_type,
            hotel_code,
            sid
        )

        return build_template("pages/cobrador/gateways/_adyen.html", context)

    def _get_request_context(self, extra_data):
        """Extracts and normalizes request context data (currency, country, language)"""
        return {
            "currency": extra_data.get("currency") or "EUR",
            "country": extra_data.get("country") or "ES",
            "language": extra_data.get("language") or "es",
        }

    def _build_template_context(self, gateway_configuration, formatted_amount, currency, payment_methods, country, locale, payment_seeker_url, payment_order_id, gateway_type, hotel_code, sid):
        """Builds complete context for rendering Adyen template"""
        dict_to_encrypt = {
            "amount": formatted_amount,
            "currency": currency,
            "payment_order_id": payment_order_id
        }
        cobrador_parameters_SHA256, cobrador_signature = _generate_signature(dict_to_encrypt, gateway_configuration, payment_order_id)
        return {
            "client_key": gateway_configuration.get("client_key"),
            "payment_methods": payment_methods.message,
            "country": country,
            "locale": locale,
            "payment_seeker_url": payment_seeker_url,
            "amount": formatted_amount,
            "currency": currency,
            "payment_order_id": payment_order_id,
            "encrypted_data": cobrador_parameters_SHA256.decode(),
            "signature": cobrador_signature.decode(),
            "hotel_code": hotel_code,
            "gateway_type": gateway_type,
            "sid": sid
        }

    def _parse_gateway_response(self, original_response):
        """Converts gateway response to dictionary if it comes as query string"""
        if not isinstance(original_response, dict):
            response = dict(urllib.parse.parse_qsl(original_response))
        else:
            response = original_response
        return response

    def _handle_encrypted_response(self, hotel_code, gateway_type, cobrador_parameters, cobrador_signature):
        """Processes encrypted responses coming from frontend JavaScript"""
        response = decrypt_paraty_own_response_from_api(
            hotel_code,
            gateway_type,
            cobrador_parameters,
            cobrador_signature,
            just_return_decrypted_data=True
        )
        logging.info(f'[ADYEN] Decrypted response {response}')
        return response

    def process_gateway_response(self, hotel_code, gateway_type, original_response):
        """Processes Adyen gateway response, handling both encrypted responses and redirects"""
        response = self._parse_gateway_response(original_response)

        cobrador_parameters = response.get("cobrador_parameters")
        cobrador_signature = response.get("cobrador_signature")

        if cobrador_parameters and cobrador_signature:
            logging.info(f"[ADYEN] cobrador_parameters and cobrador_signature found in response. Decrypting... Comes from js")
            return self._handle_encrypted_response(hotel_code, gateway_type, cobrador_parameters, cobrador_signature)

        logging.info("[ADYEN] Response comes from adyen due to an action required")
        cobrador_response = initiate_cobrador_response(ADYEN)
        gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
        adyen = client_setup(gateway_configuration)

        payment_response = _payment_response(adyen, hotel_code)
        if payment_response.message.get('resultCode') == ADYEN_AUTHORISED_PAYMENT:
            _save_reservation_metadata(payment_response.message.get('merchantReference', ''), payment_response.message, request.values.get('sid'), hotel_code)
            logging.info("[ADYEN] Payment authorised")
            cobrador_response.update(self._build_successful_payment_response(payment_response))
            logging.info(f"[ADYEN] Returning cobrador response: {cobrador_response}")
            return cobrador_response
        return cobrador_response

    def _build_successful_payment_response(self, payment_response):
        """Builds cobrador response for successful Adyen payment"""
        return {
            "CODE": GATEWAY_SUCESS_RETURNED,
            "GATEWAY_EXTRA_INFO": {
                f"{ADYEN}_credentials": {
                    "payment_method": payment_response.message.get('paymentMethod', {}).get('type', ''),
                    "psp_reference": payment_response.message.get('pspReference')
                },
                f"{ADYEN}_transactions": {
                    payment_response.message.get('merchantReference'): payment_response.message.get('pspReference')
                }
            },
            "GATEWAY_PAID_AMOUNT": payment_response.message.get('amount', {}).get('value')/100,
            "GATEWAY_ORDER_ID": payment_response.message.get('merchantReference'),
            "UPDATE_RESERVATION_COBRADOR": True
        }

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT


class AdyenController(GatewayInterface):
    def execute_payment_in_gateway(self, hotel_code, reservation, amount):
        """Executes payment in Adyen using stored token (card not present payment)"""
        gateway_type = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
        adyen = client_setup(gateway_configuration)

        json_request = self._build_request_payment_token(reservation, hotel_code, amount, gateway_configuration)
        idempotency_key = str(uuid.uuid4())
        logging.info(f"[ADYEN] Payment request: {json.dumps(json_request)} - idempotency_key: {idempotency_key}")

        result = adyen.checkout.payments_api.payments(request=json_request, idempotency_key=idempotency_key)
        logging.info(f"[ADYEN] Payment response: {result.raw_response}")

        audit(hotel_code, reservation.get("identifier"), result.raw_response, type_audit="payment", payload=json.dumps(json_request))
        payment_result = result.message
        if payment_result.get('resultCode') == ADYEN_AUTHORISED_PAYMENT:
            add_transaction_to_extraInfo(reservation, payment_result, hotel_code)
            return payment_result.get('merchantReference')

        return "ERROR:"

    def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
        """Executes refund in Adyen for a previously authorized transaction"""
        gateway_type = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
        adyen = client_setup(gateway_configuration)

        psp_reference = self._get_psp_reference(reservation, order_to_refund)
        json_request = self._build_request_refund(amount, reservation, gateway_configuration, order_to_refund, hotel_code)
        idempotency_key = str(uuid.uuid4())
        logging.info(f"[ADYEN] Refund request: {json.dumps(json_request)} - psp_reference: {psp_reference} = idempotency_key: {idempotency_key}")

        result = adyen.checkout.modifications_api.refund_captured_payment(request=json_request, paymentPspReference=psp_reference, idempotency_key=idempotency_key)
        logging.info(f"[ADYEN] Refund response: {result.raw_response}")

        audit(hotel_code, order_to_refund, result.raw_response, type_audit="refund", payload=json.dumps(json_request))

        refund_response = result.message
        if refund_response.get('status') == 'received':
            logging.info(f"[ADYEN] Refund received for order {order_to_refund}")
            save_pending_refund(hotel_code, refund_response, order_to_refund, reservation.get("identifier"), ADYEN)
            return {
					"order_id": order_to_refund,
					"extra_params": {
						"refund_pending": True,
						"create_task": False
					}
				}

        return "ERROR:"
    
    def gateway_has_token(self):
        return True

    def _build_request_refund(self, amount, reservation, gateway_configuration, order_to_refund, hotel_code):
        """Builds refund request for Adyen"""
        extra_info = json.loads(reservation.get("extraInfo", "{}"))
        return {
            "amount": {
                "currency": extra_info.get("currency", "EUR"),
                "value": gateways_format_price(amount)
            },
            "merchantAccount": gateway_configuration.get("merchant_account"),
            "reference": order_to_refund
        }

    def translate_error(self, res_payment):
        return res_payment

    def reservation_has_token(self, reservation):
        """Checks if reservation has a stored payment token and returns it"""
        extra_info = reservation.get("extraInfo", "{}")
        if extra_info:
            try:
                extra_info = json.loads(extra_info)
            except:
                logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
                extra_info = {}
            adyen_credentials = extra_info.get(f"{ADYEN}_credentials", {})
            if adyen_credentials.get("payment_method") and not is_method_tokenizable(adyen_credentials.get("payment_method")):
                return False
            stored_payment_method_id = adyen_credentials.get("stored_payment_method_id")
            if stored_payment_method_id:
                return stored_payment_method_id
        return False

    def get_initial_order_id_from_extra_info(self, extra_info):
        return extra_info.get("paymentOrderId")

    def get_configuration(self, hotel_code):
        """Gets Adyen gateway configuration for specific hotel"""

        integration_name = get_integration_name(hotel_code)
        gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

        return gateway_configuration

    def _build_request_payment_token(self, reservation, hotel_code, amount, gateway_configuration):
        """Builds payment request using stored token for recurring payments"""
        extra_info = json.loads(reservation.get("extraInfo", "{}"))
        currency = extra_info.get("currency", "EUR")
        return {
            "amount": {
                "currency": currency,
                "value": gateways_format_price(amount)
            },
            "reference": self._get_next_merchant_order(reservation),
            "paymentMethod": {
                "type": "scheme",
                "storedPaymentMethodId": self.reservation_has_token(reservation)
            },
            "shopperInteraction": "ContAuth",
            "recurringProcessingModel": "UnscheduledCardOnFile",
            "merchantAccount": gateway_configuration.get("merchant_account"),
            "shopperReference": reservation.get('identifier'),
            "applicationInfo": get_application_info()
        }

    def _get_next_merchant_order(self, reservation):
        """Generates next merchant order ID by incrementing the last used one"""
        last_merchant_order_used = ""
        if reservation.get("extraInfo"):
            extra_info = json.loads(reservation.get("extraInfo"))
            last_merchant_order_used = extra_info.get("last_merchant_order_used")

        if last_merchant_order_used and "error" not in last_merchant_order_used.lower():
            last_merchant_order_used = last_merchant_order_used[0:8]
            numerical_order = int(last_merchant_order_used)
            numerical_order += 1

            merchant_order = str(numerical_order)

            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
        else:

            merchant_order = reservation.get("identifier")
            if not merchant_order.isnumeric():
                merchant_order = randint(10000000, 99999000)

            numerical_order = int(merchant_order)
            numerical_order += 1
            merchant_order = str(numerical_order)
            logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
        return merchant_order

    def _get_psp_reference(self, reservation, order_to_refund):
        """Gets Adyen PSP reference for specific order from extraInfo"""
        extra_info = json.loads(reservation.get("extraInfo", "{}"))
        psp_reference = extra_info.get(f"{ADYEN}_transactions", {}).get(order_to_refund)
        if psp_reference:
            return psp_reference
        return False

def _payment_response(adyen, hotel_code):
    """Processes Adyen payment response using redirectResult to complete transaction"""
    result = adyen.checkout.payments_api.payments_details({"details": {"redirectResult": request.values.get('redirectResult')}})
    logging.info(f"[ADYEN] Payment response: {result.raw_response}")
    audit(hotel_code, result.message.get('merchantReference'), result.raw_response, type_audit="Process payment")
    return result


def client_setup(gateway_configuration):
    """Configures and initializes Adyen client with appropriate credentials and environment"""
    adyen = Adyen.Adyen()
    adyen.client.xapikey = gateway_configuration.get("api_key", "")  # "ADYEN_API_KEY"
    if not gateway_configuration.get("api_key", ""):
        logging.error("No api_key found for Adyen")
    # For the LIVE environment, also include your liveEndpointUrlPrefix.
    if Config.DEV or gateway_configuration.get("test"):
        adyen.client.platform = "test"
    return adyen


def get_allowed_payment_methods(gateway_configuration):
    """Gets list of allowed payment methods from gateway configuration"""
    allowed_payment_methods = ["scheme"]
    extra_payment_methods = gateway_configuration.get('allowed payment methods', "")
    if extra_payment_methods:
        allowed_payment_methods.extend(payment_method.strip() for payment_method in extra_payment_methods.split(","))
    logging.info("[ADYEN] Payment Methods: %s", allowed_payment_methods)
    return allowed_payment_methods


def get_payment_methods(gateway_configuration, amount, currency, adyen, country, locale):
    """Gets available payment methods from Adyen for specific transaction"""
    json_request = {
        "merchantAccount": gateway_configuration.get("merchant_account"),
        "amount": {
            "currency": currency,
            "value": amount
        },
        "countryCode": country,
        "shopperLocale": locale,
        "applicationInfo": get_application_info()
    }
    if not gateway_configuration.get("all payment methods"):
        json_request["allowedPaymentMethods"] = get_allowed_payment_methods(gateway_configuration)
    return adyen.checkout.payments_api.payment_methods(request=json_request, idempotency_key=str(uuid.uuid4()))


def build_payment_request(data, encrypted_data, hotel_code, gateway_configuration, sid):
    """Builds initial payment request for Adyen with all necessary parameters"""
    return {
        "amount": {
            "currency": encrypted_data.get('currency'),
            "value": encrypted_data.get('amount')
        },
        "reference": encrypted_data.get('payment_order_id'),
        "paymentMethod": data.get('payment_data').get('paymentMethod'),
        "returnUrl": add_sid_to_url(gateway_configuration.get('merchant_url'), sid),
        "merchantAccount": gateway_configuration.get("merchant_account"),
        "storePaymentMethod": True,
        "recurringProcessingModel": "UnscheduledCardOnFile",
        "shopperReference": encrypted_data.get('payment_order_id'),
        "shopperInteraction": "Ecommerce",
        "applicationInfo": get_application_info(),
        "browserInfo": data.get('payment_data').get('browserInfo'),
        "authenticationData": {
            "threeDSRequestData": {
                "nativeThreeDS": "disabled"
            }
        },
        "channel": "Web",
        "origin": data.get('origin', '')

    }


def get_application_info():
    """Builds application info for Adyen request"""
    return {
        "externalPlatform": {
            "name": "Paraty",
            "integrator": "Paraty"
        },
        "merchantApplication": {
            "name": "Paraty"
        }
    }


def handle_payment_response(hotel_code, order, json_request, payment_result, gateway_configuration, sid, three_d_secure=False):
    """Handles Adyen payment response, processing required actions or completing payment"""
    response = payment_result.message
    logging.info("[ADYEN] Payment Result: %s", response)
    # {'additionalData': {'recurring.recurringDetailReference': 'Z7QXD52RKV2DFNT5', 'recurringProcessingModel': 'CardOnFile', 'recurring.shopperReference': 'test-backend10', 'tokenization.store.operationType': 'alreadyExisting', 'tokenization.shopperReference': 'test-backend10', 'tokenization.storedPaymentMethodId': 'Z7QXD52RKV2DFNT5'}, 'pspReference': 'VSH7MR7S5NL7NVT5', 'resultCode': 'Authorised', 'amount': {'currency': 'EUR', 'value': 10000}, 'merchantReference': '96097114', 'paymentMethod': {'brand': 'mc', 'type': 'scheme'}}

    audit(hotel_code, order, payment_result.raw_response, type_audit="Process payment", payload=json.dumps(json_request))
    if response.get('action'):
        logging.info(f"[ADYEN] Action required before doing payment: {response.get('action')}")
    else:
        build_encrypted_response(response, gateway_configuration, hotel_code, sid, three_d_secure)
        _save_reservation_metadata(order, response, sid, hotel_code)
    return response


def _save_reservation_metadata(order, response, sid, hotel_code):
    payload = {
        "payment_order_id": order,
        "final_identifier": order,
        "pspReference": response.get('pspReference'),
        "gateway": ADYEN,
    }

    properties = {
        "sid": sid or 'not received correctly',
        "identifier": order,
        "hotel_code": hotel_code,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "extraInfo": json.dumps(payload)
    }

    save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")


def create_adyen_payment(data):
    """Creates new Adyen payment and handles corresponding response"""
    encrypted_data = get_encrypted_data(data)
    hotel_code = data.get('hotel_code')
    gateway_configuration = get_payment_gateway_configuration(data.get('gateway_type'), hotel_code)
    adyen = client_setup(gateway_configuration)

    json_request = build_payment_request(data, encrypted_data, hotel_code, gateway_configuration, data.get('sid'))
    payment_result = adyen.checkout.payments_api.payments(request=json_request, idempotency_key=str(uuid.uuid4()))
    return handle_payment_response(hotel_code, encrypted_data.get('payment_order_id'), json_request, payment_result, gateway_configuration, data.get('sid'))

def get_encrypted_data(data):
    """Decrypts encrypted data from frontend request"""
    return decrypt_paraty_own_response_from_api(
        data.get('hotel_code'),
        data.get('gateway_type'),
        data.get("encrypted_data"),
        data.get("signature"),
        just_return_decrypted_data=True
    )


@app.route("/adyen/payments", methods=["POST"])
@cross_origin(origin='*')
def adyen_payments():
    """API endpoint for processing Adyen payments via POST"""
    data = request.json
    return create_adyen_payment(data)


@app.route("/adyen/payments_details", methods=["POST"])
@cross_origin(origin='*')
def adyen_payments_details():
    """API endpoint for processing Adyen payments details via POST"""
    data = request.json
    hotel_code = data.get('hotel_code')
    gateway_configuration = get_payment_gateway_configuration(data.get('gateway_type'), hotel_code)
    adyen = client_setup(gateway_configuration)
    json_request = {"details": data.get('payment_data', {}).get('details')}
    payments_details_result = adyen.checkout.payments_api.payments_details(json_request)
    return handle_payment_response(hotel_code, get_encrypted_data(data).get('payment_order_id'), json_request, payments_details_result, gateway_configuration, data.get('sid'), three_d_secure=True)




def build_encrypted_response(response, gateway_configuration, hotel_code, sid, three_d_secure):
    """Builds encrypted response for frontend with Adyen payment data"""
    order_id = response.get('merchantReference')
    result = response.get('resultCode')

    cobrador_response = _build_base_cobrador_response(response, order_id, result)
    if result == ADYEN_AUTHORISED_PAYMENT:
        # Merge successful payment fields directly here
        logging.info("[ADYEN] Payment done")
        cobrador_response.update(_build_successful_payment_response_for_encrypted(response, order_id, three_d_secure))
    else:
        logging.info(f"[ADYEN] Payment not done. Result: {result}")
        if response.get('refusalReason'):
            logging.info(f"[ADYEN] Refusal reason: {response.get('refusalReason')}")
    cobrador_parameters_SHA256, cobrador_signature = _generate_signature(
        cobrador_response, gateway_configuration, order_id
    )

    cobrador_url = add_parameter_to_url(gateway_configuration.get('merchant_url'), "sid", sid)
    cobrador_url = add_parameter_to_url(cobrador_url, "namespace", hotel_code)

    response["cobrador_response"] = {
        "cobrador_parameters": cobrador_parameters_SHA256.decode(),
        "cobrador_signature": cobrador_signature.decode(),
        "cobrador_url": cobrador_url
    }


def _build_base_cobrador_response(response, order_id, result):
    """Initializes base cobrador response with main fields"""
    cobrador_response = initiate_cobrador_response(ADYEN)
    cobrador_response['no_redirect'] = False
    cobrador_response["IDENTIFIER"] = order_id
    return cobrador_response


def _build_successful_payment_response_for_encrypted(response, order_id, three_d_secure):
    """Builds specific successful payment fields for encrypted response"""
    credentials = {
        "stored_payment_method_id": response.get('additionalData', {}).get('tokenization.storedPaymentMethodId', ''),
        "pspReference": response.get('pspReference', '')
    }

    # Only add 3ds field when three_d_secure is True
    if three_d_secure:
        credentials["3ds"] = "Successful"

    return {
        "GATEWAY_EXTRA_INFO": {
            f"{ADYEN}_credentials": credentials,
            f"{ADYEN}_transactions": {
                order_id: response.get('pspReference', '')
            }
        },
        "GATEWAY_PAID_AMOUNT": response.get('amount', {}).get('value')/100,
        "GATEWAY_ORDER_ID": order_id,
        "CODE": GATEWAY_SUCESS_RETURNED,
        "UPDATE_RESERVATION_COBRADOR": True
    }


def _generate_signature(cobrador_response, gateway_configuration, order_id):
    """Generates HMAC-SHA256 signature for cobrador encrypted response"""
    secret_key_sha256 = gateway_configuration.get("secret_key_sha256") or gateway_configuration.get("inner_secret_key")
    secret_key_sha256_b64 = base64.b64decode(secret_key_sha256)
    key_for_signature = encrypt_message_DES3(order_id, secret_key_sha256_b64)

    # Serialize and encode parameters
    cobrador_parameters_SHA256 = json.dumps(cobrador_response)
    cobrador_parameters_SHA256 = cobrador_parameters_SHA256.encode("utf-8")
    cobrador_parameters_SHA256 = base64.b64encode(cobrador_parameters_SHA256)

    # Generate HMAC signature
    encrypted_parameters = hmac.new(key_for_signature, cobrador_parameters_SHA256, digestmod=hashlib.sha256).digest()
    cobrador_signature = base64.b64encode(encrypted_parameters)
    return cobrador_parameters_SHA256, cobrador_signature


@app.route("/adyen/webhook/token", methods=["POST"])
@cross_origin(origin='*')
def adyen_webhook_token():
    """Webhook for receiving Adyen token notifications (currently only logs)"""

    payload = get_payload_webhook()
    # eventId: the pspReference for the transaction made to store the shopper's payment details.
    timestamp_to_filter = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    psp_reference = payload.get('eventId')
    shopper_reference = payload.get('data', {}).get('shopperReference')
    audit(shopper_reference, psp_reference,  json.dumps(payload), type_audit="webhook")
    reservations_metadata = get_using_entity_and_params("ReservationMetadata", [("identifier", "=", shopper_reference)])
    if not reservations_metadata:
        reservations_metadata = get_using_entity_and_params("ReservationMetadata", [("timestamp", ">=", timestamp_to_filter)])
    for reservation_metadata in reservations_metadata:
        # if reservation_metadata.get("hotel_code") != shopper_reference:
        #     continue
        extra_info = json.loads(reservation_metadata.get("extraInfo", "{}"))
        if extra_info.get("pspReference") == psp_reference:
            logging.info(f'PSP reference found in reservation metadata: {reservation_metadata}')
            extra_info["stored_payment_method_id"] = payload.get('data', {}).get('storedPaymentMethodId')
            reservation_metadata["extraInfo"] = json.dumps(extra_info)
            save_to_datastore("ReservationMetadata", reservation_metadata.key.id, reservation_metadata, hotel_code="payment-seeker:")
            hotel_code = reservation_metadata.get('hotel_code')
            save_token_in_reservation(hotel_code, extra_info.get('final_identifier'), payload.get('data', {}).get('storedPaymentMethodId'))
            return "OK"
    logging.info(f'PSP reference not found in any reservation metadata: {psp_reference}')
    return "OK"


def save_token_in_reservation(hotel_code, identifier, stored_payment_method_id):
    reservations = get_using_entity_and_params("Reservation",
                                              search_params=[('identifier', '=', identifier)],
                                              hotel_code=hotel_code)
    if reservations:
        reservation = reservations[0]
        extra_info_reservation = json.loads(reservation.get("extraInfo", "{}"))
        extra_info_reservation[f"{ADYEN}_credentials"]["stored_payment_method_id"] = stored_payment_method_id
        reservation["extraInfo"] = json.dumps(extra_info_reservation)
        save_to_datastore("Reservation", reservation.key.id, reservation, hotel_code=hotel_code)
    pass


class AdyenWebhookController(GatewayInterface):
    def refund_webhook(self):

        payload = get_payload_webhook(refund=True)
        data = payload.get('notificationItems')[0].get('NotificationRequestItem')
        event_code = data.get("eventCode")
        success = data.get("success")
        psp_reference = data.get("originalReference")
        search_params = [('external_identifier', '=', psp_reference)]
        pending_refunds = get_pending_refunds(search_params)
        if not pending_refunds:
            logging.info(f"No pending refund found for psp reference {psp_reference}")
            return "OK"
        pending_refunds = pending_refunds[0]
        hotel_code = pending_refunds.get("hotel_code")
        extra_info = json.loads(pending_refunds.get("extraInfo", "{}"))
        order_id = extra_info.get("order_to_refund")
        identifier = pending_refunds.get("identifier")
        if event_code == "REFUND" and success == "true":
            logging.info(f"Correct refund: {order_id}")
            change_refund_status(hotel_code, identifier, order_id, 'devolution')
        elif event_code == "REFUND_FAILED":
            logging.info(f"Failed refund: {order_id}")
            change_refund_status(hotel_code, identifier, order_id, 'failed_devolution')
        return "[accepted]"


@app.route("/cobrador/update_reservation", methods=["POST"])
@cross_origin(origin='*')
def update_reservation_metadata():
    payload = request.json
    payment_order_id = payload.get('payment_order_id')
    final_identifier_built = payload.get('final_identifier_built')
    hotel_code = payload.get('hotel_code')
    reservations_metadata = get_using_entity_and_params("ReservationMetadata",[("identifier", "=", payment_order_id)])
    for reservation_metadata in reservations_metadata:
        if reservation_metadata.get("hotel_code") != hotel_code:
            continue
        extra_info = json.loads(reservation_metadata.get("extraInfo", "{}"))
        logging.info(f'Old identifier: {reservation_metadata.get("identifier")} - New identifier: {final_identifier_built}')
        extra_info["final_identifier"] = final_identifier_built
        reservation_metadata["extraInfo"] = json.dumps(extra_info)
        save_to_datastore("ReservationMetadata", reservation_metadata.key.id, reservation_metadata, hotel_code="payment-seeker:")
    logging.info(f'Reservation metadata not found with identifier {payment_order_id}')
    return "OK"


def get_payload_webhook(refund=False):
    if request.args.get('test'):
        if refund:
            return {'live': 'false', 'notificationItems': [{'NotificationRequestItem': {'additionalData': {'bookingDate': '2025-06-19T09:16:16Z'}, 'amount': {'currency': 'EUR', 'value': 100}, 'eventCode': 'REFUND', 'eventDate': '2025-06-19T09:15:28+02:00', 'merchantAccountCode': 'ParatyTech498ECOM', 'merchantReference': '********', 'originalReference': 'L9XTWX2W9DDHL975', 'paymentMethod': 'mc', 'pspReference': 'TL5J7K9G7KZJ2B75', 'reason': '', 'success': 'true'}}]}
        return {'createdAt': '2025-06-18T10:36:44+02:00', 'environment': 'test', 'type': 'recurring.token.created', 'data': {'merchantAccount': 'ParatyTech498ECOM', 'operation': 'created', 'shopperReference': '********', 'storedPaymentMethodId': 'PNVW4W2M6PLBTH75', 'type': 'paysafecard'}, 'eventId': 'LVXVVGVQJVMM7L75'}
    try:
        payload = request.get_json(force=False, silent=True)
        logging.info("[ADYEN WEBHOOK] request.json: %s", payload)
    except Exception as e:
        logging.warning("[ADYEN WEBHOOK] Failed to parse JSON: %s", e)
        payload = None
    return payload





def add_transaction_to_extraInfo(reservation, response, hotel_code):
    """Adds Adyen transaction information to reservation extraInfo"""
    if response.get('resultCode') == ADYEN_AUTHORISED_PAYMENT:
        extra_info = json.loads(reservation.get("extraInfo", "{}"))
        order_id = response.get('merchantReference')
        extra_info.setdefault(f"{ADYEN}_transactions", {})[order_id] = response.get('pspReference')
        reservation["extraInfo"] = json.dumps(extra_info)
        reservation_updated_id = save_to_datastore("Reservation", int(reservation.key.id),
                                                                          reservation, hotel_code=hotel_code)


def audit(hotel_code, order, payment_result, type_audit="payment_result", payload="{}"):
    """Logs audit events for Adyen transactions"""
    if not Config.DEV or True:
        try:
            sid = "not received correctly"

            gateway_type = "ADYEN"
            audit_response(hotel_code, gateway_type, order, sid,
                           payment_result, type_audit=type_audit, payload="{}")

        except Exception as e:
            logging.error(f"Error auditing response for ADYEN in {type_audit}")
            logging.error("Error auditing: %s", e)

def is_method_tokenizable(payment_method_type: str) -> bool:
    """
    Verifica si el método de pago es compatible con pagos por token (recurring).
    """
    RECURRING_COMPATIBLE_METHODS = {
        "scheme",         # Tarjeta de crédito o débito
        "paypal",
        "sepadirectdebit",
        "klarna",
        "ideal",
        "directdebit_GB",
        "mbway",
        "afterpaytouch",
        "applepay",
        "googlepay",
    }

    is_supported = payment_method_type.lower() in RECURRING_COMPATIBLE_METHODS
    if not is_supported:
        logging.info(f"El método '{payment_method_type}' no soporta pagos por token.")
    return is_supported

def build_session_request(data, encrypted_data, hotel_code, gateway_configuration, sid):
    return {
        "amount": {
            "currency": encrypted_data.get("currency"),
            "value": encrypted_data.get("amount")
        },
        "reference": encrypted_data.get("payment_order_id"),
        "returnUrl": add_sid_to_url(gateway_configuration.get("merchant_url"), sid),
        "merchantAccount": gateway_configuration.get("merchant_account"),
        "shopperReference": encrypted_data.get("payment_order_id"),
        "shopperLocale": data.get("language", "es-ES"),
        "countryCode": data.get("country", "ES"),
        "channel": "Web",
        "additionalData": {
            "allow3DS2": "true"
        }
    }

