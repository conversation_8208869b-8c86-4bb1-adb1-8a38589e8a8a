import base64
import json
from random import randint
import logging
from uuid import uuid4

from Cryptodome.Cipher import AES
from flask import request, redirect, render_template
from flask_cors import cross_origin

from onlinepayments.sdk.communicator import Communicator
from onlinepayments.sdk.defaultimpl.default_authenticator import DefaultAuthenticator
from onlinepayments.sdk.defaultimpl.default_connection import DefaultConnection
from onlinepayments.sdk.domain.amount_of_money import AmountOfMoney
from onlinepayments.sdk.domain.browser_data import BrowserData
from onlinepayments.sdk.domain.capture_payment_request import CapturePaymentRequest
from onlinepayments.sdk.domain.card_payment_method_specific_input import CardPaymentMethodSpecificInput
from onlinepayments.sdk.domain.contact_details import ContactDetails
from onlinepayments.sdk.domain.create_hosted_checkout_request import CreateHostedCheckoutRequest
from onlinepayments.sdk.domain.create_hosted_tokenization_request import CreateHostedTokenizationRequest
from onlinepayments.sdk.domain.create_payment_request import CreatePaymentRequest
from onlinepayments.sdk.domain.customer import Customer
from onlinepayments.sdk.domain.customer_device import CustomerDevice
from onlinepayments.sdk.domain.hosted_checkout_specific_input import HostedCheckoutSpecificInput
from onlinepayments.sdk.domain.line_item import LineItem
from onlinepayments.sdk.domain.order import Order
from onlinepayments.sdk.domain.order_line_details import OrderLineDetails
from onlinepayments.sdk.domain.order_references import OrderReferences
from onlinepayments.sdk.domain.payment_product_filter import PaymentProductFilter
from onlinepayments.sdk.domain.payment_product_filters_hosted_checkout import PaymentProductFiltersHostedCheckout
from onlinepayments.sdk.domain.payment_references import PaymentReferences
from onlinepayments.sdk.domain.personal_information import PersonalInformation
from onlinepayments.sdk.domain.personal_name import PersonalName
from onlinepayments.sdk.domain.redirect_payment_method_specific_input import RedirectPaymentMethodSpecificInput
from onlinepayments.sdk.domain.redirection_data import RedirectionData
from onlinepayments.sdk.domain.refund_request import RefundRequest
from onlinepayments.sdk.domain.shopping_cart import ShoppingCart
from onlinepayments.sdk.domain.three_d_secure import ThreeDSecure
from onlinepayments.sdk.factory import Factory
from onlinepayments.sdk.merchant.products.get_payment_products_params import GetPaymentProductsParams
from onlinepayments.sdk.meta_data_provider import MetaDataProvider

from paraty import app

from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, COBRADOR_QUEUE, \
	GATEWAY_ERROR_CODE_RETURNED, GATEWAY_SUCESS_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, gateways_format_price, \
	audit_response, get_integration_name
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface
from paraty.pages.cobrador.gateway_logger import build_logger_context
from paraty.utilities import session_utils
from paraty.utilities.email_utils import notify_error_by_email
from paraty.utilities.languages.language_utils import SPANISH, ENGLISH, GERMAN, FRENCH
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty_commons_3 import queue_utils
from paraty_commons_3.common_data.common_data_provider import get_rooms_of_hotel, get_rates_of_hotel, \
	get_boards_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.session.session_utils import get_session_from_hotel
from paraty.utilities.template_filters import fixurl

from paraty.utilities.languages import language_utils

KEY = b")J@NcRfUjXn2r4u7"
PAYMENT_SUBDOMAIN = "https://payment."
VALID_PAYMENT_METHODS_FOR_TROKENIZE = ["cardPaymentMethodSpecificOutput", "sepaDirectDebitPaymentMethodSpecificOutput"]

DEFERED_TASKS_TIMEOUT = {
	"payment.pending_capture": 180,
	"payment.captured": 90
}

POS_ERRORS_CODES = ["REJECTED", "UNSUCCESSFUL", "DIRECT_PLATFORM_ERROR"]

def get_locale(paraty_locale):
	locales = {
		SPANISH: "es_ES",
		ENGLISH: "en_EN",
		GERMAN: "de_DE",
		FRENCH: "fr_FR"
	}

	return locales.get(paraty_locale, "es_ES")



def logging_controlled_payment_errors_in_wl2_exceptions(capture_str):
	try:

		for error_code in POS_ERRORS_CODES:
			if error_code in capture_str:
				logging.info(capture_str)
				return True
		logging.exception(capture_str)
	except Exception as e:
		logging.error("Error in check_controlled_payment_errors_in_wl2_exceptions: %s", str(e))
	return False

class WorldLineEu2GatewayController(GatewayInterface):
	def save_last_order_in_reservation(self, hotel_code: str, reservation: dict, merchant_order: str, external_id: str) -> int:
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!",
							  reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			extra_info["last_merchant_order_used"] = merchant_order

			transactions_index = extra_info["worldline_data"].get("transactions_index", {})
			transactions_index[merchant_order] = external_id
			extra_info["worldline_data"]["transactions_index"] = transactions_index

			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id,
																			reservation, hotel_code=hotel_code)
			logging.info("New payed updated for reservation %s reservation_id: %s ", reservation.get("identifier"),
						 reservation_id)

			return reservation_updated_id

	def get_next_order(self, hotel_code, reservation):

		last_merchant_order_used = ""
		if reservation.get("extraInfo"):
			extra_info = json.loads(reservation.get("extraInfo"))
			last_merchant_order_used = extra_info.get("last_merchant_order_used", "")

		if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
			last_merchant_order_used = last_merchant_order_used[0:8]
			numerical_order = int(last_merchant_order_used)
			numerical_order += 1

			merchant_order = str(numerical_order)

			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
		else:

			merchant_order = reservation.get("identifier")
			# maybe this reservation was done before gateway was configured. So it could be an alphanumeric
			if not merchant_order.isnumeric():
				merchant_order = randint(10000000, 99999000)

			numerical_order = int(merchant_order)
			numerical_order += 1
			merchant_order = str(numerical_order)
			logging.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)

		return merchant_order

	def execute_payment_in_gateway(self, hotel_code: str, reservation: dict, amount: float) -> str:
		integration_name = get_integration_name(hotel_code)
		config = get_payment_gateway_configuration(integration_name, hotel_code)
		if config.get("dev"):
			developer_configs = {i.replace("dev_", ""): config[i] for i in config if i.startswith("dev_")}
			config.update(developer_configs)

		authenticator = DefaultAuthenticator(config.get("api_key"), config.get("secret_key"))
		metadataProvider = MetaDataProvider("OnlinePayments")

		defaultConnection = DefaultConnection(5000, 10000)

		communicator = Communicator(
			api_endpoint=config.get("target_url"),
			authenticator=authenticator,
			meta_data_provider=metadataProvider
		)
		client = Factory.create_client_from_communicator(communicator)

		try:
			extra_info = \
				json.loads(reservation.get("extraInfo", {}))
		except:
			extra_info = {}

		if extra_info:
			worldline_data = extra_info.get("worldline_data")
			currency = extra_info.get("currency", "")
			if not currency:
				currency = get_configuration_property_value("Base Price Currency")
			if not currency:
				currency = "EUR"

			currency = currency.upper()

			payment_request = CreatePaymentRequest()

			transaction_id = self.get_next_order(hotel_code, reservation)

			amountOfMoney = AmountOfMoney()
			amountOfMoney.amount = gateways_format_price(amount)
			amountOfMoney.currency_code = currency

			order = Order()
			order.amount_of_money = amountOfMoney

			customer = Customer()
			customer.merchant_customer_id = transaction_id

			order.customer = customer

			references = OrderReferences()
			references.merchant_reference = transaction_id
			order.references = references

			payment_request.order = order

			if worldline_data.get("payment_method") == "card":
				card_paymen_method_specific_input = CardPaymentMethodSpecificInput()
				card_paymen_method_specific_input.authorization_mode = "FINAL_AUTHORIZATION"
				card_paymen_method_specific_input.is_recurring = True
				card_paymen_method_specific_input.skip_authentication = False
				card_paymen_method_specific_input.token = worldline_data.get("token")
				card_paymen_method_specific_input.payment_product_id = worldline_data.get('product_id') or 1

				payment_request.card_payment_method_specific_input = card_paymen_method_specific_input

			try:
				response = client.merchant(config.get("merchant_id")).payments().create_payment(payment_request)
			except Exception as e:
				if hasattr(e, "response_body"):
					audit_response(hotel_code, "WORLDLINE_EU", str(transaction_id), "", e.response_body,
								payload=json.dumps(payment_request.to_dictionary()), type_audit="Payment")

				logging_controlled_payment_errors_in_wl2_exceptions(str(e))
				return "%s%s%s" % (GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, str(e))

			audit_response(hotel_code, "WORLDLINE_EU", str(transaction_id), "", json.dumps(response.to_dictionary(), indent=4),
						payload=json.dumps(payment_request.to_dictionary()), type_audit="Payment")

			if response.payment.status == "PENDING_CAPTURE":
				capture_body = CapturePaymentRequest()
				capture_body.amount = response.payment.payment_output.amount_of_money.amount
				try:
					capture_result = client.merchant(config.get("merchant_id")).payments().capture_payment(
						response.payment.id, capture_body)
				except Exception as e:
					if hasattr(e, "response_body"):
						audit_response(hotel_code, "WORLDLINE_EU", str(transaction_id), "", e.response_body,
									payload=json.dumps(capture_body.to_dictionary(), indent=4), type_audit="Capture")
					logging_controlled_payment_errors_in_wl2_exceptions(str(e))
					return "%s%s%s" % (GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, str(e))
				audit_response(hotel_code, "WORLDLINE_EU", str(transaction_id), "",
							json.dumps(capture_result.to_dictionary(), indent=4),
							payload=json.dumps(capture_body.to_dictionary(), indent=4), type_audit="Capture")

				error_found = self.check_posible_errors_in_status(transaction_id, capture_result)
				if error_found:
					return "%s%s%s" % (GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, str(error_found))
				else:
					self.save_last_order_in_reservation(hotel_code, reservation, transaction_id, response.payment.id)
					return transaction_id
		return GATEWAY_ERROR_RETURNED



	def check_posible_errors_in_status(self, transaction_id, capture_body):

		try:
			capture_str = json.dumps(capture_body.to_dictionary())
			if "CAPTURE_REQUESTED" in capture_str or "PENDING_CAPTURE" in capture_str:
				logging.info("CAPTURE_REQUESTED or PENDING_CAPTURE found :) %s", transaction_id)
				return False

			for error_code in POS_ERRORS_CODES:
				if error_code in capture_str:

					# TODO return error when we feel secure (not receive NEVER this email)
					message_error = "Check this body and logs: %s" % capture_str
					notify_error_by_email("[MEGA URGENT] WORLDLINE_EU2. possible OK but possible error payment: %s" % transaction_id,
										  message_error)

					logging.error("ERROR NOT EXPECTED  controlling OK in WORLDLINE_EU2: %s %s", transaction_id, message_error)

					return False

			logging.error("Response OK BUT NOT controlling in WORLDLINE_EU2: %s %s", transaction_id, capture_body)

		except Exception as e:
			logging.error("something strange controling OK in WORLDLINE_EU2: %s", transaction_id)
			logging.error("%s capture_body: %s", transaction_id, capture_body)
			logging.error("IN THEORY we are OPTIMIST because HERE we could have an OK ALWAYS: %s", transaction_id)
			logging.error(e)

			message_error = "Check this body and logs: %s" % capture_body
			notify_error_by_email("[MEGA URGENT] WORLDLINE_EU2. possible OK but possible error payment: %s" % transaction_id, message_error)


		return False

	def execute_refund_in_gateway(self, hotel_code: str, reservation: dict, amount: float, order_to_refund: str, payment=None) -> str:
		integration_name = get_integration_name(hotel_code)
		config = get_payment_gateway_configuration(integration_name, hotel_code)
		if config.get("dev"):
			developer_configs = {i.replace("dev_", ""): config[i] for i in config if i.startswith("dev_")}
			config.update(developer_configs)

		authenticator = DefaultAuthenticator(config.get("api_key"), config.get("secret_key"))
		metadataProvider = MetaDataProvider("OnlinePayments")

		defaultConnection = DefaultConnection(5000, 10000)

		communicator = Communicator(
			api_endpoint=config.get("target_url"),
			authenticator=authenticator,
			meta_data_provider=metadataProvider
		)
		client = Factory.create_client_from_communicator(communicator)

		try:
			extra_info = \
				json.loads(reservation.get("extraInfo", {}))
		except:
			extra_info = {}

		if extra_info:
			worldline_data = extra_info.get("worldline_data")

			external_id = worldline_data.get("transactions_index", {}).get(order_to_refund)
			if not external_id and worldline_data.get("first_transaction"):
				external_id = worldline_data.get("first_transaction")


			currency = extra_info.get("currency", "")
			if not currency:
				currency = get_configuration_property_value("Base Price Currency")
			if not currency:
				currency = "EUR"

			currency = currency.upper()

			transaction_id = self.get_next_order(hotel_code, reservation)

			refund_request = RefundRequest()

			amount_of_money = AmountOfMoney()
			amount_of_money.amount = gateways_format_price(amount)
			amount_of_money.currency_code = currency

			references = PaymentReferences()
			references.merchant_reference = transaction_id

			refund_request.amount_of_money = amount_of_money
			refund_request.references = references

			try:
				response = client.merchant(config.get("merchant_id")).payments().refund_payment(external_id, refund_request)
			except Exception as e:
				if hasattr(e, "response_body"):
					audit_response(hotel_code, "WORLDLINE_EU", str(transaction_id), "", e.response_body,
									payload=json.dumps(refund_request.to_dictionary(), indent=4), type_audit="Refund")
				logging_controlled_payment_errors_in_wl2_exceptions(str(e))

				return "%s%s%s" % (GATEWAY_ERROR_RETURNED, SEPARATOR_INFO_ERROR_ORDER, str(e))
			audit_response(hotel_code, "WORLDLINE_EU", str(transaction_id), "",
						json.dumps(response.to_dictionary(), indent=4),
						payload=json.dumps(refund_request.to_dictionary(), indent=4), type_audit="Refund")

			self.save_last_order_in_reservation(hotel_code, reservation, transaction_id, response.id)
			return transaction_id

		return GATEWAY_ERROR_RETURNED

	def reservation_has_token(self, reservation: dict) -> bool:
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			# and str(extra_info.get("sermepa_cof_txnid", "")) #maybe is is not mandatory.
			if extra_info.get("worldline_data", {}).get("payment_method", "") in ["card"]:
				return True

		return False

	def gateway_has_token(self) -> bool:
		return True

	def get_initial_order_id_from_extra_info(self, reservation_extra_info: dict):
		return reservation_extra_info.get("paymentOrderId")

	def translate_error(self, error: str) -> str:
		error_message = error.split(SEPARATOR_INFO_ERROR_ORDER)
		if len(error_message) > 1:
			try:
				if ("paymentResult" in error) and ("payment" in error):
					start_index = error.find("response_body='") + len("response_body='")
					end_index = error.rfind("'")
					response_body_str = error[start_index:end_index]
					# Analizar la cadena JSON a un objeto Python
					response_body = json.loads(response_body_str)
					order = response_body.get("paymentResult", {}).get("payment", {}).get("id")
					error = json.dumps(response_body.get("paymentResult", {}).get("payment", {}).get("statusOutput"))
					return "%s%s%s%s" % (GATEWAY_ERROR_RETURNED,error,SEPARATOR_INFO_ERROR_ORDER, order)

				return error_message[1]
			except Exception as e:
				return error_message[1]
		return error


	def get_configuration(self, hotel_code: str) -> dict:
		integration_name = get_integration_name(hotel_code)
		config = get_payment_gateway_configuration(integration_name, hotel_code)

		if config.get("dev"):
			developer_configs = {i.replace("dev_", ""): config[i] for i in config if i.startswith("dev_")}
			config.update(developer_configs)

		return config


class WorldLineEu2FormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):

		config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
		hotel = get_hotel_by_application_id(hotel_code)
		translations = language_utils.get_web_dictionary(extra_data.get("language", "SPANISH"))

		if config.get("dev"):
			developer_configs = {i.replace("dev_", ""): config[i] for i in config if i.startswith("dev_")}
			config.update(developer_configs)

		if config.get("currency"):
			currency = config.get("currency")
		else:
			currency = "EUR"

		currency = currency.upper()

		url_merchant = config.get("merchant_url")

		url_merchant = url_merchant + "&sid=" + str(sid) + "&reservation=" + str(payment_order_id)
		if "?" not in url_merchant:
			url_merchant = url_merchant.replace("&", "?", 1)

		alt_merchant_url = config.get("url_ok")
		alt_merchant_url = alt_merchant_url + "&sid=" + str(sid)
		if "?" not in alt_merchant_url:
			alt_merchant_url = alt_merchant_url.replace("&", "?", 1)

		authenticator = DefaultAuthenticator(config.get("api_key"), config.get("secret_key"))
		metadataProvider = MetaDataProvider("OnlinePayments")

		defaultConnection = DefaultConnection(5000, 10000)

		communicator = Communicator(
			api_endpoint=config.get("target_url"),
			authenticator=authenticator,
			meta_data_provider=metadataProvider
		)
		client = Factory.create_client_from_communicator(communicator)

		payment_methods = []

		createHostedTokenizationRequest = CreateHostedTokenizationRequest()
		createHostedTokenizationRequest.locale = get_locale(extra_data.get("language"))
		createHostedTokenizationResponse = client.merchant(config.get("merchant_id")) \
			.hosted_tokenization() \
			.create_hosted_tokenization(createHostedTokenizationRequest)

		payload = {
			"hotel_code": hotel_code,
			"gateway_type": gateway_type,
			"payment_order_id": payment_order_id,
			"amount": amount,
			"currency": currency,
			"sid": sid,
			"add_button": add_button,
			"extra_data": extra_data
		}

		payment_info_string = json.dumps(payload)

		encryptor = AES.new(KEY, AES.MODE_EAX)

		payload = encryptor.encrypt(payment_info_string.encode("utf-8"))

		encrypted_payload = base64.urlsafe_b64encode(encryptor.nonce + payload).decode("ascii")

		context = {
			"payment_order_id": payment_order_id,
			"hotel_code": hotel_code,
			"token_hosted_url": createHostedTokenizationResponse.hosted_tokenization_url,
			"merchant_url": url_merchant,
			"payload": encrypted_payload,
			"logging_session": build_logger_context(),
			"type": "insite"
		}

		payment_methods.append({
			"type": "insite",
			"label": translations.get("T_payment_days_a_t")
		})

		# 302 Apple pay
		# 320 Google pay
		# 5001 Bizum

		nrf_payment_methods = config.get("nrf_payment_methods", "").strip(";")
		if nrf_payment_methods:
			try:
				nrf_payment_methods = [int(i) for i in nrf_payment_methods.split(";")]
			except Exception as e:
				logging.warning("[WORLDLINE][%s] Wrong configuration nrf_payment_methods: %s" % (hotel_code, str(e)))
				nrf_payment_methods = []
		else:
			nrf_payment_methods = []

		if extra_data.get("nrf"):
			for payment_method in nrf_payment_methods:

				payload = {
					"hotel_code": hotel_code,
					"gateway_type": gateway_type,
					"payment_order_id": payment_order_id,
					"amount": amount,
					"sid": sid,
					"add_button": add_button,
					"extra_data": extra_data,
					"payment_method": payment_method
				}

				payment_info_string = json.dumps(payload)

				encryptor = AES.new(KEY, AES.MODE_EAX)

				payload = encryptor.encrypt(payment_info_string.encode("utf-8"))

				encrypted_payload = base64.urlsafe_b64encode(encryptor.nonce + payload).decode("ascii")

				params = {
					"redirect_url": fixurl("/worldline_eu2/redirect?data=%s" % encrypted_payload),
					"type": "checkout",
					"payment_method": payment_method
				}

				if payment_method == 302:
					params["label"] = translations.get("T_payment_apple_t")
					params["icons"] = [fixurl("/static/images/apple-pay.png")]

				elif payment_method == 320:
					params["label"] = translations.get("T_payment_google_t")
					params["icons"] = [fixurl("/static/images/google-pay.png")]
				elif payment_method == 5001:
					params["label"] = translations.get("T_payment_bizum_t")
					params["icons"] = [fixurl("/static/images/bizum-logo-DCFC870E8B-seeklogo.com.png")]

				payment_methods.append(params)

		context["payment_methods"] = payment_methods

		body = render_template("pages/cobrador/gateways/worldline_eu2_form.html", **context)

		return body

	def process_gateway_response(self, hotel_code, gateway_type, response):
		config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
		if config.get("dev"):
			developer_configs = {i.replace("dev_", ""): config[i] for i in config if i.startswith("dev_")}
			config.update(developer_configs)

		hosted_checkout_id = request.args.get("hostedCheckoutId")
		hosted_tokenization_id = request.args.get("HTID")
		payment_id = request.args.get("paymentId")

		result = {
			"CODE": GATEWAY_ERROR_CODE_RETURNED,
			"GATEWAY_ORDER_ID": request.args.get("reservation")
		}

		authenticator = DefaultAuthenticator(config.get("api_key"), config.get("secret_key"))
		metadataProvider = MetaDataProvider("OnlinePayments")

		defaultConnection = DefaultConnection(5000, 10000)

		communicator = Communicator(
			api_endpoint=config.get("target_url"),
			authenticator=authenticator,
			meta_data_provider=metadataProvider
		)

		client = Factory.create_client_from_communicator(communicator)

		if hosted_checkout_id:
			try:
				order = client.merchant(config.get("merchant_id")).hosted_checkout().get_hosted_checkout(hosted_checkout_id)
			except Exception as e:
				if hasattr(e, "response_body"):
					result["GATEWAY_ERROR_MESSAGE"] = e.response_body
					audit_response(hotel_code, "WORLDLINE_EU", str(request.args.get("reservation")), "", e.response_body,
								payload=str(hosted_checkout_id))
				return result

			audit_response(hotel_code, "WORLDLINE_EU", str(request.args.get("reservation")), "", json.dumps(order.to_dictionary(), indent=4),
						   payload=str(hosted_checkout_id))

			if order.created_payment_output.payment_status_category == "SUCCESSFUL":
				if order.created_payment_output.payment.status == "PENDING_CAPTURE":
					capture_body = CapturePaymentRequest()
					capture_body.amount = order.created_payment_output.payment.payment_output.amount_of_money.amount
					try:
						capture_response = client.merchant(config.get("merchant_id")).payments().capture_payment(
							order.created_payment_output.payment.id, capture_body)
					except Exception as e:
						if hasattr(e, "response_body"):
							audit_response(hotel_code, "WORLDLINE_EU", str(request.args.get("reservation")), "", e.response_body,
										payload=json.dumps(capture_body.to_dictionary()), type_audit="Capture")
							logging.info(e)
							result["GATEWAY_ERROR_MESSAGE"] = e.response_body
						return result

					audit_response(hotel_code, "WORLDLINE_EU", str(request.args.get("reservation")), "",
								json.dumps(capture_response.to_dictionary(), indent=4),
								payload=json.dumps(capture_body.to_dictionary()), type_audit="Capture")
				payment_method = order.created_payment_output.payment.payment_output.payment_method
				token = ""
				product_id = 1
				if payment_method == "card":
					token = order.created_payment_output.payment.payment_output.card_payment_method_specific_output.token
					product_id = order.created_payment_output.payment.payment_output.card_payment_method_specific_output.payment_product_id

				gateway_amount = order.created_payment_output.payment.payment_output.amount_of_money.amount / 100

				result["CODE"] = GATEWAY_SUCESS_RETURNED
				result["GATEWAY_PAID_AMOUNT"] = gateway_amount
				result["GATEWAY_EXTRA_INFO"] = {
					"worldline_data": {
						"payment_method": payment_method,
						"product_id": product_id,
						"first_transaction": order.created_payment_output.payment.id
					}
				}
				if token:
					result["GATEWAY_EXTRA_INFO"]["worldline_data"]["token"] = token

				# Añadir la fecha de expiración al GATEWAY_EXTRA_INFO
				self.add_expiration_date_to_response(result, order)

		elif hosted_tokenization_id:

			try:
				order = client.merchant(config.get("merchant_id")).payments().get_payment_details(payment_id)
			except Exception as e:
				if hasattr(e, "response_body"):
					audit_response(hotel_code, "WORLDLINE_EU", str(request.args.get("reservation")), "",
								e.response_body,
								payload=hosted_tokenization_id, type_audit="Tokenize")
					logging_controlled_payment_errors_in_wl2_exceptions(str(e.response_body))
					result["GATEWAY_ERROR_MESSAGE"] = e.response_body
				return result

			if order.status in ["PENDING_CAPTURE", "CAPTURED"]:
				try:
					token_data = client.merchant(config.get("merchant_id")).hosted_tokenization().get_hosted_tokenization(hosted_tokenization_id)
				except Exception as e:
					if hasattr(e, "response_body"):
						audit_response(hotel_code, "WORLDLINE_EU", str(request.args.get("reservation")), "",
									e.response_body,
									payload=hosted_tokenization_id, type_audit="Tokenize")
						logging_controlled_payment_errors_in_wl2_exceptions(str(e.response_body))
						result["GATEWAY_ERROR_MESSAGE"] = e.response_body
					return result

				audit_response(hotel_code, "WORLDLINE_EU", str(request.args.get("reservation")), "",
							json.dumps(token_data.to_dictionary(), indent=4),
							payload=hosted_tokenization_id, type_audit="Tokenize")

				gateway_amount = order.payment_output.amount_of_money.amount / 100

				result["CODE"] = GATEWAY_SUCESS_RETURNED
				result["GATEWAY_PAID_AMOUNT"] = gateway_amount
				result["GATEWAY_EXTRA_INFO"] = {
					"worldline_data": {
						"payment_method": "card",
						"product_id": token_data.token.payment_product_id,
						"first_transaction": payment_id,
						"token": token_data.token.id
					}
				}

				self.add_expiration_date_to_token_response(result, order, token_data)

		return result

	def add_expiration_date_to_response(self, result, order):
		logging.info("[WORLDLINE_EU2] Trying to add expiration date")
		try:
			# Intentamos obtener la información de la tarjeta
			payment_output = order.created_payment_output.payment.payment_output
			if hasattr(payment_output, "card_payment_method_specific_output"):
				card_output = payment_output.card_payment_method_specific_output
				if hasattr(card_output, "card") and hasattr(card_output.card, "expiry_date"):
					expiry_date = card_output.card.expiry_date

					if expiry_date and len(expiry_date) >= 4:
						# El formato típico es MMYY
						month = expiry_date[0:2]
						year = expiry_date[2:4]

						result["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{month}/{year}"
						logging.info("[WORLDLINE_EU2] Added expiration date to response: %s", result["GATEWAY_EXTRA_INFO"]["cc_expire_date"])
		except Exception as e:
			logging.warning("[WORLDLINE_EU2] Error adding expiration date to response: %s", str(e))

	def add_expiration_date_to_token_response(self, result, order, token_data):
		logging.info("[WORLDLINE_EU2] Trying to add expiration date")
		try:
			# For the tokenization, the date can be in token_data
			if hasattr(token_data, "token") and hasattr(token_data.token, "card"):
				card = token_data.token.card
				if hasattr(card, "expiry_date"):
					expiry_date = card.expiry_date

					if expiry_date and len(expiry_date) >= 4:
						# El formato típico es MMYY
						month = expiry_date[0:2]
						year = expiry_date[2:4]

						result["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{month}/{year}"
						logging.info("[WORLDLINE_EU2] Added expiration date to token response: %s", result["GATEWAY_EXTRA_INFO"]["cc_expire_date"])
			# If it is not in token_data, we try to get it from order
			elif hasattr(order, "payment_output") and hasattr(order.payment_output, "card_payment_method_specific_output"):
				card_output = order.payment_output.card_payment_method_specific_output
				if hasattr(card_output, "card") and hasattr(card_output.card, "expiry_date"):
					expiry_date = card_output.card.expiry_date

					if expiry_date and len(expiry_date) >= 4:
						month = expiry_date[0:2]
						year = expiry_date[2:4]

						result["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{month}/{year}"
						logging.info("[WORLDLINE_EU2] Added expiration date to token response: %s", result["GATEWAY_EXTRA_INFO"]["cc_expire_date"])
		except Exception as e:
			logging.warning("[WORLDLINE_EU2] Error adding expiration date to token response: %s", str(e))

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


@app.route("/worldline_eu2/authorize_payment", methods=["POST"])
@cross_origin(origin='*')
def authorize_worldline2_payment():
	if request.is_json:
		request_json = request.json
	else:
		return {}, 400
	hosted_tokenization_id = request_json.get("token_id")
	client_metadata = request_json.get("client_metadata", {})
	payload = request_json.get("payload")
	payload = base64.urlsafe_b64decode(payload)

	nounce = payload[:16]
	payload = payload[16:]
	encryptor = AES.new(KEY, AES.MODE_EAX, nonce=nounce)
	payload = encryptor.decrypt(payload)
	extra_data = json.loads(payload)

	hotel_code = extra_data.get("hotel_code")
	gateway_type = extra_data.get("gateway_type")
	payment_order_id = extra_data.get("payment_order_id")
	amount = extra_data.get("amount")
	currency = extra_data.get("currency")

	sid = extra_data.get("sid")

	try:
		language = session_utils.get_current_session().get_value("language")
	except:
		language = "SPANISH"

	translations = language_utils.get_web_dictionary(language)

	config = get_payment_gateway_configuration(gateway_type, hotel_code)

	url_merchant = config.get("merchant_url")

	url_merchant = url_merchant + "&sid=" + str(sid) + "&reservation=" + str(payment_order_id)

	url_merchant += "&HTID=%s" % hosted_tokenization_id

	if "?" not in url_merchant:
		url_merchant = url_merchant.replace("&", "?", 1)

	if config.get("dev"):
		developer_configs = {i.replace("dev_", ""): config[i] for i in config if i.startswith("dev_")}
		config.update(developer_configs)

	authenticator = DefaultAuthenticator(config.get("api_key"), config.get("secret_key"))
	metadataProvider = MetaDataProvider("OnlinePayments")

	defaultConnection = DefaultConnection(5000, 10000)

	communicator = Communicator(
		api_endpoint=config.get("target_url"),
		authenticator=authenticator,
		meta_data_provider=metadataProvider
	)
	client = Factory.create_client_from_communicator(communicator)

	try:
		token_data = client.merchant(config.get("merchant_id")).hosted_tokenization().get_hosted_tokenization(hosted_tokenization_id)

		payment_request = CreatePaymentRequest()

		amountOfMoney = AmountOfMoney()
		amountOfMoney.amount = gateways_format_price(amount)
		amountOfMoney.currency_code = currency

		order = Order()
		order.amount_of_money = amountOfMoney

		customer = Customer()
		customer.merchant_customer_id = payment_order_id


		contact_details = ContactDetails()

		contact_details.email_address = client_metadata.get("email")
		contact_details.phone_number = client_metadata.get("phone")

		customer.contact_details = contact_details

		customer_device = CustomerDevice()
		customer_device.accept_header = request.headers.get("Accept", "")
		customer_device.ip_address = request.remote_addr
		customer_device.user_agent = client_metadata.get("userAgent")
		customer_device.timezone_offset_utc_minutes = client_metadata.get("timeoffset")
		customer_device.locale = client_metadata.get("locale")

		browser_data = BrowserData()
		browser_data.java_enabled = client_metadata.get("javaEnabled")
		browser_data.java_script_enabled = True
		browser_data.color_depth = client_metadata.get("colorDepth")
		browser_data.screen_width = client_metadata.get("width")
		browser_data.screen_height = client_metadata.get("height")

		customer_device.browser_data = browser_data

		customer.device = customer_device

		order.customer = customer

		references = OrderReferences()
		references.merchant_reference = payment_order_id

		metadata = {
			"merchant_url": url_merchant,
			"hotel_code": hotel_code
		}

		references.merchant_parameters = json.dumps(metadata)
		order.references = references

		payment_request.order = order

		card_paymen_method_specific_input = CardPaymentMethodSpecificInput()
		card_paymen_method_specific_input.authorization_mode = "SALE"
		# card_paymen_method_specific_input.tokenize = True
		card_paymen_method_specific_input.unscheduled_card_on_file_requestor = "cardholderInitiated"
		card_paymen_method_specific_input.unscheduled_card_on_file_sequence_indicator = "first"
		# card_paymen_method_specific_input.token = token_data.token.id
		# card_paymen_method_specific_input.payment_product_id = token_data.token.payment_product_id or 1


		three_d_secure = ThreeDSecure()
		redirection_data = RedirectionData()
		redirection_data.return_url = url_merchant

		three_d_secure.redirection_data = redirection_data
		three_d_secure.challenge_indicator = "challenge-required"
		three_d_secure.skip_authentication = False

		card_paymen_method_specific_input.three_d_secure = three_d_secure


		payment_request.card_payment_method_specific_input = card_paymen_method_specific_input
		payment_request.hosted_tokenization_id = hosted_tokenization_id

		logging.info("[%s][%s] Tokenization order payload: %s" % (hotel_code, payment_order_id, json.dumps(payment_request.to_dictionary())))

		response = client.merchant(config.get("merchant_id")).payments().create_payment(payment_request)

		logging.info("[%s][%s] Tokenization order response: %s" % (hotel_code, payment_order_id, json.dumps(response.to_dictionary())))

		if response.payment.status in ["PENDING_CAPTURE", "CAPTURED", "PENDING_CAPTURE", "REDIRECTED"]:

			if response.merchant_action and response.merchant_action.action_type == "REDIRECT":
				return {
					"redirect_to": response.merchant_action.redirect_data.redirect_url
				}

			payment_id = response.payment.id

			return {
				"redirect_to": url_merchant + "&paymentId=%s" % payment_id
			}

		else:
			raise Exception(json.dumps(response.to_dictionary()))


	except Exception as e:
		logging.info(str(e))
		generic_tpv_error_translation = translations.get("T_TPV_GENERIC_ERROR")
		refresh_page_translation = translations.get("T_REFRESH_PAGE")
		another_card_translation = translations.get("T_PAY_WITH_ANOTHER_CARD")
		generic_tpv_error = generic_tpv_error_translation + " " + refresh_page_translation + ", " + another_card_translation
		response = generic_tpv_error

	return {
		"error": "error",
		"message": response
	}


@app.route("/worldline_eu2/redirect", methods=["GET"])
def worldline_eu2_redirect():
	data = request.args.get("data")
	payload = base64.urlsafe_b64decode(data.encode("ascii"))
	nounce = payload[:16]
	payload = payload[16:]

	encryptor = AES.new(KEY, AES.MODE_EAX, nonce=nounce)

	payload = encryptor.decrypt(payload)

	payment_data = json.loads(payload)

	hotel_code = payment_data.get("hotel_code")
	gateway_type = payment_data.get("gateway_type")
	payment_order_id = payment_data.get("payment_order_id")
	amount = payment_data.get("amount")
	sid = payment_data.get("sid")
	extra_data = payment_data.get("extra_data")
	language = extra_data.get("language")

	config = get_payment_gateway_configuration(gateway_type, hotel_code=hotel_code)
	hotel = get_hotel_by_application_id(hotel_code)

	if config.get("dev"):
		developer_configs = {i.replace("dev_", ""): config[i] for i in config if i.startswith("dev_")}
		config.update(developer_configs)

	if config.get("currency"):
		currency = config.get("currency")
	else:
		currency = "EUR"

	currency = currency.upper()

	url_merchant = config.get("merchant_url")

	url_merchant = url_merchant + "&sid=" + str(sid) + "&reservation=" + str(payment_order_id)
	if "?" not in url_merchant:
		url_merchant = url_merchant.replace("&", "?", 1)

	alt_merchant_url = config.get("url_ok")
	alt_merchant_url = alt_merchant_url + "&sid=" + str(sid)
	if "?" not in alt_merchant_url:
		alt_merchant_url = alt_merchant_url.replace("&", "?", 1)

	authenticator = DefaultAuthenticator(config.get("api_key"), config.get("secret_key"))
	metadataProvider = MetaDataProvider("OnlinePayments")

	defaultConnection = DefaultConnection(5000, 10000)

	communicator = Communicator(
		api_endpoint=config.get("target_url"),
		authenticator=authenticator,
		meta_data_provider=metadataProvider
	)
	client = Factory.create_client_from_communicator(communicator)

	createHostedCheckoutRequest = CreateHostedCheckoutRequest()


	amountOfMoney = AmountOfMoney()
	amountOfMoney.amount = gateways_format_price(amount)
	amountOfMoney.currency_code = currency

	order = Order()
	order.amount_of_money = amountOfMoney

	customer = Customer()
	customer.merchant_customer_id = "%s:%s" % (hotel_code, payment_order_id)

	prod_q = GetPaymentProductsParams()
	prod_q.country_code = "ES"
	prod_q.currency_code = "EUR"
	args = client.merchant(config.get("merchant_id")).products().get_payment_products(prod_q)


	session = get_session_from_hotel(hotel_code, sid)

	if session and session.get("personal_details_"):
		personal_details = session.get("personal_details_")

		contact_details = ContactDetails()

		contact_details.phone_number = personal_details.get("telephone", "")
		contact_details.email_address = personal_details.get("email", "")

		personal_information = PersonalInformation()

		pruned_name = personal_details.get("name", "") or ""
		pruned_lastname = personal_details.get("lastName1", "") or ""
		personal_name = PersonalName()

		personal_name.first_name = pruned_name
		personal_name.surname = pruned_lastname

		personal_information.name = personal_name

		customer.contact_details = contact_details
		customer.personal_information = personal_information
		customer.locale = "es"

		selected_options = session.get("selectedOption_")
		if selected_options:
			shopping_cart = ShoppingCart()

			rooms = []

			all_rooms = get_rooms_of_hotel(hotel, language)
			all_rooms = {i["key"]: i["name"] for i in all_rooms}
			all_rates = get_rates_of_hotel(hotel, language)
			all_rates = {i["key"]: i["name"] for i in all_rates}
			all_boards = get_boards_of_hotel(hotel, language)
			all_boards = {i["key"]: i["name"] for i in all_boards}

			selected_options = selected_options.split(";")
			rooms_total_amount = 0

			for selected_option in selected_options:
				rate_room_board = session.get("price_option_%s" % selected_option)
				if rate_room_board and len(rate_room_board) >= 3:
					rooms_total_amount += float(rate_room_board[3])


			for index, selected_option in enumerate(selected_options):
				rate_room_board = session.get("price_option_%s" % selected_option)
				if rate_room_board and len(rate_room_board) >= 3:
					rate_str = all_rates.get(rate_room_board[0])
					board_str = all_boards.get(rate_room_board[2])
					room_str = all_rooms.get(rate_room_board[1])

					room_amount = float(rate_room_board[3])

					room_percent = (room_amount * 100 / rooms_total_amount)

					room_amount = (room_percent * float(amount)) / 100

					line_item = LineItem()

					amount_of_money = AmountOfMoney()
					amount_of_money.amount = gateways_format_price(room_amount)
					amount_of_money.currency_code = currency

					order_line_details = OrderLineDetails()

					order_line_details.product_name = "%s" % rate_str
					order_line_details.product_price = gateways_format_price(room_amount)
					order_line_details.quantity = 1

					line_item.amount_of_money = amount_of_money
					line_item.order_line_details = order_line_details

					rooms.append(line_item)

			if rooms:
				total_shopping_cart = sum([int(i.amount_of_money.amount) for i in rooms])
				if total_shopping_cart != int(gateways_format_price(amount)):
					rooms[0].amount_of_money.amount = str(
						int(rooms[0].amount_of_money.amount) + int(gateways_format_price(amount)) - total_shopping_cart)

			shopping_cart.items = rooms
			order.shopping_cart = shopping_cart

	order.customer = customer

	references = OrderReferences()
	references.merchant_reference = "%s:%s" % (hotel_code, payment_order_id)
	metadata = {
		"merchant_url": url_merchant,
		"hotel_code": hotel_code
	}

	references.merchant_parameters = json.dumps(metadata)
	order.references = references

	hostedCheckoutSpecificInput = HostedCheckoutSpecificInput()

	hostedCheckoutSpecificInput.return_url = url_merchant
	# hostedCheckoutSpecificInput.is_recurring = True
	hostedCheckoutSpecificInput.showResultPage = False
	hostedCheckoutSpecificInput.locale = get_locale(language)

	if config.get("checkout template"):
		hostedCheckoutSpecificInput.variant = config.get("checkout template")

	payment_product_filter_hosted_checkout = PaymentProductFiltersHostedCheckout()
	payment_product_filter = PaymentProductFilter()
	payment_product_filter.products = [payment_data.get("payment_method")]

	payment_product_to_exclude = PaymentProductFilter()
	payment_product_to_exclude.groups = ["cards"]
	payment_product_filter_hosted_checkout.exclude = payment_product_to_exclude
	payment_product_filter_hosted_checkout.restrict_to = payment_product_filter
	hostedCheckoutSpecificInput.payment_product_filters = payment_product_filter_hosted_checkout

	createHostedCheckoutRequest.order = order
	createHostedCheckoutRequest.hosted_checkout_specific_input = hostedCheckoutSpecificInput


	card_paymen_method_specific_input = CardPaymentMethodSpecificInput()
	# card_paymen_method_specific_input.is_recurring = True
	card_paymen_method_specific_input.authorizationMode = "SALE"
	# card_paymen_method_specific_input.tokenize = True

	createHostedCheckoutRequest.card_payment_method_specific_input = card_paymen_method_specific_input

	redirect_payment_method = RedirectPaymentMethodSpecificInput()

	redirect_payment_method.requires_approval = False

	createHostedCheckoutRequest.redirect_payment_method_specific_input = redirect_payment_method

	try:
		createHostedCheckoutResponse = client.merchant(
			config.get("merchant_id")).hosted_checkout().create_hosted_checkout(createHostedCheckoutRequest)
	except Exception as e:
		if hasattr(e, "response_body"):
			audit_response(hotel_code, "WORLDLINE_EU", str(payment_order_id), sid,
						   e.response_body,
						   payload=json.dumps(createHostedCheckoutRequest.to_dictionary(), indent=4),
						   type_audit="Form Payment")
		logging_controlled_payment_errors_in_wl2_exceptions(str(e))
		return "KO"

	audit_response(hotel_code, "WORLDLINE_EU", str(payment_order_id), sid,
				   json.dumps(createHostedCheckoutResponse.to_dictionary(), indent=4),
				   payload=json.dumps(createHostedCheckoutRequest.to_dictionary(), indent=4),
				   type_audit="Form Payment")


	return redirect(createHostedCheckoutResponse.redirect_url)


def get_reservation_already_exists(identifier, merchant_url):
	if 'namespace' not in merchant_url or not identifier:
		return False
	hotel_code = merchant_url.split('namespace=')[1].split('&')[0]
	pending_reservations = datastore_communicator.get_using_entity_and_params('PendingReservation', hotel_code=hotel_code,
	                                                   search_params=[('identifier', '=', identifier)])
	if not pending_reservations:
		return False

	sorted_reservations = sorted(pending_reservations, key=lambda x: x.get('timestamp', ''), reverse=True)
	if not sorted_reservations:
		return False

	if sorted_reservations[0].get('status', '') == 'done':
		logging.info(f'Reservation with identifier {identifier} already exists in {hotel_code}')
		return True

	return False


@app.route("/worldline_eu2/webhook", methods=["GET", "POST"])
def worldline_eu2_webhook():
	logging.info("[WORLDLINE_EU] Webhook received")
	try:
		if request.is_json:
			body = request.json
			logging.info("[WORLDLINE_EU] Webhook body as JSON found: %s", body)
			if body.get("type") in ["payment.pending_capture", "payment.captured"]:
				if body.get("payment"):
					hosted_checkout_id = body['payment']['id']
					hosted_checkout_id = hosted_checkout_id.split("_")[0]
					payment_output = body.get("payment", {}).get("paymentOutput", {})
					merchant_parameters = payment_output.get("merchantParameters", "")
					identifier = payment_output.get("merchantReference", "")
					if merchant_parameters:
						merchant_parameters = json.loads(merchant_parameters)
						merchant_url = merchant_parameters.get("merchant_url")
						if merchant_url:
							merchant_url += "&hostedCheckoutId=%s" % hosted_checkout_id
							merchant_url += "&no_redirect=true"
							if "?" not in merchant_url:
								merchant_url = merchant_url.replace("&", "?", 1)

							reservation_id = get_reservation_already_exists(identifier, merchant_url)
							if reservation_id:
								logging.info(f"Reservation with identifier: {identifier} already exists, avoiding creating task")
								return "ok"

							task_timeout = DEFERED_TASKS_TIMEOUT.get(body.get("type")) or 90

							payload = {
								"merchant_url": merchant_url
							}

							logging.info("[WORLDLINE_EU] Webhook creating task. payload %s", payload)

							data = {
								"task_id": str(uuid4()),
								"data": base64.urlsafe_b64encode(json.dumps(payload).encode("utf-8")).decode("utf-8")
							}

							logging.info("[WORLDLINE_EU] Webhook creating task. data %s", data)

							queue_utils.create_task('execute_fallback_task', json.dumps(data),
													queue_name=COBRADOR_QUEUE,
													task_name='fallback_task__%s' % data["task_id"], in_seconds=task_timeout)
		return "ok", 200
	except Exception as e:
		logging.error("[WORLDLINE_EU] Webhook with exception")
		logging.exception(e)
		return "ok"
