import base64
import hashlib
import hmac
import json
import logging
import re
from random import randint
from Cryptodome.Cipher import DES3
import xml.etree.ElementTree as xee

import requests

from flask import request as r
from paraty import Config
from paraty.pages.cobrador.cobrador_constants import CREDIT_CARD_BY_TOKEN_CONFIG, GATEWAY_ERROR_RETURNED, \
	GATEWAY_SUCESS_RETURNED, SID_FROM_COBRADOR, SEPARATOR_INFO_ERROR_ORDER, GATEWAY_ERROR_CODE_RETURNED, \
	SERMEPA_CC_TOKEN_ID, SERMEPA_CC_EXPIREDATE, SERMEPA_BRAND_CC, SERMEPA_TYPE_CC, SERMEPA_ORDER_RECEIVED, \
	GATEWAY_PAID_AMOUNT, GATEWAY_ERROR_DICT, SERMEPA_COF_TXNID, LINK_TPV_PAYMENT_ORDER, CONTENT_TYPE_XML, \
	CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import encrypt_message_DES3, gateways_format_price, \
	get_payment_gateway_configuration, get_integration_name, add_sid_to_url, audit_response, \
	add_parameters_and_namespace_to_url, _get_project_id_and_namespace, get_amount_to_pay, fix_payment_order_id, \
	is_real_payment, get_all_payments_info_list
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.utilities.languages import language_utils
from paraty.utilities.languages.language_utils import build_custom_text_b3_by_key
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.common_data.common_data_provider import get_web_section
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.logging.my_gae_logging import logging as log
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

from urllib.parse import urlparse, parse_qs, parse_qsl

SERMEPA_MERCHANT_IDENTIFIER = "REQUIRED"

LANGUAGE_SERMEPA_MAP = {
	"SPANISH": 1,
	"ENGLISH": 2,
	"FRENCH": 4,
	"PORTUGUESE": 9,
	"GERMAN": 5,
}

def generate_signature_and_json(hotel_code, gateway_configuration, sermepa_token, sermepa_order, amount, direct_payment=False, cof="", sid="", language=None):

		#decode  Base64 key
		key = base64.b64decode(gateway_configuration["secret_key_sha256"])

		sermepa_amount = gateways_format_price(amount)

		merchant_datas = {
			"DS_MERCHANT_AMOUNT": sermepa_amount,
			"DS_MERCHANT_CURRENCY": gateway_configuration["currency_code"],
			"DS_MERCHANT_MERCHANTCODE": gateway_configuration["merchant_code"],
			"DS_MERCHANT_ORDER": sermepa_order,
			"DS_MERCHANT_TERMINAL": gateway_configuration["terminal"],
			"DS_MERCHANT_TRANSACTIONTYPE": gateway_configuration["transaction_type"]
		}

		if gateway_configuration.get("send_id_in_description"):
			hotel = get_hotel_by_application_id(hotel_code)
			merchant_datas["DS_MERCHANT_PRODUCTDESCRIPTION"] = f"{sermepa_order} - {hotel['name']}"

		if direct_payment:
			#direct payment from cobrador
			merchant_datas["DS_MERCHANT_DIRECTPAYMENT"] = "true"

			if gateway_configuration.get("merchant_url_rest"):
				merchant_datas["DS_MERCHANT_MERCHANTURL"] = gateway_configuration["merchant_url_rest"]

		else:
			#redirection form
			if sid:
				parameters_to_add = {
					"sid": sid,
					"origin": hotel_code,
					"no_redirect": "true",
				}
				_, multitenancy = _get_project_id_and_namespace(get_hotel_by_application_id(hotel_code))

				merchantUrl = add_parameters_and_namespace_to_url(gateway_configuration["merchant_url"], parameters_to_add, hotel_code, multitenancy)
				url_ok = add_parameters_and_namespace_to_url(gateway_configuration["url_ok"], {'sid': sid}, hotel_code, multitenancy)
				url_ko = add_parameters_and_namespace_to_url(gateway_configuration["url_ko"], {'sid': sid, 'reload': "true"}, hotel_code, multitenancy)

				if gateway_configuration.get("by_cobrador"):
					merchantUrl += "&by_cobrador=true"

				merchant_datas["DS_MERCHANT_MERCHANTURL"] = merchantUrl
				merchant_datas["DS_MERCHANT_URLOK"] = url_ok
				merchant_datas["DS_MERCHANT_URLKO"] = url_ko

		if sermepa_token:
			merchant_datas["DS_MERCHANT_IDENTIFIER"] = sermepa_token
			if (sermepa_token == "REQUIRED" and gateway_configuration.get("recurring_operation")) or gateway_configuration.get("cof_ini") == "S":
				merchant_datas["DS_MERCHANT_COF_INI"] = "S"





		if gateway_configuration.get("psd2_cof_type"):
			merchant_datas["DS_MERCHANT_COF_TYPE"] = gateway_configuration.get("psd2_cof_type")

			if gateway_configuration.get("except_sca"):
				if gateway_configuration.get("recurring_operation"):
					if sermepa_token != "REQUIRED":
						merchant_datas["DS_MERCHANT_EXCEP_SCA"] = gateway_configuration.get("except_sca")
				else:
					merchant_datas["DS_MERCHANT_EXCEP_SCA"] = gateway_configuration.get("except_sca")

			if cof:
				merchant_datas["DS_MERCHANT_COF_TXNID"] = cof

		if language:
			sermepa_language = LANGUAGE_SERMEPA_MAP.get(language, 2)
			merchant_datas["DS_MERCHANT_CONSUMERLANGUAGE"] = sermepa_language

		if sermepa_token and sermepa_token != "REQUIRED" and gateway_configuration.get("recurring_operation"):
			if not cof:
				merchant_datas["DS_MERCHANT_COF_TXNID"] = "999999999999999"
			merchant_datas["DS_MERCHANT_COF_INI"] = "N"

		if gateway_configuration.get("bizum"):
			merchant_datas["Ds_Merchant_PayMethods"] = "z"

		log.info("merchant datas for %s  %s", hotel_code, merchant_datas)
		json_merchant_datas = json.dumps(merchant_datas)
		json_merchant_datas = json_merchant_datas.encode("utf-8")

		Ds_Merchant_JsonDatasSHA256 = base64.b64encode(json_merchant_datas)

		# encrypt key + Order
		#encryptedKey = encryptMessageDES3(sermepaDict["Ds_Merchant_Order"], key)
		encryptedKey = encrypt_message_DES3(merchant_datas["DS_MERCHANT_ORDER"], key)

		#MAC256 of Ds_MerchantParameters with the new encrypted key
		encryptedParameters = hmac.new(encryptedKey, Ds_Merchant_JsonDatasSHA256, digestmod=hashlib.sha256).digest()

		Ds_Merchant_MerchantSignature = base64.b64encode(encryptedParameters)

		return Ds_Merchant_MerchantSignature, Ds_Merchant_JsonDatasSHA256


def create_json_and_signature(merchant_paramenters_no_encrypted, secret_key_sha256):
	json_merchant_datas = json.dumps(merchant_paramenters_no_encrypted)
	json_merchant_datas = json_merchant_datas.encode("utf-8")
	Ds_Merchant_JsonDatasSHA256 = base64.b64encode(json_merchant_datas)
	key = base64.b64decode(secret_key_sha256)
	merchant_order = merchant_paramenters_no_encrypted.get("DS_MERCHANT_ORDER")
	if not merchant_order:
		merchant_order = merchant_paramenters_no_encrypted.get("Ds_Order")
	encryptedKey = encrypt_message_DES3(merchant_order, key)
	encryptedParameters = hmac.new(encryptedKey, Ds_Merchant_JsonDatasSHA256, digestmod=hashlib.sha256).digest()
	Ds_Merchant_MerchantSignature = base64.b64encode(encryptedParameters)

	return Ds_Merchant_JsonDatasSHA256, Ds_Merchant_MerchantSignature


class SermepaControler(GatewayInterface):

	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		log.info("SERMEPA. execute_payment_in_gateway. reservation: %s amount: %s hotel: %s", reservation.get("identifier"), amount, hotel_code)
		#take SERMEPA configuration


		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		if gateway_configuration:

			payment_url = gateway_configuration.get("payment_url_rest")
			if payment_url:

				Ds_Merchant_MerchantSignature, Ds_Merchant_JsonDatasSHA256, order_id = self.generate_signature_and_json_SHA256(hotel_code, gateway_configuration, reservation, amount)

				payload = {
					"Ds_SignatureVersion":"HMAC_SHA256_V1",
					"Ds_MerchantParameters":Ds_Merchant_JsonDatasSHA256,
					"Ds_Signature":Ds_Merchant_MerchantSignature,
					"Ds_Merchant_DirectPayment":"true"
				}

				#POST to sermepa!
				if not gateway_configuration.get("use proxy"):
					response = requests.post(payment_url, data=payload)
				else:
					header = {
						"targetUrl": payment_url
					}

					response = requests.post("http://35.233.0.182", data=payload, headers=header)

				log.info("Response json: %s", response.text)
				result = response.json()

				log.info("result returned by SERMEPA: %s", result)


				try:
					gateway_type = "sermepa"
					audit_response(hotel_code, gateway_type, order_id, SID_FROM_COBRADOR, response.text)

				except Exception as e:
					log.error("Error auditing response for in SERMEPA")
					log.error("Error auditing: %s", e)



				if result.get("errorCode", ""):
					log.warning("Error received form sermepa: %s", result.get("errorCode", ""))
					return GATEWAY_ERROR_RETURNED + ": " + result.get("errorCode", "") + SEPARATOR_INFO_ERROR_ORDER + order_id

				if result.get('Ds_MerchantParameters'):

					decodec_parameters =base64.urlsafe_b64decode(str(result.get('Ds_MerchantParameters')))
					parameters_dict = json.loads(decodec_parameters)
					log.info("DS_MERCHANTPARAMETERS decodec : %s ", parameters_dict)
					#sermepa is not sure how they send us the parameters

					ds_response = parameters_dict.get("Ds_Response", parameters_dict.get("DS_RESPONSE"))
					#We check that the response is correct (OLD SERMEPA VERSION AND NEW!)
					#0000 a 0099	Transacción autorizada para pagos y preautorizaciones
					if ds_response and int(ds_response) > 99:
						log.warning("ERROR DS_RESPONSE TPV:  %s", ds_response)
						return GATEWAY_ERROR_RETURNED + ": " + ds_response

					ds_order = parameters_dict.get('Ds_Order',parameters_dict.get('DS_ORDER',''))
					return ds_order
		log.warning("integration_configuration NOt found!")
		log.warning("[%s] Integration name use to search configuration: %s" % (reservation.get("identifier"), integration_name))
		return GATEWAY_ERROR_RETURNED

	def get_initial_order_id_from_extra_info(self, reservation_extra_info):
		return reservation_extra_info.get("paymentOrderId")

	def gateway_has_token(self):
		return True

	def reservation_has_token(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				log.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			#and str(extra_info.get("sermepa_cof_txnid", "")) #maybe is is not mandatory.
			if str(extra_info.get("ds_MerchantIdentifier", "")):
				return str(extra_info.get("ds_MerchantIdentifier", ""))

		return False

	def get_sermepa_cof(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				log.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if str(extra_info.get("sermepa_cof_txnid", "")):
				return str(extra_info.get("sermepa_cof_txnid", ""))

		return ""


	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def get_next_sermepa_order(self, hotel_code, reservation):

		last_merchant_order_used = ""
		if reservation.get("extraInfo"):
			extra_info = json.loads(reservation.get("extraInfo"))
			last_merchant_order_used = extra_info.get("last_merchant_order_used")
			if not last_merchant_order_used and self.get_initial_order_id_from_extra_info(extra_info):
				last_merchant_order_used = self.get_initial_order_id_from_extra_info(extra_info)
				try:
					int(last_merchant_order_used)
				except:
					last_merchant_order_used = ""

		if last_merchant_order_used and not "error" in last_merchant_order_used.lower():
			last_merchant_order_used = last_merchant_order_used[0:8]
			numerical_order= int(last_merchant_order_used)
			numerical_order += 1

			merchant_order = str(numerical_order)

			log.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)
		else:

			merchant_order = reservation.get("identifier")
			#maybe this reservation was done before gateway was configured. So it could be an alphanumeric
			if not merchant_order.isnumeric():
				merchant_order = randint(10000000, 99999000)

			numerical_order= int(merchant_order)
			numerical_order += 1
			merchant_order = str(numerical_order)
			log.info("forced +1 the identifier to be used as a Merchant Order: %s", merchant_order)


		#IMPORTANT: Even redsys return us an erro, they save ALWAYS the order.
		#so we have to save it in reservation for not received a duplicate order error in next payment!
		self.save_last_order_in_reservation(hotel_code, reservation, merchant_order)

		return merchant_order


	def generate_signature_and_json_SHA256(self, hotel_code, gateway_configuration, reservation, amount):

		direct_payment = True
		sermepa_token = self.reservation_has_token(reservation)
		sermepa_order = self.get_next_sermepa_order(hotel_code, reservation)

		cof = ""
		if gateway_configuration.get("psd2_cof_type"):
			cof = self.get_sermepa_cof(reservation)

		Ds_Merchant_MerchantSignature, Ds_Merchant_JsonDatasSHA256 = generate_signature_and_json(hotel_code, gateway_configuration, sermepa_token, sermepa_order, amount, direct_payment, cof)

		return Ds_Merchant_MerchantSignature, Ds_Merchant_JsonDatasSHA256, sermepa_order


	def save_last_order_in_reservation(self, hotel_code, reservation, merchant_order):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				log.error("Extra Info bad formed for: %s. IMPOSIBLE TO SAVE amount in extra Info!", reservation.get("identifier"))

				return ""

			reservation_id = int(reservation.key.id)
			extra_info["last_merchant_order_used"] = merchant_order
			reservation["extraInfo"] = json.dumps(extra_info)

			reservation_updated_id = datastore_communicator.save_to_datastore("Reservation", reservation_id, reservation, hotel_code=hotel_code)
			log.info("New payed updated for reservation %s reservation_id: %s ",  reservation.get("identifier"), reservation_id)

			return reservation_updated_id


	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):

		log.info("SERMEPA. execute_refund_in_gateway. reservation: %s amount: %s hotel: %s", reservation.get("identifier"), amount, hotel_code)
		#take SERMEPA configuration
		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		if gateway_configuration:
			log.info("integration_configuration found!")

			payment_url = gateway_configuration.get("payment_url_rest")
			if payment_url:

				Ds_Merchant_MerchantSignature, Ds_Merchant_JsonDatasSHA256, order_id= self.generate_refund_signature_and_json_SHA256(hotel_code, gateway_configuration, reservation, amount, order_to_refund)

				payload = {
					"Ds_SignatureVersion":"HMAC_SHA256_V1",
					"Ds_MerchantParameters":Ds_Merchant_JsonDatasSHA256,
					"Ds_Signature":Ds_Merchant_MerchantSignature,
					"Ds_Merchant_DirectPayment":"true"
				}

				#"Ds_Merchant_DirectPayment":"true"

				#POST to sermepa!
				try:
					logging.info("[%s] sending payload to gateway: %s", reservation.get("identifier"), payload)

					response = requests.post(payment_url, data=payload)

					result = response.json()
				except Exception as e:
					log.error("SERMEPA didn't response. Exception: %s", str(e))
					return GATEWAY_ERROR_RETURNED + ": Redsys inaccesible. Intentar de nuevo"

				log.info("result returned by SERMEPA: %s", result)

				errorCode =  result.get("errorCode", "")
				ds_response = ""

				if result.get('Ds_MerchantParameters'):

					decodec_parameters =base64.urlsafe_b64decode(str(result.get('Ds_MerchantParameters')))
					parameters_dict = json.loads(decodec_parameters)
					log.info("DS_MERCHANTPARAMETERS decodec : %s ", parameters_dict)
					#sermepa is not sure how they send us the parameters
					ds_response = parameters_dict.get('Ds_Response',parameters_dict.get('DS_RESPONSE',''))

				if errorCode or ds_response != "0900":
					log.warning("Error received form sermepa errorCode: %s Ds_Response: %s", errorCode, ds_response)
					return GATEWAY_ERROR_RETURNED + ": " + errorCode + ", Ds_response: "+ ds_response + SEPARATOR_INFO_ERROR_ORDER + order_id

				return order_to_refund
		log.error("integration_configuration NOt found!")
		return GATEWAY_ERROR_RETURNED


	def generate_refund_signature_and_json_SHA256(self, hotel_code, gateway_configuration, reservation, amount, order_to_refund):

		#decode  Base64 key
		key = base64.b64decode(gateway_configuration["secret_key_sha256"])

		sermepa_amount = gateways_format_price(amount)

		sermepa_token = self.reservation_has_token(reservation)
		sermepa_order = self.get_next_sermepa_order(hotel_code, reservation)

		if sermepa_token and  order_to_refund:
			merchant_datas = {
			  "DS_MERCHANT_AMOUNT": sermepa_amount,
			  "DS_MERCHANT_CURRENCY": gateway_configuration["currency_code"],
			  "DS_MERCHANT_MERCHANTCODE": gateway_configuration["merchant_code"],
			  "DS_MERCHANT_ORDER": order_to_refund,
			  "DS_MERCHANT_TERMINAL": gateway_configuration["terminal"],
			  "DS_MERCHANT_TRANSACTIONTYPE": "3", #this is always the same for refunds,
			  "DS_MERCHANT_DIRECTPAYMENT": "true"
			}

			logging.info("[SERMEPA][%s] Built params for refund: %s", order_to_refund, merchant_datas)


			json_merchant_datas = json.dumps(merchant_datas)
			json_merchant_datas = json_merchant_datas.encode("utf-8")

			Ds_Merchant_JsonDatasSHA256 = base64.b64encode(json_merchant_datas)

			# encrypt key + Order
			#encryptedKey = encryptMessageDES3(sermepaDict["Ds_Merchant_Order"], key)
			encryptedKey = encrypt_message_DES3(merchant_datas["DS_MERCHANT_ORDER"], key)

			#MAC256 of Ds_MerchantParameters with the new encrypted key
			encryptedParameters = hmac.new(encryptedKey, Ds_Merchant_JsonDatasSHA256, digestmod=hashlib.sha256).digest()

			Ds_Merchant_MerchantSignature = base64.b64encode(encryptedParameters)

			return Ds_Merchant_MerchantSignature, Ds_Merchant_JsonDatasSHA256, sermepa_order
		log.error("Imposible to generate_refund_signature_and_json_SHA256. No order_to_refund or ds_MerchantIdentifier found")
		return False, False, ""

	def translate_error(self, error):
		error_result = error
		if not "ERROR:" in error_result:
			try:
				error_result = self.get_error_description(error_result)
			except:
				log.info("No se ha podido traducir el error")
				return "ERROR al ejecutar el pago"
		else:
			info_error = error.split(SEPARATOR_INFO_ERROR_ORDER)
			if len(info_error) == 2:
				error_code = info_error[0].split("ERROR: ")
				if len(error_code) == 2:
					error_code = error_code[1].strip()
					error_result = self.get_error_description(error_code)
			if len(info_error) == 1:
				if "Ds_response: " in info_error[0]:
					error_code = info_error[0].split("Ds_response: ")[-1].strip()
					error_result = self.get_error_description(error_code)

		return error_result

	def get_error_description(self, error_code):
		with open('paraty/utilities/payments_error_codes/sermepa_error_codes.json') as file:
			error_file = json.load(file)
			if error_file.get(error_code):
				error_result = "ERROR: " + error_file[error_code]
			else:
				error_result = "ERROR: " + error_code
		return error_result
class SermepaFormControler(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):

		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

		# url_ko = gateway_configuration.get('url_ko', '')
		# if extra_data.get("from_tpv_link") and 'booking3_tpv' not in url_ko:
		# 	gateway_configuration['url_ko'] = url_ko.replace('booking3', 'booking3_tpv')

		payment_order_id = fix_payment_order_id(payment_order_id)

		translations = language_utils.get_web_dictionary(extra_data.get("language", "SPANISH"))
		hotel = get_hotel_by_application_id(hotel_code)
		section_name = get_configuration_property_value(hotel_code, "b3 custom text from pep by keys")
		custom_text_by_key = {}
		custom_amounts_by_key = {}
		try:
			if section_name:
				logging.info("[SERMEPA] assembling the texts for the booking3 from section %s", section_name)
				translations_section = get_web_section(hotel, section_name, extra_data.get("language", "SPANISH"), set_languages=True)
				reservation_amount = amount
				amount_to_pay = str(get_amount_to_pay(amount, prices_per_day=extra_data.get("prices_per_day")))
				custom_text_by_key, custom_amounts_by_key = build_custom_text_b3_by_key(translations, translations_section, extra_data, reservation_amount, amount_to_pay,hotel)
		except Exception as e:
			logging.warning(e)
			logging.info("[SERMEPA] Error building custom texts by key")


		if gateway_configuration.get("gateway_type", "").lower() == "insite":
			gateway_configuration['sid'] = sid
			return self.build_form_insight(gateway_configuration, payment_order_id)
		else:
			#by default redirection form

			#TODO: cof not neccessary here??
			cof = ""
			direct_payment = False


			if r.values.get("bizum") or extra_data.get("bizum"):
				#dirty trick to not pass more params to generate_signature_and_json method
				gateway_configuration["bizum"] = True
			# TODO amazon
			amazon = r.values.get("amazon")

			#only if is a tokenizator by sermepa
			sermepa_token = ""
			if "TOKEN" in gateway_type or (get_configuration_property_value(hotel_code, CREDIT_CARD_BY_TOKEN_CONFIG) and not gateway_configuration.get("Force token disabled")):
				sermepa_token = SERMEPA_MERCHANT_IDENTIFIER


			language = extra_data.get("language", "SPANISH")
			Ds_Merchant_MerchantSignature, Ds_Merchant_JsonDatasSHA256 = generate_signature_and_json(hotel_code, gateway_configuration, sermepa_token, payment_order_id, amount, direct_payment, cof, sid, language=language)

			post_url = gateway_configuration.get("payment_url", "")

			params = {	"post_url": post_url,
				  		"Ds_Merchant_Version_SHA256": gateway_configuration.get('version_SHA256', 'HMAC_SHA256_V1'),
						"Ds_Merchant_JsonDatasSHA256": Ds_Merchant_JsonDatasSHA256.decode("utf-8"),
						"Ds_Merchant_MerchantSignature": Ds_Merchant_MerchantSignature.decode("utf-8")
				 	}



			sermepa_template = "pages/cobrador/gateways/sermepa_redirection_form.html"


		if add_button:
			params["add_button"] = True
		if custom_text_by_key:
			params["custom_text_by_key"] = custom_text_by_key
		if custom_amounts_by_key:
			params["custom_amounts_by_key"] = custom_amounts_by_key
		form_gateway = build_template(sermepa_template, params, False)

		return form_gateway

	def build_form_insight(self, gateway_configuration, payment_order_id):
		if gateway_configuration.get("insite_enviroment", "").lower() == "test":
			sermepa_js_path = "https://sis-t.redsys.es:25443/sis/NC/sandbox/redsysV2.js"
		else:
			sermepa_js_path = "https://sis.redsys.es/sis/NC/redsysV2.js"

		params = {
			"sermepa_js_path": sermepa_js_path,
			"merchant_code": gateway_configuration.get("merchant_code"),
			"merchant_terminal": gateway_configuration.get("terminal"),
			"merchant_order_id": payment_order_id,
			"merchant_url": gateway_configuration.get("merchant_url"),
			"base_url": r.host_url
		}

		if Config.DEV:
			params['merchant_url'] = "http://localhost:8090/sermepa/merchant_url"

		else:
			params['base_url'] = params['base_url'].replace("http", "https")

		params['merchant_url'] += "?sid=%s" % (gateway_configuration['sid'])

		log.info("INSITE params form: %s", json.dumps(params))

		sermepa_template = "pages/cobrador/gateways/sermepa_insite_form.html"

		form_gateway = build_template(sermepa_template, params, False)
		return form_gateway

	def process_gateway_response(self, hotel_code, gateway_type, original_response):
		if not original_response:
			log.warning(f"The gateway return a Error because not response has arrived. hotel_code: {hotel_code}.")
			return GATEWAY_ERROR_DICT

		response = ''
		if isinstance(original_response, str):
			parsed_url = urlparse(original_response)
			response = dict(parse_qsl(parsed_url.path))

		if not response:
			response = original_response

		if isinstance(response, dict):
			log.info("Is Insite?: %s", response.get("DS_MERCHANT_IDOPER", response.get("Ds_merchant_idoper")))
			if response.get("DS_MERCHANT_IDOPER"):
				return SermepaFormControler.process_gateway_insight_response(response, gateway_type, hotel_code)

		if isSHA(response):
			return CoreSHA256Sermepa(response, hotel_code, gateway_type)
		else:
			return CoreSoapSermepa(original_response, hotel_code, gateway_type)

	def process_gateway_insight_response(self, response, gateway_type, hotel_code):
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		if response['DS_MERCHANT_IDOPER'] == "ERROR":
			log.warning("Error processing insite payment")
			log.info(json.dumps(response))
			result = {
				"CODE": GATEWAY_ERROR_CODE_RETURNED,
				"GATEWAY_EXTRA_INFO": {}
			}

			return_url = gateway_configuration.get("url_ko")

			if Config.DEV:
				return_url = "http://localhost:8090/booking3"

			if "?" in return_url:
				return_url += "&sid=%s" % r.values.get("sid")

			else:
				return_url += "?sid=%s" % r.values.get("sid")

			result['GATEWAY_EXTRA_INFO']['return_url'] = return_url

			try:
				response_text = json.dumps(response)
				audit_response(hotel_code, gateway_type, response.get("DS_MERCHANT_ORDER", ""), r.values.get("sid"), response_text)

			except Exception as e:
				log.error("Error auditing response for in SERMEPA INSITE")
				log.error("Error auditing: %s", e)


			return result

		merchant_paramenters_no_encrypted = {
			"DS_MERCHANT_AMOUNT": gateways_format_price(response.get("DS_MERCHANT_AMOUNT")),
			"DS_MERCHANT_CURRENCY": gateway_configuration.get("currency_code"),
			"DS_MERCHANT_MERCHANTCODE": gateway_configuration.get("merchant_code"),
			"DS_MERCHANT_ORDER": response.get("DS_MERCHANT_ORDER"),
			"DS_MERCHANT_TERMINAL": gateway_configuration.get("terminal"),
			"DS_MERCHANT_TRANSACTIONTYPE": gateway_configuration.get("transaction_type"),
			"DS_MERCHANT_IDOPER": response.get("DS_MERCHANT_IDOPER"),
			"DS_MERCHANT_IDENTIFIER": "REQUIRED",
			"DS_MERCHANT_DIRECTPAYMENT": True
		}

		log.info("Paramos to insite: %s", merchant_paramenters_no_encrypted)
		Ds_Merchant_JsonDatasSHA256, Ds_Merchant_MerchantSignature = create_json_and_signature(merchant_paramenters_no_encrypted, gateway_configuration["secret_key_sha256"])

		payload = {
			"Ds_MerchantParameters": Ds_Merchant_JsonDatasSHA256,
			"Ds_SignatureVersion": gateway_configuration.get("version_SHA256"),
			"Ds_Signature": Ds_Merchant_MerchantSignature,
		}

		logging.info("[SERMEPA] Insite. HARCODED TEST REST!!!")
		response_sermepa = requests.post("https://sis-t.redsys.es:25443/sis/rest/trataPeticionREST", data=payload)
		response_sermepa = json.loads(response_sermepa.text)
		log.info(response_sermepa)
		result = {
			"CODE": GATEWAY_SUCESS_RETURNED,
			"GATEWAY_ORDER_ID": response.get("DS_MERCHANT_ORDER"),
			"GATEWAY_PAID_AMOUNT": response.get("DS_MERCHANT_AMOUNT"),
			"GATEWAY_EXTRA_INFO": {
				"DS_MERCHANT_IDOPER": response.get("DS_MERCHANT_IDOPER")
			}
		}

		return_url = gateway_configuration.get("url_ok")
		if response_sermepa.get("errorCode"):
			return_url = gateway_configuration.get("url_ko")
			result['CODE'] = GATEWAY_ERROR_CODE_RETURNED

		else:
			response_sermepa_data = json.loads(base64.urlsafe_b64decode(str(response_sermepa.get("Ds_MerchantParameters"))))
			signature = generate_signature_notificationSHA256(response_sermepa.get("Ds_MerchantParameters", response_sermepa.get("DS_MERCHANTPARAMETERS")), gateway_type, hotel_code)
			token_transaction = response_sermepa_data.get("Ds_Merchant_Identifier", response_sermepa_data.get("DS_MERCHANT_IDENTIFIER"))
			if token_transaction:
				result['GATEWAY_TOKEN_ID'] = token_transaction

			if signature != response_sermepa.get("Ds_Signature"):
				result['CODE'] = GATEWAY_ERROR_CODE_RETURNED

		if Config.DEV:
			return_url = "http://localhost:8090/booking3"

		if "?" in return_url:
			return_url += "&sid=%s" % r.values.get("sid")

		else:
			return_url += "?sid=%s" % r.values.get("sid")

		result['GATEWAY_EXTRA_INFO']['return_url'] = return_url
		log.info("Response insite: %s", result)


		try:
			response_text = json.dumps(response)
			audit_response(hotel_code, gateway_type, response.get("DS_MERCHANT_ORDER", ""), r.values.get("sid"), response_text)

		except Exception as e:
			log.error("Error auditing response for in SERMEPA")
			log.error("Error auditing: %s", e)


		return result

	def get_fast_response(self, hotel_code, gateway_type, response):

		is_SHA = False
		# response = response.decode()

		if isinstance(response, str):
			parsed_url = urlparse(response)
			formated_response = dict(parse_qsl(parsed_url.path))
			is_SHA = formated_response.get("Ds_Signature", formated_response.get("DS_SIGNATURE"))

		if isinstance(response, dict):
			is_SHA = response.get("Ds_Signature", response.get("DS_SIGNATURE"))

		if is_SHA:
			return get_sha_response(), CONTENT_TYPE_TEXT
		else:
			return get_soap_response(response, gateway_type, hotel_code), CONTENT_TYPE_XML


def generate_signature_notificationSHA256(parameters, gateway_type, hotel_code):
	log.info("generating signature from DS_MERCHANTPARAMETERS: %s. hotel_code: %s", parameters, hotel_code)

	# get from configuration the secret key sha256

	gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
	if gateway_configuration:
		log.info("integration_configuration %s found! hotel_code: %s", gateway_type, hotel_code)

		secret_key_sha256 = gateway_configuration.get("secret_key_sha256")

		# decode  Base64 key
		key = base64.b64decode(secret_key_sha256)

		parametersDict = extract_parameters_from_ds_merchant(parameters)
		log.info("parameters decoded from datosSHA256 : %s. hotel_code: %s", parameters, hotel_code)
		orderReceived = parametersDict.get('Ds_Order',parametersDict.get('DS_ORDER',''))
		if len(orderReceived) < 8 or len(orderReceived) > 10:
			return "ERROR_SIGNATURE"

		# encrypt key + Order
		key = encrypt_message_DES3(orderReceived, key)
		log.info("Creating signature notification from order received: %s. hotel_code: %s ", orderReceived, hotel_code)

		encryptedParameters = hmac.new(key, parameters.encode("utf-8"), digestmod=hashlib.sha256).digest()

		return base64.urlsafe_b64encode(encryptedParameters).decode("utf-8")

	return "ERROR_SIGNATURE"


def extract_parameters_from_ds_merchant(parameters):
	# Se decodifican los datos Base64 (be careful they come from an url)
	decodecParameters = base64.urlsafe_b64decode(str(parameters))
	# load the parameters data received
	return json.loads(decodecParameters)


def generateSignatureNotificationSoapVersionSHA256(message, order_received, hotel_code, typePaymentGateway):
	logging.info("generating signature from body: %s ", message)

	# get from configuration the secret key sha256
	secret_key_sha256 = ""

	# SERMEPA CONFIG MUST BE ALLWAYS THE FIRST. be carefull with Paypal!

	logging.info("Advanced configuration about GateWay: %s ", typePaymentGateway)

	config = get_payment_gateway_configuration(typePaymentGateway, hotel_code)

	logging.info("XML configuration about GateWay: %s ", config)

	secret_key_sha256 = config.get("secret_key_sha256")

	# decode  Base64 key
	key = base64.b64decode(secret_key_sha256)

	# encrypt key + Order
	key = encryptMessageDES3(order_received, key)
	logging.info("Creating signature notification from order received: %s ", order_received)
	logging.info("Forcing order received in future reservation.identifier: %s ", order_received)
	encryptedParameters = hmac.new(key, message.strip().encode(), hashlib.sha256).digest()

	return base64.b64encode(encryptedParameters)


def encryptMessageDES3(message, password):
	if not Config.DEV:
		iv = b"\0\0\0\0\0\0\0\0"
		des3 = DES3.new(password, DES3.MODE_CBC, iv)
		message = des3.encrypt(message.encode())
	return message


def CoreSHA256Sermepa(response, hotel_code, gateway_type):

	ds_ErrorCode = response.get("Ds_ErrorCode", response.get("DS_ERRORCODE", ""))
	if ds_ErrorCode:
		# we make sure that we are not to say NEVER in email that this user has pay some money
		log.warning(f"The gateway return a Error. hotel_code: {hotel_code}. DS ERROR RECEIVED: {ds_ErrorCode}")
		return GATEWAY_ERROR_DICT

	signatureSHA256 = response.get("Ds_Signature", response.get("DS_SIGNATURE"))
	datosSHA256 = response.get("Ds_MerchantParameters", response.get("DS_MERCHANTPARAMETERS"))
	sucess_response = {}
	sermepa_token_id = ""
	# Se decodifican los datos Base64 (be careful they come from an url)
	decodec_parameters = base64.urlsafe_b64decode(str(datosSHA256))
	# load the parameters data received
	parametersDict = json.loads(decodec_parameters)

	ds_Payment_Order = parametersDict.get("Ds_Order", parametersDict.get("DS_ORDER", ""))
	sid = r.args.get("sid")
	if not sid:
		logging.warning("[%s] Maybe it's a payment alreay done. hotel_code: %s", ds_Payment_Order, hotel_code)
		filter_params = [('order', '=', ds_Payment_Order)]
		payment_reservation = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=True)

		if payment_reservation and is_real_payment(payment_reservation[0]):
			sucess_response['PAYMENT_ALREADY_DONE'] = True
	try:
		response_text = decodec_parameters
		audit_response(hotel_code, gateway_type, parametersDict.get("Ds_Order", parametersDict.get("DS_ORDER", "")),
		               sid, response_text)

	except Exception as e:
		log.warning("Error auditing response for in SERMEPA JSON")
		log.warning(f"Error auditing: {e}")

	ds_ExpiryDate = parametersDict.get("Ds_ExpiryDate", parametersDict.get("DS_EXPIRE_DATE", ""))
	ds_MerchantIdentifier = parametersDict.get("Ds_Merchant_Identifier",
	                                           parametersDict.get("DS_MERCHANT_IDENTIFIER", ""))

	if ds_MerchantIdentifier:
		sermepa_token_id = ds_MerchantIdentifier
		if len(ds_ExpiryDate) >= 3:
			ds_ExpiryDate = ds_ExpiryDate[2:] + "/" + ds_ExpiryDate[0:2]

		log.info(f"receiving correctly SERMEPA TOKEN. hotel_code: {hotel_code}. ds_MerchantIdentifier: {ds_MerchantIdentifier} - ds_ExpiryDate: {ds_ExpiryDate}")

	cof_txnid = parametersDict.get("Ds_Merchant_Cof_Txnid", parametersDict.get("DS_MERCHANT_COF_TXNID", ""))
	if cof_txnid:
		sucess_response[SERMEPA_COF_TXNID] = cof_txnid

	firma = generate_signature_notificationSHA256(datosSHA256, gateway_type, hotel_code)

	log.info(f"hotel_code: {hotel_code}. Comparing signature generated {firma} with signature received {signatureSHA256}")
	if not firma == signatureSHA256:
		log.warning(f"RETURNING ERROR because signtaure received ere not equals. hotel_code: {hotel_code}")
		# we make sure that we are not to say NEVER in email that this user has pay some money
		return GATEWAY_ERROR_DICT

	client_payment_amount = str(parametersDict.get("Ds_Amount", "0"))
	if len(client_payment_amount) > 2:
		client_payment_amount = client_payment_amount[:-2] + "." + client_payment_amount[-2:]
	else:
		client_payment_amount = "0." + client_payment_amount
	log.info(f"Amount given to gateway: {client_payment_amount}. hotel_code: {hotel_code}")

	log.info(f"ds_Payment_Order received from gateway: {ds_Payment_Order}. hotel_code: {hotel_code}")

	ds_response = parametersDict.get('Ds_Response', parametersDict.get('DS_RESPONSE', ''))
	log.info(f"ds_response received from gateway: {ds_response}. hotel_code: {hotel_code}")

	# We check that the response is correct (OLD SERMEPA VERSION AND NEW!)
	if (not ds_response) or int(ds_response) > 99:
		log.warning(
			f"Returning error because gateway response with this error: DS_RESPONSE: {ds_response} (if empty we consider that an error). hotel_code: {hotel_code}")
		return GATEWAY_ERROR_DICT
	if sermepa_token_id:
		sucess_response['GATEWAY_TOKEN_ID'] = sermepa_token_id

	sucess_response["GATEWAY_EXTRA_INFO"] = {
		"ds_MerchantIdentifier": ds_MerchantIdentifier,
		"no_redirect": True
	}

	if cof_txnid:
		sucess_response["GATEWAY_EXTRA_INFO"]['sermepa_cof_txnid'] = cof_txnid
	if ds_ExpiryDate:
		sucess_response["GATEWAY_EXTRA_INFO"]['ds_ExpiryDate'] = sermepa_token_id

	sucess_response['CODE'] = GATEWAY_SUCESS_RETURNED
	sucess_response['GATEWAY_ORDER_ID'] = ds_Payment_Order
	sucess_response['GATEWAY_PAID_AMOUNT'] = client_payment_amount
	sucess_response['PAYMENT_GATEWAY_NAME'] = "REDSYS"

	sucess_response['GATEWAY_EXTRA_INFO']['redsys_info'] = get_redsys_info(parametersDict, ds_MerchantIdentifier, client_payment_amount)

	add_expiration_date_to_response(sucess_response, parametersDict)

	return sucess_response


def CoreSoapSermepa(original_response, hotel_code, gateway_type):
	sucess_response = {}
	parametersDict = {}
	logging.info(f"Body received from SERMEPA SOAP: {original_response}")
	tree = xee.fromstring(original_response)

	request_data = tree.findall(".//XML")
	if not request_data or len(request_data) == 0:
		return GATEWAY_ERROR_DICT
	message = request_data[0].text
	response_data = xee.fromstring(message)

	request_signature = response_data.findall(".//Signature")
	if not request_signature or len(request_signature) == 0:
		return GATEWAY_ERROR_DICT
	request_signature = request_signature[0]
	signature = request_signature.text

	request_json = response_data.findall(".//Request")
	if not request_json or len(request_json) == 0:
		return GATEWAY_ERROR_DICT
	request_json = request_json[0]
	logging.info("SERMEPA Request SOAP: %s", request_json.attrib)

	ds_Order = request_json.findall('Ds_Order')
	if len(ds_Order) > 0:
		ds_Order = ds_Order[0]
		ds_Order = ds_Order.text

	try:
		audit_response(hotel_code, gateway_type, ds_Order, "", original_response)

	except Exception as e:
		log.warning("Error auditing response for in SERMEPA SOAP")
		log.warning(f"Error auditing: {e}")

	message_aux = ""
	signature_coded = ""
	message_partial = re.search("<Message>(.+?)<Signature>", message)
	if message_partial:
		message_aux = message_partial.group(1)
		signature_coded = generateSignatureNotificationSoapVersionSHA256(message_aux, ds_Order, hotel_code, gateway_type)
		sucess_response['GATEWAY_ORDER_ID'] = ds_Order

	logging.info(f"Message to coded: <{message_aux}>")

	logging.info(f"Comparing signature generated {signature} with signature received {signature_coded}")
	if signature != signature_coded.decode():
		sucess_response['CODE'] = GATEWAY_ERROR_CODE_RETURNED
		sucess_response['GATEWAY_ORDER_ID'] = ds_Order
		logging.error("Oh oh, it is really extrange, because it appears like it is paid, but signatures are not the same!")
		return GATEWAY_ERROR_DICT
	ds_ErrorCode = request_json.findall('Ds_ErrorCode')
	if len(ds_ErrorCode) > 0:
		ds_ErrorCode = ds_ErrorCode[0]
		ds_ErrorCode = ds_ErrorCode.text

		sucess_response[GATEWAY_PAID_AMOUNT] = ""
		sucess_response['CODE'] = GATEWAY_ERROR_CODE_RETURNED
		sucess_response['GATEWAY_ORDER_ID'] = ds_Order
		logging.warning(f"Not making Reservation as the gateway didn't finish correctly. DS ERROR RECEIVED: {ds_ErrorCode}")
		return GATEWAY_ERROR_DICT
	ds_ExpiryDate = request_json.findall('Ds_ExpiryDate')
	if len(ds_ExpiryDate) > 0:
		ds_ExpiryDate = ds_ExpiryDate[0]
		ds_ExpiryDate = ds_ExpiryDate.text

	ds_MerchantIdentifier = request_json.findall('Ds_MerchantCode')
	if len(ds_MerchantIdentifier) > 0:
		ds_MerchantIdentifier = ds_MerchantIdentifier[0]
		ds_MerchantIdentifier = request_json.find('Ds_MerchantCode').text

		if len(ds_ExpiryDate) >= 3:
			ds_ExpiryDate = ds_ExpiryDate[2:] + "/" + ds_ExpiryDate[0:2]

		logging.info(f"Receiving correctly SERMEPA TOKEN. ds_MerchantIdentifier: {ds_MerchantIdentifier} - ds_ExpiryDate: {ds_ExpiryDate}")

		sucess_response[GATEWAY_PAID_AMOUNT] = "0"

	ds_Card_Brand = request_json.findall('Ds_Card_Brand')
	if len(ds_Card_Brand) > 0:
		ds_Card_Brand = ds_Card_Brand[0]
		parametersDict['Ds_Card_Brand'] = ds_Card_Brand.text

	ds_Card_Type = request_json.findall('DS_Card_Type')
	if len(ds_Card_Type) > 0:
		ds_Card_Type = ds_Card_Type[0]
		parametersDict['DS_Card_Type'] = ds_Card_Type.text

	ds_Card_Country = request_json.findall('Ds_Card_Country')
	if len(ds_Card_Country) > 0:
		ds_Card_Country = ds_Card_Country[0]
		parametersDict['Ds_Card_Country'] = ds_Card_Country.text
	client_payment_amount = "0"

	ds_Amount = request_json.findall('Ds_Amount')
	if len(ds_Amount) > 0:
		ds_Amount = ds_Amount[0]
		ds_Amount = ds_Amount.text
		client_payment_amount = str(ds_Amount)
		if len(client_payment_amount) > 2:
			client_payment_amount = client_payment_amount[:-2] + "." + client_payment_amount[-2:]
		else:
			client_payment_amount = "0." + client_payment_amount

	ds_Response = request_json.findall('Ds_Response')
	if len(ds_Response) > 0:
		ds_Response = ds_Response[0]
		response = ds_Response.text

	ds_MerchantData = request_json.findall('Ds_MerchantData')
	if len(ds_MerchantData) > 0:
		ds_MerchantData = ds_MerchantData[0]
		parametersDict['Ds_MerchantData'] = ds_MerchantData.text

	sucess_response[GATEWAY_PAID_AMOUNT] = client_payment_amount

	if (not response) or int(response) > 99:
		# we make sure that we are not to say NEVER in email that this user has pay some money
		client_payment_amount = ''

		sucess_response[GATEWAY_PAID_AMOUNT] = client_payment_amount
		sucess_response['CODE'] = GATEWAY_ERROR_CODE_RETURNED
		sucess_response['GATEWAY_ORDER_ID'] = ds_Order

		logging.warning(
			f"Not making reservation as the gateway didn't finish correctly: DS_RESPONSE: {response} (if empty we consider that an error)")
		return GATEWAY_ERROR_DICT
	ds_Payment_Order = request_json.findall('Ds_Order')

	sucess_response["GATEWAY_EXTRA_INFO"] = {
		"ds_MerchantIdentifier": ds_MerchantIdentifier,
		"no_redirect": True
	}

	if ds_ExpiryDate:
		sucess_response["GATEWAY_EXTRA_INFO"]['ds_ExpiryDate'] = ds_MerchantIdentifier

	sucess_response['CODE'] = GATEWAY_SUCESS_RETURNED
	sucess_response['GATEWAY_ORDER_ID'] = ds_Order
	sucess_response['GATEWAY_PAID_AMOUNT'] = client_payment_amount
	sucess_response['PAYMENT_GATEWAY_NAME'] = "REDSYS"

	sucess_response['GATEWAY_EXTRA_INFO']['redsys_info'] = get_redsys_info(parametersDict, ds_MerchantIdentifier, client_payment_amount)

	add_expiration_date_to_soap_response(sucess_response, request_json)

	return sucess_response


def isSHA(response):
	if isinstance(response, dict):
		signatureSHA256 = response.get("Ds_Signature", response.get("DS_SIGNATURE"))
		versionSHA256 = response.get("Ds_SignatureVersion", response.get("DS_SIGNATUREVERSION"))
		datosSHA256 = response.get("Ds_MerchantParameters", response.get("DS_MERCHANTPARAMETERS"))
		return versionSHA256 and datosSHA256 and signatureSHA256
	return False


def get_sha_response():
	return ""

def get_soap_response(response, gateway_type, hotel_code):
	tree = xee.fromstring(response)
	request_data = tree.findall(".//XML")
	message = request_data[0].text
	response_data = xee.fromstring(message)
	request_json = response_data.findall(".//Request")
	request_json = request_json[0]
	ds_Order = request_json.findall('Ds_Order')
	if len(ds_Order) > 0:
		ds_Order = ds_Order[0]
		ds_Order = ds_Order.text

	message = '<Response Ds_Version="0.0"><Ds_Response_Merchant>OK</Ds_Response_Merchant></Response>'
	signature_response = generateSignatureNotificationSoapVersionSHA256(message, ds_Order, hotel_code, gateway_type)

	if isinstance(signature_response, bytes):
		signature_response = signature_response.decode()
		
	message_response = '<Message>%s<Signature>%s</Signature></Message>' % (message, signature_response)

	soap = '<?xml version="1.0" encoding="UTF-8"?>' \
			'<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" ' \
			'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ' \
			'xmlns:xsd="http://www.w3.org/2001/XMLSchema">' \
			'<SOAP-ENV:Body>' \
			'<ns1:procesaNotificacionSIS xmlns:ns1="InotificacionSIS" ' \
			'SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">' \
			'<return xsi:type="xsd:string"><![CDATA[%s]]></return>' \
			'</ns1:procesaNotificacionSIS>' \
			'</SOAP-ENV:Body>' \
			'</SOAP-ENV:Envelope>' % message_response

	logging.info("Sending response to Sermepa... %s" % soap)

	return soap


def get_redsys_info(parametersDict, ds_MerchantIdentifier, client_payment_amount):
		auth_code = parametersDict.get("Ds_AuthorisationCode", parametersDict.get("DS_AUTHORISATIONCODE", ))
		date_op = parametersDict.get("Ds_Date", parametersDict.get("DS_DATE", "")).replace('%2F', '/')
		hour_op = parametersDict.get("Ds_Hour", parametersDict.get("DS_HOUR", "")).replace('%3A', ':')
		timestamp_op = f"{date_op} {hour_op}"
		card_brand = parametersDict.get("Ds_Card_Brand","")

		return {
			'merchant_fuc': ds_MerchantIdentifier,
			'auth_code': auth_code,
			'timestamp_op': timestamp_op,
			'amount': client_payment_amount,
			'card_brand': card_brand
		}

def add_expiration_date_to_response(cobrador_response, parameters_dict):
	logging.info("[SERMEPA] Trying to add expiration date")
	try:
		ds_ExpiryDate = parameters_dict.get("Ds_ExpiryDate", parameters_dict.get("DS_EXPIRE_DATE", ""))
		if ds_ExpiryDate:
			# Sermepa suele devolver la fecha en formato YYMM o MMYY
			if len(ds_ExpiryDate) >= 4:
				# Asumimos formato YYMM (pero podría ser MMYY según la configuración)
				month = ds_ExpiryDate[2:4]
				year = ds_ExpiryDate[0:2]
				cobrador_response["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{month}/{year}"
				log.info("[SERMEPA] Added expiration date to response: %s", cobrador_response["GATEWAY_EXTRA_INFO"]["cc_expire_date"])
	except Exception as e:
		logging.error("[SERMEPA] Error adding expiration date to response: %s", str(e))

def add_expiration_date_to_soap_response(cobrador_response, request_json):
	logging.info("[SERMEPA] Trying to add expiration date in SOAP version")
	try:
		ds_ExpiryDate = request_json.findall('Ds_ExpiryDate')
		if len(ds_ExpiryDate) > 0:
			ds_ExpiryDate = ds_ExpiryDate[0].text
			if ds_ExpiryDate and len(ds_ExpiryDate) >= 4:
				# Asumimos formato YYMM (pero podría ser MMYY según la configuración)
				month = ds_ExpiryDate[2:4]
				year = ds_ExpiryDate[0:2]
				cobrador_response["GATEWAY_EXTRA_INFO"]["cc_expire_date"] = f"{month}/{year}"
				logging.info("[SERMEPA] Added expiration date to SOAP response: %s", cobrador_response["GATEWAY_EXTRA_INFO"]["cc_expire_date"])
	except Exception as e:
		logging.error("[SERMEPA] Error adding expiration date to response: %s", str(e))
