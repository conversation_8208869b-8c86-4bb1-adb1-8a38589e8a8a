# Sandbox: https://sandbox.affirm.com
# Live: https://api.affirm.com
# TODO NO SE ACABÓ PORQUE EL HOTEL SE FUE
import base64
import datetime
import urllib.parse

import json
from paraty import app

from flask_cors import cross_origin
import requests
import logging
from flask import request
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface, GatewayInterface

from paraty.utilities.templates.templates_processor import build_template

from paraty.pages.cobrador.cobrador_utils import get_integration_name, get_payment_gateway_configuration, \
	gateways_format_price, get_request_body, \
	get_ultimate_cobrador_path, add_sid_to_url, send_cobrador_response_to_client_server, \
	decrypt_paraty_own_response_from_api, audit_response
from paraty.pages.cobrador.cobrador_constants import COBRADOR_SERVER_PATH, GATEWAY_ERROR_RETURNED, METADATA_URL, USE_ALTERNATIVE_PAYMENT_SEEKER

from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

API_URL = "https://api.affirm.com/api/v2/checkout/direct"
AUTHORIZE_TRANSACTION_URL_SANDBOX = "https://sandbox.affirm.com/api/v2/charges"
AUTHORIZE_TRANSACTION_URL_LIVE = "https://api.affirm.com/api/v2/charges"

CAPTURE_TRANSACTION_URL_SANDBOX = "https://sandbox.affirm.com/api/v1/transactions/%s/capture"
CAPTURE_TRANSACTION_URL_LIVE = "https://api.affirm.com/api/v1/transactions/%s/capture"

REFUND_TRANSACTION_URL_SANDBOX = "https://sandbox.affirm.com/api/v1/transactions/%s/refund"
REFUND_TRANSACTION_URL_LIVE = "https://api.affirm.com/api/v1/transactions/%s/refund"

FINALIZE_CARD_URL_SANDBOX = "https://sandbox.affirm.com/api/v2/cards/%s/finalize"
FINALIZE_CARD_URL_LIVE = "https://api.affirm.com/api/v2/cards/%s/finalize"


class AffirmFormController(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		gateways_format_amount = gateways_format_price(amount)
		logging.debug(f"[AFFIRM] Checkout json: ''")

		context = {
			'checkout_json': 'checkout_json',
			'url_ko': gateway_configuration.get('url_ko'),
			'url_ok': gateway_configuration.get('url_ok'),
			'amount': gateways_format_amount,
			'currency': extra_data.get('currency', 'MEX'),
			'merchant_url': add_sid_to_url(gateway_configuration.get('merchant_url'), sid),
			'cobrador_path': get_ultimate_cobrador_path(get_hotel_by_application_id(hotel_code)),
			'public_api_key': gateway_configuration.get('public_api_key'),
			'payment_order_id': payment_order_id,
			'do_payment_by_post': get_do_payment_by_post_url(sid, hotel_code),
			'hotel_code': hotel_code,
			'vcn': gateway_configuration.get('vcn', "true")
		}
		save_info_to_reservation_metadata(hotel_code, sid, payment_order_id, amount)
		logging.debug(f"[AFFIRM] Cobrador context: {context}")
		return build_template("pages/cobrador/gateways/_affirm.html", context)

	def execute_payment_in_gateway_by_api(self, gateway_type, hotel_code, datas, res_client_server=''):
		sid = datas.get('sid', '')
		metadata = get_reservation_metadata_as_dict(sid)
		amount = metadata.get('extrainfo', {}).get('amount', 0)
		payment_order_id = metadata.get('identifier', '')
		logging.info(f"[AFFIRM] Executing payment for order {payment_order_id}")
		request = process_request()
		logging.info(f"[AFFIRM] Request body: {request}")
		extra_params = {
			'checkout_token': request.get('checkout_token'),
		}

		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		# transaction_id = do_authorization(payment_order_id, gateway_configuration)
		succeeded_payment = finalize_card(request, gateway_configuration, hotel_code, sid, payment_order_id)
		if succeeded_payment:
			res_client_server = send_cobrador_response_to_client_server(gateway_configuration, sid, amount, payment_order_id, extra_params)

		if res_client_server:
			logging.info(f'Payment OK {payment_order_id}')
			return payment_order_id

		# extra_params = {"transaction_id": transaction_id}
		#
		# if amount:
		# 	capture_response = capture_transaction(payment_order_id, gateway_configuration, amount, transaction_id=transaction_id, reservation=None)
		# 	return send_cobrador_response_to_client_server(gateway_configuration, sid, amount, payment_order_id, extra_params)
		# return send_cobrador_response_to_client_server(gateway_configuration, sid, amount, payment_order_id, extra_params)

	def process_gateway_response(self, hotel_code, gateway_type, response):
		# todo tener en cuenta cambios en payment by post
		if not isinstance(response, dict):
			response = dict(urllib.parse.parse_qsl(response))
		cobrador_parameters = response.get("cobrador_parameters")
		cobrador_signature = response.get("cobrador_signature")
		return decrypt_paraty_own_response_from_api(hotel_code, gateway_type, cobrador_parameters, cobrador_signature)


def finalize_card(request, gateway_configuration, hotel_code, sid, payment_order_id):
	response = requests.request("POST",
							  get_finalize_url(gateway_configuration, request.get('checkout_token')),
							  auth=get_auth(gateway_configuration),
							  headers=get_headers(gateway_configuration))

	try:
		gateway_type = "AFFIRM"
		audit_response(hotel_code, gateway_type, payment_order_id, sid, json.dumps(response.json()))

	except Exception as e:
		logging.error("Error auditing response for in AFFIRM Process Response")
		logging.error("Error auditing: %s", e)


	return True if response.json().get('code') == 'success' else False




class AffirmController(GatewayInterface):
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):
		logging.info(f"[AFFIRM] Executing payment for order {reservation.get('identifier')}")
		amount = int(gateways_format_price(amount))
		gateway_type = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		if gateway_configuration:

			capture_transaction(reservation.get('identifier'), gateway_configuration, amount, hotel_code, reservation=reservation, transaction_id=None)

	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		logging.info(f"[AFFIRM] Executing refund for order {order_to_refund}")
		gateway_type = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		if gateway_configuration:
			transaction_id = order_to_refund
			refund_response = do_refund(gateway_configuration, amount, transaction_id)
			logging.info(f"[AFFIRM] Refund response: {refund_response.text}")
			return order_to_refund
		return GATEWAY_ERROR_RETURNED

	def reservation_has_token(self, reservation):
		try:
			extra_info = json.loads(reservation.get('extraInfo', "{}"))
			return extra_info.get("payment_by_api_info", {}).get('transaction_id') or False
		except:
			logging.warning("[AFFIRM] Reservation extra info was not JSON")
			return False


def do_authorization(payment_order_id, gateway_configuration):
	# https://docs.affirm.com/developers/reference/authorize_transaction

	response = requests.request("POST",
							  get_authorization_url(gateway_configuration),
							  auth=get_auth(gateway_configuration),
							  json=get_authorization_payload(payment_order_id),
							  headers=get_headers(gateway_configuration))
	print(response.text)
	return response


def capture_transaction(payment_order_id, gateway_configuration, amount, hotel_code, transaction_id=None, reservation=None):
	# https://docs.affirm.com/developers/reference/capture_transaction

	if not transaction_id and reservation:
		transaction_id = AffirmController().reservation_has_token(reservation)

	if not transaction_id:
		logging.error("[AFFIRM] No transaction id found for reservation")
		return "ERROR "

	response = requests.request("POST",
							  get_capture_url(gateway_configuration, transaction_id),
							  json=get_capture_payload(payment_order_id, amount),
							  headers=get_headers(gateway_configuration))


	try:
		gateway_type = "AFFIRM"
		audit_response(hotel_code, gateway_type, payment_order_id, "FROM_COBRADOR", response.text)

	except Exception as e:
		logging.warning("Error auditing response for in AFFIRM EXECUTE")
		logging.warning("Error auditing: %s", e)

	return response


def do_refund(gateway_configuration, amount, transaction_id):
	response = requests.request("POST",
							  get_refund_url(gateway_configuration, transaction_id),
							  json=get_refund_payload(transaction_id, amount),
							  headers=get_headers(gateway_configuration))
	return response


def get_auth(gateway_configuration):
	return gateway_configuration.get('public_api_key'), gateway_configuration.get('private_api_key')


def get_authorization_url(gateway_configuration):
	return AUTHORIZE_TRANSACTION_URL_SANDBOX if gateway_configuration.get('status') == 'TEST' else AUTHORIZE_TRANSACTION_URL_LIVE


def get_capture_url(gateway_configuration, transaction_id):
	return CAPTURE_TRANSACTION_URL_SANDBOX % transaction_id if gateway_configuration.get('status') == 'TEST' else CAPTURE_TRANSACTION_URL_LIVE % transaction_id


def get_refund_url(gateway_configuration, transaction_id):
	return REFUND_TRANSACTION_URL_SANDBOX % transaction_id if gateway_configuration.get('status') == 'TEST' else REFUND_TRANSACTION_URL_LIVE % transaction_id


def get_finalize_url(gateway_configuration, transaction_id):
	return FINALIZE_CARD_URL_SANDBOX % transaction_id if gateway_configuration.get('status') == 'TEST' else FINALIZE_CARD_URL_LIVE % transaction_id


def get_authorization(gateway_configuration):
	credentials = f"{gateway_configuration.get('public_api_key')}:{gateway_configuration.get('private_api_key')}"
	encoded_credentials = base64.b64encode(credentials.encode('utf-8')).decode('utf-8')
	return f"Basic {encoded_credentials}"


def get_headers(gateway_configuration):
	authorization_header = get_authorization(gateway_configuration)
	return {
		"Accept": "*/*",
		"Content-Type": "application/json",
		"Authorization": authorization_header
	}


def get_authorization_payload(payment_order_id):
	return {
		"expand": "checkout",
		"order_id": payment_order_id,
		"reference_id": payment_order_id,
		"checkout_token": get_checkout_token()
}


def get_capture_payload(payment_order_id, amount):
	# todo amount es cantidad por 100, ex 10 = 1000 como entero
	return {
		"order_id": payment_order_id,
		"reference_id": payment_order_id,
		"amount": amount
	}


def get_refund_payload(payment_order_id, amount):
	return {
		"reference_id": payment_order_id,
		"amount": amount
	}


def get_do_payment_by_post_url(sid, hotel_code):
	hotel = get_hotel_by_application_id(hotel_code)
	alternative_seeker = get_hotel_advance_config_item(hotel, USE_ALTERNATIVE_PAYMENT_SEEKER)
	url = alternative_seeker[0].get('value') if alternative_seeker else COBRADOR_SERVER_PATH
	url += "/forms/cobrador/do_payment_by_post"
	return add_sid_to_url(url, sid)


def get_checkout_token():
	return process_request().get('checkout_token')


def save_info_to_reservation_metadata(hotel_code, sid, payment_order_id, amount):
	extra_info = {
		'amount': amount
	}
	properties = {
		"sid": sid,
		"identifier": payment_order_id,
		"hotel_code": hotel_code,
		"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
		"extraInfo": json.dumps(extra_info)
	}
	datastore_communicator.save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")


def get_reservation_metadata_as_dict(sid):
	reservation_metadata = requests.get(f"{METADATA_URL}?sid={sid}", headers={'Content-Type': 'application/json'}, timeout=5)
	reservation_metadata = json.loads(reservation_metadata.text)
	return reservation_metadata


def process_request():
	request = get_request_body().decode()
	# request = 'checkout_token=0WIV0FT145I6OSAK'
	key = request.split('=')[0]
	value = request.split('=')[1]
	return {key: value}
