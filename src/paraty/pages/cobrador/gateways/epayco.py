import json
import logging
from flask import request as r
from paraty import Config
from paraty.pages.cobrador.cobrador_constants import GATEWAY_SUCESS_RETURNED, GATEWAY_ERROR_RETURNED, \
    GATEWAY_PENDING_RETURNED, GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, audit_response, add_sid_to_url
from paraty.pages.cobrador.gateway_interface import FormGatewayInterface
from paraty.utilities.languages.language_utils import get_language_code
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore
from datetime import datetime


class EPaycoFormControler(FormGatewayInterface):

    def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):

        gateway_config = get_payment_gateway_configuration(gateway_type, hotel_code)

        context = {
            "order_id": payment_order_id,
            "amount": amount,
            "language": get_language_code(extra_data.get("language"))
        }
        context.update(gateway_config)
        context["description"] = payment_order_id
        context['rejected_url'] = context['merchant_url']
        context['confirmation_url'] = context['merchant_url']

        if Config.DEV:
            context['merchant_url'] = "http://localhost:8090/cobrador/proxy/merchant_url"
            context['rejected_url'] = "http://localhost:8090/cobrador/proxy/merchant_url"
            context['confirmation_url'] = "http://localhost:8090/cobrador/proxy/merchant_url"


        context['merchant_url'] = add_sid_to_url(context['merchant_url'], sid)
        context['rejected_url'] = add_sid_to_url(context['rejected_url'], sid)
        #context['merchant_url'] += "?sid=%s" % sid
        #context['rejected_url'] += "?sid=%s" % sid
        context['confirmation_url'] += "?sid=%s&reservation=%s" % (sid, payment_order_id)

        properties = {
            "sid": sid,
            "identifier": payment_order_id,
            "hotel_code": hotel_code,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "extraInfo": json.dumps({})
        }
        save_to_datastore("ReservationMetadata", None, properties, hotel_code="payment-seeker:")

        template_path = "pages/cobrador/gateways/epayco/_epayco_form.html"
        form_gateway = build_template(template_path, context, False)
        return form_gateway

    def process_gateway_response(self, hotel_code, gateway_type, response):
        # TODO
        if not isinstance(response, dict):
            response_txt = response
            response = {x.split("=")[0]: x.split("=")[1]
                        for x in response.split("&") if len(x.split("=")) > 1}


        result = {
            "CODE": GATEWAY_ERROR_CODE_RETURNED,
            "IDENTIFIER": r.values.get('reservation')
        }

        order_id = ""

        if response:
            result['GATEWAY_ORDER_ID'] = response.get("x_id_factura")
            order_id = response.get("x_id_factura")

            result['GATEWAY_PAID_AMOUNT'] = response.get("x_amount")

            if response.get("x_cod_transaction_state", "4") == "1":
                result['CODE'] = GATEWAY_SUCESS_RETURNED

            elif response.get("x_cod_transaction_state", "4") == "3":
                result['CODE'] = GATEWAY_PENDING_RETURNED



        try:
            sid = r.values.get('sid', '')
            audit_response(hotel_code, gateway_type, order_id, sid, json.dumps(response))

        except Exception as e:
            logging.error("Error auditing response for in EPAYCO")
            logging.error("Error auditing: %s", e)

        if order_id and not result.get("IDENTIFIER"):
            result["IDENTIFIER"] = order_id

        return result

    def get_fast_response(self, hotel_code, gateway_type, response):
        return "", CONTENT_TYPE_TEXT
