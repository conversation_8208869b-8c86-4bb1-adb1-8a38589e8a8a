from time import sleep

from paraty import app
from flask import request
from base64 import urlsafe_b64decode
from requests import request as rq
import logging
import json
from uuid import uuid4
from paraty.pages.cobrador.cobrador_constants import COBRADOR_QUEUE
from paraty_commons_3 import queue_utils


@app.route("/forms/cobrador/fallback", methods=["GET", "POST"])
def cobrador_fallback():
	task_id = str(uuid4())
	logging.info("[%s]booking process closed in booking3 with tpv, checking payment." % task_id)
	payload = {
		"data": request.args.get("data", ""),
		"task_id": task_id
	}
	queue_utils.create_task('execute_fallback_task', json.dumps(payload), queue_name=COBRADOR_QUEUE,
							task_name='fallback_task__%s' % task_id, in_seconds=90)

	return "ok", 200


@app.route("/execute_task/execute_fallback_task", methods=["GET", "POST"])
def execute_fallback_task():
	body = json.loads(request.data)
	logging.info(f"Body: {body}")
	data = body.get("data")
	task_id = body.get("task_id")
	logging.info("execute_fallback_task TASK_ID: %s", task_id)

	make_second_attempt = body.get("make_second_attempt")

	second_attempt = body.get("second_attempt")
	new_task_id = body.get("new_task_id")
	if new_task_id:
		logging.info("second_attempt found! new_task_id: %s", new_task_id)

	data_json = json.loads(urlsafe_b64decode(data))
	logging.info("Body received for execute task: %s" % data_json)
	if data_json.get("merchant_url"):
		merchant_url = data_json.get("merchant_url")
		method = "GET"
		body = None
		if data_json.get("body"):
			method = "POST"
			body = data_json.get("body")

		merchant_url += "&no_redirect=true"
		if "?" not in merchant_url:
			merchant_url = merchant_url.replace("&", "?", 1)
		data_to_send = json.dumps(body) if body else data_json
		if method == "GET":
			data_to_send = None
		logging.info(f"[{task_id}]Making request to: {merchant_url} - method: {method} - data: {data_to_send}")
		response = rq(method, merchant_url, json=data_to_send)
		logging.info(f"[{task_id}] response: {response.text}")

	if body and make_second_attempt and not (body.get("second_attempt") or second_attempt):
		body["second_attempt"] = True
		new_task_id = str(uuid4())

		body["task_id"] = task_id
		body["new_task_id"] = new_task_id
		if not body.get("data"):
			body["data"] = data

		logging.info("[%s]Doing second attempt %s" % (task_id, new_task_id))
		logging.info("[%s]Doing second attempt body: %s" % (task_id, body))
		queue_utils.create_task('execute_fallback_task', json.dumps(body), queue_name=COBRADOR_QUEUE,
								task_name='fallback_task__%s' % new_task_id, in_seconds=900)

	return "ok"