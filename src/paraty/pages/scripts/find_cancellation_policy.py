from datetime import datetime, timedelta
from openpyxl import Workbook

from paraty.pages.scripts.nrf_reservations_paid_checker import calculate_cancel_policy_for_period
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_conditions_of_hotel
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

if __name__ == "__main__":
    hotel_code = "bahia-serena"
    three_days = (datetime.now() - timedelta(days=6)).strftime("%Y-%m-%d %H:%M:%S")
    all_payments = get_using_entity_and_params('PaymentsReservation', search_params=[("timestamp", ">=", three_days)],
                                hotel_code=hotel_code)

    # obtenemos los rates del hotel
    hotel = get_hotel_by_application_id(hotel_code)
    all_rates = get_rates_of_hotel(hotel)
    all_rates = {i.get('key'): i for i in all_rates}

    condition_rates = get_conditions_of_hotel(hotel)
    condition_rates = {x.get("key"): x for x in condition_rates}

    flexibles_data = []
    semi_flexibles_data = []

    for payment in all_payments:

        if payment.get("order") == "78605708":
            pass
        # obtener la reserva
        reservation = get_using_entity_and_params('Reservation', search_params=[("identifier", "=", payment.get("reservation_identifier"))],
                                hotel_code=hotel_code)

        if reservation:
            reservation = reservation[0]
        else:
            continue

        #obtenemos el rate de la reserva:
        reservation_rate = reservation.get("rate", "")
        reservation_timpestamp = datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
        july_9 = datetime.strptime("2025-07-09 00:00:00", "%Y-%m-%d %H:%M:%S")

        rate = all_rates.get(reservation_rate)
        cancellation_policy = rate.get('cancellationPolicy')

        name_flex = False

        if "flex" in rate.get("name").lower() or "especial" in rate.get("name").lower():
            name_flex = True
            # diferenciar entre semi flexible y flexible normal
            if "semi" in rate.get("name").lower():
                # si es semi, devolvemos las que tengan fecha de esntrada posterior al 9 de julio
                if reservation_timpestamp >= july_9:
                    semi_flexibles_data.append({
                        "Identifier": reservation.get("identifier"),
                        "Fecha de Reserva": reservation.get("timestamp"),
                        "Fecha de entrada": reservation.get("startDate"),
                        "Fecha del Cobro": payment.get("timestamp"),
                        "Importe Cobrado": payment.get("amount"),
                        "Politica de Tarifa": rate.get("name")
                    })
            else:
                june_25 = datetime.strptime("2025-06-25 00:00:00", "%Y-%m-%d %H:%M:%S")

                if reservation_timpestamp >= june_25:
                    flexibles_data.append({
                        "Identifier": reservation.get("identifier"),
                        "Fecha de Reserva": reservation.get("timestamp"),
                        "Fecha de entrada": reservation.get("startDate"),
                        "Fecha del Cobro": payment.get("timestamp"),
                        "Importe Cobrado": payment.get("amount"),
                        "Politica de Tarifa": rate.get("name")
                    })
        # De aquí para abajo no aplica a las reservas afectadas, lo dejo por si en un futuro sirve, pero no se ejecuta nunca
        elif cancellation_policy == "Multiples politicas":
            for name in ["FLEX", "PVP"]:
                if name in (rate.get("localName") or "").upper():
                    name_flex = True
                    break
            if name_flex:
                flexibles_data.append({
                    "Identifier": reservation.get("identifier"),
                    "Fecha de Reserva": reservation.get("timestamp"),
                    "Fecha de entrada": reservation.get("startDate"),
                    "Fecha del Cobro": payment.get("timestamp"),
                    "Importe Cobrado": payment.get("amount"),
                    "Politica de Tarifa": rate.get("localName")
                })

        if not name_flex:
            cancel_policy = calculate_cancel_policy_for_period(reservation, hotel_code, rate, condition_rates)
            if cancel_policy == "No cancelable":
                #comprobamos si se han cobrado 16 dias antes de lo que se debía
                reservation_timpestamp = reservation.get("startDate")
                payment_timestamp = payment.get("timestamp")
                fifteen_days = (reservation_timpestamp - timedelta(days=15)).strftime("%Y-%m-%d %H:%M:%S")

                if payment_timestamp > fifteen_days:
                    flexibles_data.append({
                        "Identifier": reservation.get("identifier"),
                        "Fecha de Reserva": reservation.get("timestamp"),
                        "Fecha de entrada": reservation.get("startDate"),
                        "Fecha del Cobro": payment.get("timestamp"),
                        "Importe Cobrado": payment.get("amount"),
                        "Politica de Tarifa": cancellation_policy
                    })

    # Crear el archivo Excel
    wb = Workbook()
    ws_flex = wb.active
    ws_flex.title = "TARIFAS FLEXIBLES"
    ws_semi = wb.create_sheet(title="TARIFAS semi FLEXIBLES")

    headers = ["Identifier", "Fecha de Reserva", "Fecha de entrada", "Fecha del Cobro", "Importe Cobrado", "Politica de Tarifa"]
    ws_flex.append(headers)
    ws_semi.append(headers)

    for row in flexibles_data:
        ws_flex.append([row[h] for h in headers])
    for row in semi_flexibles_data:
        ws_semi.append([row[h] for h in headers])

    wb.save("cancellation_policies.xlsx")
    print("Archivo Excel 'cancellation_policies.xlsx' creado correctamente.")