import base64
import hashlib
import json
import xml.etree.ElementTree as xml
from datetime import datetime, timedelta
from xml.dom import minidom
import re

from google.cloud.datastore import Entity
import xml.etree.ElementTree as xee
from paraty import app
from paraty.pages.cobrador.cobrador import get_audit_response_with_retry
from paraty.pages.cobrador.cobrador_constants import PARATY_EPAYMENT_SUPERVISORS
from paraty.pages.cobrador.cobrador_gateway_form import authorization_required
from paraty.pages.cobrador.cobrador_utils import get_all_payments_info_list, get_all_payment_reservation_list, \
    get_payment_gateway_configuration, get_integration_name
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty_commons_3.common_data.data_management.reservation_utils import get_reservations_of_hotel
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_hotel_by_application_id
from flask import request
from paraty.utilities.email_utils import sendEmail_localdev
from paraty_commons_3.email_utils import sendEmail

# estival es el unico que tiene soap, hay que controlar si es
LEGACY_TIMESTAMP_BY_GATEWAY = {
    "REDSYS": "2025-05-15",
    "SERMEPA": "2025-05-15",
    "WORLDLINE_EU2" : "2025-05-15",
    "STRIPE": "2025-05-15",
    "ADDONS": "2022-05-15",
    'ADDON_PAYMENTS': "2022-05-15",
    "SANTANDER": "2025-05-15",
    "BANORTE": "2025-05-15",
    "PAYPAL_V2": "2025-04-05",
    "PAYCOMET": "2025-04-15",
    "AZUL" : "2024-09-19",
    "CECA": "2025-05-15",
    "EPAYCO": "2025-05-15",
    "EVO_SESSIONS": "2025-05-15",
    "EVO": "2025-05-15",
    "EVOSESSIONS": "2025-05-15",
    "PAYBYRD": "2024-12-10",
    "PAYLANDS": "2025-06-30",
    "PAYPAL_V1": "2025-04-02",
    "PAYU": "2025-05-15",
    "PLACETOPAY": "2025-05-15",
    "REDUNICRE": "2025-05-15",
    "RESORTCOM": "2025-05-15",
    "SIBS": "2025-06-30",
    "WOMPI": "2025-06-30"
}

'''"
    "UniversalPay": "2025-05-15",
    "W2M": "2025-05-15",
    Need to add wordline
    "WORLDLINE" : "2025-05-15",
    '''

@app.route("/pages/cobrador/diary_payment_checker", methods=['GET'])
def check_daily_reservations_payments():
    hotel_code_from_request = request.values.get("hotel_code")

    hotels_to_check = [hotel_code_from_request] if hotel_code_from_request else [hotel.get('applicationId') for hotel in
                                                                                 get_all_valid_hotels() if
                                                                                 'test' not in hotel.get(
                                                                               'applicationId')]

    today = datetime.now().strftime("%Y-%m-%d")
    html_global_report = f"<h1 style='text-align: center;'>Informe de pagos {today}</h1><div style='width: 90%; background-color: #f2f2f2; padding: 10px; margin: 0 auto; border-radius: 4px;'>"
    global_success = 0
    hotel_counter = 0
    global_no_audit_count = 0

    global_failed_reports = "<div style='width: 90%; background-color: #ff6961; border: 1px solid red; padding: 10px; margin: 5px auto; border-radius: 4px;'><h3>Reservas con pagos KO</h3>"


    for hotel_code in hotels_to_check:
        print(f"{hotel_counter}/{len(hotels_to_check)}")

        hotel = get_hotel_by_application_id(hotel_code)
        # Aquí tendré un listado de reservas a checkear
        reservations_to_check = get_reservations_to_check(hotel)
        hotel_success, failed_reports, no_audit_count = check_reservation_payments(reservations_to_check, hotel_code)

        global_failed_reports += failed_reports
        global_no_audit_count += no_audit_count

        hotel_counter += 1
        global_success += hotel_success

    global_failed_reports += "</div>"


    html_global_report += f"<div style='width: 90%; background-color: #B2FBA5; border: 1px solid green; padding: 10px; margin: 5px auto; border-radius: 4px;'><h3>Se han comprobado un total de {global_success} pagos correctamente procesados en un total de {hotel_counter} hoteles</h3></div>"
    global_no_audit_reports = f"<div style='width: 90%; background-color: #fdfd96; border: 1px solid #FFC94D; padding: 10px; margin: 5px auto; border-radius: 4px;'><h3>Hay un total de {global_no_audit_count} pagos que no se han auditado</h3>"
    html_global_report += global_failed_reports + global_no_audit_reports

    html_global_report += "</div>"
    # 6. send only one email with the report
    title = "daily_payment_checker"
    send_emails_to = "<EMAIL>"

    sendEmail_localdev(send_emails_to, title, "", html_global_report)

    return html_global_report
    #return {"message": "OK"}


def get_reservations_to_check(hotel):
    # hint -> use get_reservations_of_hotel for 1.2.3
    reservations_to_check =  []

    three_months = (datetime.now() - timedelta(weeks=52)).strftime("%Y-%m-%d %H:%M:%S")
    try:
        no_filter_reservations  = get_using_entity_and_params('Reservation', search_params=[("timestamp", ">=", three_months)], hotel_code=hotel["applicationId"])
    except Exception as e:
        print(e)
        # posibles timeout from google datastore in case there is something wrong. Lets retry
        try:
            no_filter_reservations  = get_using_entity_and_params('Reservation', search_params=[("timestamp", ">=", three_months)], hotel_code=hotel["applicationId"])
        except Exception as e:
            return []

    no_test_reservations = [x for x in no_filter_reservations if "TEST" not in (x.get("comments") or "") and "@@@BYPASSS@@@" not in (x.get("comments") or "") and "PRUEBA" not in (x.get("comments") or "")]

    # de aqui filtramos las que no tengan paraty en el nombre por si se cuela alguna
    reservations = [x for x in no_test_reservations if "paraty" not in x.get("email", "")]

    # 1. Get all reservation paid made today.
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
    new_reservations_with_payment_today = [reservation for reservation in reservations if reservation.get("timestamp") >= yesterday and json.loads(reservation.get("extraInfo")).get("payed")]
    reservations_to_check.extend(new_reservations_with_payment_today)
    # hint -> use get_payments_from_all_source

    # 2. Get all reservations that arrive today and check if they have a payment
    today = datetime.now().strftime("%Y-%m-%d")
    reservations_arriving_today = [reservation for reservation in reservations if reservation.get("startDate") == today]
    reservations_to_check.extend(reservations_arriving_today)

    # 3. Get all reservations that leave today and check if they have a payment
    reservations_leaving_today = [reservation for reservation in reservations if reservation.get("endDate") == today]
    reservations_to_check.extend(reservations_leaving_today)

    # 4. Get all reservations that have a payment made today and check if they have a reservation
    new_payments_in_hotel = get_all_payments_info_list([["timestamp", ">=", yesterday]], hotel_code=hotel["applicationId"], only_real_payments=True)
    new_payments_reservations_id = [payment.get("reservation_identifier") for payment in new_payments_in_hotel]
    # we filter all reservations searching for one in the list of reservations with new payments
    reservations_with_payments_today = [reservation for reservation in reservations if reservation.get("identifier") in new_payments_reservations_id and not reservation in reservations_to_check]
    reservations_to_check.extend(reservations_with_payments_today)
    # hint: use get_all_payments

    return reservations_to_check


def check_reservation_payments(reservations_to_check, hotel_code):


    success_reports = ""
    failed_reports =  ""
    no_audit_reports = ""

    hotel_success = 0
    no_audit_count = 0

    for reservation in reservations_to_check:
        extra_info = json.loads(reservation.get("extraInfo"))
        gateway_type = extra_info.get("payment_gateway_used")

        if not gateway_type:
            gateway_type =  extra_info.get("payment_gateway", extra_info.get("gateway_type"))

        if gateway_type:
            gateway_type = gateway_type.split()[0]

        if gateway_type and gateway_type != "Paypal":
            gateway_type = gateway_type.upper()

        if gateway_type == "COBRADOR" or not gateway_type:
            try:
                # esto tampoco lo tienen para pagos que no se han hecho en el momento de la reserva
                gateway_type = extra_info.get("gateways_shown_in_b3_by_cobrador").split(" ")[0]
            except Exception as e:
                # esto es un parche, si tiene worldline data es wordline, preguntar por esto
                if extra_info.get("worldline_data"):
                    gateway_type = "WORLDLINE_EU2"
                elif extra_info.get("stripe_customer_id"):
                    gateway_type = "STRIPE"
                elif extra_info.get("addon_credencials"):
                    gateway_type = "ADDONS"
                elif extra_info.get("redsys_info"):
                    gateway_type = "REDSYS"
                elif extra_info.get("azul_credencials"):
                    gateway_type = "AZUL"
                elif extra_info.get("payment_gateway_name"):
                    gateway_type = extra_info.get("payment_gateway_name").split()[0]
                elif extra_info.get("datatransdata"):
                    gateway_type = "DATATRANS"


        elif gateway_type == "Paypal":
            gateway_type = "PAYPAL_V2"
        elif gateway_type == "PAYPAL" or gateway_type == "PAYPALV1":
            gateway_type = "PAYPAL_V1"

        if (extra_info.get("payed") and float(extra_info["payed"]) > 0 and (not extra_info.get("created_by") or extra_info.get("created_by") == "hotel webs")):

            order_id = extra_info.get("paymentOrderId", extra_info.get("last_merchant_order_used,", extra_info.get("gateway_order_id")))
            sid = extra_info.get("session_search_info", {}).get("id")
            if not order_id:
                order_id = reservation.get("gateway_order_id")


            # if we get an stop redirecting and from gateway, we dont have those fields in extrainfo
            if gateway_type == "BANORTE":
                order_id = extra_info.get("banorte_info").get("codigoAut")
            elif not order_id:
                order_id = reservation.get("identifier")
            timestamp = reservation.get("timestamp")
            if gateway_type == None:
                pass

            result = check_payment_audit(order_id, hotel_code, gateway_type, timestamp, sid)

            if result == "not implemented yet" or result == "pass":
                pass
            elif result == "ok":
                success_reports += f"<p>Reserva: {reservation.get('identifier')} - Hotel: {hotel_code} - Pago: {order_id} - Pasarela: {gateway_type} - OK</p>"
                hotel_success += 1
            elif "NOT AUDITED" in result:
                no_audit_count += 1
            else:
                failed_reports += f"<p>Reserva: {reservation.get('identifier')} - Hotel: {hotel_code} - Pago: {order_id} - Pasarela: {gateway_type} - ERROR - CHECK URGENT</p>"

        all_payments = get_all_payment_reservation_list(reservation.get('identifier'), hotel_code, only_real_amounts_in_reservation=True)

        for payment in all_payments:
            if "manual" in payment.get("type"):
               continue

            order_id = payment.get("order")

            timestamp = payment.get("timestamp")

            result = check_payment_audit(order_id, hotel_code, gateway_type, timestamp)

            if result == "not implemented yet" or result == "pass":
                pass
            elif result == "ok":
                success_reports += f"<p>Reserva: {reservation.get('identifier')} - Hotel: {hotel_code} - Pago: {order_id} - Pasarela: {gateway_type} - OK</p>"
                hotel_success += 1
            elif "NOT AUDITED" in result:
                no_audit_count += 1
            else:
                failed_reports += f"<p class='color-red'>Reserva: {reservation.get('identifier')} - Hotel: {hotel_code} - Pago: {order_id} - Pasarela: {gateway_type} - ERROR - CHECK URGENT</p>"

    return hotel_success, failed_reports, no_audit_count


def check_payment_audit(order, hotel_code, gateway_type, payment_timestamp, sid=None):
    # estival torremora - hay cosas que no se estaban auditando nunca, hay que controlar que si el timestamp es anterior
    # a la corrección de los audits, entonces mala suerte, no estaba auditado en ningun lado
    # si si lo es, entonces si que hay un problema con el audit y hay que mirarlo
    audit = None

    if order == "R49781910":
        pass
    if not "estival" in hotel_code:
        try:
            legacy_timestamp = datetime.strptime(LEGACY_TIMESTAMP_BY_GATEWAY.get(gateway_type), "%Y-%m-%d")
        except:
            pass
            # not implemented yet
    else:
        legacy_timestamp = datetime.strptime("2025-05-17", "%Y-%m-%d")
        order = order.replace("AQ", "")

    payment_timestamp = datetime.strptime(payment_timestamp, "%Y-%m-%d %H:%M:%S")
    if hotel_code == "ms-amaragua":
        order = order.replace("R","")


    audit = get_audit_response_with_retry(hotel_code, search_params=[("payment_order_id", "=", order)])

    if not audit:
        # THIS IS A PATCH AND COULD HIDE ERRORS, ADD TIMESTAMP WHEN FIXED
        audit = get_audit_response_with_retry(hotel_code, search_params=[("web_sid", "=", sid)])


    try:
        if not audit and legacy_timestamp > payment_timestamp:
            return f"NOT AUDITED: {payment_timestamp} - {gateway_type}"
    except:
        return "not implemented yet"

    if not audit and not gateway_type:
        return "ERROR"

    if gateway_type == "REDSYS":
        gateway_type = "sermepa"
    elif gateway_type == "SIBS2.0" or gateway_type == "SIBS_MULTIBANCO":
        return "not implemented yet"
    # si hemos encontrado el audit pero no hay pasarela, vamos a intentar forzalo de mala manera
    if not gateway_type or gateway_type == "COBRADOR":
        gateway_type = audit[0].get("gateway_type")
        if gateway_type == "PAYPAL":
            gateway_type = "PAYPAL_V1"

    if not gateway_type:
        return "ERROR"

    if gateway_type == "PAYPALV1":
        gateway_type = "paypal_v1"

    gateway_type = gateway_type.replace("_", "")
    result = get_ok_by_gateway_name(gateway_type, audit, hotel_code)

    if result == "ok":
        return result
    elif result == "not implemented yet" or result == "pass":
        return result

    return "ERROR"


def get_ok_by_gateway_name(gateway, audit, hotel):
    gateway_result = "not implemented yet"

    if "sermepa" in gateway.lower():
        gateway_result = get_ok_sermepa(audit)

    elif "worldlineeu2" in gateway.lower():
        gateway_result = get_ok_wordline(audit)

    elif "stripe" in gateway.lower():
        gateway_result = get_ok_stripe(audit)

    elif "addon" in gateway.lower():
        gateway_result = get_ok_addons(audit)

    elif "mitec" in gateway.lower() or "santander" in gateway.lower():
        gateway_result = get_ok_santander(audit)

    elif "banorte" in gateway.lower():
        gateway_result = get_ok_banorte(audit)

    elif "paypalv2" in gateway.lower():
        gateway_result = get_ok_paypalv2(audit)

    elif "paycomet" in gateway.lower():
        gateway_result = get_ok_paycomet(audit)

    elif "azul" in gateway.lower():
        gateway_result = get_ok_azul(audit)

    elif "ceca" in gateway.lower():
        gateway_result = get_ok_ceca(audit, hotel)

    elif "epayco" in gateway.lower():
        gateway_result = get_ok_epayco(audit)

    elif "evo_sessions" in gateway.lower() or "evosessions" in gateway.lower() or "evo" in gateway.lower():
        gateway_result = get_ok_evoSessions(audit)

    elif "paybyrd" in gateway.lower():
        gateway_result = get_ok_paybird(audit)

    elif "paylands" in gateway.lower():
        gateway_result = get_ok_paylands(audit)

    elif "paypalv1" in gateway.lower():
        gateway_result = get_ok_paypalv1(audit)

    elif "payu" in gateway.lower():
        gateway_result = get_ok_payu(audit)

    elif "placetopay" in gateway.lower():
        gateway_result = get_ok_placetopay(audit)

    elif "redunicre" in gateway.lower():
        gateway_result = get_ok_redunicre(audit)

    elif "resortcom" in gateway.lower():
        gateway_result = get_ok_resortcom(audit)

    elif "sibs" in gateway.lower():
        gateway_result = get_ok_sibs(audit)

    elif "wompi" in gateway.lower():
        gateway_result = get_ok_wompi(audit)

    '''
    elif "sibs2" in gateway.lower():
        gateway_result = get_ok_sibs2(audit)
    elif "trust" in gateway.lower():
        gateway_result = get_ok_trust(audit)
        '''

    return gateway_result

# SERMEPA
def get_ok_sermepa (audit):
    #filtramos para que coja solo los de sermepa, porque ha pasado algo raro con addons
    sermepa_audit = [x for x in audit if "sermepa" in x.get('gateway_type').lower()]

    # Getting audit response
    if audit:
        for single_audit in sermepa_audit:
            audited_response = single_audit.get('response')
            if isinstance(audited_response, bytes):
                audited_response = json.loads(bytearray(audited_response))
                response = audited_response.get('Ds_Response')
            else:
                try:
                    encrypted_response = json.loads(audited_response).get('Ds_MerchantParameters')
                    decrypted_response = json.loads(bytearray(base64.b64decode(encrypted_response)))
                    response = decrypted_response.get('Ds_Response')
                except Exception as e:
                    # xml
                    try:
                        tree = xee.fromstring(audited_response)
                        request_data = tree.findall(".//XML")
                        message = request_data[0].text
                        response_data = xee.fromstring(message)
                        request_json = response_data.findall(".//Request")
                        request_json = request_json[0]
                        ds_error_code = request_json.findall('Ds_ErrorCode')
                        if len(ds_error_code) > 0:
                            continue
                        response = request_json.findall('Ds_Response')
                        response = response[0].text
                    except:
                        print("error")
                        continue

            # Test ok - response
            if response and int(response) < 100:
                # sermepa valid payment codes [0-99]
                return "ok"

    return "ko"

# WORLDLINE
def get_ok_wordline(audit):
    # Must find if its from process or from execute
    type = "execute"
    if len(audit) == 1:
        type = audit[0].get('type')
        if type == "Tokenize":
            # if its a tokenize only we return pass and dont process this payment because there is no payment to begin with. For some reason we are getting those alone too
            # even it there is not a payment, only tokenize
            return "ok"

        type = "process"
    else:
        # we must search the array and look if we have one audit with 'tokenize' type
        for single_audit in audit:
            # get get the type
            audit_type = single_audit.get('type')
            if audit_type == "Tokenize":
                type = "process"

    tokenize_audits = [x for x in audit if x.get("type") == "Tokenize"]
    if len(tokenize_audits) == len(audit):
        return "ok"

    # If the audit comes from a process, we get 1 audits, if it comes from execute, it has 2
    if type == "process":
        response = ""

        for single_audit in audit:
            if single_audit.get('type') == "Process payment":
                response = json.loads(single_audit.get('response'))

        if response:
            payment_status_category = response.get("createdPaymentOutput").get("paymentStatusCategory")
            payment_status = response.get("createdPaymentOutput").get("payment").get("status")
        else:
            return "ko"

        if payment_status_category and payment_status_category == "SUCCESSFUL" and not (payment_status == "PENDING_CAPTURE"):
           return "ok"

    elif type == "execute":

        process_audit = [x for x in audit if x.get('type') == "Process payment" or x.get('type') == "Payment"]
        capture_audit = [x for x in audit if x.get('type') == "Capture"]

        # recorremos ambas listas en busca de un success, por que hay 800 audits para cada uno, algunos pueden ser errores y otros no
        final_capture_response = "ko"
        final_process_status = "ko"

        for process in process_audit:
            try:
                audit_response = json.loads(process.get('response'))
                if audit_response.get("createdPaymentOutput").get("paymentStatusCategory") == "SUCCESSFUL":
                    final_process_response = "ok"
                    final_process_status = audit_response.get("status")
            except:
                final_process_status = audit_response.get('payment').get('paymentStatusCategory')



        for capture in capture_audit:
            audit_response = json.loads(capture.get('response'))

            if audit_response.get("status") == "CAPTURE_REQUESTED" or audit_response.get("status") == "PENDING_CAPTURE":
                final_capture_response = "ok"


        # note: we dont get payment confirmation in any of this audits, its a leap of faith
        # we assume that its actually an OK when we turn 'pending_capture' into 'capture_requested'
        if final_process_status == "PENDING_CAPTURE" and final_capture_response == 'CAPTURE_REQUESTED' or final_capture_response == "ok":
            return "ok"

        return 'ko'

    return "ko"

def get_ok_stripe(audit):
    # stripe can have a shit ton of audits per payment_order_id // process _ build // both multiple
    # process payload its only the payment id
    # we filter the audits that have id
    process_audits = [x for x in audit if len(json.loads(x.get('payload'))) <= 1]

    if len(process_audits) == 0:
        return "pass"

    # we already got them filtered, but are they actually the same one repeated? or a diffetent on
    #both seem to be repeated, we take the first one porque me sale de los huevos
    for audit in process_audits:
        process_audit_response = json.loads(audit.get('response'))
        response_status = process_audit_response.get("status")

        if response_status == "succeeded":
            return "ok"

    return "ko"

def get_ok_addons(audit):
    response_status = ""
    # process comes with 2 audits, one for build and one for process, execute has only one as XML
    if len(audit) > 1:
        #get json response
        # we take the last one
        audit = [x for x in audit if x.get('response') and x.get('type') == "Process payment"]

        # recorremos y buscamos el más reciente

        last_audit = audit[0]
        timestamp = last_audit.get('timestamp')
        for single_audit in audit:
            if single_audit.get('timestamp') > timestamp:
                timestamp = single_audit.get('timestamp')
                last_audit = single_audit
        try:
            audit_response = json.loads(last_audit.get('response'))
            response_status = audit_response.get("RESULT")
        except Exception as e:
            # we could get multiple if one is a refund
            audit_response = audit[0].get('response')
            # we need to seach for the result tag
            try:
                start = audit_response.index('<result>') + 8
                end = audit_response.index('</result>')
                response_status = audit_response[start:end]
            except Exception as e:
                return "ko"
    elif len(audit) == 1:
        # xml
        audit_response = audit[0].get('response')
        # we need to seach for the result tag
        try:
            start = audit_response.index('<result>') + 8
            end = audit_response.index('</result>')
            response_status = audit_response[start:end]
        except Exception as e:
            return "ko"

    if response_status and response_status == "00":
        return 'ok'

    return "ko"

def get_ok_santander(audit):
    # mitec audits the same audit over and over until it gets bored. It could be 5, 6 and up to 16 times
    # we take the first one
    audit_response = audit[0].get('response')
    xml_response = xml.fromstring(audit_response)
    response_status = xml_response.find('.//response').text

    if response_status and response_status == "approved":
        return "ok"

    return 'ko'

def get_ok_banorte(audit):

    # for some reason we can have multiple audits repeated, we get the first
    audit_response = audit[0].get('response')

    if audit_response:
        # banorte returns "Aprobado" in texto
        respose_status = audit_response.get("resultadoPayw")
        if respose_status == "A":
            return "ok"

    return "ko"

# PAYPAL_V2
def get_ok_paypalv2(audit):
    # paypalv2 makes 2 audits. We could potentially only have one if its a re process, only having the capture
    if len(audit) == 1:
        capture_audit_response = json.loads(audit[0].get('response'))
        capture_status = capture_audit_response.get("status")
        if capture_status == "COMPLETED":
            return 'ok'
    else:

        final_result = ""
        final_capture = ""

        for single_audit in audit:
            single_audit_response = json.loads(single_audit.get("response"))

            if single_audit_response.get("intent") == "CAPTURE":
                capture_audit_response = single_audit_response
                capture_status = capture_audit_response.get("status")
                if capture_status in ["APPROVED", "COMPLETED"]:
                    final_capture = capture_status
            else:
                payment_audit_response = single_audit_response
                payment_status = payment_audit_response.get('status')
                if payment_status in ["COMPLETE", "COMPLETED"]:
                    final_result = payment_status

        if final_result in ["COMPLETE", "COMPLETED"] and final_capture in  ["APPROVED", "COMPLETED"]:
            return "ok"

    return "ko"

def get_ok_paycomet(audit_call):
    # we could have the same audit repeated in paycomet, we take the first one
    audit = audit_call[0]

    audit_response = json.loads(audit.get('response'))

    if audit_response:
        status = audit_response.get("Response")
        errors = audit_response.get("ErrorID")

        if status == "OK" and errors == "0":
            return "ok"
    # no tienen por qué ser duplicados, pueden ser fallor y aciertos
    if status == "KO":
        for single_audit in audit_call:
            audit_response = json.loads(single_audit.get('response'))
            status = audit_response.get("Response")
            errors = audit_response.get("ErrorID")

            if status == "OK" and errors == "0":
                return "ok"

    return "ko"

def get_ok_azul(audit_call):
    #for the same payment_order we have at least 2 audit, con from process and the other from build
    audit_response = ""
    for single_audit in audit_call:
        try:
            audit_response = json.loads(single_audit.get('response'))
            break
        except Exception as e:
            #wrong one
            continue


    # accepted payments have isocode 00
    iso_code = ""
    if audit_response:
        iso_code = audit_response.get("IsoCode")

    if iso_code and iso_code == "00":
        return "ok"

    return "ko"


def get_ok_ceca(audit_call, hotel_code):
    # in ceca we can get 2 duplicated audits too
    for audit in audit_call:
        audit_response = audit.get('response')

        # We are getting the response as xml from ceca when its a failed payment (from pep) PREGUNTAR
        try:
            response_json = json.loads(audit_response)
            #if its a json, we continue verifying the payment
            gateway_type = get_integration_name(hotel_code)
            gateway_type = [x for x in gateway_type.split(";") if "CECA" in x][0]
            gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
            ceca_hotel_key = gateway_configuration.get("key_secret")

            if _check_sign(ceca_hotel_key, response_json):
                return 'ok'
        except Exception as e:
            try:
                response_xml = xml.ElementTree(xml.fromstring(audit_response))
            except:
                pass


            operacion = response_xml.find('OPERACION')
            result = operacion.attrib.get('tipo')
            referencia = operacion.find('numeroAutorizacion').text

            if referencia and result == "000":
                return "ok"

    return 'ko'

# CECA
def _check_sign(key, response_json):
    sign = response_json.get("Firma")[0]

    merchant_id = response_json.get("MerchantID")[0]
    acquirer_bin = response_json.get("AcquirerBIN")[0]
    terminal_id = response_json.get("TerminalID")[0]
    num_operacion = response_json.get("Num_operacion")[0]
    amount = response_json.get("Importe")[0]
    currency = response_json.get("TipoMoneda")[0]
    exponente = response_json.get("Exponente")[0]
    referencia = response_json.get("Referencia")[0]

    sign_target = "%s%s%s%s%s%s%s%s%s" % (
    key, merchant_id, acquirer_bin, terminal_id, num_operacion, amount, currency, exponente, referencia)

    result = hashlib.sha256(sign_target.encode()).hexdigest()

    if sign == result:
        return True

    return False


def get_ok_epayco(audit_call):
    #in epayco we can audit the same response twice. one for pending and one for accepted
    #we take the response one
    for audit in audit_call:
        audit_response = audit.get('response')

        # Before the update the audit we were auditing the response as text, we audit as json
        try:
            response_json = json.loads(audit_response)

            response_status = response_json.get("x_cod_transaction_state", "")
        except Exception as e:
            # we have to search on the string
            try:
                tag_length = len("x_cod_transaction_state")
                start = audit_response.index("x_cod_transaction_state") + tag_length + 1
                end = start + 2
                response_status = audit_response[start:end]

                # we might be getting a code state with 1 digit or 2. (10 or 1&, for example) We have to control it
                if response_status[1] == "&":
                    response_status = response_status[0]
            except Exception as e:
                pass

        if response_status == "1" or response_status == "3":
                return "ok"

    return "ko"


# EVO_SESSIONS
def get_ok_evoSessions(audit_call):
    # in evoSession we have a lot of audit for the same order_id, none for process, this seems weird. skip

    # los que tienen init session y form payment son de build gateway form
    # filtramos los que el type es "Process payment"
    process_audits = []

    for audit in audit_call:
        # we got 3, but those 3 are different, we need to pick which one to choose
        # one of them is form when de order is created, this one has a payload, lets filter by that too
        correct_audit_type = audit.get('type') == "Process payment"
        if correct_audit_type:
            process_audits.append(audit)

    # we can still have the same one duplicated, so we take the first one
    for audit in process_audits:
        audit_response = json.loads(audit.get('response'))

        order_status_is_ok = audit_response.get("order", {}).get("status", "") in ["CAPTURED", "VERIFIED"]
        response_status_is_ok = audit_response.get("response", {}).get("gatewayCode", "") in ["APPROVED"]
        result_status_is_ok = audit_response.get("result") in ["SUCCESS"]

        if order_status_is_ok and result_status_is_ok and response_status_is_ok:
            return "ok"

    return "ko"

# PAYBIRD
def get_ok_paybird(audit_call):
    # in theory, we could be getting multiple audits from execute or process,
    # but it seems like we can process them the same
    for audit in audit_call:
        process_response = json.loads(audit.get('response'))
        response_status = process_response.get("status")

        if response_status == "Success" or response_status == "paid":
            return "ok"
        #elif response_status == "pending":
        #    return 'ok'

    return "ko"


def get_ok_paylands(audit_call):
    # in theory we are only getting one audit per order_id, mutiple per sessions or reservation
    for audit in audit_call:
        try:
            audit_response = json.loads(audit.get('response'))
            response_stauts = audit_response.get("order").get("status")

            if response_stauts == "SUCCESS":
                return "ok"
        except Exception as e:
            continue

    return "ko"

#PAYPAL_V1
def get_ok_paypalv1(audit_call):
    # we get the first audit
    for audit in audit_call:
        audit_response = json.loads(audit.get('response'))
        if audit_response.get("ACK") in ["Success", 'SuccessWithWarning']:
            return "ok"

    return 'ko'

def get_ok_payu(audit_call):
    for audit in audit_call:
        try:
            audit_response = json.loads(audit.get('response'))
            response_stauts = audit_response.get("state_pol")

            if response_stauts == "4":
                return "ok"
        except:
            continue

    return 'ko'

def get_ok_placetopay(audit_call):
    # we could be getting a lot of audits for the same order id, rejected or approved
    # i need to see how the script will work so I know how can I handle this shit
    for audit in audit_call:

        audit_response = json.loads(audit.get('response'))
        response_status = audit_response.get("status").get("status")

        if response_status == "APPROVED":
            return "ok"

    return 'ko'

# REDUNICRE
def get_ok_redunicre(audit_call):
    # we get an encrypted response from the audit as bytes
    for audit in audit_call:
        audit_response = audit.get('response')

        # final response is a fucking xml, and the only one avialable is a failed payment
        xmldoc = minidom.parseString(audit_response.decode('utf-8').replace("obj:", "").replace("impl:", "").encode('utf-8'))
        code = xmldoc.getElementsByTagName('code')[0].firstChild.nodeValue

        if code == "00000":
            return "ok"

    return "ko"

# RESORTCOM
def get_ok_resortcom(audit_call):
    # we can have a few audits for the same order_id if it has a previous failed payment
    for audit in audit_call:
        audit_response = json.loads(audit.get('response'))
        resortcom_response_ok = audit_response and audit_response.get("CardAuthorizationStatus") == 6

        if resortcom_response_ok:
            return 'ok'

    return 'ko'


def get_ok_sibs(audit_call):
    # this one is tricky // maybe not, must test
    PAYMENT_SUCCESS = "^(000\.000\.|000\.100\.1)"

    # we filter those that are not Form Payment
    process_audits = [x for x in audit_call if x.get("type") != "Form Payment"]

    if len(process_audits) == 0:
        return "pass"

    # we could get one or two audits, maybe more. [0] is form, we get the last
    for audit in audit_call:
        process_audit_response = json.loads(audit.get('response'))
        try:
            response_code = process_audit_response.get("result").get("code")
        except:
            # not this one oopsie
            continue

        if re.match(PAYMENT_SUCCESS, response_code):
            return "ok"

    return "ko"

# WOMPI
def get_ok_wompi(audit_call):
    # in theory were only getting one audit per order_id
    for audit in audit_call:
        audit_response = json.loads(audit.get('response'))
        response_data = audit_response.get("data")
        if response_data:
            response_status = response_data.get("status")
        else:
            # webhook response
            webhook_response = audit_response.get("already_processed")
            if webhook_response:
                return "ok"

        if response_status == "APPROVED":
            return "ok"

    return 'ko'