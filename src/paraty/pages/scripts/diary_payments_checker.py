
from paraty import app
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from flask import request
from paraty.utilities.email_utils import sendEmail_localdev
from paraty_commons_3.email_utils import sendEmail


#TODO
LEGACY_TIMESTAMP_BY_GATEWAY = {
    "SERMEPA_SOAP": "2025-05-01"

}

@app.route("/pages/cobrador/diary_payment_checker", methods=['GET'])
def check_reservations_with_missing_payments():


    hotel_code_from_request = request.values.get("hotel_code")

    hotels_to_check = [hotel_code_from_request] if hotel_code_from_request else [hotel.get('applicationId') for hotel in
                                                                                 get_all_valid_hotels() if
                                                                                 'test' not in hotel.get(
                                                                               'applicationId')]
    html_global_report = ""
    for hotel_code in hotels_to_check:
        # Aquí tendré un listado de reservas a checkear
        reservations_to_check = get_reservations_identifiers_to_check(hotel_code)

        html_global_report +=check_reservation_payments(reservations_to_check, hotel_code)


    # 6. send only one email with the report
    title = "diary_payment_checker"
    send_emails_to = "<EMAIL>"
    sendEmail_localdev(send_emails_to, title, "", html_global_report)

    return "OK"


def get_reservations_identifiers_to_check(hotel_code):
    reservations_to_check = []
    # hint -> use get_reservations_of_hotel for 1.2.3

    # 1. Get all reservation paid made today.
    # hint -> use get_payments_from_all_source

    # 2. Get all reservations that arrive today and check if they have a payment

    # 3. Get all reservations that leave today and check if they have a payment

    # 4. Get all reservations that have a payment made today and check if they have a reservation
    # hint: use get_all_payments

    return reservations_to_check


def check_reservation_payments(reservations_to_check, hotel_code):

    html_report = ""
    for reservation in reservations_to_check:
        # 5. Check if the response in the audit is OK
        # for each reservation we have to checked
            # 5.1  Paid in booking process. Payment_id -> Identifer extra_info -> payed
            # 5.2 check the payments (programmed or manual)  Use: get_all_payments

        id_payment = ""
        check_payment_audit(id_payment, hotel_code)
        html_report += f"<p>Reserva: {reservation}</p>"

    return html_report


def check_payment_audit(id_payment, hotel_code):
    type_gateway = ""
    payment_timestamp = ""
    if LEGACY_TIMESTAMP_BY_GATEWAY.get(type_gateway) < payment_timestamp:
        pass
    else:
        pass







