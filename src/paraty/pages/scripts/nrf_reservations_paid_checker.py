import logging
import json
from datetime import datetime, timedelta
from paraty import app
from flask import request
from paraty.pages.cobrador.cobrador import get_rules_all, get_rules_to_apply_for_reservation, calculate_payment_by_type

from paraty.pages.cobrador.cobrador_constants import PAYMENT_TYPE_IN_WEB, PAYMENT_TYPE_EARLY_PAYMENT, \
    PAYMENT_TYPE_CREATE_TOKEN, PAYMENT_TYPE_FLIGHT

from paraty.utilities.email_utils import sendEmail_localdev
from paraty_commons_3.email_utils import sendEmail
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_hotel_by_application_id
from paraty_commons_3.common_data.common_data_provider import get_rooms_of_hotel, get_rates_of_hotel, get_conditions_of_hotel, get_hotel_advance_config_value
from paraty_commons_3.datastore import datastore_communicator


@app.route("/pages/cobrador/check_reservations_with_missing_payments", methods=['GET'])
def check_reservations_with_missing_payments():
    hotel_code_from_request = request.values.get("hotel_code")
    hotels_to_check = [hotel_code_from_request] if hotel_code_from_request else [hotel.get('applicationId') for hotel in get_all_valid_hotels() if 'test' not in hotel.get('applicationId')]
    timestamp = (datetime.now() - timedelta(hours=4)).strftime('%Y-%m-%d %H:%M:%S')
    search_params = [('timestamp', '>=', timestamp)]
    if request.values.get("identifier"):
        search_params = [('identifier', '=', request.values.get("identifier"))]
    send_emails_to = request.values.get("send_emails_to") if request.values.get("send_emails_to") else '<EMAIL>'

    for hotel_code in hotels_to_check:
        hotel_info = get_hotel_by_application_id(hotel_code)
        has_rules_by_cobrador = get_hotel_advance_config_value(hotel_code, "Gateway rules by cobrador")
        if not get_hotel_advance_config_value(hotel_code, "Use Payment Gateway") or not (has_rules_by_cobrador or get_hotel_advance_config_value(hotel_code, "Gateway Amount")):
            continue

        reservations = datastore_communicator.get_using_entity_and_params('Reservation', search_params=search_params, hotel_code=hotel_code)
        for reservation in reservations:
            if skip_reservation(reservation):
                continue
            if has_rules_by_cobrador:
                extra_info = json.loads(reservation.get('extraInfo'))
                amount_paid_in_reservation = _get_total_payed_amount_from_all_sources(extra_info)
                reservation_payment_structure = get_reservation_payment_structure(reservation, extra_info)
                amount_that_should_be_paid = amount_that_should_have_paid(hotel_code, reservation_payment_structure, reservation, extra_info)
                if amount_that_should_be_paid:
                    bad_paid = compare_amounts_payments(amount_that_should_be_paid, amount_paid_in_reservation, extra_info)
                    if bad_paid:
                        title = f"[{hotel_code}][{reservation.get('identifier')}] NR Reservation with bad payment amount. Checkin date: {reservation.get('startDate')}"
                        logging.info(f"Sending email: {title}")
                        sendEmail_localdev(send_emails_to, f"{title}", "", f"Reserva con falta de pago. Pagado: {amount_paid_in_reservation:.2f} - Cantidad esperada: {amount_that_should_be_paid:.2f}")
            elif reservation_is_nr_and_has_no_payment(reservation, hotel_code, hotel_info):
                title = f"[{hotel_code}][{reservation.get('identifier')}] NR Reservation with bad payment amount. Checkin date: {reservation.get('startDate')}"
                logging.info(f"Sending email: {title}")
                sendEmail_localdev(send_emails_to, title, "", "Reserva con falta de pago")
    return "Finished"


def get_reservation_payment_structure(reservation, extra_info):
    price_total = float(reservation["price"])
    if reservation.get('priceSupplements', 0):
        price_total += float(reservation['priceSupplements'])
    reservation_payment_structure = {
        "price": price_total,
        "price_days": {},
        "startDate": reservation.get("startDate"),
        "endDate": reservation.get("endDate"),
        "supplements": reservation.get("supplements")
    }

    if reservation and reservation.get("price_days"):
        reservation_payment_structure['price_days'] = reservation["price_days"]

    elif extra_info and extra_info.get("prices_per_day"):
        if extra_info.get("save_from_manager2") or not reservation.get("modificationTimestamp"):
            for room, days_prices in extra_info['prices_per_day'].items():
                start_date_str = reservation.get("startDate")
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                end_date_str = reservation.get("endDate")
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                room_aux = room.split(":")[1].strip()
                index = int(room.split(":")[0]) - 1
                room_result = room_aux + "@@" + str(index)
                reservation_payment_structure['price_days'][room_result] = []
                while start_date < end_date:
                    day = start_date.strftime("%d/%m/%Y")
                    reservation_payment_structure['price_days'][room_result].append(float(days_prices[day][2]))

                    start_date += timedelta(days=1)
    return reservation_payment_structure


def skip_reservation(reservation):
    extra_info = json.loads(reservation.get('extraInfo'))
    comments = reservation.get('comments') or ''
    if '@@@test@@@' in comments or '@paratytech' in reservation.get('email') or reservation.get('cancelled') or reservation.get("agent") or extra_info.get("status_reservation") == 'pending' or extra_info.get("reservation_after_cc_denied")\
            or extra_info.get("transfer_bank") or extra_info.get('agency_name') or not reservation.get('price'):
        return True


def compare_amounts_payments(amount_that_should_be_paid, amount_paid_in_reservation, extra_info):
    if abs(float(amount_that_should_be_paid) - float(amount_paid_in_reservation)) > 1:
        accommodation_value = extra_info.get('price_info', {}).get('taxes', {}).get('accommodation', {}).get('value', 0) or 0
        return abs(float(amount_that_should_be_paid + accommodation_value) - float(amount_paid_in_reservation)) > 1
    return False

    # if abs(float(amount_that_should_be_paid) - float(amount_paid_in_reservation)) > 1:
    #     if amount_paid_in_reservation > amount_that_should_be_paid:
    #         return False
    # return True


def amount_that_should_have_paid(hotel_code, reservation_payment_structure, reservation, extra_info):
    hotel = get_hotel_by_application_id(hotel_code)
    all_rates = get_rates_of_hotel(hotel, include_removed=True)
    all_rates_map = {x.get("key"): x for x in all_rates}

    all_rules = get_rules_all(hotel_code)
    type_rules_filter = [PAYMENT_TYPE_IN_WEB, PAYMENT_TYPE_EARLY_PAYMENT, PAYMENT_TYPE_CREATE_TOKEN]
    if reservation_payment_structure.get("price_only_flight"):
        type_rules_filter = [PAYMENT_TYPE_FLIGHT]
    filtered_rules = [rule for rule in all_rules if rule.get("type_rule", "") in type_rules_filter]

    all_rooms = get_rooms_of_hotel(hotel, include_removed=True)
    all_rooms_map = {x.get("key"): x for x in all_rooms}
    rules_to_apply_filtered = get_rules_to_apply_for_reservation(reservation, filtered_rules, all_rates_map, all_rooms_map, hotel_code)
    force_rule_by_extra_info = [x for x in rules_to_apply_filtered if x.get('description', "") == extra_info.get('rule_applied', "")]

    if extra_info.get('rule_applied') and force_rule_by_extra_info:
        rules_to_apply_filtered = force_rule_by_extra_info

    if rules_to_apply_filtered:
        rules_to_apply_filtered = rules_to_apply_filtered[0]
        if rules_to_apply_filtered.get('type_rule') == 'in_web':
            amount = calculate_payment_by_type(rules_to_apply_filtered, reservation_payment_structure, all_rooms_map)
            if amount:
                return amount

    return 0


def reservation_has_something_paid(extra_info):
    return float(extra_info.get("payed", 0) or 0) or extra_info.get("payed_by_tpv_link") or extra_info.get('payed_by_cobrador')



def calculate_cancel_policy_for_period(reservation,hotel_code, current_rate, condition_rates):
	try:
		hotel = get_hotel_by_application_id(hotel_code)

		if not current_rate:
			return ""

		star_date = reservation.get("startDate")
		end_date = reservation.get("endDate")
		condition_rates_now = condition_rates.get(current_rate.get("rateCondition"))
		for condition_period in condition_rates_now.get('cancellationPeriods', ''):
			star_date_period = condition_period.split(";")[0]
			end_date_period = condition_period.split(";")[1]
			rate_conditions = condition_period.split(";")[2]
			if star_date_period >= star_date and star_date <= end_date_period:
				continue


			current_rate_period = condition_rates.get(rate_conditions)
			cancellation_policy_period = current_rate_period.get('cancellationPolicy', '')
			return cancellation_policy_period

		return ""
	except Exception as e:
		return ""


def reservation_is_nr_and_has_no_payment(reservation, hotel_code, hotel_info):
    all_rates = get_rates_of_hotel(hotel_info)
    all_rates = {i.get('key'): i for i in all_rates}

    condition_rates = get_conditions_of_hotel(hotel_info)
    condition_rates = {x.get("key"): x for x in condition_rates}
    try:
        extra_info = json.loads(reservation.get("extraInfo"))
    except:
        return False
    
    if extra_info.get("SIBS_MULTIBANCO"):
         return False

    rate_key = reservation.get("rate")

    rate = all_rates.get(rate_key)

    if rate:
        cancel_policy = rate.get("cancellationPolicy")

        if cancel_policy == "Multiples politicas":
            name_flex = False
            for name in ["FLEX", "PVP"]:
                if name in (rate.get("localName") or "").upper():
                    name_flex = True
                    break

            if name_flex:
                return False

            cancel_policy = calculate_cancel_policy_for_period(reservation, hotel_code, rate, condition_rates)

        if _get_total_payed_amount_from_all_sources(extra_info):
            return False

        status = extra_info.get("status_reservation")
        not_from_callcenter = not reservation.get("agent")
        not_reservation_after_cc_denied = not extra_info.get("reservation_after_cc_denied")
        not_canceled = not reservation.get("cancellationTimestamp")
        not_bank_transfer = not extra_info.get("transfer_bank")

        if cancel_policy == "No cancelable" and status != "pending" and not_canceled and not_from_callcenter and not_reservation_after_cc_denied and not_bank_transfer:
            return True

    return False


def _get_total_payed_amount_from_all_sources(extra_info_reservation):
	payed_amount = 0
	if extra_info_reservation.get("payed") and float(extra_info_reservation.get("payed")):
		payed_amount = float(extra_info_reservation.get("payed", "0"))
	if extra_info_reservation.get("payed_by_cobrador"):
		payed_amount += float(extra_info_reservation.get("payed_by_cobrador", "0"))

	if extra_info_reservation.get("payed_by_tpv_link"):
		payed_by_tpv_link = extra_info_reservation.get("payed_by_tpv_link", [])
		for payment in payed_by_tpv_link:
			payed_amount += float(payment.get("amount", "0") or 0)

	if extra_info_reservation.get("extra_payed_by_cobrador"):
		payed_amount += float(extra_info_reservation.get("extra_payed_by_cobrador", "0"))

	return payed_amount
