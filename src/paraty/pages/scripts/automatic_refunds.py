from datetime import datetime, timedelta

from paraty.pages.cobrador.cobrador import execute_refund_in_gateway, save_payment_info_in_reservation, \
    save_payment_reservation
from paraty.pages.cobrador.cobrador_constants import PAYMENT_TYPE_REFUND
from paraty.pages.cobrador.cobrador_utils import send_payment_confirmation_emails
from paraty.pages.scripts.nrf_reservations_paid_checker import calculate_cancel_policy_for_period
from paraty.utilities.languages import language_utils
from paraty_commons_3.common_data.common_data_provider import get_rates_of_hotel, get_conditions_of_hotel
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id

if __name__ == "__main__":


    EXCLUDED_IDENTIFIERS = ["MB-21388658", "WB-17569740", "MB-63793699", "MB-16453745", "13922652", "BS-30307197"]

    ONLY_THIS_IDENTIFIER = [
    "MB-35531884",
    "MB-65379235",
    "MB-76555867",
    "MB-83155409",
    "WB-17209340",
    "WB-17447206",
    "WB-77082470",
    "WB-77332234",
    "WB-90290603"
]


    hotel_code = "bahia-serena"
    three_days = (datetime.now() - timedelta(days=4)).strftime("%Y-%m-%d %H:%M:%S")
    all_payments = get_using_entity_and_params('PaymentsReservation', search_params=[("timestamp", ">=", three_days)],
                                hotel_code=hotel_code)

    # obtenemos los rates del hotel
    hotel = get_hotel_by_application_id(hotel_code)
    all_rates = get_rates_of_hotel(hotel)
    all_rates = {i.get('key'): i for i in all_rates}

    condition_rates = get_conditions_of_hotel(hotel)
    condition_rates = {x.get("key"): x for x in condition_rates}

    output = ""
    semi_output = ""

    list_to_refunds = []


    for payment in all_payments:



        # obtener la reserva
        reservation = get_using_entity_and_params('Reservation', search_params=[("identifier", "=", payment.get("reservation_identifier"))],
                                hotel_code=hotel_code)

        if reservation:
            reservation = reservation[0]
        else:
            continue


        if reservation.get("identifier") in EXCLUDED_IDENTIFIERS:
            print("EXCLUYENDO RESERVA: %s" % reservation.get("identifier"))
            continue


        if ONLY_THIS_IDENTIFIER and (reservation.get("identifier") not in ONLY_THIS_IDENTIFIER):
            continue

        #obtenemos el rate de la reserva:
        reservation_rate = reservation.get("rate", "")
        reservation_timpestamp = datetime.strptime(reservation.get("startDate"), "%Y-%m-%d")
        july_9 = datetime.strptime("2025-07-09 00:00:00", "%Y-%m-%d %H:%M:%S")

        rate = all_rates.get(reservation_rate)
        cancellation_policy = rate.get('cancellationPolicy')

        name_flex = False

        if "flex" in rate.get("name").lower():
            name_flex = True
            # diferenciar entre semi flexible y flexible normal
            if "semi" in rate.get("name").lower():
                # si es semi, devolvemos las que tengan fecha de esntrada posterior al 9 de julio
                if reservation_timpestamp >= july_9:
                    #semi_output += f"\n \n Identifier: {reservation.get("identifier")} // Fecha de Reserva: {reservation.get("timestamp")} // Fecha de entrada: {reservation.get("startDate")} // Fecha del Cobro: {payment.get("timestamp")} // Importe Cobrado: {payment.get("amount")} // Politica de Tarifa: {rate.get("name")}"
                    # devolución aquí
                    payment_info = {
                        "reservation": reservation,
                        "payment": payment
                    }
                    list_to_refunds.append(payment_info)


            else:
                june_25 = datetime.strptime("2025-06-25 00:00:00", "%Y-%m-%d %H:%M:%S")

                if reservation_timpestamp >= june_25:
                    #output += f"\n \n Identifier: {reservation.get("identifier")} // Fecha de Reserva: {reservation.get("timestamp")} // Fecha de entrada: {reservation.get("startDate")} // Fecha del Cobro: {payment.get("timestamp")} // Importe Cobrado: {payment.get("amount")} // Politica de Tarifa: {rate.get("name")}"
                    # devolución aquí
                    # devolución aquí
                    payment_info = {
                        "reservation": reservation,
                        "payment": payment
                    }
                    list_to_refunds.append(payment_info)




        # De aquí para abajo no aplica a las reservas afectadas, lo dejo por si en un futuro sirve, pero no se ejecuta nunca
        elif cancellation_policy == "No cancelable":
            for name in ["FLEX", "PVP"]:
                if name in (rate.get("localName") or "").upper():
                    name_flex = True
                    break
            if name_flex:
                #output += f"\n \n Identifier: {reservation.get("identifier")} // Fecha de Reserva: {reservation.get("timestamp")} // Fecha de entrada: {reservation.get("startDate")} // Fecha del Cobro: {payment.get("timestamp")} // Importe Cobrado: {payment.get("amount")} // Politica de Tarifa: {rate.get("localName")}"
                output +=""
        if not name_flex:
            cancel_policy = calculate_cancel_policy_for_period(reservation, hotel_code, rate, condition_rates)
            if cancel_policy == "No cancelable":
                #comprobamos si se han cobrado 16 dias antes de lo que se debía
                reservation_timpestamp = reservation.get("startDate")
                payment_timestamp = payment.get("timestamp")
                fifteen_days = (reservation_timpestamp - timedelta(days=15)).strftime("%Y-%m-%d %H:%M:%S")

                if payment_timestamp > fifteen_days:
                    #output += f"\n \n Identifier: {reservation.get("identifier")} // Fecha de Reserva: {reservation.get("timestamp")} // Fecha de entrada: {reservation.get("startDate")} // Fecha del Cobro: {payment.get("timestamp")} // Importe Cobrado: {payment.get("amount")} // Politica de Tarifa: {cancellation_policy}"
                    output += ""
    print("TARIFAS FLEXIBLES \n")
    print(output)
    print("\n TARIFAS semi FLEXIBLES \n")
    print(semi_output)

    list_to_refunds_oks = []
    list_to_refunds_kos = []

    if list_to_refunds:

        body = { "send_customer_error_email":   "off",
                "send_modification_email": "off",
                "automatic_refund": "on",
                "comment_email": "Devolución automática por script"}

        print("RESERVAS A DEVOLVER")
        for payment_info in list_to_refunds:
            reservation = payment_info.get("reservation")
            payment = payment_info.get("payment")

            amount_to_be_refund = payment.get("amount")
            order_to_refund = payment.get("order")


            print("RESERVA: %s ORDER: %s" % (reservation.get("identifier"), order_to_refund))



            order = execute_refund_in_gateway(hotel_code, body, reservation, amount_to_be_refund,
                                              order_to_refund=order_to_refund,
                                              payment=payment)

            if not "ERROR" in order:
                amount_to_be_refund = float(amount_to_be_refund) * -1

                reservation_updated_id, total_payed_by_cobrador, total_extra_payed_by_cobrador = save_payment_info_in_reservation(
                    hotel_code, reservation,
                    amount_to_be_refund, order,
                     is_a_refund=True)

                extra_params = {}
                payment_type = PAYMENT_TYPE_REFUND
                extra_payment = False

                today_ts_txt = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                payment_params = {
                    "order": order,
                    "error": "",
                    "comments": "Automatic refund for error",
                    "type": payment_type,
                    "user": "Automatic refund",
                    "reservation_identifier": reservation.get("identifier"),
                    "amount": amount_to_be_refund,
                    "timestamp": today_ts_txt,
                    "extra_info": {},
                    "excluded_rules": None,
                    "extra_params": extra_params
                }

                save_payment_reservation(hotel_code, payment_params)

                list_to_refunds_oks.append({"identifier": reservation.get("identifier"), "order": order_to_refund, "amount": amount_to_be_refund})

                send_email = False
                if send_email:
                    comment_email = '''
                            Estimado cliente,
                            <br><br>
                            Debido a un error informático en la gestión de cobros automáticos ajenos a Bahía Serena, se han ejecutado cobros en fechas anticipadas a lo estipulado en las condiciones de reserva.
                            <br><br>
                            Por ello, procedemos a la devolución de este importe debidamente cobrado.
                            <br><br>
                            Aprovechamos la ocasión para pedirle disculpas por esta situación.
                            <br><br>
                            Quedamos a su disposición para cualquier duda que pueda surgirle.
                            <br><br>
                            Atentamente
                            <br>
                            '''


                    translations = language_utils.get_web_dictionary(False)

                    send_customer_email = True
                    send_hotel_email = False
                    send_modification_email = False
                    manual_payment = False
                    is_refund = True
                    avoid_sending_payment = False

                    response = send_payment_confirmation_emails(hotel_code, reservation.get('identifier'), comment_email,
                                                                send_customer_email, send_hotel_email, send_modification_email,
                                                                amount_to_be_refund, order, manual_payment, is_refund,
                                                                avoid_sending_payment, translations, reservation=reservation)
            else:
                list_to_refunds_kos.append({"identifier": reservation.get("identifier"), "order": order_to_refund, "amount": amount_to_be_refund})



    print("RESERVAS DEVUELTAS")
    for payment_info in list_to_refunds_oks:
        print("RESERVA OK: %s ORDER: %s: AMOUNT%s" % (payment_info.get("identifier"), payment_info.get("order"), payment_info.get("amount")))

    print("RESERVAS NO DEVUELTAS")
    for payment_info in list_to_refunds_kos:
        print("RESERVA KOS: %s ORDER: %s: AMOUNT%s" % (payment_info.get("identifier"), payment_info.get("order"),
                                                      payment_info.get("amount")))




