import base64

from flask import json
import xml.etree.ElementTree as xee
from paraty.pages.cobrador.cobrador import get_integration_cobrador_name
from paraty.pages.scripts.diary_payments_checker import get_ok_sermepa
from paraty_commons_3.common_data.common_data_provider import get_all_integrations_configuration_of_hotel
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params

def get_ok (audit):
    #filtramos para que coja solo los de sermepa, porque ha pasado algo raro con addons
    sermepa_audit = [x for x in audit if "sermepa" in x.get('gateway_type').lower()]

    # Getting audit response
    if audit:
        for single_audit in sermepa_audit:
            audited_response = single_audit.get('response')
            if isinstance(audited_response, bytes):
                audited_response = json.loads(bytearray(audited_response))
                response = audited_response.get('Ds_Response')
            else:
                try:
                    encrypted_response = json.loads(audited_response).get('Ds_MerchantParameters')
                    decrypted_response = json.loads(bytearray(base64.b64decode(encrypted_response)))
                    response = decrypted_response.get('Ds_Response')
                except Exception as e:
                    # xml
                    try:
                        tree = xee.fromstring(audited_response)
                        request_data = tree.findall(".//XML")
                        message = request_data[0].text
                        response_data = xee.fromstring(message)
                        request_json = response_data.findall(".//Request")
                        request_json = request_json[0]
                        ds_error_code = request_json.findall('Ds_ErrorCode')
                        if len(ds_error_code) > 0:
                            continue
                        response = request_json.findall('Ds_Response')
                        response = response[0].text
                    except:
                        print("error")
                        continue

            # Test ok - response
            if response and int(response) < 100:
                # sermepa valid payment codes [0-99]
                return "ok"

    return "ko"


if __name__ == "__main__":
    audit_call = get_using_entity_and_params("AuditResponse",
                                             search_params=[("payment_order_id", "=", "93625360")],
                                             hotel_code="payment-seeker:")

    get_ok(audit_call)

