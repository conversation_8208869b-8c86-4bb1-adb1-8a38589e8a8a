import logging
import traceback

from flask import request, send_from_directory
from flask_cors import cross_origin
from werkzeug.exceptions import HTTPException

from paraty import app
from paraty.pages.cobrador.gateways.multibanco import MultibancoWebhookController, MultibancoCheckController, GenericWebhookController
from paraty.pages.cobrador.gateways.sibs import SIBSControlForm
from paraty.pages.cobrador.gateways.webhooks import Refund<PERSON>ebhookController
from paraty_commons_3.decorators.cache import timebased_cache, cache_controller


@app.errorhandler(Exception)
def handle_exception(e):
    # pass through HTTP errors
    logging.error(e)
    try:
        logging.error(traceback.format_exc())
    except Exception as e:
        pass
    if isinstance(e, HTTPException):
        return e

    # now you're handling non-HTTP exceptions only

    return 'KO'
    # return render_template("500_generic.html", e=e), 500


@app.route('/healthcheck')
def health_check():
    return 'OK'


@app.route('/clean_cache')
def clean_cache_endpoint():
    logging.info("Cleaning Cache")
    timebased_cache.clear_cache()
    return "OK"

@app.route('/clean_cache_all')
def clean_cache_endpoint_all():
    logging.info("Cleaning Cache")
    timebased_cache.clear_cache_all()
    return "OK"


@app.route('/clean_cache_specific')
def clean_specific_cache():
    hotel_code = request.args.get("hotel_code")
    cache_controller.invalidate_cache(hotel_code)
    return "OK"

@app.route("/external/<path:name>")
@cross_origin(origin='*')
def static_file_with_cors(name):
    for valid_file in ["payment_widget.js"]:
        if valid_file in request.path:
            response = send_from_directory("static", name)
            return response
    return b"", 404


app.add_url_rule("/sibs/check_error_fields", view_func=SIBSControlForm.as_view("sibs_check_error_fields"))
app.add_url_rule("/multibanco/webhook", view_func=MultibancoWebhookController.as_view("webhook_multibanco"))
app.add_url_rule("/multibanco/check_reservation", view_func=MultibancoCheckController.as_view("webhook_check"))
app.add_url_rule("/epayments/webhook", view_func=GenericWebhookController.as_view("generic_webhook_check"))
app.add_url_rule("/refund/webhook", view_func=RefundWebhookController.as_view("refund_webhook"))

