from abc import ABCMeta, abstractmethod


class BasicTestUpdate(metaclass=ABCMeta):
	'''
	Tests to be performed by any integration that needs to update information from the channel.
	One test for each of the main use cases.
	'''

	@abstractmethod
	def test_happy_flow(self):
		'''
		Perform a simple call and verify that no exceptions have ocurred
		:return:
		'''
		raise NotImplementedError

	@abstractmethod
	def test_set_availability_to_0(self):
		'''
		Check that the availability is correctly set to 0
		:return:
		'''
		raise NotImplementedError

	@abstractmethod
	def test_multiple_updates_executed_in_correct_order(self):
		'''
		Check that multiple updates in the same call produce the expected result
		'''
		raise NotImplementedError

	@abstractmethod
	def test_close_rate_board(self):
		raise NotImplementedError

	@abstractmethod
	def test_open_rate_board(self):
		raise NotImplementedError

	@abstractmethod
	def test_change_min_stay(self):
		raise NotImplementedError

	@abstractmethod
	def test_change_availability(self):
		raise NotImplementedError

	@abstractmethod
	def test_change_price(self):
		raise NotImplementedError

	@abstractmethod
	def test_change_multiple_rooms(self):
		raise NotImplementedError

	@abstractmethod
	def test_invalid_room_code(self):
		raise NotImplementedError

	@abstractmethod
	def test_invalid_rate_code(self):
		raise NotImplementedError