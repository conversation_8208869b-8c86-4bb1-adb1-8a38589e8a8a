from datetime import datetime
import time
from unittest.mock import MagicMock, patch, Mock

from tests.test_base import BaseTest

from model.audit import EndpointCallAuditEvent

from handlers import redo_handler
from paraty.integration import modify_prices_batch
from paraty.utils.date_utils import datetime_to_string


class RedoHandlerTest(BaseTest):
    @staticmethod
    def _build_simulated_audit_event():
        xml_simulations = [TEST_PRICE_MODIFY_CALL_ROOM_1, TEST_PRICE_MODIFY_CALL_ROOM_1_CLOSED, TEST_PRICE_MODIFY_CALL_ROOM_1_MIXED, TEST_PRICE_MODIFY_CALL_ROOM_2, TEST_PRICE_MODIFY_CALL_ROOM_2_CLOSED, TEST_PRICE_MODIFY_CALL_ROOM_2_MIXED]
        call_audit_events = []
        for xml_element in xml_simulations:
            call_audit_event = EndpointCallAuditEvent()
            call_audit_event.request = xml_element
            call_audit_event.timestamp = datetime.now()
            call_audit_events.append(call_audit_event)

        return call_audit_events

    @patch('handlers.redo_handler.send_notification_to_bug_seeker')
    @patch('handlers.redo_handler.notifyExceptionByEmail')
    @patch('handlers.redo_handler.email_utils')
    @patch('handlers.redo_handler.__get_operations_by_hotel')
    def _test_performance(self, *args):
        '''
        test_performance
        Does the execution work in the expected time?

        # TODO: This a specific test by adapter, XML from a specific adapter has been hardcoded, this can't be done in generic tests
        '''

        call_audit_events = self._build_simulated_audit_event()
        args[0].return_value = call_audit_events

        MAX_TIME = 2
        timestamp = datetime.now()
        start = time.time()
        ok = redo_handler._redo_operations_from_date("yield-planet1", datetime_to_string(timestamp, "%Y-%m-%d %H:%M:%S"))
        self.assertTrue(ok)
        end = time.time()

        # Performance is reasonable?
        self.assertTrue((end - start) < MAX_TIME)

    @patch('handlers.redo_handler.IntegrationDataCache')
    @patch('handlers.redo_handler.__get_operations_by_hotel', return_value=[])
    def test_no_operations(self, *args):
        """
        test_no_operations
        Does the execution work if there are no operations to process?
        """
        ok = redo_handler._redo_operations_from_date("yield-planet1", datetime_to_string(datetime.now(), "%Y-%m-%d %H:%M:%S"))
        self.assertTrue(ok)
        args[1].assert_not_called()


    @patch('handlers.redo_handler.perform_bigquery_operations')
    def _test_redo_operation_in_memory(self, *args):
        """
        test_redo_operation_in_memory
        Is the expected call performed to execute_modify_prices_batch?

        # TODO: This is specific by adapter, cant be interpreted as a generic test
        """

        cache = {}
        operation = EndpointCallAuditEvent(request=TEST_PRICE_MODIFY_CALL_ROOM_1_CLOSED)
        modify_prices_batch.execute_modify_prices_batch = MagicMock()

        redo_handler._redo_operation_in_memory(operation, cache, 'yield-planet1')

        calls = modify_prices_batch.execute_modify_prices_batch.mock_calls
        self.assertEqual(1, len(calls))
        self.assertEqual("yield-planet1", calls[0][1][0])

        availability_items_in_call = calls[0][1][1]

        self.assertEqual(9, len(availability_items_in_call))
        self.assertEqual(cache, calls[0][1][5])


    @patch('handlers.redo_handler.IntegrationDataCache.save_data_to_datastore')
    @patch('handlers.redo_handler._redo_operation_in_memory')
    @patch('handlers.redo_handler.get_hotel_by_application_id')
    @patch('handlers.redo_handler.__get_operations_by_hotel')
    def test_operations_executed_in_expected_order(self, *args):
        """
        test_operations_executed_in_expected_order
        Execute redo_operations_from_date
        Are the operations executed in the expected order?
        """

        args[0].return_value = self._build_simulated_audit_event()
        args[2].return_value = (None, None)

        ok = redo_handler._redo_operations_from_date("yield-planet1", datetime_to_string(datetime.now(), "%Y-%m-%d %H:%M:%S"))
        self.assertTrue(ok)

        timestamps = []
        for call_element in args[2].call_args_list:
            timestamps.append(call_element.args[0].timestamp)

        self.assertEqual(sorted(timestamps, reverse=True), timestamps)


TEST_PRICE_MODIFY_CALL_ROOM_1 = '''<?xml version="1.0" encoding="UTF-8"?>
<Request userName="park-plaza" password="demo">
   <modify hotelId="yield-planet1" rateId="6243983994912768_5072753191288832" startDate="2025-08-14" endDate="2025-08-23">
	  <availability day="2025-08-14" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="3" status="open" price="29.93" />
	  <availability day="2025-08-15" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-16" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-17" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-18" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-19" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-20" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="open" />
	  <availability day="2025-08-21" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="open" />
   </modify>
</Request>'''

TEST_PRICE_MODIFY_CALL_ROOM_1_CLOSED = '''<?xml version="1.0" encoding="UTF-8"?>
<Request userName="park-plaza" password="demo">
   <modify hotelId="yield-planet1" rateId="6243983994912768_5072753191288832" startDate="2025-08-14" endDate="2025-08-23">
	  <availability day="2025-08-14" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="3" status="closed" />
	  <availability day="2025-08-15" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-16" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-17" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-18" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-19" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-20" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="closed" />
	  <availability day="2025-08-21" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="closed" />
	  <availability day="2025-08-22" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="7" status="closed" />
   </modify>
</Request>'''

TEST_PRICE_MODIFY_CALL_ROOM_1_MIXED = '''<?xml version="1.0" encoding="UTF-8"?>
<Request userName="park-plaza" password="demo">
   <modify hotelId="yield-planet1" rateId="6243983994912768_5072753191288832" startDate="2025-08-14" endDate="2025-08-23">
	  <availability day="2025-08-14" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="3" status="open" />
	  <availability day="2025-08-15" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-16" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-17" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-18" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-19" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-20" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="open" />
	  <availability day="2025-08-21" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="closed" />
	  <availability day="2025-08-22" roomId="6197422522892288" rateId="6243983994912768_5072753191288832" roomRateId="6197422522892288_6243983994912768_5072753191288832" currency="EUR" quantity="7" status="open" />
   </modify>
</Request>'''

TEST_PRICE_MODIFY_CALL_ROOM_2 = '''<?xml version="1.0" encoding="UTF-8"?>
<Request userName="park-plaza" password="demo">
   <modify hotelId="yield-planet1" rateId="6243983994912768_5072753191288832" startDate="2025-08-14" endDate="2025-08-23">
	  <availability day="2025-08-14" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="3" status="open" />
	  <availability day="2025-08-15" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-16" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-17" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-18" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-19" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-20" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="open" />
	  <availability day="2025-08-21" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="open" />
	  <availability day="2025-08-22" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="7" status="open" />
   </modify>
</Request>'''

TEST_PRICE_MODIFY_CALL_ROOM_2_CLOSED = '''<?xml version="1.0" encoding="UTF-8"?>
<Request userName="park-plaza" password="demo">
   <modify hotelId="yield-planet1" rateId="6243983994912768_5072753191288832" startDate="2025-08-14" endDate="2025-08-23">
	  <availability day="2025-08-14" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="3" status="closed" />
	  <availability day="2025-08-15" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-16" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-17" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-18" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-19" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-20" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="closed" />
	  <availability day="2025-08-21" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="closed" />
	  <availability day="2025-08-22" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="7" status="closed" />
   </modify>
</Request>'''

TEST_PRICE_MODIFY_CALL_ROOM_2_MIXED = '''<?xml version="1.0" encoding="UTF-8"?>
<Request userName="park-plaza" password="demo">
   <modify hotelId="yield-planet1" rateId="6243983994912768_5072753191288832" startDate="2025-08-14" endDate="2025-08-23">
	  <availability day="2025-08-14" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="3" status="open" />
	  <availability day="2025-08-15" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-16" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-17" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-18" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="open" />
	  <availability day="2025-08-19" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="4" status="closed" />
	  <availability day="2025-08-20" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="open" />
	  <availability day="2025-08-21" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="5" status="closed" />
	  <availability day="2025-08-22" roomId="5656058538229760" rateId="6243983994912768_5072753191288832" roomRateId="5656058538229760_6243983994912768_5072753191288832" currency="EUR" quantity="7" status="open" />
   </modify>
</Request>'''
