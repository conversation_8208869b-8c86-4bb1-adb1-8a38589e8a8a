import os
import unittest

os.environ['UNIT_TEST_EXECUTION'] = "true"


import interface_to_implement
if not interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE:
    # If we are executing unit test of base-integration-v3, we dont care about anything else, so everything is reset
    os.environ['INTEGRATION_NAME'] = ''
    from tests.test_specific_integration.test_integration_interface import TestIntegrationInterface
    interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE = TestIntegrationInterface()
    import routes
    routes.build_routes()

from paraty import app, Config


class BaseTest(unittest.TestCase):
    def setUp(self):
        self.app = app
        self.testapp = app.test_client()
        Config.TESTING = True
