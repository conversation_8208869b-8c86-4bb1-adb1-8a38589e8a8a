from unittest.mock import patch

from paraty.utils.creadit_card_utils import extract_extra_info_ccdata
from tests.test_base import BaseTest


class TestCreditCardUtils(BaseTest):

    @patch('paraty.utils.creadit_card_utils.get_hotel_advance_config_item')
    def test_extract_extra_info_ccdata(self, *args):
        simulated_configs = {
            "Save CC datas": [{'value': 'true'}],
            "Password Tarjetas": [{'value': 'secure80'}]
        }
        args[0].side_effect = lambda hotel, config_name: simulated_configs[config_name]

        demo_dict = {}
        hotel = {'applicationId': 'demo'}
        reservation_simulation = {'extraInfo': '{"cc_datas": "CgySj0fpCXi70rFMrXgp4w=="}'}
        results = extract_extra_info_ccdata(reservation_simulation, hotel, demo_dict)

        expected_cc_datas = {'cc_company': 'MC', 'cc_cvv': None, 'cc_expired': '1230', 'cc_holder': ' ', 'cc_number': '0000000000000042', 'currency_code': 'EUR'}
        for key, value in results['cc_datas'].items():
            self.assertEqual(expected_cc_datas[key], value)