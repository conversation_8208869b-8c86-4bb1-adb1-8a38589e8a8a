from unittest.mock import patch

from tests.test_base import BaseTest

from paraty.utils.queues.queues_utils import defer

def example_defer_function(*args, **kwargs):
    pass

class TestQueuesUtils(BaseTest):
    @patch('paraty.utils.queues.queues_utils.Config.DEV', new=False)
    @patch('paraty.utils.queues.queues_utils.queue_utils.create_task')
    def test_task_creation(self, *args):
        defer(example_defer_function, "argument1", "argument2", countdown=5, queue_name='test', task_name='task')

        with self.subTest('Task is created'):
            args[0].assert_called_once()

        with self.subTest('Task is created with correct params'):
            params = args[0].call_args[1]
            self.assertTrue(params['payload'])
            self.assertEqual(params['in_seconds'], 5)
            self.assertEqual(params['queue_name'], 'test')
            self.assertEqual(params['task_name'], 'task')
