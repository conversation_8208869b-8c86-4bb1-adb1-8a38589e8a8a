from google.cloud.exceptions import NotFound

from tests.test_base import BaseTest

from unittest.mock import patch, Mock
from handlers.bigquery_handler import <PERSON>QueryHandler
from handlers.redo_handler import perform_bigquery_operations


class TestBigQueryHandler(BaseTest):
    @patch('handlers.bigquery_handler.bigquery.Client')
    def test_create_table_for_hotel(self, MockClient):
        mock_client = MockClient.return_value
        mock_table = Mock()
        mock_client.get_table.side_effect = NotFound("Table not found")
        mock_client.create_table.return_value = mock_table

        handler = BigQueryHandler('unittest')
        handler.create_table_for_hotel("test_hotel_code")

        mock_client.create_table.assert_called_once()

    @patch('handlers.bigquery_handler.bigquery.Client')
    def test_insert_data_into_table(self, MockClient):
        mock_client = MockClient.return_value

        handler = BigQueryHandler('unittest')
        availability_items = [{"day": "2023-10-27", "roomId": 1}]
        handler.insert_data_into_table("test_hotel_code", availability_items)

        mock_client.insert_rows_json.assert_called_once()

    @patch('handlers.redo_handler.interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name')
    @patch('handlers.redo_handler.BigQueryHandler')
    def test_perform_bigquery_operations(self, *args):
        mock_handler = args[0].return_value

        from paraty.constants.audit import YIELDPLANET
        args[1].return_value = YIELDPLANET

        perform_bigquery_operations("test_hotel_code", [])

        mock_handler.create_table_for_hotel.assert_called_once_with("test_hotel_code")
        mock_handler.insert_data_into_table.assert_called_once_with("test_hotel_code", [])
