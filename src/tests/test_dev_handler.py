import hashlib
import logging
from unittest.mock import patch

from tests.test_base import BaseTest

from model.audit import EndpointCallAuditEvent

from model import audit


class DevHandlerTest(BaseTest):
    '''
    - Create several entities
    - Perform a request
    - Is the result the expected one?
    '''

    @patch('handlers.dev_handler.get_audit_logs')
    def test_rest(self, *args):
        simulated_entities = self._create_test_data()

        hotel_code = "test"
        offset = "10"
        startDate = "2017-01-01"
        num_elements = "5"

        query = "/dev?action=rest&hotel=" + hotel_code + "&offset=" + offset + "&startDate=" + startDate + "&numElements=" + num_elements

        from_index = int(offset) * int(num_elements)
        to_index = from_index + int(num_elements)
        args[0].return_value = simulated_entities[from_index:to_index]

        response = self.testapp.get('/dev?action=rest&%s' % query)

        obtained_entities = response.json

        self.assertEqual(len(obtained_entities), int(num_elements))
        for i, result in enumerate(obtained_entities):
            target_match = simulated_entities[from_index + i]
            self.assertEqual(result['request'], target_match.request)

    def test_show_mapping_for_humans(self):
        session = hashlib.md5(("%s_@_%s" % ('best', 'best-siroco')).encode('utf-8')).hexdigest()
        url = '/dev?action=showmapping&hotel_code=best-siroco&user=best&language=SPANISH&session=%s' % session
        logging.info(url)
        response = self.testapp.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertGreater(len(response.text), 150)

    @patch('handlers.dev_handler.EndpointCallAuditEvent')
    def test_show_audit(self, *args):
        audit = self._add_audit_event()
        args[0].query().get.return_value = audit
        response = self.testapp.get('/dev?action=showaudit&request_id=%s' % audit.request_id)
        self.assertEqual(audit.request, response.text)

    def _test_show_data(self):
        # TODO: What is supposed to test this? commented until I talk with fmatheis
        response = self.testapp.get('/dev?action=showdata&rate_id=5075236890345472&board_id=5072753191288832&&room_id=5656058538229760&&date=2020-07-08&hotel_code=yield-planet1')

    def _add_audit_event(self):
        audit = EndpointCallAuditEvent()
        audit.response = "TEST"
        audit.request = "EUREKA"
        audit.request_id = 'PRUEBA-PRUEBA-PRUEBA-PRUEBA'
        return audit

    def _create_test_data(self, max_entities=100):
        entities_to_add = []
        for i in range(0, max_entities):
            new_entity = audit.EndpointCallAuditEvent()
            new_entity.request = "Test Request: %s" % i
            new_entity.hotel_code = "test"
            new_entity.path = '/prices/modify'
            entities_to_add.append(new_entity)

        return entities_to_add
