from datetime import datetime
from unittest.mock import patch, <PERSON><PERSON>

import pandas as pd

from tests.test_base import BaseTest


class SaveLogsHandlerTest(BaseTest):
    @patch('handlers.log_handler.ndb')
    @patch('handlers.log_handler.LogBackup')
    @patch('handlers.log_handler.get_logs_in_period')
    def test_save_log(self, *args):
        log_demo = {
            'payload': 'demo content log',
            'timestamp': datetime.now()
        }
        args[0].return_value = pd.DataFrame([log_demo])

        mock_query = Mock()
        mock_query.fetch.return_value = ['mocked_key_1', 'mocked_key_2']
        args[1].date.__le__ = lambda self, other: 'demo' <= other

        args[1].query.return_value = mock_query
        args[2].side_effect = Mock()

        list_query_params = [
            # 'read_date=2022-07-02'
            'log_level=INFO',
            # "status=200",
            "hours=24",
            # "hosts=vertical,tro",
            'end_time=2024-11-05 00:00:00',
            # "path=restriction"
        ]

        query_param = '?' + '&'.join(list_query_params)

        url = '/gae_logs/save_logs/' + query_param
        response = self.testapp.get(url)

        self.assertEqual(response.status_code, 200)
