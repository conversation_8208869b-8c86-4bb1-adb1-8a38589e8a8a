# -*- coding: utf-8 -*-
from tests.test_base import BaseTest


class AuditServiceTest(BaseTest):

	def _test_happy_flow(self):
		#Choose what integration you want to test here
		# from siteminder import siteminder_integration

		search_filter = {
			'max_days': 1,
			'rates': [],
			'boards': [],
			'rooms': [],
			'price_day': '',
			'only_last': '',
			'production_url': 'https://new1-dot-siteminder-adapter.appspot.com/'
		}

		from paraty.audit import audit_service
		audit_service.find_operations("test_audit_id", "hotel-don-pancho", search_filter)

