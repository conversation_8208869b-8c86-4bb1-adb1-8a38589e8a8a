from unittest.mock import patch, Mock

from tests.test_base import BaseTest
from handlers.audit.clean_audit import clean_old_audits, SIZE_PAGE


class CleanAuditTest(BaseTest):
    @patch('handlers.audit.clean_audit.ndb.delete_multi_async')
    @patch('handlers.audit.clean_audit.EndpointCallAuditEvent.query')
    def test_stops_cleaning_when_max_to_delete_is_reached(self, mock_event, *args):
        mock_query = Mock()
        mock_query.fetch_page.side_effect = [
            ([Mock()] * SIZE_PAGE, None, True),
            ([Mock()] * SIZE_PAGE, None, True),
            ([<PERSON>ck()] * SIZE_PAGE, None, False)
        ]
        mock_event.return_value = mock_query

        clean_old_audits(max_to_delete=SIZE_PAGE * 2)

        assert mock_query.fetch_page.call_count == 2

    @patch('handlers.audit.clean_audit.ndb.delete_multi_async')
    @patch('handlers.audit.clean_audit.EndpointCallAuditEvent.query')
    def test_handles_no_audits_to_clean(self, mock_event, *args):
        mock_query = Mock()
        mock_query.fetch_page.return_value = ([], None, False)
        mock_event.return_value = mock_query

        clean_old_audits()

        assert mock_query.fetch_page.call_count == 1
