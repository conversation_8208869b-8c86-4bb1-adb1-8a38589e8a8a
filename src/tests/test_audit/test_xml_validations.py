from paraty.audit.verifications.xml_consolidation_validator import validate_xml_consolidation_from_n_time
from tests.test_base import BaseTest


class TestXMLValidations(BaseTest):
    """
    THIS TEST CAN BE USED AS BASELINE, BUT NEED TO BE IN EACH ADAPTER TESTS!
    """
    def _test_xml_consolidation_against_datastore(self, *mocked):
        with self.app.app_context():
            # Its x100 faster if you specify the hotel_code (ie. retreive hotel-codes before processing)
            # is_valid = validate_xml_consolidation_from_n_time(days=10, hotel_code='solvasa-laurisilva')
            is_valid = validate_xml_consolidation_from_n_time(days=0, hours=5)

        self.assertTrue(is_valid, "The XML consolidation validation should be valid.")
