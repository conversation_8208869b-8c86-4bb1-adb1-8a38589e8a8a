from unittest.mock import patch, Mock

from tests.test_base import BaseTest
from paraty.audit.audit_utils import split_str_in_chunks, audit

TEST_RESPONSE = '''

<?xml version="1.0" encoding="UTF-8"?>
<FHDT_ConfigurationRS xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <PropertyCode>flavia</PropertyCode>
    <RoomTypes Lang="en">

        <RoomType Code="4825119268536320">Habitación Familiar con baño privado</RoomType>
        <RoomType Code="5139717033033728">Habitación Individual Estándar</RoomType>
        <RoomType Code="5196459188158464">Habitación Cuádruple Estándar</RoomType>
        <RoomType Code="5651124426113024">Habitación Doble - 2 camas</RoomType>
        <RoomType Code="5700305828184064">Habitación Doble Estándar</RoomType>
        <RoomType Code="5734743547052032">Habitación Individual</RoomType>
        <RoomType Code="5937533514940416">Habitación Triple Estándar</RoomType>
    </RoomTypes>
    <BoardTypes>

        <BoardType Code="6005635590127616">Alojamiento y desayuno</BoardType>
    </BoardTypes>
    <RateTypes Lang="en">

        <RateType Code="4891571002540032">TARIFA FLEXIBLE</RateType>
        <RateType Code="6603834071711744">TARIFA NO REEEMBOLSABLE</RateType>
    </RateTypes>
</FHDT_ConfigurationRS>

'''

class AuditTest(BaseTest):
    def test_split_string_in_chunks(self):
        text = '1234'
        chunks = split_str_in_chunks(text, 2)

        self.assertEqual(2, len(chunks))
        self.assertEqual('12', chunks[0])
        self.assertEqual('34', chunks[1])

    @patch('paraty.audit.audit_utils.EndpointCallAuditEvent')
    def test_non_ascii_characters(self, *args):
        mocked_entity = Mock()
        args[0].return_value = mocked_entity
        audit("TEST", "/", "kjkjkjkjkjk", TEST_RESPONSE, "test-hotel")

        self.assertEqual(TEST_RESPONSE, mocked_entity.response)
        self.assertTrue(mocked_entity.put.called)
