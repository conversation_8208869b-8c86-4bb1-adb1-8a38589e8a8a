from unittest.mock import patch, MagicMock

from tests.test_base import BaseTest

def _fake_my_is_hotel_integrated(hotel_code):
	if hotel_code == 'test-without-integration':
		return False, []

	if hotel_code == 'test-with-availpro':
		return False, ['Availpro']

	if hotel_code == 'test-with-unknown-integration':
		return False, ['Availpro', 'Siteminder']

def _fake_get_hotel(hotel_code):
	return {'name': hotel_code}


def _fake_get_all_hotels():
	return {
		'test-without-integration': {'name': 'test-without-integration', 'applicationId':'test-without-integration', 'enabled': 'True'},
		'test-with-availpro': {'name': 'test-with-availpro', 'applicationId':'test-with-availpro', 'enabled': 'True'},
		'test-with-unknown-integration': {'name': 'test-with-unknown-integration', 'applicationId':'test-with-unknown-integration', 'enabled': 'True'}
	}


class Audit1HandlerTest(BaseTest):

	@patch('handlers.audit.audit1_handler.hotel_manager_utils.get_all_hotels', MagicMock(side_effect=_fake_get_all_hotels))
	def test_render_shows_all_hotels_and_error(self):
		response = self.testapp.get('/audit1?error=TestMessage')
		self.assertIn('TestMessage', response.text)


	@patch('handlers.audit.audit1_handler.hotel_manager_utils.get_all_hotels', MagicMock(side_effect=_fake_get_all_hotels))
	@patch('handlers.audit.audit1_handler.hotel_manager_utils.get_hotel_by_application_id', MagicMock(side_effect=_fake_get_hotel))
	@patch('paraty.development.new_audit_service.is_hotel_integrated', MagicMock(side_effect=_fake_my_is_hotel_integrated))
	def test_hotel_without_integration_redirects_back(self):
		response = self.testapp.post('/audit1?hotel_name=test-without-integration')

		self.assertEqual(302, response.status_code)
		self.assertIn('audit1', response.location)
		self.assertIn('no parece estar integrado', response.text)

	@patch('handlers.audit.audit1_handler.hotel_manager_utils.get_all_hotels', MagicMock(side_effect=_fake_get_all_hotels))
	@patch('handlers.audit.audit1_handler.hotel_manager_utils.get_hotel_by_application_id', MagicMock(side_effect=_fake_get_hotel))
	@patch('paraty.development.new_audit_service.is_hotel_integrated', MagicMock(side_effect=_fake_my_is_hotel_integrated))
	def test_hotel_with_integration_redirects_to_next_step(self):
		response = self.testapp.post('/audit1?hotel_name=test-with-availpro')

		self.assertEqual(302, response.status_code)
		self.assertIn('hotel_code=test-with-availpro', response.location)
		self.assertIn('audit2', response.text)

	@patch('handlers.audit.audit1_handler.hotel_manager_utils.get_all_hotels', MagicMock(side_effect=_fake_get_all_hotels))
	@patch('handlers.audit.audit1_handler.hotel_manager_utils.get_hotel_by_application_id', MagicMock(side_effect=_fake_get_hotel))
	@patch('paraty.development.new_audit_service.is_hotel_integrated', MagicMock(side_effect=_fake_my_is_hotel_integrated))
	def test_hotel_with_unknown_integration_redirects_back_with_error_message(self):
		response = self.testapp.post('/audit1?hotel_name=test-with-unknown-integration')

		self.assertEqual(302, response.status_code)
		self.assertIn('audit1', response.location)
		self.assertIn('no parece estar integrado en este servidor, integraciones', response.text)



