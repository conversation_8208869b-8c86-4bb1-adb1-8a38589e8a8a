from unittest.mock import patch, Mock

from tests.test_base import BaseTest


class Audit2HandlerTest(BaseTest):

    @patch('handlers.audit.audit2_handler.get_rooms_of_hotel')
    @patch('handlers.audit.audit2_handler.get_boards_of_hotel')
    @patch('handlers.audit.audit2_handler.get_rates_of_hotel')
    @patch('handlers.audit.audit2_handler.datastore_utils.alphanumeric_to_id')
    @patch('handlers.audit.audit2_handler.hotel_manager_utils.get_hotel_by_application_id')
    def test_expected_information_is_shown(self, *args):
        """
        Are we showing the expected rates, rooms, boards, capacities?
        """
        args[0].return_value = {'applicationId': 'test-hotel'}
        args[1].return_value = 123123
        args[2].return_value = [{'key': 'test-key', 'localName': 'PVP'}]
        args[3].return_value = [{'key': 'test-key', 'name': 'Alojamiento'}]
        args[4].return_value = [{'key': 'test-key', 'name': 'individual', 'capacities': ['2-0-0']}]

        response = self.testapp.get('/audit2?hotel_code=test-hotel')

        self.assertEqual(200, response.status_code)
        self.assertIn('PVP', response.text)
        self.assertIn('individual', response.text)
        self.assertIn('Alojamiento', response.text)
        self.assertIn('2-0-0', response.text)

    @patch('handlers.audit.audit2_handler.requests.post')
    def test_search_is_correctly_created(self, *args):
        body = {
            'hotel_code': 'test-hotel',
            'max_days': 5,
            'rates': [],
            'rooms': [],
            'boards': [],
            'priceday': '',
            'only_last': 'False',
            'integration': 'availpro'
        }

        args[0].return_value = Mock(status_code=500, text='{"status": "bad"}')
        response = self.testapp.post('/audit2?', data=body)
        self.assertEqual(response.status_code, 200)
        self.assertIn('Bad response from audit assistant', response.text)

        args[0].return_value = Mock(status_code=200, text='{"status": "ok"}')
        response = self.testapp.post('/audit2?', data=body)
        self.assertEqual(response.status_code, 302)
        self.assertIn('/audit3', response.location)
