import hashlib
import time
from unittest.mock import patch, Mock

from tests.test_base import BaseTest


TEST_PRICE_MODIFY_CALL = '''
<?xml version="1.0" encoding="UTF-8"?>
<Request userName="park-plaza" password="demo">
   <modify hotelId="4002" rateId="5634371100868608_6446483649855488" startDate="2015-08-14" endDate="2015-08-23">
	  <availability day="2015-08-14" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="3" status="open" />
	  <availability day="2015-08-15" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="4" status="open" />
	  <availability day="2015-08-16" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="4" status="open" />
	  <availability day="2015-08-17" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="4" status="open" />
	  <availability day="2015-08-18" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="4" status="open" />
	  <availability day="2015-08-19" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="4" status="open" />
	  <availability day="2015-08-20" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="5" status="open" />
	  <availability day="2015-08-21" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="5" status="open" />
	  <availability day="2015-08-22" roomId="5018959195668480" rateId="5634371100868608_6446483649855488" roomRateId="5018959195668480_5634371100868608_6446483649855488" currency="EUR" quantity="7" status="open" />
   </modify>
</Request>
'''

class PriceHandlerTest(BaseTest):
	@patch('handlers.base_handler.audit')
	@patch('handlers.base_handler.render_result')
	@patch('handlers.prices_handler.defer')
	@patch('handlers.base_handler.get_user_by_name')
	def test_happy_flow(self, user_mock, create_task_mock, ok_results_mock, audit_mock):
		password = 'testPassword'
		hashed_password = hashlib.md5(password.encode('utf-8')).hexdigest()
		user_mock.return_value = Mock(enabled=True, password=hashed_password)
		ok_results_mock.return_value = 'ok'

		MAX_TIME = 5
		start = time.time()
		self.testapp.post('/prices/modify', data=TEST_PRICE_MODIFY_CALL)
		end = time.time()

		with self.subTest('Task has been created correctly'):
			create_task_mock.assert_called_once()

		with self.subTest('Performance is correct'):
			# This has more sense at specific adapter instead of here
			self.assertTrue((end-start) < MAX_TIME)

		with self.subTest('Audit is called with correct params'):
			args, kwargs = audit_mock.call_args
			audit_mock.assert_called_once()
			self.assertEqual(TEST_PRICE_MODIFY_CALL, args[2])

		with self.subTest('Response is correct'):
			self.assertEqual('ok', ok_results_mock.return_value)
