import interface_to_implement
from interface_to_implement import SpecificIntegration

DATE_FORMAT = '%Y-%m-%d'

class TestIntegrationInterface(SpecificIntegration):
	'''
	Example for tests purposes
	'''

	def validate_update_request(self, hotel_id, xml_finder, request_body):
		pass

	def gets_additional_paths(self):
		return []

	def get_modify_url(self):
		return '/prices/modify'

	def get_hotel_id(self, request, request_id):
		return 'yield-planet1'

	def get_availability_items(self, operation, request_id, queue_name=None):

		from paraty.utils.xml_utils import XmlFinder

		xml = operation.request
		xml_finder = XmlFinder(xml)

		availability_items = list(map(_create_availability_item, xml_finder.find_elements('availability')))
		return availability_items

	def get_integration_name(self):
		return "test_integration"

	def _get_username_and_password(self, request):
		return "test", 'testPassword'

	def _handle_exception(self, handler, exception, debug):
		#Shit in, shit out
		raise exception

	def get_response_extra_params(self, handler):
		return {
		}

interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE = TestIntegrationInterface()

def _create_availability_item(availability_tag):
	# attributes are in lowercase
	# attributes 'roomRateId' and 'currency' are ignored

	rate_id, board_id = availability_tag['rateid'].split("_")
	capacity = '2-0-0'

	return {
		'day': availability_tag['day'],
		'roomId': availability_tag['roomid'],
		'rateId': rate_id,
		'price': availability_tag.get('price'),
		'minimumStay': availability_tag.get('minimumstay'),
		'quantity': availability_tag.get('quantity'),
		'status': availability_tag.get('status'),
		'capacity': capacity,
		'boardId': board_id,
	}
