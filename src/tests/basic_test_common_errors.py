from abc import ABCMeta, abstractmethod


class BasicTestCommonErrors(metaclass=ABCMeta):
	'''
	Tests to verify common errors, to be implemented by all integrations
	'''

	@abstractmethod
	def test_wrong_login(self):
		'''
		Test that authentication errors are correctly treated
		'''
		raise NotImplementedError

	@abstractmethod
	def test_wrong_parameters(self):
		'''
		Test that a wrong request will return the expected value
		'''
		raise NotImplementedError

	@abstractmethod
	def test_internal_error(self):
		'''
		Test that an internal error returns the expected result
		'''
		raise NotImplementedError