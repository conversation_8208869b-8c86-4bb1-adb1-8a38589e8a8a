from unittest.mock import patch, Mock

from tests.test_base import BaseTest


class ErrorHandlerTest(BaseTest):

	@patch('handlers.error_finder_handler.logging_cloud.Client')
	@patch('handlers.error_finder_handler.email_utils.sendEmail')
	def test_happy_flow(self, *args):
		demo_log = Mock(
			trace='test-identifier',
			http_request={'requestUrl': 'http://localhost:5000'},
			timestamp='2022-07-21 00:00:00',
			payload='This is a test error'
		)

		mock_client = Mock()
		mock_client.list_entries.return_value = [demo_log]
		args[1].return_value = mock_client

		response = self.testapp.get('/errorfinder')
		# print(response.data)

		args, kwargs = args[0].call_args
		self.assertIn('This is a test error', args[3])