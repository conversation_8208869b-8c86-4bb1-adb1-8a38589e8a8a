queue:
- name: default
  rate: 50/s
  max_concurrent_requests: 4
  retry_parameters:
    task_retry_limit: 5

- name: maintainance
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: processor0
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: audit
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 3
  target: background

- name: processor
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: processor1
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: processor2
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: processor3
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: processor4
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: processor5
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: processor6
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 30
  target: background

- name: processor7
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: processor8
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: processor9
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: processor10
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: processor11
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: processor12
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: processor13
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: processor14
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: processor15
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: background

- name: largeserver
  rate: 50/s
  max_concurrent_requests: 1
  retry_parameters:
    task_retry_limit: 5
    min_backoff_seconds: 20
  target: largeserver

