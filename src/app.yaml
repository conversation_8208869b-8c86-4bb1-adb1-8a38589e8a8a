runtime: python312
service: @@MODULE@@

# Note that I am supposing we are using a F4 instance and we want to prevent out of memory
# See https://cloud.google.com/appengine/docs/standard/python3/runtime
entrypoint: gunicorn -b :$PORT -w 1 -t 600 main:app


instance_class: @@MACHINE_TYPE@@
automatic_scaling:
  min_idle_instances: 0
  max_idle_instances: 2  # default value
  max_instances: 10 # Just a safety measure in case of something broken
  min_pending_latency: 1000ms  # default value
  max_pending_latency: 10000ms
  max_concurrent_requests: 25

handlers:
- url: /static
  static_dir: static

- url: .*
  script: auto