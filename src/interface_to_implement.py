import os
from abc import ABC<PERSON>eta, abstractmethod

AVAILABILITY_ITEM_DATE_FORMAT = '%Y-%m-%d'

OPEN_STATUS = 'open'
CLOSED_STATUS = 'closed'

class SpecificIntegration(metaclass=ABCMeta):
	'''
	This interface needs to be implemented by any integration using this project as a base
	'''

	@abstractmethod
	def gets_additional_paths(self):
		'''
		Returns a list of webapp2.Route to be added to the default routes
		'''
		raise NotImplementedError

	@abstractmethod
	def get_modify_url(self):
		'''
		Returns the url that is called when we want to modify prices and availability
		'''
		raise NotImplementedError

	@abstractmethod
	def get_response_extra_params(self, request_body: str):
		'''
		Returns extra params to be returned in the response
		'''
		raise NotImplementedError

	@abstractmethod
	def get_hotel_id(self, request, request_id):
		'''
		Obtains the hotel_id for the given request
		'''
		raise NotImplementedError

	def get_availability_items_with_cache(self, operation, request_id, queue_name=None, cache=None):
		'''
		Useful when there might be dependencies between different calls, i.e. if we close something in a previous call we don't want to get the data from the datastore
		'''
		return self.get_availability_items(operation, request_id, queue_name)

	@abstractmethod
	def get_availability_items(self, operation, request_id, queue_name=None):
		'''
		Returns 1 object:
		- list_of_availability_items

		Each availability item has at least the following structure:
		{
			'day': '2016-12-30',
			'roomId': 38484,
			'boardId': 3838,
			'capacity': '2-0-0',
			'rateId': 4849494,
			'price': "200.0",
			'minimumStay': 23,
			'maximumStay': 28,
			'closedToArrival': True or False
			'closedToDeparture': True or False
			'quantity': 20,
			'status': 'closed'   #'closed' or 'open'
		}

		'''
		raise NotImplementedError

	@abstractmethod
	def _get_username_and_password(self, xml_finder):
		'''
		Obtain the user and password
		'''
		raise NotImplementedError


	def get_integration_name(self):
		'''
		Returns the name of the integration in case we need to look for configuration properties in IntegrationConfiguration
		By default it returns the name of its package (i.e. rategain or availpro)
		'''
		return self.__module__.split(".")[0]


	def get_content_type(self):
		# zzzzzz
		# return 'application/xml;charset=UTF-8'
		return 'application/xml'


	def control_redundant_calls(self):
		'''
		Very dangerous call, if we activate this we might be losing some calls (i.e. like in Siteminder) but we are not queuing tasks when we are overflown
		'''
		return False

	def get_time_between_tasks(self):
		return 60


	def get_mapping_info(self, hotel_code, language):
		'''
		Used to show the integration mapping codes in a more human format
		Expected a dictionary with: rooms, boards, rates, name
		'''
		pass


	@abstractmethod
	def _handle_exception(self, handler, exception, debug):
		'''
		Handles things when there are problems.

		Note: Make sure that calls are not audited, otherwise you run the the risk of executing things that are invalid or
		without the expected credentials
		'''
		raise NotImplementedError

	@abstractmethod
	def validate_update_request(self, hotel_id, xml_finder, request_body):
		'''
		Performs validation to update requests, in case there is any problem it raises exceptions
		'''
		raise NotImplementedError

	def maintenance_time(self, request):
		'''
		This method will be called each hour, used in case a integration needs to do hourly maintainances
		'''
		pass

	def max_operations_per_request(self):
		return 1000

	def is_request_sync(self, request):
		return False


#Change this dynamically in each interface implementation (i.e. interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE = RateGainIntegration())
SPECIFIC_INTEGRATION_INTERFACE = None