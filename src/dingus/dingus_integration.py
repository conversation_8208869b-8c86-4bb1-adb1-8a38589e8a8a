import copy
import datetime
import json
import re

import requests
from flask import request

from dingus import dingus_utils
from dingus.dingus_utils import get_mix_remote_hotels, hotel_with_remote_mix, log_only_once
from interface_to_implement import SpecificIntegration
from model.audit import EndpointCallAuditEvent
from model.availability_item_model import AvailabilityItem
from model.price_model import CAPACITY_FOR_BASE_PRICES
from paraty.exceptions import InvalidCredentialsException
from paraty.exceptions import WrongParamsException
from paraty.integration.exception import BusinessEntityNotFoundException
from paraty.integration.integration_data_cache import IntegrationDataCache
from paraty.utils import date_utils
from paraty.utils import hotel_manager_utils as integration_hotel_manager_utils
from paraty.utils.email_utils import notify_error_by_email
from paraty.utils.hotel_manager_utils import get_integration_configuration_by_key, \
	get_final_price_days_of_hotel_and_room
from paraty.utils.queues.queues_utils import defer
from paraty.utils.xml_utils import XmlFinder

from dingus.dingus_constants import ADAPTER_URL_BASE
from dingus.dingus_constants import GET_HOTEL_INFO_OP
from dingus.dingus_constants import UPDATE_AVAIL_AND_RATE_OP
from dingus.dingus_constants import BOOKING_RETRIEVAL_OP
from dingus.dingus_constants import ERROR_MESSAGE_UNEXPECTED
from dingus.dingus_constants import ERROR_MESSAGE_INVALID_USER
from dingus.dingus_constants import UPDATE_AVAIL_AND_RATE_RESPONSE_TEMPLATE
import interface_to_implement
import logging

from paraty_commons_3.common_data.common_data_provider import get_rooms_of_hotel, get_boards_of_hotel, \
	get_rates_of_hotel, get_hotel_advance_config_value, get_reservations_of_hotel
from paraty_commons_3.common_data.data_management.integrations_utils import get_integration_configuration
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id, get_all_valid_hotels


class DingusIntegration(SpecificIntegration):
	def get_modify_url(self):
		return ADAPTER_URL_BASE % UPDATE_AVAIL_AND_RATE_OP

	def _handle_exception(self, handler, exception, debug):
		logging.info(exception)

		if not hasattr(handler, 'operation'):
			# This is the case of updating (ModifyPricesHandler)
			setattr(handler, 'operation', UPDATE_AVAIL_AND_RATE_OP)
			setattr(handler, 'template', UPDATE_AVAIL_AND_RATE_RESPONSE_TEMPLATE)

		context = {'operation': handler.operation, }

		if isinstance(exception, InvalidCredentialsException):
			context['error'] = ERROR_MESSAGE_INVALID_USER

		elif isinstance(exception, WrongParamsException):
			context['error'] = exception.__str__()

		else:
			context['error'] = ERROR_MESSAGE_UNEXPECTED

		response = handler._write_response(context, handler.template, audited_enabled=True)
		response.status_code = 400
		return response

	def _get_username_and_password(self, xml_finder: XmlFinder):
		user = xml_finder.find_element('user')

		if not user:
			raise WrongParamsException({})

		user_name = user.text

		if not user_name:
			raise WrongParamsException({})

		return user_name, None

	def get_hotel_id(self, request, request_id):
		request_body = request.data.decode()
		xml_finder = XmlFinder(request_body)

		if 'HotelAvailRateMessages' in request_body:
			return xml_finder.find_attribute("HotelAvailRateMessages", "HotelCode")

		if 'BookingRetrievalRQ' in request_body:
			return xml_finder.find_element("HotelCode").text

		return None

	def validate_update_request(self, hotel_id, xml_finder: XmlFinder, request_body):

		#For consistency with previous integration we allow invalid Rate_ids and Room_ids

		content = xml_finder.find_element('hotelavailratemessages')
		if not content:
			raise WrongParamsException('Missing mandatory tag: hotelavailratemessages')


	def get_availability_items(self, operation, request_id, queue_name=None):
		return self.get_availability_items_with_cache(operation, request_id, queue_name)

	def get_availability_items_with_cache(self, operation: EndpointCallAuditEvent, request_id, queue_name=None, cache=None):
		result = []

		xml_finder = XmlFinder(operation.request)
		content = xml_finder.find_element('hotelavailratemessages')
		messages = content.find_all('hotelavailratemessage')

		is_base_price_integration = integration_hotel_manager_utils.setup_base_price_integration(operation.hotel_code)
		apply_room_base_price_for_all_capacities = integration_hotel_manager_utils.get_integration_configuration_by_key(operation.hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "apply prices base capacities")
		quantity_by_rate_board = get_integration_configuration_by_key(operation.hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "availability by rate board")
		use_babies_for_save_juniors = get_integration_configuration_by_key(operation.hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "use babies for save juniors")

		if quantity_by_rate_board:
			logging.info("hotel_has_quantity_by_rate_board! %s", operation.hotel_code)

		closes_by_capacity = get_integration_configuration_by_key(operation.hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "CLOSES BY CAPACITY")
		if closes_by_capacity:
			logging.info("hotel has closes_by_capacity: %s", operation.hotel_code)

		channel_hotel_configurations: dict = {
			"is_base_price_integration": is_base_price_integration,
			"apply_room_base_price_for_all_capacities": apply_room_base_price_for_all_capacities,
			"use_babies_for_save_juniors": use_babies_for_save_juniors,
			"quantity_by_rate_board": quantity_by_rate_board,
			"closes_by_capacity": closes_by_capacity
		}

		for message_elem in messages:

			availability_items_from_message = self._process_message(operation.hotel_code, message_elem, channel_hotel_configurations, cache=cache)
			result.extend(availability_items_from_message)

		logging.info("Availaility items: %s", len(result))

		return result


	def get_response_extra_params(self, request_body: str):

		#this method is always called, so we use it as dirty trick to duplicate XML when we have a remote mix in dingus
		try:
			target_hotel_id = self.get_hotel_id(request, None)
			if target_hotel_id and not request.values.get("already_duplicated"):
				modify_path = self.get_modify_url()
				control_param = "?already_duplicated=true"
				remote_hotels = get_mix_remote_hotels(target_hotel_id)
				#post the xml also for other destinations hotels
				for remote_hotel in remote_hotels:

					replace_string_hotel_code_re = r'HotelCode=[\"|\'](%s)[\"|\']' % target_hotel_id
					final_hotel_code_string = 'HotelCode="%s"' % remote_hotel

					body_xml = re.sub(replace_string_hotel_code_re, final_hotel_code_string, request.data.decode(), flags=re.IGNORECASE)

					if final_hotel_code_string in body_xml:
						#body_xml = handler.request.body.replace(replace_string_hotel_code, final_string_hotel_code)
						result = requests.post("https://dingus-adapter.appspot.com%s%s" % (modify_path, control_param), data=body_xml)

		except Exception as e:
			logging.error("Problem duplicating XML for a remote mix in dingus: %s",  e)



		return {}

	def gets_additional_paths(self):
		from dingus.handlers.hotel_info_handler import HotelInfoHandler
		from dingus.handlers.reservation_handler import ReservationListHandler, ReservationsHandler

		logging.info("Access in gets_additional_paths")

		return [
			(ADAPTER_URL_BASE % GET_HOTEL_INFO_OP, HotelInfoHandler),
			(ADAPTER_URL_BASE % BOOKING_RETRIEVAL_OP, ReservationListHandler),

			# Push reservations
			(r"/push_reservation", ReservationsHandler),

		]


	def is_pax_price(self, meal_plan_elem):
		for price_elem in meal_plan_elem.find_all('price'):
			if price_elem.get('paxprice'):
				return True

		return False

	def get_amount_pax(self, pax_type):
		clean_pax_type = pax_type.replace("Adult", "").replace("ChildA", "").replace("ChildB", "")
		return {
			"First": 1,
			"Second": 2,
			"Third": 3,
			"Fourth": 4,
			"Fifth": 5,
			"Sixth": 6,
			"Seventh": 7,
			"Eighth": 8,
			"Nineth": 9
		}.get(clean_pax_type, 0)

	def get_price_for_pax(self, meal_elem, current_capacity, use_babies_for_save_juniors, hotel_code):


		'''


		  <Price PaxPrice="312.00" PaxType="Adult"/>
<Price PaxPrice="27.00" PaxType="ThirdAdult"/>
<Price PaxPrice="27.00" PaxType="FourthAdult"/>
<Price PaxPrice="27.00" PaxType="FifthAdult"/>
<Price PaxPrice="27.00" PaxType="SixthAdult"/>
<Price PaxPrice="27.00" PaxType="SeventhAdult"/>
<Price PaxPrice="17.00" PaxType="FirstChildA"/>
<Price PaxPrice="17.00" PaxType="SecondChildA"/>
<Price PaxPrice="17.00" PaxType="ThirdChildA"/>
<Price PaxPrice="17.00" PaxType="FourthChildA"/>


		'''

		try:
			#Example: <Price PaxPrice="49.47" PaxType="Adult" />
			price = 0

			info_capacity = current_capacity.split("-")

			adults = int(info_capacity[0])
			kids = int(info_capacity[1])
			infants = int(info_capacity[2])

			if not use_babies_for_save_juniors:
				infants = 0

			pending_adults = 0
			pending_kids = kids
			pending_infants = infants
			base_adults = adults

			if adults > 2:
				base_adults = 2
				pending_adults = adults - 2

			default_adult_price = 0
			default_kid_price = 0
			default_infant_price = 0
			posible_price_for_single_use = 0
			extra_already_calculated = []

			for price_elem in meal_elem.find_all('price'):
				pax_type = price_elem['paxtype']
				pax_price = float(price_elem['paxprice'])
				pax_amount = self.get_amount_pax(pax_type)

				if pax_type in extra_already_calculated:
					continue

				if pax_type == "SingleUse" and adults == 1 and kids == 0:
					#full price for single use!!!!
					price = pax_price
					break

				elif pax_type == "Adult" and not (adults == 1 and kids == 0):
					#never take this value for a single room, becasue it is already given with paxtype="SingleUse"
					price += pax_price * base_adults
					default_adult_price = pax_price

				elif pax_type == "Adult" and (adults == 1 and kids == 0):
					#If we are in a 1-0-0 capacuty and single Used is not arriving in the XML
					#we assume the Adult price as the single sed
					posible_price_for_single_use = pax_price

				#KIDS!!!!
				elif pax_type == "FirstChildA" and pending_kids and kids >= 1:
					default_kid_price = pax_price

				elif pax_type == "FirstChildB" and pending_infants and pending_infants >= 1:
					default_infant_price = pax_price

				if not pax_type == "Adult" and not pax_type == "SingleUse":
					if "Adult" in pax_type:

						log_only_once(hotel_code, "THIRD ADULT found in hotel: ")
						pending_adults -= 1

						if adults >= pax_amount:
							price += pax_price

					elif "ChildA" in pax_type and pending_kids > 0:
						log_only_once(hotel_code, "THIRD ADULT found in hotel: ")
						pending_kids -= 1

						if 0 < pax_amount <= kids:
							price += pax_price

					elif "ChildB" in pax_type and pending_infants > 0:
						log_only_once(hotel_code, "THIRD ADULT found in hotel: ")
						pending_infants -= 1

						if 0 < pax_amount <= infants:
							price += pax_price

				extra_already_calculated.append(pax_type)

			if default_adult_price and (pending_adults > 0 ):
				#if we have still pending_adults to be added, we have to calculate extras from paxtype="Adult"
				price += default_adult_price * pending_adults

			if default_kid_price and (pending_kids > 0):
				#if we have still kids to be added...
				price += default_kid_price * pending_kids

			if default_infant_price and (pending_infants > 0):
				#if we have still kids to be added...
				price += default_infant_price * pending_infants

			#same for Single Used. It is posuble taht sibgle Used not arrive in the XML
			if not price and posible_price_for_single_use:
				price = posible_price_for_single_use


		except Exception as e:
			raise BusinessEntityNotFoundException("", "Final Prices integration BY PAX but not adult or children or occupancyprice found: %s" % e)


		return float("%.2f" % price)


	def get_mapping_info(self, hotel_code, language):

		from dingus.handlers.hotel_info_handler import get_hotel_product

		hotel = get_hotel_by_application_id(hotel_code)

		hotel_products = get_hotel_product(hotel, language)

		return hotel_products


	def _build_capacities_list_of_room(self, room_code, all_rooms):

		capacities = []
		for room_element in all_rooms:
			if str(alphanumeric_to_id(room_element.get("key", ""))) == str(room_code):
				if 'capacities' in room_element:
					capacities = room_element['capacities']

				break

		return capacities

	def _process_message(self, hotel_code, message_elem, channel_hotel_configurations={}, cache=None):

		result = []

		is_base_price_integration = channel_hotel_configurations.get("is_base_price_integration")
		apply_room_base_price_for_all_capacities = channel_hotel_configurations.get("apply_room_base_price_for_all_capacities")
		quantity_by_rate_board = channel_hotel_configurations.get("quantity_by_rate_board")
		use_babies_for_save_juniors = channel_hotel_configurations.get("use_babies_for_save_juniors")
		closes_by_capacity = channel_hotel_configurations.get("closes_by_capacity")

		#Not used so far
		# currency = message_elem['currencycode']
		# release = self._safe_find_text(message_elem, 'release')

		hotel = get_hotel_by_application_id(hotel_code)
		from_date = message_elem['fromdate']
		to_date = message_elem['todate']
		room_code = message_elem['roomcode']
		rate_code = message_elem['rateplancode']

		quantity = int(self._safe_find_text(message_elem, 'roomstosell'))

		min_stay = self._safe_find_text(message_elem, 'minimumstay')
		release = self._safe_find_text(message_elem, 'release')
		max_stay = self._safe_find_text(message_elem, 'maximumstay')
		closed = self._safe_find_text(message_elem, 'closed')

		# En hoteles como en precise-rompido los cierres de tarifa vienen como cupo=0. Nosotros interpretaremos cupo=0 como cierre de tarifa.
		status = None
		if closed.lower() == 'true':
			status = 'closed'
		elif closed.lower() == 'false':
			status = 'open'

		if int(quantity) == 0 and not quantity_by_rate_board:
			status = 'closed'


		dates = date_utils.get_dates_in_range(from_date, to_date, include_last=True)

		rates = message_elem.find_all('rate')
		room_id = int(room_code)
		rate_id = int(rate_code)
		logging.info("calculating price for room %s and rate %s", room_code, rate_code)

		all_rooms = get_rooms_of_hotel(hotel, include_removed=True)
		all_boards = {alphanumeric_to_id(x['key']): x for x in get_boards_of_hotel(hotel, include_removed=True)}
		all_rates = get_rates_of_hotel(hotel, include_removed=True)

		all_rooms_map =  {alphanumeric_to_id(x['key']): x for x in all_rooms}
		all_rates_map =  {alphanumeric_to_id(x['key']): x for x in all_rates}

		all_mapping = {"all_rooms": all_rooms_map, "all_boards": all_boards, "all_rates": all_rates_map}

		hotel_has_remote_mix = hotel_with_remote_mix(hotel_code)

		if hotel_has_remote_mix and (not rate_id in list(all_rates_map.keys())):
			logging.info("descarting rate %s for hotel %s. It has to be a duplicated XML", rate_id, hotel_code)
			return []
		if hotel_has_remote_mix and (not room_id in list(all_rooms_map.keys())):
			logging.info("descarting room %s for hotel %s. It has to be a duplicated XML", room_id, hotel_code)
			return []


		for date in dates:

			date_str = date_utils.date_to_string(date, format="%Y-%m-%d")

			#TODO: maybe we haven't to discart global quantity when quantity_by_rate_board
			#NOTE: If quantity 0 it really means status closed
			if quantity != 0:
				new_item = AvailabilityItem(quantity=quantity, room_id=room_id, day=date_str).to_json()
				result.append(new_item)

			for rate in rates:

				#Note that Dingus applies certain properties at rate level, not per board
				#WARNING, THIS IS DIFFERENT BEHAVIOUR COMPARED WITH EXISTING INTEGRATION
				#THIS NEEDS TO BE KEPT AS WE HAVE TO CLOSE ALL BOARDS if CLOSED
				for current_board in all_boards.keys():
					new_item = AvailabilityItem(day=date_str,
												room_id=room_id,
												rate_id=rate_id,
												maximum_stay=max_stay,
												minimum_stay=min_stay,
												release=release,
												status=status,
												board_id=current_board).to_json()

					result.append(new_item)


				mealplans = rate.find_all('mealplan')

				for mealplan in mealplans:

					mealplan_code = mealplan['code']
					board_id = dingus_utils.board_id_from_dingus_code(hotel, mealplan_code)

					if quantity_by_rate_board and quantity is not None:
						#build a independent availabilityItem only for this purpose (like we did with global room quantity)
						#quantity_rate_board = {"rate_id": rate_code, "board_id": board_id, "quantity": quantity}

						logging.info("Creating availability for item quantity_rate_board: %s %s", quantity, hotel_code)
						new_item = AvailabilityItem(quantity_rate_board=quantity, room_id=room_id, rate_id=rate_code, board_id=board_id, day=date_str).to_json()
						result.append(new_item)


					if self.is_pax_price(mealplan):

						#PAX prices!!! We have differents prices for adults and children in differents lines!
						capacities = self._build_capacities_list_of_room(room_code, all_rooms)

						priceAmountForced = None
						if apply_room_base_price_for_all_capacities and (apply_room_base_price_for_all_capacities.lower() == "true" or room_code in apply_room_base_price_for_all_capacities):
							priceAmountForced =self.get_price_for_pax(mealplan, capacities[0], use_babies_for_save_juniors, hotel_code)
							logging.info("apply_room_base_price_for_all_capacities price from first capacity in room %s, for all capacities %s", capacities[0], capacities)


						for current_capacity in capacities:

							if priceAmountForced:
								priceAmount = priceAmountForced
							else:
								priceAmount = self.get_price_for_pax(mealplan, current_capacity, use_babies_for_save_juniors, hotel_code)

							if priceAmount:
							#only save item, if we have a valid amount
								new_item = AvailabilityItem(price=priceAmount,
															capacity=current_capacity,
															day=date_str,
															room_id=room_id,
															rate_id=rate_id,
															board_id=board_id).to_json()

								result.append(new_item)

					else:
						#OCCUPANCY OR ROOM prices!!!
						prices = mealplan.find_all('price')

						for price in prices:

							if is_base_price_integration:

								try:

									if price.get('roomprice'):
										priceAmount = price['roomprice']
									else:
										#assuming occupancy price as baseprice
										priceAmount = price['occupancyprice']

									final_capacity = CAPACITY_FOR_BASE_PRICES
									new_item = AvailabilityItem(price=priceAmount,
																capacity=final_capacity,
																day=date_str,
																room_id=room_id,
																rate_id=rate_id,
																board_id=board_id).to_json()

									result.append(new_item)

								except Exception as e:
									raise BusinessEntityNotFoundException("", "Base Price integration but not  roomprice or occupancyprice found: %s" % e)

							else:
								try:
									room_price_in_xml = price.get('roomprice')
									price_in_all_caps_by_config = apply_room_base_price_for_all_capacities and (apply_room_base_price_for_all_capacities.lower() == "true" or room_code in apply_room_base_price_for_all_capacities)
									price_in_all_caps = room_price_in_xml or price_in_all_caps_by_config

									if price_in_all_caps:

										#we have to loop until we found the price given for firts capacity in room, and then reply this price for the rest of capacites in room
										capacities = self._build_capacities_list_of_room(room_code, all_rooms)

										priceAmount = 0
										if price.get("adults") and price.get("children"):

											adults = price['adults']
											children = price['children']
											base_capacity = capacities[0]
											price_given_for_capacity = '%s-%s-0' % (adults, children)

											if price_given_for_capacity == base_capacity:
												#we got it!
												priceAmount = price['occupancyprice']

										elif price.get('roomprice'):
											priceAmount = price['roomprice']


										if priceAmount:
											for room_capaty in capacities:
												new_item_new_capacity = AvailabilityItem(price=priceAmount,
																			capacity=room_capaty,
																			day=date_str,
																			room_id=room_id,
																			rate_id=rate_id,
																			board_id=board_id).to_json()
												result.append(new_item_new_capacity)

									else:

										#NORMAL PRICE BY CAPACITY
										trick_price_cbc = False
										trick_negative_price_cbc = False
										trick_positive_price_cbc = False

										if closes_by_capacity and price.get("closed"):
											#READ IN DINGUS DOC:
											'''
											Stop Sales at occupancy level
											This stop sales option is available for occupancy price model only
											The Closed tag is sent at Price element level.

											<Price OccupancyPrice="134.82" Closed="True|False" />

											'''

											capacity_closed = price.get("closed", "").lower() == "true"

											if capacity_closed:
												#CLOSE!!
												trick_negative_price_cbc = True
												logging.info("closes_by_capacity and CLOSE found. Discarting status and tricking to negative amount  %s", hotel_code)

											else:
												#OPEN!!.
												trick_positive_price_cbc = True
												logging.info("closes_by_capacity and OPEN found. tricking to positive amount the capacity  %s", hotel_code)

											trick_price_cbc = trick_negative_price_cbc or trick_positive_price_cbc



										priceAmount = price['occupancyprice']
										adults = price['adults']
										children = price['children']
										capacity = '%s-%s-0' % (adults, children)
										new_item = AvailabilityItem(price=priceAmount,
																	capacity=capacity,
																	day=date_str,
																	room_id=room_id,
																	rate_id=rate_id,
																	board_id=board_id).to_json()


										if closes_by_capacity and trick_price_cbc:

											#TODO (semi duplicated code): (by nmarin) delete this from here AND FROM Siteminder, and filter and convert final availability items in BASE INTEGRATION!
											new_item = self._trick_price_closing_by_capacity(hotel, new_item, trick_negative_price_cbc, trick_positive_price_cbc, cache, all_mapping)

										result.append(new_item)



								except Exception as e:
									logging.warning(e)
									raise BusinessEntityNotFoundException("", "Final Price integration but not  occupancyprice or adults or children found: %s" % e)

				duplicate_price_in_capacity = integration_hotel_manager_utils.get_integration_configuration_by_key(hotel_code, interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),"duplicate price in capacity")

				if duplicate_price_in_capacity:
					logging.info("duplicate_price_in_capacity found: duplicate_price_in_capacity")
					new_result = []
					room_and_capacity_list = duplicate_price_in_capacity.split("||")
					for option in room_and_capacity_list:
						option = option.split("@@")
						if len(option) == 3:
							current_room = int(option[0])
							origin_capacity = option[1]
							target_capacit = option[2]
							for item in result:
								if item.get("price") and (item.get("capacity") == origin_capacity and item.get("roomId") == current_room):
									new_item = copy.deepcopy(item)
									new_item["capacity"] = str(target_capacit)
									new_result.append(new_item)
					if new_result:
						logging.info("duplicate_price_in_capacity new items generated: %s", len(new_result))
						result += new_result

		return result



	def _trick_price_closing_by_capacity(self, hotel, new_item_new_capacity, trick_negative_price_cbc, trick_positive_price_cbc, cache, all_mapping):

		#project_id_with_prefix = datastore_utils.get_project_gae_app(hotel["applicationId"])
		#project_id, namespace = datastore_utils._get_project_id_and_namespace(hotel)

		#board_key = datastore_utils.id_to_alphanumeric(long(new_item_new_capacity.board_id), "Regimen", namespace, project_id_with_prefix)
		#room_key = datastore_utils.id_to_alphanumeric(long(new_item_new_capacity.room_id), "RoomType", namespace, project_id_with_prefix)
		#rate_key = datastore_utils.id_to_alphanumeric(long(new_item_new_capacity.rate_id), "Rate", namespace, project_id_with_prefix)


		all_rooms = all_mapping.get("all_rooms")
		all_boards = all_mapping.get("all_boards")
		all_rates = all_mapping.get("all_rates")

		board_key = all_boards.get(int(new_item_new_capacity.get("boardId")), {}).get("key")
		room_key = all_rooms.get(int(new_item_new_capacity.get("roomId")), {}).get("key")
		rate_key = all_rates.get(int(new_item_new_capacity.get("rateId")), {}).get("key")

		if (not board_key) or (not room_key) or (not rate_key):
			return new_item_new_capacity

		trick_price_cbc = trick_negative_price_cbc or trick_positive_price_cbc

		price = new_item_new_capacity.get("price")
		capacity = new_item_new_capacity.get("capacity")
		day = new_item_new_capacity.get("day")

		if not price:
			# OMG! it is a close/open but without prices given, take the price already in manager
			price = self.get_final_price_day(hotel, room_key, rate_key, board_key, capacity, day, cache, new_item_new_capacity.get("roomId"))
			logging.info(f"closes_by_capacity found. OMG! it is a close/open but without prices given, take the price already in manager to check the signus. Current price: {price} {hotel['applicationId']}")

		elif not trick_price_cbc:
			# OK, we have prices in XML, but NOT change of status!
			# we can't put this prices so easly, we have to mantein the signus (close or open)
			current_price = self.get_final_price_day(hotel, room_key, rate_key, board_key, capacity, day, cache, new_item_new_capacity.get("roomId"))
			logging.info("closes_by_capacity %s we have prices in XML, but NOT change of status! Price in XML: %s Current price: %s %s", capacity, price, current_price, hotel['applicationId'])
			if current_price and float(current_price) < 0:
				# maintening negative, but with the new value received
				price = str(float(price) * -1)

		if price and float(price) > 0 and trick_negative_price_cbc:
			price = str(float(price) * -1)

		elif price and float(price) < -1 and trick_positive_price_cbc:
			# never trick a -1!
			price = str(float(price) * -1)

		logging.info("closes_by_capacity %s Final price calculated: %s %s", capacity, price, hotel['applicationId'])
		new_item_new_capacity["price"] = price
		return new_item_new_capacity


	def get_final_price_day(self, hotel, room_key, rate_key, board_key, capacity, str_date, cache, room_id):

		price = -1

		date_info = str_date.split("-")
		year_month = "%s-%s" % (date_info[0], date_info[1])
		day_index = int(date_info[2]) - 1

		content_fpd = None
		if cache:
			fpd = cache.get_data(int(room_id), IntegrationDataCache.FINAL_PRICE_DAY, year_month)
			if fpd:
				content_fpd = fpd.content

		if not content_fpd:
			fpds = get_final_price_days_of_hotel_and_room(hotel, room_key, year_month, year_month)
			if fpds:
				content_fpd = json.loads(fpds[0].get("content"))

		if content_fpd:
			key_fpd_capacity = "%s@@%s@@%s" % (rate_key, capacity, board_key)
			fpd = content_fpd.get(key_fpd_capacity)
			if fpd and len(fpd) > day_index:
				price = fpd[day_index]
				if price:
					price = float(price)

		return price



	def _safe_find_text(self, reservation_xml, name):
		found = reservation_xml.find(name)
		if found:
			return found.text
		else:
			return ''

	def maintenance_time(self, request):
		'''
		This method will be called each hour, used in case a integration needs to do hourly maintainances
		'''
		current_hour = datetime.datetime.now().hour


		#Excute only the checker at 1:00 AM
		if current_hour == 1:

			logging.info("deffering check_all_dingus_reservations")
			defer(check_all_dingus_reservations, _queue="largeserver")
			check_all_dingus_reservations()


def check_all_dingus_reservations():
	all_hotels = get_all_dingus_hotels()

	'''
	Formatos de las dos fechas:
	from_datetime = '2021-01-24 00:00:00'
	to_datetime = '2021-01-28'

	'''

	now_ts = datetime.datetime.now()
	antes_de_ayer_ts = now_ts - datetime.timedelta(days=2)

	antes_de_ayer = antes_de_ayer_ts.strftime("%Y-%m-%d %H:%M:%S")
	today = now_ts.strftime("%Y-%m-%d")

	logging.info("checkin reservations from %s to %s", antes_de_ayer, today)
	for hotel in all_hotels:
		defer(_check_pull_reservations_of_a_hotel, hotel, antes_de_ayer, today, _queue="largeserver")



def _check_pull_reservations_of_a_hotel(hotel: dict, antes_de_ayer: str, today: str):

	send_cc = get_hotel_advance_config_value(hotel['applicationId'], "Save CC datas")

	#in this moment, we an only check reservations tha went thorw datatrans
	if send_cc:
		reservations = get_reservations_of_hotel(hotel, antes_de_ayer, today, include_end_date=True)
		message = ""
		if reservations:
			logging.info("checkin pull reservations in hotel %s. %s reservations found", len(reservations), hotel.get("name"))
			hotel_header_mess = ""
			for reservation in reservations:
				#reservation["modificationTimestamp"] = modification_timestamp
				#rest_client.update(url + "/rest", 'Reservation', reservation)
				if (not str(reservation.get("cancelled", "")) == "true") and reservation.get("comments", "") and "@@@TEST@@@" not in reservation.get("comments", ""):

					extra_info = json.loads(reservation.get("extraInfo", "{}"))

					maintenance_time = False
					if 1 <= datetime.datetime.now().hour <= 2:
						maintenance_time = True
						logging.info("Dingus is under maintenance, %s - %s" % (str(hotel.get("applicationId", "")), str(reservation.get("identifier", ""))))

					if not extra_info.get("pciTimestamp") and not maintenance_time:

						if not hotel_header_mess:
							hotel_header_mess = "<br><br><b>HOTEL WITH PROBLEMS: %s</b> <br>" % (hotel.get("name"))
							message += hotel_header_mess
							logging.error(hotel_header_mess)

						reservation_mess = "RESERVATION %s NOT DELIVERED. %s <br>" % (reservation.get("identifier"), reservation.get("timestamp"))
						logging.error(reservation_mess)
						message += reservation_mess
		if message:
			notify_error_by_email("DAILY DINGUS RESERVATIONS CHECKER %s" % hotel.get("name"), message)


@managers_cache(hotel_code_provider=lambda f,a,k: 'dingus-adapter', ttl_seconds=864000) #10 days
def get_all_dingus_hotels():
	all_hotels = get_all_valid_hotels()
	dingus_hotels = []
	for hotel in all_hotels:
		integration_configuration = get_integration_configuration(hotel['applicationId'], "dingus")
		if integration_configuration:
			dingus_hotels.append(hotel)

	return dingus_hotels



interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE = DingusIntegration()
