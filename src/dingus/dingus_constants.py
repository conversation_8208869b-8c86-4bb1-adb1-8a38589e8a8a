# URL BASE OF ADAPTER
ADAPTER_URL_BASE = '/dingus/%s'


# AVAILABLE OPERATIONS
GET_HOTEL_INFO_OP = 'GetHotelInfo'
UPDATE_AVAIL_AND_RATE_OP = 'HotelAvailRateUpdate'
BOOKING_RETRIEVAL_OP = 'BookingRetrieval'


# RESPONSE TEMPLATES
GET_HOTEL_INFO_RESPONSE_TEMPLATE = 'hotel_info_response.xml'
UPDATE_AVAIL_AND_RATE_RESPONSE_TEMPLATE = 'ok_response.xml'
BOOKING_RETRIEVAL_TEMPLATE = 'booking_retrieval_response.xml'


TAX_DENOMINATOR = 1.06  # 6% taxes
DEFAULT_LANGUAGE_CODE = "Es"
DEFAULT_LANGUAGE = "SPANISH"


DEFAULT_AGE_FOR_ADULTS = "30"
DEFAULT_AGE_FOR_CHILDREN = "8"
DEFAULT_AGE_FOR_BABY = "1"
ADITIONAL_SERVICES_INCLUDED = "Servicios Adicionales Incluidos"


DUPLICATE_RATES_FOR_CALLCENTERS = "duplicate rates for callcenter"
CALL_CENTER_USER_GROUP = "call center users group"
HOTEL_BEST = "HOTEL BEST"

ADD_SHARED_ROOMS = "Add shared rooms"

DEFAULT_CURRENCY = "EUR"

URL_PRODUCTION = 'http://dingus-adapter.appspot.com/dingus/'


DINGUS_VS_DATATRANS_CONTENT_TYPE = "text; charset=UTF-8"
#DINGUS_VS_DATATRANS_CONTENT_TYPE = "application/json; charset=UTF-8"
DINGUS_VS_DATATRANS_URL_POST = "https://testpci-dot-dingus-adapter.appspot.com/receiver-datatransinfo"
#DINGUS_VS_DATATRANS_URL_POST = "http://pci-test-dot-siteminder-adapter.appspot.com/receiver"


# ERROR MESSAGES
ERROR_MESSAGE_UNEXPECTED = "Unexpected error."
ERROR_MESSAGE_INVALID_USER = "Invalid User/Password"


# XML TAGS
HOTEL_CODE_TAG = 'HotelCode'



CC_NAMES = {
        'VI': 'Visa',
        'MC': 'Mastercard',
        'AX': 'American Express',
        'JC': 'JCB',
        'DN': 'Diners Club',
        'CU': 'CU',
        'PR': 'PR',
    }


# FOR THESE HOTELS A HACK IS NEEDED TO BE MADE
HACKED_HOTELS_FOR_DINGUS = {
	'precise-resort': {
		'valid_rates_for_dingus': (
			****************,  # Tarifa FLEXIBLE: PAGO DIRECTO en el Hotel
			****************,  # Escapada Romantica
			5649050225344512,  # NRF AD
			5746055551385600,  # NRF MP
			6058530796732416,  # Escapada SPA
			6144859102511104,  # Escapada Golf
			6205504040730624,  # NRF PC
			6605245236903936   # NRF SA
		),

		'equivalent_rates': {
			'flexible': ****************,
			'nrf-RO': 6605245236903936,
			'nrf-BB': 5649050225344512,
			'nrf-HB': 5746055551385600,
			'nrf-FB': 6205504040730624,
		}
	},

	'precise-rompido': {
		'valid_rates_for_dingus': (
			****************,  # Escapada BIKE
			****************,  # Tarifa FLEXIBLE: PAGO DIRECTO en el Hotel,
			5071662873575424,  # NRF MP
			5686683802533888,  # NRF AD
			5711129414205440,  # NRF FB
			6044436056244224,  # Escapada segway
			6128376360206336,  # Escapada quad
			6312732462678016,  # NRF SA
		),

		'equivalent_rates': {
			'flexible': ****************,
			'nrf-RO': 6312732462678016,
			'nrf-BB': 5686683802533888,
			'nrf-HB': 5071662873575424,
			'nrf-FB': 5711129414205440,
		}
	}
}

NO_RETRIEVAL_ROOMS_IN_DINGUS = {"ipv-palace":[****************]}

ADD_PROMOCODE_TO_COMMENTS = 'Add promocode to comments'

NO_SHOW_MESSAGE_PAID_WITHOUT_CARD = 'No show message paid without card'

MARK_RATE_COMMENTS = "Mark rate to comments"

ADD_MARKETING_IN_COMMENTS = "Add marketing in comments"

SHOW_OLD_FIELDS = "send old fields too"

NOT_INCLUDE_CLUB_IN_XML = "not include club in xml"

INCLUDE_CLUB_IN_CLUB_LABEL = "include the club in the club label"

MARK_RESERVATIONS_DEPENDING_ON_RATE = "mark reservations depending on rate"

# for mix several paraty hoteles in only one target hotel code in Dingus
TARGET_CHANNEL_HOTEL_CODE = "target channel hotel code"
ORIGIN_MIX_CHANNEL_HOTEL_CODES = "origin mix channel hotel codes"
DISABLE_SPECIAL_WORD_PARKING ="disable special word parking"


#TODO: get this from XML -> config
# for mix several paraty hoteles in only one target hotel code in Dingus
#DINGUS -> ALSO IN PARATY (hotel_target in DIngus, local_hotels in Paraty)
MIX_REMOTE_HOTELES = {"onhotel": ["on-village"]}

COBRADOR_SERVER_PATH = "https://payment-seeker.appspot.com"
COBRADOR_FAILOVER_SERVER_PATH = "https://failover-dot-payment-seeker.appspot.com"
USE_ALTERNATIVE_PAYMENT_SEEKER = "Use alternative payment seeker"
ACCEPT_CONDITIONS_AND_POLICIES = "send promotions and news in comments"




