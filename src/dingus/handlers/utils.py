# -*- coding: utf-8 -*-
import requests

from dingus.dingus_constants import HOTEL_CODE_TAG
from dingus.dingus_utils import get_ultimate_cobrador_path
from paraty.exceptions import WrongParamsException, InvalidCredentialsException
import logging
import unicodedata
import string

from paraty_commons_3.common_data.common_data_provider import get_boards_of_hotel
from paraty_commons_3.common_data.data_management.integrations_utils import get_integration_board_map_properties
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


def get_hotel_info(xml_finder, user_hotels) -> dict:
	hotel_code_elem = xml_finder.find_element(HOTEL_CODE_TAG).text
	if not hotel_code_elem:
		raise WrongParamsException(
			"HotelCode not found: %s" % hotel_code_elem)

	if not hotel_code_elem in user_hotels:
		raise InvalidCredentialsException(
			"HotelCode not found: %s" % hotel_code_elem)

	return get_hotel_by_application_id(hotel_code_elem)




def normalize_for_board_name(text: str) -> str:
	""" This is a special normalize for make good board names """

	# text must be unicode
	text = text.lower()
	udata = text

	return ''.join(x for x in unicodedata.normalize('NFKD', udata) if (x in string.ascii_lowercase) or (x in ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]))


def _getBoardCode(name, all_boards, exclude=None):
	if isinstance(all_boards, dict):
		hb = list(filter(lambda x: name in normalize_for_board_name(x['name']), all_boards.values()))
	if isinstance(all_boards, list):
		hb = list(filter(lambda x: name in normalize_for_board_name(x['name']), all_boards))

	if hb and len(hb) > 1 and exclude:
		hb = list(filter(lambda x: exclude not in normalize_for_board_name(x['name']), hb))

	if hb:
		return alphanumeric_to_id(hb[0]['key'])

	return None


def get_board_code(spanish_board_name):
	spanish_board_name = normalize_for_board_name(spanish_board_name)

	codes = {
		"mediapension": "HB",
		"soloalojamiento": "RO",
		"alojamientoydesayuno": "BB",
		"pensioncompleta": "FB",
		"todoincluido": "AI"
	}

	if spanish_board_name not in codes.keys():
		return "---"

	return codes[spanish_board_name]




def get_dingus_board_code_from_mappping(hotel: dict, board_id: str):
	#FIRST chance!!! Maybe we have the map explicitly defined
	dingus_boards_xml_config = get_integration_board_map_properties("dingus", hotel['applicationId'])
	if dingus_boards_xml_config:
		boards_mapping = {str(alphanumeric_to_id(key)): value for key, value in dingus_boards_xml_config.items()}
		if board_id in boards_mapping:
			logging.info("Using explicitly mapped board for board_id %s . FOUND dingus COde: %s ", board_id, boards_mapping[board_id])
			return boards_mapping[board_id]

	return None


@managers_cache(
	hotel_code_provider=lambda f,a,k: a[0]['applicationId'],
	key_generator=lambda f,a,k: f"to_dingus_code_{a[0]['applicationId']}_{a[1]}",
	ttl_seconds=60*100
)
def to_dingus_code(hotel: dict, board_code: int, /):
	'''
	Dingus requires one of these boards code: RO, BB, HB, FB, TI
	Whe should traslate from our board code to dingus codes
	'''
	all_boards = get_boards_of_hotel(hotel, include_removed=True)
	board_name = None

	for board in all_boards:
		if str(board_code) == str(alphanumeric_to_id(board['key'])):
			board_name = normalize_for_board_name(board['name'])

			#FIRST chance!!! Maybe we have the map explicitly defined
			dingus_code = get_dingus_board_code_from_mappping(hotel, str(alphanumeric_to_id(board['key'])))

			if dingus_code:
				return dingus_code


			#SECOND chance!!! As ALWAYS!
			if "media" in board_name:
				return "HB"
			if "completa" in board_name:
				return "FB"
			if "cena" in board_name:
				return "HB"
			if "gala" in board_name:
				return "HB"
			if "solo" in board_name:
				return "RO"
			if "desayuno" in board_name:
				return "BB"

			if "todo" in board_name:
				return "AI"

	if board_name:
		logging.warning('Missing translation for board code: %s, board name normalized: board_name: %s', board_code, board_name)
	else:
		logging.warning("Missing translation for board_code: %s and cannot normalize board_name because it has not been able to recover!!!", board_code)

	code_found = board_code
	return code_found


def _get_breakdown_payments_from_all_sources(extra_info):
	payments_breakdown = []
	if extra_info.get("payed"):
		payments_breakdown.append(float(extra_info.get("payed", "0")))
	if extra_info.get("payed_by_cobrador"):
		payments_breakdown.append(float(extra_info.get("payed_by_cobrador", "0")))

	if extra_info.get("payed_by_tpv_link"):
		payed_by_tpv_link = extra_info.get("payed_by_tpv_link", [])
		for payment in payed_by_tpv_link:
			payments_breakdown.append(float(payment.get("amount", "0")))

	if extra_info.get("extra_payed_by_cobrador"):
		payments_breakdown.append(float(extra_info.get("extra_payed_by_cobrador", "0")))

	return payments_breakdown

def get_reservation_payments(hotel_info: list[dict], reservation,only_last_payment=None):
	USER = 'paratytech'
	PASS = 's3cur1tyRul3s@2021!'
	url_with_params = "/pages/cobrador/get_reservation_payments?hotel_code=%s&identifier=%s" % (hotel_info[0]["applicationId"], reservation.get("identifier"))
	if only_last_payment:
		url_with_params = url_with_params + '&only_last_payment=True'
	url_to_use = get_ultimate_cobrador_path(hotel_info=hotel_info[0])
	url_gateway = "%s%s" % (url_to_use, url_with_params)
	payload = {}
	headers = {'Content-Type': 'application/json'}
	try:
		response = requests.get(url_gateway, headers=headers, data=payload, auth=(USER, PASS))
	except Exception as e:
		logging.warning("Exception trying calling %s " % e)
		url_to_use = get_ultimate_cobrador_path(hotel_info=hotel_info[0], fail_over=True)
		url_gateway = "%s%s" % (url_to_use, url_with_params)
		response = requests.get(url_gateway, headers=headers, data=payload, auth=(USER, PASS))
	return response
