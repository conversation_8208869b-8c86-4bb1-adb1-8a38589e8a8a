import interface_to_implement
from dingus.dingus_utils import get_mix_remote_hotels
from dingus.handlers import utils
from handlers.base_handler import BaseIntegrationHandler
from dingus.dingus_constants import GET_HOTEL_INFO_OP, DUPLICATE_RATES_FOR_CALLCENTERS, CALL_CENTER_USER_GROUP
from dingus.dingus_constants import GET_HOTEL_INFO_RESPONSE_TEMPLATE
from dingus.dingus_constants import DEFAULT_LANGUAGE
from dingus.handlers.utils import get_hotel_info
from paraty.utils.hotel_manager_utils import get_integration_configuration_by_key, get_properties_at_integration_configuration
from paraty.utils import hotel_manager_utils as integration_hotel_manager_utils
from paraty.utils.xml_utils import XmlFinder
from paraty_commons_3.common_data.common_data_provider import get_promotions_of_hotel
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id


def get_hotel_product(hotel, language):


	rooms_list = build_rooms_list(hotel, language)
	rates_list = build_rate_list(hotel, language, add_callcenter_rates=True)
	boards_list = build_boards_list(hotel, language)
	promotions_list = build_promotions_list(hotel, language)

	hotel_products = {
		'name': hotel['name'],
		'key': hotel['applicationId'],
		'rooms': rooms_list,
		'boards': boards_list,
		'rates': rates_list,
		'promotions': promotions_list
	}

	remote_hotel_codes = get_mix_remote_hotels(hotel['applicationId'])

	if remote_hotel_codes:
		remote_product = []
		for remote_hotel_code in remote_hotel_codes:

			remote_hotel_obj = get_hotel_by_application_id(remote_hotel_code)
			if remote_hotel_obj:
				remote_hotel_product = _build_remote_product(remote_hotel_obj, language)
				remote_product.append(remote_hotel_product)

		hotel_products["remote_product"] = remote_product
	return hotel_products



def build_rooms_list(hotel, language, sufix_name = ""):
	rooms = integration_hotel_manager_utils.filtered_get_rooms_of_hotel(hotel, language)
	rooms_list = []
	for r in rooms:
		room = {
			'name': "%s%s" % (r['name'], sufix_name),
			'key': alphanumeric_to_id(r['key']),
			'capacities': get_room_capacities(r),
			'key_safe': r['key']
		}
		rooms_list.append(room)
	return rooms_list


def build_rate_list(hotel, language, add_callcenter_rates=True, sufix_name = ""):
	rates = integration_hotel_manager_utils.filtered_get_rates_of_hotel(hotel, language)

	multirates = integration_hotel_manager_utils.filtered_get_mutirates_of_hotel(hotel, language)
	rates.extend(multirates)

	all_callcenter_groups = {}
	if add_callcenter_rates:
		call_center_rates_remote_hotel = get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), DUPLICATE_RATES_FOR_CALLCENTERS)
		if call_center_rates_remote_hotel:
			external_hotel_code = hotel['applicationId']
			if not call_center_rates_remote_hotel.lower() == "true":
				external_hotel_code = call_center_rates_remote_hotel

			all_callcenter_groups = get_properties_at_integration_configuration(external_hotel_code, CALL_CENTER_USER_GROUP)


	rates_list = []
	for r in rates:
		rate = {
			'name': "%s%s" % (r.get('name', 'N/A'), sufix_name),
			'localName': "%s%s" % (r.get('localName', 'N/A'), sufix_name),
			'key': alphanumeric_to_id(r['key']),
			'key_safe': r['key']
		}
		rates_list.append(rate)


		if all_callcenter_groups:
			groups_call_center = all_callcenter_groups.keys()
			sufix_already_created = ""
			for sufix_rate_config in groups_call_center:
				sufix_rate = sufix_rate_config.replace("2", "")
				if not sufix_rate in sufix_already_created.split(";"):

					new_fake_rate = {
						'name': "%s - %s" % (r.get('name', 'N/A'), sufix_rate),
						'localName': "%s-%s" % (r.get('localName', 'N/A'), sufix_rate),
						'key': "%s-%s" % (alphanumeric_to_id(r['key']), sufix_rate),
						'key_safe': r['key']
					}
					rates_list.append(new_fake_rate)
					sufix_already_created += sufix_rate + ";"

	return rates_list



def build_boards_list(hotel, language, sufix_name=""):
	boards = integration_hotel_manager_utils.filtered_get_boards_of_hotel(hotel, language)
	boards_list = []
	for b in boards:
		board = {'name': "%s%s" % (b.get('name', 'N/A'), sufix_name),
				 'key': utils.to_dingus_code(hotel, alphanumeric_to_id(b['key'])),
				 'key_id':alphanumeric_to_id(b['key']),
				 'key_safe': b['key']}

		boards_list.append(board)

	return boards_list



def build_promotions_list(hotel, language, sufix_name=""):
	promotions = get_promotions_of_hotel(hotel)
	promotions_list = []
	for o in promotions:
		promotion = {
			'name': o.get('name', 'N/A'),
			'localName': o.get('localName', 'N/A'),
			'key': alphanumeric_to_id(o['key']),
			'key_safe': o['key']
		}
		promotions_list.append(promotion)
	return promotions_list


def _build_remote_product(hotel: dict, language: str) -> dict:

	remote_hotel_product = {"hotel_name": hotel.get("name")}

	sufix_name = " (%s)" % hotel.get("name")

	remote_hotel_product['rooms'] = build_rooms_list(hotel, language, sufix_name=sufix_name)
	remote_hotel_product['rates'] = build_rate_list(hotel, language, add_callcenter_rates=False, sufix_name=sufix_name)
	remote_hotel_product['boards'] = build_boards_list(hotel, language, sufix_name=sufix_name)

	return remote_hotel_product


def get_room_capacities(r):
	if 'capacities' not in r.keys():
		return

	capacities = r['capacities']
	normalized_capacities = []
	for c in capacities:
		c_split = c.split('-')
		item = {
			'adults': int(c_split[0]),
			'kids': int(c_split[1]),
			'babies': int(c_split[2]),
			'total': int(c_split[0]) + int(c_split[1]) + int(c_split[2])
		}
		normalized_capacities.append(item)

	capacity = {
		'all': normalized_capacities,
		'maxPax': sorted(
			normalized_capacities,
			key=lambda x: x['total'], reverse=True)[0]['total'],
		'minAdults': sorted(
			normalized_capacities, key=lambda x: x['adults'])[0]['adults'],
		'maxAdults': sorted(
			normalized_capacities,
			key=lambda x: x['adults'], reverse=True)[0]['adults'],
		'minKids': sorted(
			normalized_capacities, key=lambda x: x['kids'])[0]['kids'],
		'maxKids': sorted(
			normalized_capacities,
			key=lambda x: x['kids'], reverse=True)[0]['kids']
	}

	return capacity



class HotelInfoHandler(BaseIntegrationHandler):
	"""
	Provides information about the hotel in order to do the mappings (i.e. available rooms, rates and boards)
	"""

	operation = GET_HOTEL_INFO_OP
	template = GET_HOTEL_INFO_RESPONSE_TEMPLATE

	def _do_post(self, xml_finder: XmlFinder, request_id, request_body: str):
		user_hotels = self.user.hotels
		hotel = get_hotel_info(xml_finder, user_hotels)

		hotel_products = get_hotel_product(hotel, DEFAULT_LANGUAGE)
		return self._write_response(hotel_products, self.template)
