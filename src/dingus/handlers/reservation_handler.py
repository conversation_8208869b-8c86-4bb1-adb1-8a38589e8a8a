# -*- coding: utf-8 -*-
import copy
import datetime
import json
import logging

import re

import requests
from flask import request
from flask.views import MethodView
from google.cloud import ndb

import interface_to_implement
from dingus.dingus_constants import (
	BOOKING_RETRIEVAL_OP,
	NO_RETRIEVAL_ROOMS_IN_DINGUS,
	DEFAULT_AGE_FOR_BABY,
	DEFAULT_CURRENCY,
	CC_NAMES, ADD_PROMOCODE_TO_COMMENTS, NO_SHOW_MESSAGE_PAID_WITHOUT_CARD, MARK_RATE_COMMENTS, SHOW_OLD_FIELDS,
	DUPLICATE_RATES_FOR_CALLCENTERS, CALL_CENTER_USER_GROUP, MIX_REMOTE_HOTELES, ADD_MARKETING_IN_COMMENTS,
	NOT_INCLUDE_CLUB_IN_XML, ADD_SHARED_ROOMS, DISABLE_SPECIAL_WORD_PARKING, ACCEPT_CONDITIONS_AND_POLICIES,
	MARK_RESERVATIONS_DEPENDING_ON_RATE, INCLUDE_CLUB_IN_CLUB_LABEL, COBRADOR_SERVER_PATH)
from dingus.dingus_utils import get_text_version
from dingus.dingus_constants import BOOKING_RETRIEVAL_TEMPLATE
from dingus.dingus_constants import DEFAULT_AGE_FOR_ADULTS
from dingus.dingus_constants import DEFAULT_AGE_FOR_CHILDREN
from dingus.dingus_constants import HACKED_HOTELS_FOR_DINGUS
from dingus.handlers.utils import to_dingus_code, get_reservation_payments
from handlers.base_handler import BaseIntegrationHandler
from paraty.constants.integration_properties import SPECIAL_CURRENCY
from paraty.exceptions import WrongParamsException
from paraty.utils import date_utils
from paraty.utils import hotel_manager_utils
from paraty.utils.handler_utils import (
	extract_data_to_push_reservation_from_json,
	extract_extra_info_ccdata,
	_cc_are_throw_datatrans, _get_total_payed_amount_from_all_sources
)
from paraty.utils.hotel_manager_utils import (
	hotel_has_filter_reservation_rate,
	ADD_SERVICES_AMOUNT_IN_DAILY_ROOMS,
	get_properties_at_integration_configuration,
	DISCARD_BABIES_VIRTUAL_ROOMS_CAPACITY, get_integration_configuration_by_key, NEVER_SENT_PAYMENT_IN_MODIFICATIONS)
from paraty.utils.xml_utils import XmlFinder
from paraty_commons_3.common_data.common_data_provider import get_boards_of_hotel, get_rooms_of_hotel, \
	get_rates_of_hotel, get_conditions_of_hotel, get_pictures_for_entity, \
	get_multirates_of_hotel
from paraty_commons_3.common_data.data_management.advance_configs_utils import get_config_property_value
from paraty_commons_3.common_data.data_management.integrations_utils import get_integration_extra_map_properties, \
	get_integration_configuration_properties
from paraty_commons_3.common_data.data_management.marketing_logo_utils import get_all_marketing_logos
from paraty_commons_3.common_data.data_management.reservation_utils import get_reservations_of_hotel
from paraty_commons_3.common_data.data_management.web_configs_utils import get_web_configuration
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.decorators.cache.managers_cache.entities_utils import get_all_entities_timestamps, \
	refresh_entity_timestamp
from paraty_commons_3.decorators.cache.timebased_cache import set_thread_max_expiration_time
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id, get_internal_url
from paraty_commons_3.language_utils import SPANISH
from paraty_commons_3.utils.pci_utils import filter_for_pci_company


class ReservationsHandler(MethodView):

	def post(self):
		'''
		Note that Dingus doesn't support push reservation so we emulate it with a cache that is cleared when a reservation is sent
		'''
		hotel_code = request.values.get('hotel_code')
		logging.info("Clearing reservations cache for %s", hotel_code)
		refresh_entity_timestamp(hotel_code, 'Reservation')

		dont_send_last_payment = hotel_manager_utils.get_integration_configuration_by_key(
			hotel_code,
			interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
			"dont send last payment separated")
		if dont_send_last_payment:
			return "", 200

		if request.values.get('payed_in_this_transaction') and request.values.get('identifier') and request.values.get('payment_order_id'):
			logging.info("Push made from COBRADOR!!")
			#mark this reservation to be pushed!
			save_reservation_payment(hotel_code, request.values.get('identifier'), request.values.get('payment_order_id'), request.values.get('payed_in_this_transaction'))

		return "", 200


def get_nested_value(d, path):
	keys = path.split(".")
	for key in keys:
		if not isinstance(d, dict):
			return None
		d = d.get(key)
		if d is None:
			return None
	return d

def get_identifier_additional_service(hotel: dict, service_key: str) -> str|bool:
	'''
	Get the necessary identifier to sent in XML
	'''

	if not service_key:
		return "SERVICE"

	integration_name = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()

	integration_extra_maps = get_integration_extra_map_properties(integration_name, hotel['applicationId'])

	if not integration_extra_maps:
		logging.info("Not found integrationConfiguration for hotel: %s" % hotel['applicationId'])
		return False

	return integration_extra_maps.get(service_key, 'SERVICE')


def get_reservations_with_split(hotel_info, date_from, date_to, reservation_id):
	try:
		# First try to obtain reservations in one shot
		return get_reservations(hotel_info[0], date_from, date_to, reservation_id)
	except Exception as e:
		logging.warning("list of reservation too long... splitting by dates")
		logging.warning(e)

		# It failed (more than 1mb), split the range in 3 and try again
		date_from_obj = datetime.datetime.strptime(date_from, "%Y-%m-%d %H:%M:%S")
		date_to_obj = datetime.datetime.strptime(date_to, "%Y-%m-%d %H:%M:%S")

		midpoint = date_from_obj + (date_to_obj - date_from_obj) / 4
		midpoint_str = midpoint.strftime("%Y-%m-%d %H:%M:%S")

		midpoint_2 = midpoint + (date_to_obj - date_from_obj) / 6
		midpoint_2_str = midpoint_2.strftime("%Y-%m-%d %H:%M:%S")

		midpoint_3 = midpoint_2 + (date_to_obj - date_from_obj) / 6
		midpoint_3_str = midpoint_3.strftime("%Y-%m-%d %H:%M:%S")

		logging.info("Date from: %s", date_from)
		logging.info("Date to: %s", date_to)
		logging.info("Splitting reservations in 3 periods: %s - %s - %s", midpoint_str, midpoint_2_str, midpoint_3_str)

		# Get bookings for each period
		reservations_first_half = get_reservations(hotel_info[0], date_from, midpoint_str, reservation_id)
		reservations_second_half = get_reservations(hotel_info[0], midpoint_str, midpoint_2_str, reservation_id)
		reservations_third_half = get_reservations(hotel_info[0], midpoint_2_str, midpoint_3_str, reservation_id)
		reservations_fourth_half = get_reservations(hotel_info[0], midpoint_3_str, date_to, reservation_id)

		# Combine result and return it
		return reservations_first_half + reservations_second_half + reservations_third_half + reservations_fourth_half

class PendingPaymentReservation(ndb.Model):

	#key map
	# String property is indexed by default (can't be avoided on actual NDB version)
	hotel_code = ndb.StringProperty(indexed=True)
	identifier = ndb.StringProperty()
	payment_order = ndb.StringProperty()
	amount = ndb.TextProperty(indexed=False)
	timestamp = ndb.DateTimeProperty(auto_now=True)

def save_reservation_payment(hotel_code, identifier, payment_order, amount):
	'''
	This is required so that later on we can finish the booking ()
	'''
	try:
		id_payment = hotel_code + identifier + payment_order
		logging.info("Saving payment for  this reservation: %s Payment order Id: %s amount paid: %s", identifier, payment_order, amount)

		new_payment = PendingPaymentReservation(id=id_payment)
		new_payment.hotel_code = hotel_code
		new_payment.identifier = identifier
		new_payment.amount = str(amount)
		new_payment.payment_order = payment_order

		new_payment.put()
	except Exception as e:
		logging.error("Error _save_reservation_payment")
		logging.error(e)

def get_reservations_with_pending_payments(hotel: dict):
	result_reservations = []
	try:
		hotel_code = hotel['applicationId']
		logging.info("get_reservations_with_pending_payments for  hotel_code: %s", str(hotel_code))

		payments_for_hotel = PendingPaymentReservation.query(PendingPaymentReservation.hotel_code == hotel_code)

		#payments_for_hotel = PendingPaymentReservation.query()
		#payments_for_hotel = payments_for_hotel.filter(PendingPaymentReservation.hotel_code == hotel_code)


		# DEV purposes
		'''
		aux = PendingPaymentReservation()
		aux.identifier = "57F8ADC70"
		aux.amount = "123.45"
		aux.payment_order = "33333"
		aux.timestamp = datetime.datetime.now()
		payments_for_hotel = [aux]
		'''


		list_of_keys = []
		for payment_info in payments_for_hotel:
			identifier = payment_info.identifier

			#thirty trick!!! to be removed in 2 days!!
			if "madeira-centro" in hotel['url']:
				pending_reservations = get_reservations_no_cache(hotel, "", "", identifier)
			else:
				pending_reservations = get_reservations(hotel, "", "", identifier)

			if pending_reservations:
				new_res = pending_reservations[0]
				logging.info("payment_info key: %s", payment_info.key.id())
				list_of_keys.append(payment_info.key.id())
				new_res["send_this_transaction"] = {
					"amount": payment_info.amount,
					"payment_order": payment_info.payment_order,
					"timestamp": payment_info.timestamp.strftime("%Y-%m-%d")
				}
				result_reservations.append(new_res)

				logging.info("Payment to be sent found  for %s: %s", identifier, new_res["send_this_transaction"])


		#and remove pending payment
		if list_of_keys:
			logging.info("Deleting payments %s", list_of_keys)
			#ndb.delete_multi(list_of_keys)
			ndb.delete_multi([i.key for i in payments_for_hotel])
	except Exception as e:
		logging.error("Error get_reservations_with_pending_payments")
		logging.error(e)



	return result_reservations


def clean_html_reservation(reservation):
	try:
		extra_info_json = json.loads(reservation.get('extraInfo'))
		rate_condition_info = extra_info_json.get('RateConditionsOrigin', {})
		rate_condition_info_hotel = extra_info_json.get('RateConditionsOriginForHotel', {})

		if rate_condition_info.get('rateConditionDescription'):
			rate_condition_info.pop('rateConditionDescription')
			extra_info_json["RateConditionsOrigin"] = rate_condition_info

		if rate_condition_info_hotel.get('rateConditionDescription'):
			rate_condition_info_hotel.pop('rateConditionDescription')
			extra_info_json["RateConditionsOriginForHotel"] = rate_condition_info_hotel

		reservation["extraInfo"] = json.dumps(extra_info_json)
	except Exception as e:
		logging.warning("Error cleaning HTML all reservations")
		logging.warning(e)

'''@managers_cache(
	hotel_code_provider=lambda f,a,k: a[0]['applicationId'],
	key_generator=lambda f,a,k: f'get_reservations_{a[0]["applicationId"]}_{a[1]}_{a[2]}_{a[3]}',
	entities='Reservation',
	requires_compression=True,
	ttl_seconds=60*60*24*2,
	background_refresh=False
)'''
def get_reservations(hotel, date_from, date_to, reservation_id):
	logging.info("Fetching reservations for hotel: %s", hotel['applicationId'])

	try:

		# json is too big to be cacheed. let's clean long html (rateConditionDescription)
		reservations = get_reservations_of_hotel(hotel, date_from, date_to, reservation_id=reservation_id, include_cancelled_reservations=True, include_end_date=True, discard_test_reservations=True, include_modified_reservations=True)
		for reservation in reservations:
			clean_html_reservation(reservation)

		return reservations
	except Exception as e:
		logging.error("Error getting all reservations. Maybe too many reservations to show")
		logging.warning(e)
		logging.warning("Let's show at least new and cancel reservations. Excluding modifications!")
		return get_reservations_of_hotel(hotel, date_from, date_to, reservation_id=reservation_id, include_modified_reservations=False, include_cancelled_reservations=True, include_end_date=True)



def get_reservations_no_cache(hotel, date_from, date_to, reservation_id):
	logging.info("get_reservations_no_cache Fetching reservations for hotel: %s", hotel['applicationId'])

	try:
		return get_reservations_of_hotel(hotel, date_from, date_to, reservation_id=reservation_id, include_cancelled_reservations=True, include_end_date=True, include_modified_reservations=True)
	except Exception as e:
		logging.error("Error getting all reservations. Maybe too many reservations to show")
		logging.warning(e)
		logging.warning("Let's show at least new and cancel reservations. Excluding modifications!")
		return get_reservations_of_hotel(hotel, date_from, date_to, reservation_id=reservation_id, include_modified_reservations=False, include_cancelled_reservations=True, include_end_date=True)


class ReservationListHandler(BaseIntegrationHandler):
	operation = BOOKING_RETRIEVAL_OP
	template = BOOKING_RETRIEVAL_TEMPLATE

	def _do_post(self, xml_finder: XmlFinder, request_id, request_body: str):

		default_currency = DEFAULT_CURRENCY

		specific_params = {
			"RES_STATUS_NEW_RESERVATION": "Reserved",
			"RES_STATUS_CANCELLATION": "Cancelled",
			"AGE_QUALIFYING_CODE_ADULT": 10,
			"AGE_QUALIFYING_CODE_CHILD": 8,
			"AGE_QUALIFYING_CODE_BABIES": 2,
			"LANGUAGE": "SPANISH",
			"INCLUDE_CAPACITIES": True, #for not merged same rooms with differents capacities
			"NOT_MERGE_SAME_ROOMS": True,
			"DISCARD_BABIES": False
		}

		hotel_code_elem = xml_finder.find_element("HotelCode")
		if not hotel_code_elem:
			raise WrongParamsException("HotelCode not found: %s" % hotel_code_elem)

		hotel_id = hotel_code_elem.text
		logging.info("Booking retrieval for hotel: %s", hotel_id)
		hotel = get_hotel_by_application_id(hotel_id)
		if not hotel:
			raise WrongParamsException('Hotel Not Found')

		# is posible that we have to add extra reservations for other hotel
		mix_remote_hotels = MIX_REMOTE_HOTELES.get(hotel_id, [])
		mix_remote_hotels = mix_remote_hotels.copy()

		if not mix_remote_hotels:
			mix_remote_hotels = [hotel_id]
		else:
			logging.info("mix_remote_hotels FOUND for hotel: %s -> %s", hotel_id, mix_remote_hotels)
			if hotel_id not in mix_remote_hotels:
				mix_remote_hotels.append(hotel_id)

		reservations = []
		disable_reservations = hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "disable reservations")
		if not disable_reservations:
			for remote_hotel_id in mix_remote_hotels:
				remote_reservations = self.get_list_reservations_from_hotel(default_currency, remote_hotel_id, specific_params, xml_finder)
				logging.info("Total reservations for hotel %s: %s", remote_hotel_id, len(remote_reservations))
				reservations += remote_reservations

		logging.info("Total reservations after posible mix: %s", len(reservations))

		integration_name = interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name()
		integration_configurations = get_integration_configuration_properties(integration_name, hotel['applicationId'])

		not_send_pay_info = bool(integration_configurations.get(NEVER_SENT_PAYMENT_IN_MODIFICATIONS, False))
		show_old_fields = bool(integration_configurations.get(SHOW_OLD_FIELDS, False))
		hide_babies_tag = bool(integration_configurations.get("hide babies tag", False))

		hide_cvv = True
		if integration_configurations.get("send cvv"):
			hide_cvv = False

		context_to_render = {
			'hotelName': hotel['name'],
			'hotelCode': hotel_id,
			'reservations': reservations,
			'show_old_fields': show_old_fields,
			'not_send_pay_info': not_send_pay_info,
			'hide_cvv': hide_cvv,
			'hide_babies_tag': hide_babies_tag
		}

		for reservation in reservations:
			identifier = reservation.get('global_info').get('res_identifier')
			logging.info("Giving back identifier: %s", identifier)

		return self._write_response(context_to_render, self.template)


	def get_list_reservations_from_hotel(self, default_currency, hotel_id, specific_params, xml_finder: XmlFinder):
		logging.info("Getting reservations for hotel: %s", hotel_id)
		hotel_info = [get_hotel_by_application_id(hotel_id)]
		if not hotel_info:
			raise WrongParamsException('Hotel Not Found')
		hotel = hotel_info[0]

		if hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), ADD_SERVICES_AMOUNT_IN_DAILY_ROOMS):
			logging.info("ADD_SERVICES_AMOUNT_IN_DAILY_ROOMS FOUND!!")
			specific_params["ADD_SERVICES_AMOUNT_IN_DAILY_ROOMS"] = True
		special_currency = hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), SPECIAL_CURRENCY)
		if special_currency:
			logging.info("Special currency FOUND FOUND!!: %s", special_currency)
			specific_params["CURRENCY_CODE"] = special_currency
		else:
			specific_params["CURRENCY_CODE"] = default_currency
		discard_babies_virtual_rooms = hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), DISCARD_BABIES_VIRTUAL_ROOMS_CAPACITY)
		if discard_babies_virtual_rooms:
			specific_params["DISCARD_BABIES"] = True

		if hotel_manager_utils.get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), ADD_SHARED_ROOMS):
			specific_params["ADD_SHARED_ROOMS"] = True

		# it is posible that we're sending reservations after integrations with multitars so...
		all_rates_and_multirates = get_rates_of_hotel(hotel, include_removed=True) + get_multirates_of_hotel(hotel, include_removed=True)
		hotel_all_rates = {str(alphanumeric_to_id(x['key'])): x for x in all_rates_and_multirates}

		# Mandatory tags
		try:
			date_from = xml_finder.find_element('ModifiedDateFrom')
			date_to = xml_finder.find_element('ModifiedDateTo')

			logging.info("Booking retrieval for dates from: %s to: %s", date_from, date_to)

			if not date_from or not date_from.text:
				raise WrongParamsException('Missing mandatory tag: ModifiedDateFrom')
			if not date_to or not date_to.text:
				raise WrongParamsException('Missing mandatory tag: ModifiedDateTo')

			date_from = date_from.text
			date_to = date_to.text

		except KeyError as e:
			raise WrongParamsException('Missing mandatory attribute: %s' % e)

		# Optional tags
		try:
			reservation_id = xml_finder.find_element('ReservationID')
			if reservation_id:
				reservation_id = reservation_id.text
		except:
			reservation_id = None
		reservations = self._generate_reservations_list(hotel_info, hotel_all_rates, date_from, date_to, reservation_id, specific_params)
		logging.info("Total reservations found: %s", len(reservations))
		# note that for not loose reservations, we're going to send twice in Dingus
		# send_twice = False
		# if "best-soldor" in hotel_id:
		#	logging.info("SENDING TWICE -> ON")
		send_twice = True
		reservations = filter_for_pci_company(hotel, reservations, send_twice=send_twice)
		logging.info("Total reservations after pci filtering: %s", len(reservations))

		return reservations

	def _exist_in(self, filters, text):
		for filter_element in filters:
			if filter_element.strip().lower() in text.strip().lower():
				return True
		return False


	def _reservation_has_bad_rate(self, blacklist_rates_, extracted_data):
		if blacklist_rates_:
			for room_stay in extracted_data.get("room_stays", []):
				for text in blacklist_rates_:
					if text and text.strip().lower() in room_stay.get("rate_internal_name", "").lower():
						return True
		return False

	def _generate_reservations_list(self, hotel_info: list[dict], hotel_all_rates, date_from, date_to, reservation_id, specific_params):
		reservations_list = []
		all_rooms = {str(alphanumeric_to_id(x['key'])): x for x in get_rooms_of_hotel(hotel_info[0], include_removed=True)}
		blacklist_rates = hotel_has_filter_reservation_rate(hotel_info[0]['applicationId']).split("@@")
		rates_map = get_properties_at_integration_configuration(hotel_info[0]['applicationId'], "dingus","rateMap")
		rooms_map = get_properties_at_integration_configuration(hotel_info[0]['applicationId'], "dingus","roomMap")

		boards = get_boards_of_hotel(hotel_info[0], include_removed=True)
		board_map = {x['key']: x for x in boards}

		all_marketing_logos = []
		add_marketing_in_comments = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																						  interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																						  ADD_MARKETING_IN_COMMENTS)

		integrate_pending_reservations = hotel_manager_utils.get_integration_configuration_by_key(
										hotel_info[0]['applicationId'],
										interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
										"push reservation even status pending")


		if add_marketing_in_comments:
			#important! marketingLogos hasn't to be enabled for works!!!!!
			all_marketing_logos = get_all_marketing_logos(hotel_info[0]['applicationId'])

		#reservation_list = get_reservations(hotel_info[0], date_from, date_to, reservation_id)
		reservation_list = get_reservations_with_split(hotel_info, date_from, date_to, reservation_id)

		#add posible payemnts from cobrador!
		reservation_list += get_reservations_with_pending_payments(hotel_info[0])

		for reservation in reservation_list:

			extra_info_text = reservation.get('extraInfo')
			if extra_info_text and isinstance(extra_info_text, str):
				extra_info = json.loads(extra_info_text)
			else:
				extra_info = reservation.get('extraInfo')

			extracted_data = extract_data_to_push_reservation_from_json(reservation, hotel_info[0]['applicationId'], specific_params=specific_params)
			extracted_data = self._generate_reservation_data_dict(reservation, extracted_data, hotel_info, hotel_all_rates, rates_map, rooms_map, board_map, all_marketing_logos, specific_params, all_rooms)
			if reservation.get("send_this_transaction"):
				extracted_data["send_this_transaction"] = reservation.get("send_this_transaction")
			else:
				extracted_data["send_this_transaction"] = None

			rate_is_ok = not self._reservation_has_bad_rate(blacklist_rates, extracted_data)
			status_is_ok = True
			if extra_info.get("status_reservation") and not extra_info["status_reservation"] == "confirmed":
				status_is_ok = extra_info["status_reservation"] == "pending" and integrate_pending_reservations

			reservation_is_ok = rate_is_ok and status_is_ok
			if extracted_data and reservation_is_ok:
				reservations_list.append(extracted_data)

		return reservations_list

	def _extract_num_guest(self, guest_info, specific_params):

		adults = 0
		kids = 0
		babies = 0

		for guest in guest_info:
			if guest.get("qualifying_code", "") == specific_params["AGE_QUALIFYING_CODE_ADULT"]:
				adults = guest.get("num_persons", "")
			if guest.get("qualifying_code", "") == specific_params["AGE_QUALIFYING_CODE_CHILD"]:
				kids = guest.get("num_persons", "")
			if guest.get("qualifying_code", "") == specific_params["AGE_QUALIFYING_CODE_BABIES"]:
				babies = guest.get("num_persons", "")

		return adults, kids, babies


	def _extract_ccdata_raw(self, reservation):

		extra_info = reservation.get('extraInfo')
		if extra_info and isinstance(extra_info, str):
			extra_info = json.loads(extra_info)
			return extra_info.get('cc_datas')

		return None

	def _add_promocode_to_comments(self, extracted_data):

		promocode = extracted_data['global_info']['promocode']
		if promocode:
			comments = extracted_data['global_info']['comments']
			extracted_data['global_info']['comments'] = "%s\n\nPromocode: %s" % (comments, promocode)

	def _add_mark_rate_to_comments(self, token, rate_key, hotel_all_rates, extracted_data):

		for key, value in hotel_all_rates.items():
			if rate_key == value.get('key',''):
				if token.upper() in value.get('localName','').upper():
					comments = extracted_data['global_info']['comments']
					extracted_data['global_info']['comments'] = "%s\n\n%s" % (comments, token.upper())


	def marketind_dates_are_ok(self, marketing_logo, start_date, end_date):
		if marketing_logo.get('startDate') and marketing_logo.get('endDate'):
			return marketing_logo['startDate'] <= start_date and marketing_logo['endDate'] >= end_date

		if marketing_logo.get('searchRequiredDate'):
			for requiredDate in marketing_logo['searchRequiredDate']:
				if start_date <= requiredDate < end_date:
					return True

			logging.info("Starpromotion dates aren't satisfied")
			return False

		return True

	def night_count_is_satisfied(self, marketing_logo, start_date, end_date):
		search_start = datetime.datetime.strptime(start_date, '%Y-%m-%d')
		search_end = datetime.datetime.strptime(end_date, '%Y-%m-%d')
		search_nights = (search_end - search_start).days

		if marketing_logo.get('min_nights'):
			if search_nights < int(marketing_logo['min_nights']):
				return False

		if marketing_logo.get('max_nights'):
			if search_nights > int(marketing_logo['max_nights']):
				return False

		if marketing_logo.get('nights'):
			if search_nights != int(marketing_logo['nights']):
				return False

		return True


	def get_marketing_logos_text(self, pic_info_for_text):
		marketing_info = {}
		if pic_info_for_text:
			marketing_info["marketing_name"] = pic_info_for_text.get("name", "")
			marketing_info["marketing_description"] = pic_info_for_text.get("description", "")

		return marketing_info

	def _add_marketing_text_to_comments(self, hotel: dict, room_key, rate_key, board_key, promotion_key, all_marketing_logos, extracted_data):

		for marketing_logo in all_marketing_logos:
			marketing_key = marketing_logo.get("key")

			board_ok = (not marketing_logo.get("board")) or board_key in marketing_logo.get("board", "")
			rate_ok = (not marketing_logo.get("rate")) or rate_key in marketing_logo.get("rate")
			room_ok = (not marketing_logo.get("room")) or rate_key in marketing_logo.get("room")
			promotion_ok = (not marketing_logo.get("promotion")) or (promotion_key and promotion_key in marketing_logo.get("promotion"))

			conditions_ok = board_ok and rate_ok and room_ok and promotion_ok
			with_a_pic = marketing_logo.get("pictures")

			#easy and direct checks!
			if conditions_ok and with_a_pic:
				#conditions from advanced properties of main pic
				pic_info_for_text = get_pictures_for_entity(hotel['applicationId'], marketing_key, SPANISH, include_extra_props=True)
				if pic_info_for_text:
					pic_info_for_text = pic_info_for_text[0]

					start_date = extracted_data.get("global_info", {}).get("start")
					end_date = extracted_data.get("global_info", {}).get("end")
					dates_ok = self.marketind_dates_are_ok(marketing_logo, start_date, end_date)
					min_stays_ok = self.night_count_is_satisfied(marketing_logo, start_date, end_date)

					if dates_ok and min_stays_ok:
						marketing_info = self.get_marketing_logos_text(pic_info_for_text)
						if marketing_info:

							marketing_description = get_text_version(marketing_info.get("marketing_description", ""))
							marketing_name = get_text_version(marketing_info.get("marketing_name", ""))

							if marketing_description or marketing_name:
								text_marketing = "%s\n%s" % (marketing_name,marketing_description)
								comments = extracted_data['global_info']['comments']
								extracted_data['global_info']['comments'] = "%s\n\n%s" % (comments, text_marketing)


	def get_additional_services_1(self, reservation, index_room, service_unitary_price, extracted_data):
		# meter aquí TODOS los extras SÓLO en la primera habitación a no ser que tenga servicios unitarios
		'''
		que tenemos:
		extracted_data['extras'] = [{servicio: servicio1; quantity: 1}, {servicio: servicio2; quantity: 2}]
		que queremos:
		room1: [{servicio: servicio1; quantity: 1}, {servicio: servicio2; quantity: 1}]
		room2: [{servicio: servicio2; quantity: 1}]


		PERO
		Si tenemos
		extracted_data['extras'] = [{servicio: servicio1; quantity: 6}]
		Y 3 habitiaciones:

		Queremos:

		room1: [{servicio: servicio1; quantity: 2}]
		room2: [{servicio: servicio1; quantity: 2}]
		room3: [{servicio: servicio1; quantity: 2}]
		'''

		all_extras = extracted_data.get('extras', [])
		real_index_room = index_room + 1
		extras_in_this_room = []

		num_rooms = int(reservation.get("numRooms", 1))

		if (not service_unitary_price) and real_index_room == 1:
			# inicializate  in first room always ALL sups (as always)
			extras_in_this_room = all_extras


		elif service_unitary_price and all_extras:

			if num_rooms == 1:
				# inicializate  in the "unique" room  always ALL sups
				extras_in_this_room = all_extras
			else:

				for extra in all_extras:

					if not "quantity_count" in extra:
						extra["quantity_count"] = extra["original_quantity"]

					# solo añadimos la capacidad en la siguiente habitación si me quedan servicios que añadir
					# extra_in_room = copy.deepcopy(extra)
					# quantity_in_room = int(extra_in_room.get("original_quantity", 0))
					# if extra_in_room.get("original_quantity", 0) >= real_index_room:

					if real_index_room < num_rooms:
						# IT IS NOT THE LAST ROOMS!! so only always one quantity!

						quantity_in_this_room = 1
						if int(extra["original_quantity"]) > num_rooms:
							quantity_in_this_room = int(int(extra["original_quantity"]) / num_rooms)

						extra["quantity"] = quantity_in_this_room
						extra["quantity_count"] = extra["quantity_count"] - quantity_in_this_room
						extras_in_this_room.append(extra)

					elif extra.get("quantity_count", 0) > 0:
						# LAST ROOM!!! the pending amounts!!
						extra["quantity"] = extra.get("quantity_count")
						extras_in_this_room.append(extra)
		return extras_in_this_room


	def get_map_service_code(self, hotel, service):
		inventory_code = get_identifier_additional_service(hotel, service.get("service_key"))
		if inventory_code and "@@" in inventory_code:
			info_service = inventory_code.split("@@")
			prefix = info_service[0]
			if len(info_service) > 1:
				inventory_code = ""
				for json_path in info_service[1:]:
					service_data = get_nested_value(service, json_path)
					if service_data:
						inventory_code += service_data
					elif json_path:
						# not a path, maybe a separator,just include it
						inventory_code += json_path

			inventory_code = prefix + inventory_code
		return inventory_code
	def build_additional_service_2(self, service, extracted_data, hotel):

		service_2 = {}

		name = service.get("name")
		if service.get("hour"):
			name += ". Hour: %s" % service.get("hour")
		service_2['name'] = name
		service_2['quantity'] = service.get("amount")

		days = service.get("days", 1)

		reservation_date_start = extracted_data['global_info']['start']
		date_start_ts = datetime.datetime.strptime(reservation_date_start, "%Y-%m-%d")
		reservation_date_end = extracted_data['global_info']['end']
		end_ts = datetime.datetime.strptime(reservation_date_end, "%Y-%m-%d")
		reservation_days = (end_ts - date_start_ts).days

		# be careful with modification: it's posible that dates were changed, but not services.
		# Never add more days in services that total reservations days
		if days > reservation_days:
			days = reservation_days


		if service.get("date"):
			service_date_start = service.get("date")
		else:
			service_date_start = reservation_date_start

		# pay attention that for one-day-service start and end data have to be the same (it's not like stays)
		service_start_ts = datetime.datetime.strptime(service_date_start, "%Y-%m-%d")
		delta = datetime.timedelta(days=days - 1)
		date_end_ts = service_start_ts + delta
		service_date_end = date_end_ts.strftime("%Y-%m-%d")

		total_price = service.get("price")
		if not total_price:
			total_price = 0.0

		details = {'price': total_price, 'date_start': service_date_start, 'date_end': service_date_end}
		service_2['details'] = details

		map_code = self.get_map_service_code(hotel, service)

		service_2['code'] = map_code


		return service_2

	def get_additional_services_2(self, reservation, index_room, extracted_data, hotel):
		extras_in_this_room = []
		if reservation.get('additionalServices2'):
			services_all = reservation.get("additionalServices2", {})
			if isinstance(services_all, str):
				services_all = json.loads(services_all)
			for service in services_all.get("services_list", []):

				service_index_room = service.get("room_index", "")

				im_the_correct_room = False
				if service_index_room or service_index_room == 0 or service_index_room == "0":
					service_index_room = int(service_index_room)
					im_the_correct_room = service_index_room == index_room

				no_index_but_im_first_room = (index_room == 0) and not service_index_room
				add_service_in_this_room = no_index_but_im_first_room or im_the_correct_room

				if add_service_in_this_room:
					service_2 = self.build_additional_service_2(service, extracted_data, hotel)
					extras_in_this_room.append(service_2)


		return extras_in_this_room

	def build_extras_for_room(self, reservation, index_room, service_unitary_price, extracted_data, hotel):

		if reservation.get("additionalServices2"):
			extras_in_this_room = self.get_additional_services_2(reservation, index_room, extracted_data, hotel)

		else:
			extras_in_this_room = self.get_additional_services_1(reservation, index_room, service_unitary_price, extracted_data)


		return extras_in_this_room


	#TODO: refactor this method in several submethods!
	def _generate_reservation_data_dict(self, reservation, extracted_data, hotel_info: list[dict], hotel_all_rates, rates_map, rooms_map, board_map, all_marketing_logos, specific_params, all_rooms):
		extracted_data['update_date'] = extracted_data['creation_time']

		try:
			extra_info = json.loads(reservation.get("extraInfo", "{}"))
		except Exception as e:
			logging.error("Error decoding extra_info: %s" % str(e))
			extra_info = {}

		extracted_data['payment_method'] = ""
		payment_method = reservation.get('creditCard')
		if payment_method:
			if "BANK TRANSFER" in payment_method:
				extracted_data['payment_method'] = "Bank Transfer"
			elif "PAYPAL" in payment_method:
				extracted_data['payment_method'] = "Paypal"
			elif len(payment_method) > 0 and "CREDIT CARD CONDITIONAL" not in payment_method:
				extracted_data['payment_method'] = "Credit Card"

		integration_configuration = get_integration_configuration_properties(interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), hotel_info[0]['applicationId'])

		send_cc = get_config_property_value("Save CC datas", hotel_info[0]['applicationId'])
		dont_send_cc = integration_configuration.get("dont send cc datas")

		if send_cc and (not dont_send_cc):
			cc_datas = self._get_desencrypt_cc_datas(reservation, hotel_info)
			#in this moment we can only show CC if they are enclypted throw datatrans!!

			bookandpayment = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "bookandpayment")
			if bookandpayment:
				cc_datas = self.replace_name_creditcards(cc_datas)

			cc_tokens_already_sent_once = extra_info.get('pciTimestamp')
			cc_tokens_already_sent_twice = extra_info.get('pciTimestamp2')

			if cc_tokens_already_sent_once:
				logging.info("reservation %s cc tokens already sent FIRST TIME to datatrans the date %s", reservation.get("identifier"), cc_tokens_already_sent_once)

			if cc_tokens_already_sent_twice:
				logging.info("reservation %s cc tokens already sent TWICE to datatrans the date %s", reservation.get("identifier"), cc_tokens_already_sent_twice)

			if (not cc_tokens_already_sent_once) and cc_datas and _cc_are_throw_datatrans(reservation):
				extracted_data["cc_datas"] = cc_datas

		no_show_message_paid_without_card = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																									 interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																									 NO_SHOW_MESSAGE_PAID_WITHOUT_CARD)
		if not no_show_message_paid_without_card and not extra_info.get("payed"):
			ccdata_raw = self._extract_ccdata_raw(reservation)
			if not ccdata_raw:
				extracted_data['global_info']['comments'] += "\nReservation done without card\n"

		add_promocode_to_comments = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																				  interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																				  ADD_PROMOCODE_TO_COMMENTS)
		if add_promocode_to_comments:
			self._add_promocode_to_comments(extracted_data)

		extra_room_options = extra_info.get("extra_room_options")
		if extra_room_options:
			self._add_info_extra_to_comments(all_rooms, extra_room_options, extracted_data)

		mark_reservations_depending_on_rate = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), MARK_RESERVATIONS_DEPENDING_ON_RATE)
		if mark_reservations_depending_on_rate:
			rate_condition_key = json.loads(reservation.get('extraInfo')).get('RateConditionsOriginForHotel', {}).get('condition_key', '')
			self._add_nrf_info_to_comments(extracted_data, hotel_info[0], rate_condition_key)

		if extracted_data['reservation_status'] == specific_params['RES_STATUS_CANCELLATION'] and 'modification_time' in extracted_data.keys():
			extracted_data['update_date'] = extracted_data['modification_time']

		if extra_info.get("payed") or extra_info.get("payed_by_cobrador") or extra_info.get("payed_by_tpv_link"):
			last_payment_reservation = get_reservation_payments(hotel_info, reservation, only_last_payment=True)
			if last_payment_reservation.status_code == 200 and json.loads(last_payment_reservation.text):
				last_payment = json.loads(last_payment_reservation.text.encode('utf-8'))
				if extra_info.get("payed_by_tpv_link"):
					last_payment_by_link = sorted(extra_info.get("payed_by_tpv_link"), key=lambda k: k['timestamp'], reverse=True)[0]
					if last_payment_by_link.get("timestamp") > last_payment.get("timestamp"):
						last_payment = last_payment_by_link
				extracted_data['last_payment_timestamp'] = last_payment.get("timestamp")
				extracted_data['update_date'] = datetime.datetime.now().isoformat()

		service_unitary_price = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "services unitary price")
		service_unitary_price_with_quantity = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "services unitary price with quantity")

		add_unique_room_id_date =  hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																	interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																	"add unique room id from date")

		if add_unique_room_id_date and add_unique_room_id_date <= reservation.get("timestamp"):
			extracted_data['add_unique_room_id'] = True

		extracted_data['extras'] = []
		extracted_data['additional_infos'] = []

		additional_services = extracted_data['global_info']['additional_services']

		percent_discounted = 0
		total_price_supplements = 0
		try:
			if reservation.get("priceSupplements"):
				reservation_price_supplements = float(reservation.get("priceSupplements"))
				if additional_services:
					for service in additional_services:
						price = service.get("price")
						if price:
							total_price_supplements += float(price)
				if reservation_price_supplements < total_price_supplements:
					percent_discounted = abs((reservation_price_supplements - total_price_supplements) / total_price_supplements * 100)
		except Exception as e:
			logging.warning("Error calculating discounts")
			logging.warning(e)



		if additional_services:
			for service in additional_services:

				name = service['name']
				quantity = service['quantity']
				days = service['days']
				price = service['price']
				if percent_discounted:
					try:
						price = str(float(price) - (float(price) *(percent_discounted/100)))
					except Exception as e:
						logging.warning("Error calculating discounts")
						logging.warning(e)


				price_per_day = float(price)/float(days)
				quantity_for_xml = quantity
				if service_unitary_price:
					price_per_day = (price_per_day / float(quantity))

				if service_unitary_price_with_quantity:
					price_per_day = (price_per_day / float(quantity))

				price_per_day = "%.2f" % price_per_day

				inventory_code = service['inventory_code']
				description = "%s - cantidad: %s - dias: %s - precio: %s" % (name, quantity, days, price)
				extracted_data['additional_infos'].append({'name': inventory_code, 'value': description})

				service_date_start = extracted_data['global_info']['start']
				date_start_ts = datetime.datetime.strptime(service_date_start, "%Y-%m-%d")

				reservation_date_end = extracted_data['global_info']['end']
				end_ts = datetime.datetime.strptime(reservation_date_end, "%Y-%m-%d")

				reservation_days= (end_ts-date_start_ts).days

				#be careful with modification: it's posible taht dates were changed, but not services. Never add more days in services that total reservations days
				if days > reservation_days:
					days = reservation_days

				#pay attention that for ona-day-service start and end data have to be the same (it's not like stays)
				delta = datetime.timedelta(days=days-1)
				date_end_ts = date_start_ts + delta
				service_date_end = date_end_ts.strftime("%Y-%m-%d")

				details = {'price': price_per_day, 'date_start': service_date_start, 'date_end': service_date_end}
				if inventory_code and (not inventory_code == "SERVICE"):
					#service is the defaiult code, so this service is unmapped
					extracted_data['extras'].append({'name': name, 'code': inventory_code, 'quantity': quantity_for_xml, 'details': details, 'value': description, 'original_quantity': quantity})


		index_room = 0

		for room_stay in extracted_data['room_stays']:
			room_stay['reserved_rooms'] = []

			if not hotel_all_rates.get(str(room_stay['rate_type_code'])):
				logging.warning("Discarding reservation %s because its rate is not a valid rate (maybe delete)", reservation["identifier"])
				return {}

			#check virtual room (map)

			room_stay_key = all_rooms.get(str(room_stay.get("room_type_code")), {}).get("key")

			if room_stay_key and rooms_map and rooms_map.get(room_stay_key):
				logging.info("Converting Room: %s to %s becasue of rateMaps", room_stay['room_type_code'], rooms_map.get(room_stay_key))
				room_stay['room_type_code'] = int(rooms_map.get(room_stay_key))

			if NO_RETRIEVAL_ROOMS_IN_DINGUS.get(hotel_info[0]['applicationId']) and (int(room_stay["room_type_code"]) in NO_RETRIEVAL_ROOMS_IN_DINGUS.get(hotel_info[0]['applicationId'])):
				logging.warning("Discarding reservation %s because have a prohibited room code", reservation["identifier"])
				return {}

			#check virtual rates (map)
			if rates_map and rates_map.get(reservation.get("rate", "")):
				logging.info("Converting rate: %s to %s becasue of rateMaps", room_stay['rate_type_code'], rates_map.get(reservation.get("rate", "")))
				room_stay['rate_type_code'] = rates_map.get(reservation.get("rate", ""))



			try:
				room_stay['rate_name'] = hotel_all_rates[str(room_stay['rate_type_code'])]['name']
			except Exception as e:
				room_stay['rate_name'] = u""

			try:
				room_stay['rate_internal_name'] = hotel_all_rates[str(room_stay['rate_type_code'])]['localName']
			except Exception as e:
				room_stay['rate_internal_name'] = u""

			#check if its a callcenter reservation and we have to fake the rate

			change_rates_for_call_center = not get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "dont change rates for call center")

			if change_rates_for_call_center:
				all_callcenter_groups = {}
				call_center_rates_remote_hotel = get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), DUPLICATE_RATES_FOR_CALLCENTERS)
				if call_center_rates_remote_hotel:
					external_hotel_code = hotel_info[0]['applicationId']
					if not call_center_rates_remote_hotel.lower() == "true":
						external_hotel_code = call_center_rates_remote_hotel

					all_callcenter_groups = get_properties_at_integration_configuration(external_hotel_code, CALL_CENTER_USER_GROUP)


				if all_callcenter_groups and reservation.get('source') and reservation.get("source", "").lower() == "callcenter":

					agent = reservation.get("agent", "")

					default_rate_sufix = ""
					rate_sufix_found = ""
					for rate_sufix, users_list in all_callcenter_groups.items():
						if agent in users_list:
							rate_sufix_found = rate_sufix
						if users_list == "default":
							default_rate_sufix = rate_sufix

					if (not rate_sufix_found) and default_rate_sufix:
						rate_sufix_found = default_rate_sufix


					if rate_sufix_found:
						rate_sufix_found = rate_sufix_found.replace("2", "")

						logging.info("Converting rate: %s to %s because of call center configuration or Agent: %s", room_stay['rate_type_code'], rate_sufix_found, agent)
						room_stay['rate_type_code'] = "%s-%s" % (room_stay['rate_type_code'], rate_sufix_found)



			rate_key = reservation.get("rate", "")
			board_key = reservation['regimen']
			mark_rate_comments = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																						  interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																						  MARK_RATE_COMMENTS)
			if mark_rate_comments:
				self._add_mark_rate_to_comments(mark_rate_comments, rate_key, hotel_all_rates, extracted_data)


			if all_marketing_logos:
				promotion_key = reservation.get('promotions', "")
				self._add_marketing_text_to_comments(hotel_info[0], room_stay_key, rate_key, board_key, promotion_key, all_marketing_logos, extracted_data)


			conditions_and_terms = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), ACCEPT_CONDITIONS_AND_POLICIES).lower() == "true"
			if conditions_and_terms and extra_info.get("accept-conditions-and-policies") == "on":
				comments = extracted_data['global_info']['comments']
				extracted_data['global_info']['comments'] = "%s\n\n%s" % (comments, "Acepta el envio de promociones y novedades del hotel")

			adults = 0
			kids = 0
			babies = 0

			if int(room_stay['number_of_units']) <= 1:
				#diferents room
				adults, kids, babies = self._extract_num_guest(room_stay["guest_count"], specific_params)


			for reserved_room in range(int(room_stay['number_of_units'])):
				# Gathering information of reserved rooms

				#if int(room_stay['number_of_units']) > 1 is because we´re getting iguals rooms
				#so we have to take occupancy directly from reservation

				if int(room_stay['number_of_units']) > 1:
					adults =  reservation['adults%d' % (index_room + 1)]
					kids = reservation['kids%d' % (index_room + 1)]
					babies = reservation['babies%d' % (index_room + 1)]

				if specific_params['DISCARD_BABIES']:
					babies = 0



				reserved_room_data = {
					'guests': [],
					'num_adults': int(adults),
					'num_children': int(kids),
					'num_babies': int(babies),
					'total_guests': 0,
					"index_room": room_stay.get("index_room")
				}

				reserved_room_data['total_guests'] = (
					reserved_room_data['num_adults'] + reserved_room_data['num_children'] +
					reserved_room_data['num_babies'])

				if extracted_data['guest']:
					reserved_room_data['guests'].append({
						'name': extracted_data['guest']['name'],
						'surname': extracted_data['guest']['last_name'],
						'age': DEFAULT_AGE_FOR_ADULTS})

				for num_adult in range(2, reserved_room_data['num_adults'] + 1):
					reserved_room_data['guests'].append({
						'name': 'Adult%d' % num_adult, 'surname': 'Adult%d' % num_adult,
						'age': DEFAULT_AGE_FOR_ADULTS})

				for num_child in range(1, reserved_room_data['num_children'] + 1):
					reserved_room_data['guests'].append({
						'name': 'Child%d' % num_child, 'surname': 'Child%d' % num_child,
						'age': DEFAULT_AGE_FOR_CHILDREN})

				for num_baby in range(1, reserved_room_data['num_babies'] + 1):
					reserved_room_data['guests'].append({
						'name': 'Baby%d' % num_baby, 'surname': 'Baby%d' % num_baby,
						'age': DEFAULT_AGE_FOR_BABY})



				extras_in_this_room = self.build_extras_for_room(reservation, index_room, service_unitary_price, extracted_data, hotel_info[0])


				if extras_in_this_room:
					reserved_room_data["extras"] = extras_in_this_room

				room_stay['reserved_rooms'].append(copy.deepcopy(reserved_room_data))
				index_room += 1


			board_key = reservation['regimen']

			if room_stay.get("board_key"):
				#hotels with shopping cart could have different board for each room
				board_key = room_stay.get("board_key")

			board_id = alphanumeric_to_id(board_key)
			room_stay['board_code'] = to_dingus_code(hotel_info[0], board_id)
			room_stay['board_name'] = board_map.get(board_key)['name']


			if hotel_info[0]['applicationId'] in HACKED_HOTELS_FOR_DINGUS.keys():
				# Dingus hotels hack is needed
				room_stay['rate_type_code'], room_stay['rate_name'] = self._get_rate_code_and_name(hotel_info, room_stay, hotel_all_rates)
				logging.info('Rate code found: %s' % room_stay['rate_type_code'])


			room_stay['amount_all_daily_rates'] = "%.2f" % (float(room_stay['room_amount']))
			#if not hotel_manager_utils.discount_supplements_in_total_reservation(hotel_info[0]['applicationId']):
			#	room_stay['amount_all_daily_rates'] = "%.2f" % (float(room_stay['room_amount']))
			#else:
			#	#daily_rates

			# room_stay['amount_all_daily_rates'] = "%.2f" % sum([float(x['amount']) for x in room_stay['daily_rates']])

			#some hotels (iE Best) whant here the amount of the Bono
			send_bono_amount_as_payed = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "send bono amount as payed")
			if send_bono_amount_as_payed:
				if "final_discounted_price" in reservation.get('extraInfo', '') and "original_price_before_discount" in reservation.get('extraInfo', ''):
					extra_info = json.loads(reservation.get('extraInfo', {}))
					len_rooms = len(extracted_data['room_stays'])
					original_price_before_discount = float(extra_info.get('original_price_before_discount', 0))
					final_discounted_price = float(extra_info.get('final_discounted_price', 0))

					total_with_suplements_configuration = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "add services amount in daily rooms")

					#original_price_before_discount comes always with price supplements included (if booked)
					if not total_with_suplements_configuration:
						price_suplements = float(reservation.get('priceSupplements', '0'))
						original_price_before_discount = original_price_before_discount - price_suplements



					room_stay["room_amount_payed"] =  float(extra_info.get('original_price_before_discount', 0)) - final_discounted_price
					room_stay["room_amount_payed"] = "%.2f" % (room_stay["room_amount_payed"]/len_rooms)
					extracted_data['global_info']['amount_after_taxes'] = original_price_before_discount

					reservation_has_modified_prices = self.reservation_has_modified_prices(reservation)


					#if it is a price modification, we don't have to change this amount. Because the hotel need the new price in the channel
					if not reservation_has_modified_prices:

						room_stay['amount_all_daily_rates'] = "%.2f" % (original_price_before_discount / len_rooms)
						daily_amount = float((original_price_before_discount / len_rooms)) / len(room_stay['daily_rates'])

						#and fix also daily rates!
						for daily in room_stay['daily_rates']:
							daily["amount"] = "%.2f" %  daily_amount

		if hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),"accumulate_supplement_prices_in_first_room"):
			first_room_payed_without_extras = float(extracted_data.get("room_stays")[0].get("room_amount_payed", extracted_data.get("room_stays")[0].get("room_amount")))
			first_room_payed_with_extras = first_room_payed_without_extras + total_price_supplements
			extracted_data.get("room_stays")[0]['include_services_in_first_room_payed'] = "%.2f" % first_room_payed_with_extras

		#!IMPORTANT: this is only for modifications or cancellations wich have new payments from PeP.
		#Otherwise the accuracy of what is paid for will not be accurate.
		same_paid_amount_for_each_room = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),"same paid amount for each room")
		if same_paid_amount_for_each_room.lower() == "true" or same_paid_amount_for_each_room.lower() == "split":
			payed_amount = float(_get_total_payed_amount_from_all_sources(extra_info))
			new_payment = self._has_new_payment(extra_info, reservation.get("identifier"), hotel_info[0]['applicationId'])
			if payed_amount and new_payment and reservation.get("modificationTimestamp") or reservation.get("cancellationTimestamp"):
				payed_amount_to_set = payed_amount
				if same_paid_amount_for_each_room.lower() == "split":
					payed_amount_to_set = payed_amount / len(extracted_data.get("room_stays"))
				for room_stay in extracted_data.get("room_stays"):
					room_stay["room_amount_payed"] = str(payed_amount_to_set)

		if specific_params.get("ADD_SHARED_ROOMS"):
			self.add_virtual_communicated_rooms(all_rooms, extracted_data, hotel_all_rates, hotel_info)


		if extra_info.get("currency_conversion_for_gateway"):

			original_currency = extra_info["currency_conversion_for_gateway"].get("original_currency")
			original_price = extra_info["currency_conversion_for_gateway"].get("original_price")
			new_currency = extra_info["currency_conversion_for_gateway"].get("new_currency")
			new_price = extra_info["currency_conversion_for_gateway"].get("new_price")

			extracted_data['global_info']['comments'] += u"\nSe ha aplicado una conversión de %.2f %s a %.2f %s para realizar el cobro.\n" % (original_price, original_currency, new_price, new_currency)

		extracted_data['nightDates'] = [
			date_utils.date_to_string(x)
			for x in date_utils.get_dates_in_range(extracted_data['global_info']['start'], extracted_data['global_info']['end'])]


		not_include_club_in_xml = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																						  interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																						  NOT_INCLUDE_CLUB_IN_XML)
		
		include_club_in_club_label = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																							  interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																							  INCLUDE_CLUB_IN_CLUB_LABEL)


		if 'clubMember' in reservation.get('extraInfo', '') and not not_include_club_in_xml and not include_club_in_club_label:
			club_member = json.loads(reservation.get('extraInfo', {})).get('clubMember', 0)

			try:
				club_member_id = str(int(club_member))
			except:
				club_member_id = str(club_member)

			extracted_data['additional_infos'].append({'name': 'clubMember', 'value': club_member_id})
		
		
		
		
			
		
		if 'clubMember' in reservation.get('extraInfo', '') and include_club_in_club_label:
			club_member = json.loads(reservation.get('extraInfo', {})).get('clubMember', 0)
			
			try:
				club_member_id = str(int(club_member))
			except:
				club_member_id = str(club_member)
			
			extracted_data['additional_infos'].append({'name': 'CLUB', 'value': club_member_id})


		send_token = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), "send token")
		if send_token and "addon_credencials" in reservation.get('extraInfo', ''):
			#in this moment only available for Addons
			token_card = json.loads(reservation.get('extraInfo', {})).get('addon_credencials', {}).get("token_card")
			if token_card:
				extracted_data['additional_infos'].append({'name': 'token', 'value': token_card})




		additional_services = reservation.get("additionalServices", '')
		if not additional_services:
			additional_services = ''


		#controling posible extra discount for example dor early pays get a 5%
		#imposible to know now, but we have the final priceSupplements correctly discounted.
		#So: if we have priceSupplements -> 95 and the sum of all of them 100, we know we have a 5% discount
		#also works for manual modifications in manager



		disable_special_word_parking = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), DISABLE_SPECIAL_WORD_PARKING)

		# Added parking and price as additional service to json
		for service in additional_services.split(";"):
			if service and "parking" in service.lower() and not disable_special_word_parking:
				value = re.search('Precio:( *)([0-9.]*)', service, re.IGNORECASE)
				if value:
					price = value.group(2)
					if price:
						if percent_discounted:
							try:
								price = str(float(price) - (float(price) *(percent_discounted/100)))
							except Exception as e:
								logging.warning("Error calculating discounts")
								logging.warning(e)

						extracted_data['additional_infos'].append({'name': 'parking', 'value': price})

		# --------

		if payment_method and "CREDIT CARD CONDITIONAL" in payment_method:
			extracted_data['additional_infos'].append({'name': 'payment_method', 'value': "Without credit card"})


		if 'agency_hash' in reservation.get('extraInfo', ''):
			agency_hash = json.loads(reservation.get('extraInfo', {})).get('agency_hash', 0)
			extracted_data['additional_infos'].append({'name': 'AgencyID', 'value': agency_hash})


		if reservation.get("timestamp"):
			extracted_data['timestamp_reservation_date'] = reservation['timestamp'].split(" ")[0]

		if reservation.get('promocode', ''):
			custom_field_promocode = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																						 interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																						 "custom field promocode")
			if not custom_field_promocode:
				custom_field_promocode = "promocode"

			extracted_data['additional_infos'].append({'name': '%s' % custom_field_promocode, 'value': reservation.get('promocode', '')})

		if reservation.get('address', ''):
			address = reservation.get('address', '')
			extracted_data['address'] = {}
			if address and len(address.split(",")) > 3:
				extracted_data['address']['street'] = address.split(",")[0].replace("@", ",")
				extracted_data['address']['postal_code'] = address.split(",")[1]
				extracted_data['address']['city'] = address.split(",")[2]
				extracted_data['address']['province'] = address.split(",")[3]

				include_province = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],
																								  interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),
																								  "include province in additional_info")

				if include_province and extracted_data['address']['province']:
					extracted_data['additional_infos'].append({'name': 'Provincia', 'value': extracted_data['address']['province']})

			if reservation.get('country', ''):
				country = reservation.get('country', '')
				extracted_data['address']['country'] = country
				extracted_data['address']['country_name'] = country
				#address = "%s (%s)" % (address, country)

		elif reservation.get('country', ''):
			extracted_data['address'] = {
				"country": reservation["country"],
				"country_name": reservation["country"]
			}

			#extracted_data['additional_infos'].append({'name': 'address', 'value': address })
		if hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),"send_breakdown_payments").lower() == "cobrador":
			all_payments_reservation = []
			if extra_info.get("payed"):
				all_payments_reservation.append(
					{u'timestamp': reservation.get("timestamp", "").split(" ")[0],
					 u'amount': extra_info.get("payed"),
					 u'type': u'web',
					 u'order': reservation.get("identifier")}
				)

			if extra_info.get("payed_by_tpv_link"):
				payment_by_link = extra_info.get("payed_by_tpv_link")
				for payed in payment_by_link:
					all_payments_reservation.append({
						u'timestamp': payed.get("timestamp", "").split(" ")[0],
						u'amount': float(payed.get("amount")),
						u'type': u'Payment by link',
						u'order': payed.get("order")
					})
			only_last_payment = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'],interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(),"only send new payments in modification").lower() == "true"
			response_payments = get_reservation_payments(hotel_info, reservation, only_last_payment=only_last_payment)
			if response_payments.status_code == 200 and json.loads(response_payments.text):
				response_payments = json.loads(response_payments.text.encode("utf-8"))

				if isinstance(response_payments, list):
					for res_pay in response_payments:
						res_pay["timestamp"] = res_pay.get("timestamp", "").split(" ")[0]
					all_payments_reservation.extend(response_payments)
				else:
					response_payments["timestamp"] = response_payments.get("timestamp", "").split(" ")[0]
					all_payments_reservation.append(response_payments)

			extracted_data["global_info"]["payments_breakdown"] = all_payments_reservation

		return extracted_data

	def add_virtual_communicated_rooms(self, all_rooms, extracted_data, hotel_all_rates, hotel_info: list[dict]):
		new_rooms_to_add = []
		for room_index, room_stay in enumerate(extracted_data['room_stays']):
			shared_room = get_web_configuration("shared_room_%s" % room_stay.get("room_type_code", ""), hotel_info[0]['applicationId'])

			if shared_room:
				# TODO: use rate_aux_key??
				new_rate_key = shared_room.get("rate_aux_key", "")
				new_room_key = shared_room.get("room_aux_key", "")
				room_data = all_rooms.get(new_room_key, {})

				if room_data:

					virtual_room_tmpl = copy.deepcopy(room_stay)
					# make a copy
					virtual_room_tmpl["index_room"] += 15
					virtual_room_tmpl["room_description"] = room_data.get("description", "")
					virtual_room_tmpl["number_of_units"] = "1"
					virtual_room_tmpl["room_name"] = room_data.get("name", "") or ""
					virtual_room_tmpl["room_type_code"] = int(new_room_key)

					# change prices
					try:
						tricked_room_amount_before_taxes = "%.2f" % (float(room_stay["room_amount_before_taxes"]) / 2)
						tricked_amount_all_daily_rates = "%.2f" % (float(room_stay["amount_all_daily_rates"]) / 2)


						virtual_room_tmpl["room_amount_before_taxes"] = tricked_room_amount_before_taxes
						virtual_room_tmpl["amount_all_daily_rates"] = tricked_amount_all_daily_rates

						room_stay["room_amount_before_taxes"] = tricked_room_amount_before_taxes
						room_stay["amount_all_daily_rates"] = tricked_amount_all_daily_rates

						tricked_room_taxes = "%.2f" % (float(room_stay["room_taxes"]) / 2)
						virtual_room_tmpl["room_taxes"] = tricked_room_taxes
						room_stay["room_taxes"] = tricked_room_taxes


					except Exception as e:
						shared_tricked_price = "0.0"
						virtual_room_tmpl["room_amount_before_taxes"] = shared_tricked_price
						virtual_room_tmpl["amount_all_daily_rates"] = shared_tricked_price
						virtual_room_tmpl["room_taxes"] = shared_tricked_price


					# change daily prices
					day_index = 0
					for daily in virtual_room_tmpl['daily_rates']:
						try:
							tricked_day_amount = "%.2f" % (float(room_stay['daily_rates'][day_index]["amount"]) / 2)
							daily["amount"] = tricked_day_amount
							room_stay['daily_rates'][day_index]["amount"] = tricked_day_amount
						except Exception as e:
							daily["amount"] = 0
						day_index += 1

					# And also a balance of capacities! we must be working with NUmberOfUnits = 1 always!
					original_reserved_room = room_stay.get('reserved_rooms')[0]
					if original_reserved_room:
						second_reserved_room = virtual_room_tmpl.get('reserved_rooms')[0]

						origina_total_guests = int(original_reserved_room.get("total_guests"))
						original_num_adults = int(original_reserved_room.get("num_adults"))
						original_num_children = int(original_reserved_room.get("num_children"))
						original_num_babies = int(original_reserved_room.get("num_babies"))

						new_capacities = self.balance_communicated_rooms_capacities(origina_total_guests,
														original_num_adults, original_num_children, original_num_babies)

						original_reserved_room["total_guests"] = new_capacities.get("total_guests_1")
						original_reserved_room["num_adults"] = new_capacities.get("adults_1")
						original_reserved_room["num_children"] = new_capacities.get("children_1")
						original_reserved_room["num_babies"] = new_capacities.get("babies_1")
						second_reserved_room["total_guests"] = new_capacities.get("total_guests_2")
						second_reserved_room["num_adults"] = new_capacities.get("adults_2")
						second_reserved_room["num_children"] = new_capacities.get("children_2")
						second_reserved_room["num_babies"] = new_capacities.get("babies_2")

						#Supplements go alwas only in the first room, don't duplicated!
						second_reserved_room["extras"] = []


					# TODO use rate convertion??
					if new_rate_key:
						rate_data = hotel_all_rates.get(new_rate_key, {})
						if rate_data:
							virtual_room_tmpl["rate_internal_name"] = rate_data.get("localName", "")
							virtual_room_tmpl["rate_name"] = rate_data.get("name", "") or ""
							virtual_room_tmpl["rate_plan_code"] = virtual_room_tmpl["rate_plan_code"].replace(
								str(virtual_room_tmpl["rate_type_code"]), new_rate_key)
							virtual_room_tmpl["rate_type_code"] = int(new_rate_key)

					new_rooms_to_add.append(virtual_room_tmpl)
		extracted_data['room_stays'] += new_rooms_to_add


	def balance_communicated_rooms_capacities(self, total_guests, adults, children, babies):
		# Check if there are enough people to fill both rooms

		if total_guests < 2:
			result = {"total_guests_1": total_guests,
					  "adults_1": adults,
					  "children_1": children,
					  "babies_1": babies,
					  "total_guests_2": 0,
					  "adults_2": 0,
					  "children_2": 0,
					  "babies_2": 0}
			logging.info("There are not enough people to divide into two rooms.")
			return result

		# Calculate the number of people per room
		people_per_room = total_guests // 2

		# Divide adults, children, and babies into two rooms
		adults_room1 = min(adults, people_per_room)
		adults_room2 = adults - adults_room1


		people_per_room -= adults_room1

		children_room1 = min(children, people_per_room)
		children_room2 = children - children_room1

		people_per_room -= children_room1

		babies_room1 = min(babies, people_per_room)
		babies_room2 = babies - babies_room1

		result = {"total_guests_1": adults_room1 + children_room1 + babies_room1,
				  "adults_1": adults_room1,
				  "children_1": children_room1,
				  "babies_1": babies_room1,
				  "total_guests_2": adults_room2 + children_room2 + babies_room2,
				  "adults_2": adults_room2,
				  "children_2": children_room2,
				  "babies_2": babies_room2}

		return result


	def reservation_has_modified_prices(self, reservation):
		extra_info = json.loads(reservation.get('extraInfo', {}))
		return reservation.get("modificationTimestamp") and extra_info.get("shopping_cart") and extra_info.get("prices_per_day")
		#with new modificatoins, not needed to check this beacuse pricesPerdays are also modified


	def _get_desencrypt_cc_datas(self, reservation, hotel_info):

		currency_code = DEFAULT_CURRENCY
		special_currency = hotel_manager_utils.get_integration_configuration_by_key(hotel_info[0]['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), SPECIAL_CURRENCY)
		if special_currency:
			logging.info("Special currency FOUND FOUND!!: %s", special_currency)
			currency_code = special_currency


		cc_datas = extract_extra_info_ccdata(reservation, hotel_info[0]['applicationId'], hotel_info[0], {}, currency=currency_code)
		if cc_datas:
			cc_datas = cc_datas.get("cc_datas")

			cc_holder = reservation['name'] + " " + reservation['lastName']
			if not cc_datas.get("cc_holder"):
				cc_datas["cc_holder"] = cc_holder

			return cc_datas

		return None

	def replace_name_creditcards(self, cc_datas):
		if cc_datas and cc_datas.get("cc_company"):
			cc_datas["cc_company"] = CC_NAMES.get(cc_datas["cc_company"], cc_datas["cc_company"])
		return cc_datas


	@staticmethod
	def _get_rate_code_and_name(hotel_info, room_stay, hotel_all_rates):
		if room_stay['rate_type_code'] in HACKED_HOTELS_FOR_DINGUS[hotel_info[0]['applicationId']]['valid_rates_for_dingus']:
			return room_stay['rate_type_code'], room_stay['rate_name']

		logging.info('Reservation with RATE CODE not valid for Dingus [%s]. Looking for a valid code....' % (
			room_stay['rate_type_code'], ))
		if 'flexible' in room_stay['rate_name'].lower():
			logging.info('Rate code found: %s' % room_stay['rate_type_code'])

			rate_code = HACKED_HOTELS_FOR_DINGUS[hotel_info[0]['applicationId']]['equivalent_rates'].get('flexible')
		else:
			rate = 'nrf-%s' % room_stay['board_code']
			rate_code = HACKED_HOTELS_FOR_DINGUS[hotel_info[0]['applicationId']]['equivalent_rates'].get(rate)

		return rate_code, hotel_all_rates[str(rate_code)]['name']

	def _add_info_extra_to_comments(self, all_rooms, extra_room_option, extracted_data):

		room_map = {room["key"]: room["name"] for room in all_rooms.values()}

		for index, item_extra in enumerate(extra_room_option):
			room_key = item_extra.get("room_key")
			room_name = room_map.get(room_key, "Unknown Room")
			value = item_extra.get("value")

			if value == "two_beds":
				text_comment = "2 camas individuales"
			elif value == "one_bed":
				text_comment = "Cama matrimonial"
			else:
				text_comment = value or "Sin comentario adicional"

			extracted_data['global_info']['comments'] = "\n\nhab.%s %s: Prefencia: %s" % (str(index + 1), room_name, text_comment)

	def _add_nrf_info_to_comments(self, extracted_data, hotel, rate_condition_key, customer_language="SPANISH"):
		comments = extracted_data['global_info']['comments']
		if self._rate_is_nrf(hotel, customer_language, rate_condition_key):
			extracted_data['global_info']['comments'] = "%s\nRESERVA CON TARIFAS NRF" % comments
		else:
			extracted_data['global_info']['comments'] = "%s\nPAGO DIRECTO" % comments

	def _rate_is_nrf(self, hotel, customer_language, rate_condition_key):
		rate_conditions_entity = get_conditions_of_hotel(hotel, customer_language, include_removed=True)
		for rate_condition in rate_conditions_entity:
			if rate_condition_key and rate_condition.get('key') == rate_condition_key:
				if rate_condition.get('cancellationPolicy', '').lower() == "no cancelable":
					return True
		return False

	def _has_new_payment(self, extraInfo, identifier, hotel_code):
		if not extraInfo.get("pciTimestamp") and not extraInfo.get("pciTimestamp2") and extraInfo.get(
				"lastPaymentTimestamp"):
			return False
		if extraInfo.get("lastPaymentTimestamp") and extraInfo.get("last_merchant_order_used"):
			last_pci_timestamp = extraInfo.get("pciTimestamp2") or extraInfo.get("pciTimestamp")
			last_payment_timestamp = extraInfo.get("lastPaymentTimestamp")

			last_pci_timestamp_date_format = date_utils.string_to_date(last_pci_timestamp, format="%Y-%m-%d %H:%M:%S")
			last_payment_timestamp_date_format = date_utils.string_to_date(last_payment_timestamp,
																		   format="%Y-%m-%d %H:%M:%S")

			if last_payment_timestamp_date_format >= last_pci_timestamp_date_format:
				logging.info("Getting the last payment of reservation %s ", )
				endpoint_to_call = "/pages/cobrador/get_reservation_payments?hotel_code=%s&identifier=%s&only_last_payment=True" % (
				hotel_code, identifier)
				final_url_to_call = COBRADOR_SERVER_PATH + endpoint_to_call
				response = requests.get(final_url_to_call)
				if response.status_code == 200 and response.text:
					return json.loads(response.text)
		return {}




