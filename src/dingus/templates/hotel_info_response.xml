<GetHotelInfoResponse>
    {% if error %}
        <Errors>
            <Error Type="{{error}}"/>
        </Errors>
    {% else %}
        <Hotel Code="{{key}}" Name="{{name}}">
            <Rooms>
                {% for room in rooms %}
                    <Room Code="{{room.key}}" Description="{{room.name}}" PriceType="Occupancy">
                        <Occupations>
                             <MinNumberOfPersons>1</MinNumberOfPersons>
                             <MaxNumberOfPersons>{{room.capacities.maxPax}}</MaxNumberOfPersons>
                             <MinNumberOfAdults>{{room.capacities.minAdults}}</MinNumberOfAdults>
                             <MaxNumberOfAdults>{{room.capacities.maxAdults}}</MaxNumberOfAdults>
                             <MinNumberOfChildren>{{room.capacities.minKids}}</MinNumberOfChildren>
                             <MaxNumberOfChildren>{{room.capacities.maxKids}}</MaxNumberOfChildren>
                        </Occupations>

                        <RatePlans>
                            {% for rate in rates %}
                                <RatePlan Code="{{rate.key}}" Description="{{rate.localName}}" CurrencyCode="EUR" RateType="Gross"/>
                            {% endfor %}
                        </RatePlans>

                        <MealPlans>
                            {% for board in boards %}
                                <MealPlan Code="{{board.key}}" Description="{{board.name}}"/>
                            {% endfor %}
                        </MealPlans>
                    </Room>
                {% endfor %}


                {% for remote_hotel_product in remote_product %}

                    {% for room in remote_hotel_product.rooms %}
                        <Room Code="{{room.key}}" Description="{{room.name}}" PriceType="Occupancy">
                            <Occupations>
                                 <MinNumberOfPersons>1</MinNumberOfPersons>
                                 <MaxNumberOfPersons>{{room.capacities.maxPax}}</MaxNumberOfPersons>
                                 <MinNumberOfAdults>{{room.capacities.minAdults}}</MinNumberOfAdults>
                                 <MaxNumberOfAdults>{{room.capacities.maxAdults}}</MaxNumberOfAdults>
                                 <MinNumberOfChildren>{{room.capacities.minKids}}</MinNumberOfChildren>
                                 <MaxNumberOfChildren>{{room.capacities.maxKids}}</MaxNumberOfChildren>
                            </Occupations>

                            <RatePlans>
                                {% for rate in remote_hotel_product.rates %}
                                    <RatePlan Code="{{rate.key}}" Description="{{rate.localName}}" CurrencyCode="EUR" RateType="Gross"/>
                                {% endfor %}
                            </RatePlans>

                            <MealPlans>
                                {% for board in remote_hotel_product.boards %}
                                    <MealPlan Code="{{board.key}}" Description="{{board.name}}"/>
                                {% endfor %}
                            </MealPlans>
                        </Room>
                    {% endfor %}
                {% endfor %}

            </Rooms>
        </Hotel>
    {% endif %}
</GetHotelInfoResponse>