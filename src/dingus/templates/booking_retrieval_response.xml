<BookingRetrievalResponse responseDate="2012-12-19T10:52:25.687">
    {% if error %}
            <Errors>
                <Error Type="{{error}}"/>
            </Errors>
            <HotelReservations>
            </HotelReservations>

    {% else %}
            <HotelReservations>
                {% for res in reservations %}
                    <HotelReservation ResStatus="{{res.reservation_status}}" HotelCode="{{hotelCode}}" HotelName="{{hotelName}}" BookingDate="{{res.creation_time}}.000" LastModifyDateTime="{{res.update_date}}.000" CheckinDate="{{res.global_info.start}}" CheckOutDate="{{res.global_info.end}}">
                        <Rooms>
                            {% for room_stay in res.room_stays %}
                                {% for reserved_room in room_stay.reserved_rooms %}
                                    <Room CheckinDate="{{res.global_info.start}}" CheckOutDate="{{res.global_info.end}}" RoomCode="{{room_stay.room_type_code}}" RoomDescription="{{room_stay.room_name}}"
                                          {% if res.add_unique_room_id %}RoomId="{{res.global_info.res_identifier}}-{{reserved_room.index_room}}"{% endif %} MealPlanCode="{{room_stay.board_code}}" MealPlanDescription="{{room_stay.board_name}}"
                                          RatePlanCode="{{room_stay.rate_type_code}}" RatePlanDescription="{{room_stay.rate_name}}"
                                          NumberOfGuests="{{reserved_room.total_guests}}" NumberOfAdults="{{reserved_room.num_adults}}" NumberOfChildren="{{reserved_room.num_children}}" {% if not hide_babies_tag %}NumberOfBabies="{{reserved_room.num_babies}}"{% endif %}>
                                       <Guests>
                                          {% if reserved_room.guests %}
                                              {% for guest in reserved_room.guests %}
                                                   <Guest Title="MR" GivenName="{{guest.name}}" SurName="{{guest.surname}}" Age="{{guest.age}}" />
                                              {% endfor %}
                                          {% endif %}
                                       </Guests>
                                       <Pricing>
                                          <DailyRates>
                                             {% for daily_rate in room_stay.daily_rates %}
                                                 <DailyRate EffectiveDate="{{daily_rate.effective_date}}">
                                                    <Total AmountAfterTax="{{daily_rate.amount}}" CurrencyCode="{{room_stay.currency_code}}" />
                                                 </DailyRate>
                                             {% endfor %}
                                          </DailyRates>
                                          <RoomTotals>
                                             <RoomTotal AmountAfterTax="{{room_stay.amount_all_daily_rates}}" CurrencyCode="{{res.global_info.currency_code}}" IsNetRate="false" IsGrossRate="true" Commission="0" />
                                          </RoomTotals>
                                       </Pricing>
                                        {% if reserved_room.extras %}
                                        <Extras>
                                            {% for extra in reserved_room.extras %}
                                            <Extra Code="{{ extra.code }}" Name="{{ extra.name }}" Quantity="{{ extra.quantity }}">
                                                <ExtraDetails>
                                                    <ExtraDetail>
                                                    <Price AmountAfterTax="{{ extra.details.price }}"/>
                                                    <TimeSpan Start="{{ extra.details.date_start }}" End="{{ extra.details.date_end }}"/>
                                                    </ExtraDetail>
                                                </ExtraDetails>
                                            </Extra>
                                            {% endfor %}
                                        </Extras>
                                        {% endif %}


                                        {% if res.send_this_transaction and not_send_pay_info != True and res.global_info.include_current_payment_in_xml == True%}
                                        <Payments>
                                            <Payment>
                                                <PaymentDate>{{ res.send_this_transaction.timestamp }}</PaymentDate>
                                                <PaymentAmount>{{ res.send_this_transaction.amount }}</PaymentAmount>
                                                <PaymentCurrency>{{ res.global_info.currency_code }}</PaymentCurrency>
                                                <PaymentOrder>{{ res.send_this_transaction.payment_order }}</PaymentOrder>
                                            </Payment>
                                        </Payments>

                                        {% else %}

                                            {% if not res.modificationTimestamp or not_send_pay_info != True and res.global_info.include_current_payment_in_xml == True%}
                                                {% if room_stay.room_amount_payed %}
                                                    <Payments>
                                                        {% if res.global_info.payments_breakdown %}
                                                            {% for payment in res.global_info.payments_breakdown %}
                                                                <Payment>
                                                                    <PaymentDate>{{ payment.timestamp }}</PaymentDate>
                                                                    <PaymentAmount>{{ payment.amount }}</PaymentAmount>
                                                                    <PaymentCurrency>{{ res.global_info.currency_code }}</PaymentCurrency>
                                                                    <PaymentOrder>{{ payment.order }}</PaymentOrder>
                                                                    <PaymentMethod>{{ res.payment_method }}</PaymentMethod>
                                                                </Payment>
                                                            {% endfor %}
                                                        {% else %}
                                                            <Payment>
                                                                {% if room_stay.room_amount_payed %}
                                                                <PaymentDate>{{ res.timestamp_reservation_date }}</PaymentDate>
                                                                {% if room_stay.include_services_in_first_room_payed %}
                                                                    <PaymentAmount>{{ room_stay.include_services_in_first_room_payed }}</PaymentAmount>
                                                                {% else %}
                                                                    <PaymentAmount>{{ room_stay.room_amount_payed }}</PaymentAmount>
                                                                {% endif %}
                                                                <PaymentCurrency>{{ res.global_info.currency_code }}</PaymentCurrency>
                                                                <PaymentOrder>{{ res.global_info.res_identifier }}</PaymentOrder>
                                                                {% endif %}
                                                                {% if res.payment_method %}
                                                                <PaymentMethod>{{ res.payment_method }}</PaymentMethod>
                                                                {% endif %}
                                                            </Payment>
                                                        {% endif %}
                                                    </Payments>
                                                {% endif %}
                                            {% endif %}

                                        {% endif %}
                                    </Room>
                                {% endfor %}
                            {% endfor %}
                        </Rooms>
                         <SpecialRequests>
                            <SpecialRequest>
                               <Text>{{res.global_info.comments}}</Text>
                            </SpecialRequest>
                         </SpecialRequests>
                         <ReservationInfo>
                            <Total AmountAfterTax="{{res.global_info.amount_after_taxes}}" IsNetRate="false" CurrencyCode="{{res.global_info.currency_code}}" IsGrossRate="true" Commission="0" />
                            <Customer>
                               <LeadPax GivenName="{{res.guest.name}}" SurName="{{res.guest.last_name}}" DocType="01" DocID="{% if res.guest.doc_id and res.guest.doc_id != 'NA' %}{{ res.guest.doc_id }}{% endif %}" />
                                {% if res.cc_datas %}
                                <PaymentForm>
                                    <PaymentCard CardType="{{ res.cc_datas.cc_company}}" CardNumber="{{ res.cc_datas.cc_number}}" SeriesCode="{% if not hide_cvv %}{{ res.cc_datas.cc_cvv}}{% endif %}" ExpireDate="{{ res.cc_datas.cc_expired}}">

                                    <CardHolderName>{{ res.cc_datas.cc_holder}}</CardHolderName>

                                    </PaymentCard>
                                </PaymentForm>
                                {% endif %}
                                {% if res.address %}
                                    {% if res.address.street or res.address.city or res.address.postal_code or res.address.country %}
                                    <Address>
                                        {% if res.address.street %}
                                        <StreetName>{{ res.address.street }}</StreetName>
                                        {% endif %}
                                        {% if res.address.city %}
                                        <CityName>{{ res.address.city }}</CityName>
                                        {% endif %}
                                        {% if res.address.postal_code %}
                                        <PostalCode>{{ res.address.postal_code }}</PostalCode>
                                        {% endif %}
                                        {% if res.address.country %}
                                        <CountryName Code="{{ res.address.country }}"></CountryName>
                                        {% endif %}
                                    </Address>
                                    {% endif %}
                                {% endif %}
                                <Contact>
                                     <Telephone>{{ res.guest.telephone }}</Telephone>
                                     <Email>{{ res.guest.email }}</Email>
                                </Contact>
                            </Customer>
                            <ReservationIDs>
                               <ReservationID Value="{{res.global_info.res_identifier}}" />
                            </ReservationIDs>
                         </ReservationInfo>

                        <AdditionalInfoList>
                            {% if show_old_fields %}
                                <AdditionalInfo Code="Phone number">{{res.guest.telephone}}</AdditionalInfo>
                                <AdditionalInfo Code="email">{{res.guest.email}}</AdditionalInfo>
                            {% endif %}
                            {% for additional_info in res.additional_infos %}
                                <AdditionalInfo Code="{{additional_info.name}}">{{additional_info.value}}</AdditionalInfo>
                            {% endfor %}
                        </AdditionalInfoList>
                    </HotelReservation>
                {% endfor %}
            </HotelReservations>
    {% endif %}
</BookingRetrievalResponse>