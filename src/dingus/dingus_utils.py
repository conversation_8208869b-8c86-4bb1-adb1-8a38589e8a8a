# -*- coding: utf-8 -*-
import logging
import re
import string

import unicodedata

from dingus.dingus_constants import MIX_REMOTE_HOTELES, COBRADOR_FAILOVER_SERVER_PATH, COBRADOR_SERVER_PATH, \
	USE_ALTERNATIVE_PAYMENT_SEEKER
from paraty_commons_3.common_data.common_data_provider import get_boards_of_hotel
from paraty_commons_3.common_data.data_management.advance_configs_utils import get_config_property_value
from paraty_commons_3.common_data.data_management.integrations_utils import get_integration_board_map_properties
from paraty_commons_3.content.web_content_utils import unescape
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.email_utils import notify_error_by_email


def get_text_version(html_version):

	if not html_version:
		return ''

	result = re.sub('<.*?>', '', html_version.replace('<br>','\n'))

	result = result.replace("&lt;div&gt;", "")
	result = result.replace("&lt;/div&gt;", "")

	result = unescape(result)

	result = result.replace("\n", "")

	return result


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], only_thread_local_and_memory=True, ttl_seconds=172800)
def log_only_once(hotel_code, text):
	logging.info ("%s %s", text, hotel_code)

def normalize_board_name(text: str) -> str:
	"""
	this is a special normalize for make good board names
	"""
	return ''.join(x for x in unicodedata.normalize('NFKD', text.lower()) if (x in string.ascii_lowercase) or (x in ["0","1","2","3","4","5","6","7","8","9"]))


def _some_element_in(excludes, text):
	for exclude in excludes:
		if exclude.strip().lower() in text:
			return True
	return False


def _getBoardCode(name, all_boards, exclude=None):

	hb = list(filter(lambda x: name in normalize_board_name(x['name']), all_boards))

	if hb and len(hb) > 1 and exclude:
		excludes = exclude.strip().split(',')

		hb = list(filter(lambda x: not _some_element_in(excludes, normalize_board_name(x['name'])), hb))


	if hb:
		return alphanumeric_to_id(hb[0]['key'])

	return None


#@timed_cache(minutes=100)
def board_id_from_dingus_code(hotel: dict, dingus_code: str):
	'''
	Dingus requires one of these boards code: RO, BB, HB, FB, TI
	Whe should translate from dingus code to our board code
	'''

	#Note that we have detected circumstances where they write by mistake 'HB '
	dingus_code = dingus_code.strip()

	#FIRST chance!!! Maybe we have the map explicitly defined
	dingus_boards_xml = get_integration_board_map_properties("dingus", hotel['applicationId'])
	if dingus_boards_xml:
		dingus_boards_xml = {v: k for k, v in dingus_boards_xml.items()}
		if dingus_code in dingus_boards_xml:
			logging.info("Using explicitly mapped board")
			return alphanumeric_to_id(dingus_boards_xml[dingus_code])

	#SECOND chand, standard logic by default
	all_boards = get_boards_of_hotel(hotel)

	codes = {
		"HB": _getBoardCode("media", all_boards),
		"RO": _getBoardCode("solo", all_boards),
		"BB": _getBoardCode("desayuno", all_boards, "media, cena, almuerzo"),
		"FB": _getBoardCode("completa", all_boards),
		"AI": _getBoardCode("todo", all_boards),
	}

	code_found = codes.get(dingus_code)
	if not code_found:


		#LAST try: lets check erased boards
		all_boards = get_boards_of_hotel(hotel, include_removed=True)
		codes = {
			"HB": _getBoardCode("media", all_boards),
			"RO": _getBoardCode("solo", all_boards),
			"BB": _getBoardCode("desayuno", all_boards, "media, cena, almuerzo"),
			"FB": _getBoardCode("completa", all_boards),
			"AI": _getBoardCode("todo", all_boards),
			"SC": _getBoardCode("catering", all_boards)
		}
		code_found = codes.get(dingus_code)
		if not code_found:

			valid_codes = [str(alphanumeric_to_id(x['key'])) for x in all_boards]
			logging.info('Missing translation for board code: %s', dingus_code)
			code_found = dingus_code

			if not dingus_code in valid_codes:
				logging.error('Missing translation for board code and it is not a valid board: %s', dingus_code)
				email_error_subject = "Missing board translation for board code (from_dingus_code)"
				email_error_message = "Missing board translation for board code (from_dingus_code). <br>Hotel: %s <br> Codigo Dingus: %s Regimenes del hotel: %s" % (hotel, dingus_code, all_boards)
				notify_error_by_email(email_error_subject, email_error_message)

	if code_found and isinstance(code_found, str) and code_found.isdigit():
		try:
			return int(code_found)
		except Exception as e:
			logging.warning("Problem with strange board code received from dingus: %s ->  %s", code_found, e)

	return str(code_found)



def get_mix_remote_hotels(hotel_code):
	#todo get from DB if needed again for other hotel
	#get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), ORIGIN_MIX_CHANNEL_HOTEL_CODES)
	return MIX_REMOTE_HOTELES.get(hotel_code, [])


def get_mix_target_hotel(hotel_code):
	#todo get from DB if needed again for other hotel
	#get_integration_configuration_by_key(hotel['applicationId'], interface_to_implement.SPECIFIC_INTEGRATION_INTERFACE.get_integration_name(), TARGET_MIX_CHANNEL_HOTEL_CODES)
	for hotel_target, local_hotels in MIX_REMOTE_HOTELES.items():
		if hotel_code in local_hotels:
			return hotel_target
	return None


def hotel_with_remote_mix(hotel_code):
	return get_mix_remote_hotels(hotel_code) or get_mix_target_hotel(hotel_code)


def get_ultimate_cobrador_path(hotel_info: dict, fail_over: bool = False):

	if fail_over:
		logging.info("Returning fail_over url")
		return COBRADOR_FAILOVER_SERVER_PATH

	url_to_use = COBRADOR_SERVER_PATH
	use_external_payment_seeker = get_config_property_value(USE_ALTERNATIVE_PAYMENT_SEEKER, hotel_info['applicationId'])
	if use_external_payment_seeker:
		url_to_use = use_external_payment_seeker

	return url_to_use
