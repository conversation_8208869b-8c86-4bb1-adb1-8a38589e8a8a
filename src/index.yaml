indexes:

# AUTOGENERATED

# This index.yaml is automatically updated whenever the dev_appserver
# detects that a new type of query is run.  If you want to manage the
# index.yaml file manually, remove the above marker line (the line
# saying "# AUTOGENERATED").  If you want to manage some indexes
# manually, move them above the marker line.  The index.yaml file is
# automatically uploaded to the admin console when you next deploy
# your application using appcfg.py.

- kind: AuditResult
  properties:
  - name: request_id
  - name: max_date
  - name: min_date

- kind: EndpointCallAuditEvent
  properties:
  - name: hotel_code
  - name: operation
  - name: request_timestamp
    direction: desc

- kind: EndpointCallAuditEvent
  properties:
  - name: hotel_code
  - name: path
  - name: end_date

- kind: EndpointCallAuditEvent
  properties:
  - name: hotel_code
  - name: path
  - name: timestamp
    direction: desc

- kind: UserRescue
  properties:
    - name: last_booking
    - name: sent_at
    - name: send_after
    