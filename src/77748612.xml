<BookingRetrievalResponse responseDate="2012-12-19T10:52:25.687">
    
            <HotelReservations>
                
                    <HotelReservation ResStatus="Reserved" HotelCode="secure-granhoteldelcoto" HotelName="Gran Hotel del Coto" BookingDate="2025-04-21T17:04:55.000" LastModifyDateTime="2025-04-21T17:04:55.000" CheckinDate="2025-04-26" CheckOutDate="2025-04-28">
                        <Rooms>
                            
                                
                                    <Room CheckinDate="2025-04-26" CheckOutDate="2025-04-28" RoomCode="51004" RoomDescription="HABITACIÓN DOBLE"
                                           MealPlanCode="HB" MealPlanDescription="MEDIA PENSIÓN"
                                          RatePlanCode="5212565437153280" RatePlanDescription="Tarifa FLEXIBLE"
                                          NumberOfGuests="4" NumberOfAdults="2" NumberOfChildren="2" NumberOfBabies="0">
                                       <Guests>
                                          
                                              
                                                   <Guest Title="MR" GivenName="<PERSON><PERSON><PERSON>" SurName="Casanueva Cabeza" Age="30" />
                                              
                                                   <Guest Title="MR" GivenName="Adult2" SurName="Adult2" Age="30" />
                                              
                                                   <Guest Title="MR" GivenName="Child1" SurName="Child1" Age="8" />
                                              
                                                   <Guest Title="MR" GivenName="Child2" SurName="Child2" Age="8" />
                                              
                                          
                                       </Guests>
                                       <Pricing>
                                          <DailyRates>
                                             
                                                 <DailyRate EffectiveDate="2025-04-26">
                                                    <Total AmountAfterTax="182.32" CurrencyCode="EUR" />
                                                 </DailyRate>
                                             
                                                 <DailyRate EffectiveDate="2025-04-27">
                                                    <Total AmountAfterTax="182.33" CurrencyCode="EUR" />
                                                 </DailyRate>
                                             
                                          </DailyRates>
                                          <RoomTotals>
                                             <RoomTotal AmountAfterTax="364.65" CurrencyCode="EUR" IsNetRate="false" IsGrossRate="true" Commission="0" />
                                          </RoomTotals>
                                       </Pricing>
                                        


                                        

                                            
                                                
                                                    <Payments>
                                                        
                                                            <Payment>
                                                                
                                                                <PaymentDate>2025-04-21</PaymentDate>
                                                                
                                                                    <PaymentAmount>364.78</PaymentAmount>
                                                                
                                                                <PaymentCurrency>EUR</PaymentCurrency>
                                                                <PaymentOrder>50992571</PaymentOrder>
                                                                
                                                                
                                                                <PaymentMethod>Credit Card</PaymentMethod>
                                                                
                                                            </Payment>
                                                        
                                                    </Payments>
                                                
                                            

                                        
                                    </Room>
                                
                            
                        </Rooms>
                         <SpecialRequests>
                            <SpecialRequest>
                               <Text>.
 SERVICIOS ADICIONALES INCLUIDOS: Cesta de Fruta + Agua - cantidad: 1 - dias: 1 - precio: 7.0; .
 PROMOCODE: FAMILIACOTO
 Total de la reserva con todos los servicios incluidos: 371.65
 Servicios incluidos: 7.0
 Total de la reserva con todos los servicios incluidos: 371.65
 Servicios incluidos: 7.0
 PAGADO POR TPV (PAID BY GATEWAY): 371.65
ORIGEN RESERVA: [WEB]</Text>
                            </SpecialRequest>
                         </SpecialRequests>
                         <ReservationInfo>
                            <Total AmountAfterTax="371.65" IsNetRate="false" CurrencyCode="EUR" IsGrossRate="true" Commission="0" />
                            <Customer>
                               <LeadPax GivenName="María Teresa" SurName="Casanueva Cabeza" DocType="01" DocID="" />
                                
                                
                                    
                                    <Address>
                                        
                                        
                                        
                                        
                                        <CountryName Code="ES"></CountryName>
                                        
                                    </Address>
                                    
                                
                                <Contact>
                                     <Telephone>*********</Telephone>
                                     <Email><EMAIL></Email>
                                </Contact>
                            </Customer>
                            <ReservationIDs>
                               <ReservationID Value="50992571" />
                            </ReservationIDs>
                         </ReservationInfo>

                        <AdditionalInfoList>
                            
                            
                                <AdditionalInfo Code="SERVICE">Cesta de Fruta + Agua - cantidad: 1 - dias: 1 - precio: 7.0</AdditionalInfo>
                            
                                <AdditionalInfo Code="promocode">FAMILIACOTO</AdditionalInfo>
                            
                        </AdditionalInfoList>
                    </HotelReservation>
                
            </HotelReservations>
    
</BookingRetrievalResponse>