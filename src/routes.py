import logging
import re

from flask import jsonify
from flask_cors import CORS

from handlers.audit.audit1_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from handlers.audit.audit2_handler import <PERSON><PERSON>2<PERSON><PERSON>ler
from handlers.audit.audit3_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from handlers.audit.clean_audit import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from handlers.check_integration_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from handlers.common_handler import <PERSON>Check<PERSON><PERSON><PERSON>
from handlers.dev_handler import <PERSON><PERSON><PERSON>ler
from handlers.error_finder_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ler
from handlers.log_handler import <PERSON><PERSON>Handler
from handlers.maintenance_handler import MaintenanceHandler
from handlers.prices_handler import ModifyPricesHandler
from handlers.queue_handler import QueueHandler
from handlers.redo_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE
from paraty import app
from paraty.utils.queues.queues_utils import QueuesSerializedHandler


@app.errorhandler(404)
def page_not_found(e):
	logging.warning("Resource Not Found!")
	return jsonify(message="Resource Not Found!"), 404


def build_routes():

	from paraty import app, INTEGRATION_NAME

	specific_routes = SPECIFIC_INTEGRATION_INTERFACE.gets_additional_paths()
	modify_url = SPECIFIC_INTEGRATION_INTERFACE.get_modify_url()
	is_customized_additional_path = modify_url in [x[0] for x in specific_routes]

	# Endpoint to modify prices and availability
	if not is_customized_additional_path:
		# Needed because at integration as booking expert has difference with content but request.path are the same
		# ie. we have only the path / and all calls are performed to this endpoint, but the content allow to detect what
		# action need to be performed
		app.add_url_rule(modify_url, view_func=ModifyPricesHandler.as_view("modify_prices_handler"))

	app.add_url_rule("/dev", view_func=DevHandler.as_view("dev_handler"), methods=['GET'])

	app.add_url_rule("/audit1", view_func=Audit1Handler.as_view("audit_1_handler"))
	app.add_url_rule("/audit2", view_func=Audit2Handler.as_view("audit_2_handler"))
	app.add_url_rule("/audit3", view_func=Audit3Handler.as_view("audit_3_handler"))

	app.add_url_rule("/redo", view_func=RedoHandler.as_view("redo_handler"))

	app.add_url_rule("/check_integration", view_func=CheckIntegrationHandler.as_view("check_integration_Handler"))

	app.add_url_rule("/get_availability_items", view_func=DevHandler.as_view("get_availability_items_handler"), methods=['POST'])

	app.add_url_rule("/errorfinder", view_func=ErrorFinderHandler.as_view("error_finder_handler"))

	app.add_url_rule("/maintenance", view_func=MaintenanceHandler.as_view("maintenance_handler"))

	app.add_url_rule("/gae_logs/save_logs/", view_func=LogsHandler.as_view("logs_handler"))

	app.add_url_rule("/clean_audits/", view_func=CleanAuditsHandler.as_view("clean_audits_handler"))

	app.add_url_rule('/execute_task/_redo_operations_from_date', view_func=QueueHandler.as_view("queue_handler_redo"))

	app.add_url_rule('/execute_task/_do_reset_hotel_manager_cache', view_func=QueueHandler.as_view("queue_handler_reset_cache"))

	app.add_url_rule('/execute_task/clean_old_audits', view_func=QueueHandler.as_view("queue_handler_audit"))

	app.add_url_rule('/execute_task/add_log_entry', view_func=QueueHandler.as_view("queue_handler_add_log_entry"))

	app.add_url_rule('/healthcheck', view_func=HealthCheckHandler.as_view("health_check_handler"))

	app.add_url_rule('/execute_task', view_func=QueuesSerializedHandler.as_view("queue_handler"))


	#TODO, ADD SPECIFIC
	try:
		cors_modify = SPECIFIC_INTEGRATION_INTERFACE.get_path_call_center()
		logging.info(f"url cors {cors_modify}")
	except:
		cors_modify = {}
		logging.info("No mod routes")
	for current_rule, handler_class in specific_routes:
		snake_case_handler_name = re.sub(r'(?<!^)(?=[A-Z])', '_', handler_class.__name__).lower()
		if current_rule in cors_modify.keys():
			view_func = handler_class.as_view(snake_case_handler_name)
			CORS(app, resources={current_rule: {"origins": cors_modify.get(current_rule)}})
			app.add_url_rule(current_rule, view_func=view_func)
		else:
			# view_func = handler_class.as_view(snake_case_handler_name)
			app.add_url_rule(current_rule, view_func=handler_class.as_view(snake_case_handler_name))

	#TODO, ADD Specific routes to app
