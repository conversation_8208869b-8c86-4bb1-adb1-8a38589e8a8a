requests

Flask
Flask-HTTPAuth
sets
mock
dicttoxml

xmldiff
cryptography==42.0.2
pycryptodomex==3.20
pycryptodome==3.20

google-cloud-datastore==2.20.2
google-cloud-ndb==2.3.2

beautifulsoup4
lxml
google-auth==2.38.0
google-cloud-tasks==2.19.2
requests_futures
gunicorn==20.1.0
gevent
google-cloud-logging

#To be able to decrypt reservations
google-cloud-secret-manager
html-sanitizer

pandas
protobuf
basicauth
aiohttp==3.10.5
certifi
redis

stripe==10.9.0

google-cloud-bigquery
flask_cors
boto3
pytz==2024.1
python-dotenv
Unidecode == 1.3.8
