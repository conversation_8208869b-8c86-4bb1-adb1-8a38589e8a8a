# import os
# import sys
#
# # Configurar PYTHONPATH para incluir el src de base-integration primero
# base_integration_src = os.path.abspath(os.path.dirname(__file__))
# sys.path.append(base_integration_src)
#
# # Ahora podemos importar config de manera segura
# INTEGRATION_NAME = "siteminder-adapter"
#
# # Configurar PYTHONPATH para incluir el directorio src del proyecto actual
# project_src = os.path.abspath(os.path.join(os.path.dirname(__file__), f'../../{INTEGRATION_NAME}/src'))
# sys.path.append(project_src)
#
# # Asegurarnos de que base_integration_3 está en el PYTHONPATH
# base_integration_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
# sys.path.append(base_integration_root)

from paraty import app


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8087, debug=app.config['DEV'])

'''
To deploy this you can use:

FLEXIBLE
gcloud app deploy app_flex.yaml --project hotelads-adapter

STANDARD
gcloud app deploy --project hotelads-adapter

Add --no-promote if you don't want to promote it
Add --version if you want a specific name for this version
'''