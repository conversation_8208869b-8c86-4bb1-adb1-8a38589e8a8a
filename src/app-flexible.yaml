runtime: python
env: flex
entrypoint: gunicorn -c gunicorn.conf.py -b :$PORT main:app
service: @@MODULE@@

runtime_config:
    operating_system: "ubuntu22"
    runtime_version: "3.11"

automatic_scaling:
  min_num_instances: 3
  max_num_instances: 5
  cool_down_period_sec: 180
  cpu_utilization:
    target_utilization: 0.8


# This sample incurs costs to run on the App Engine flexible environment.
# The settings below are to reduce costs during testing and are not appropriate
# for production use. For more information, see:
# https://cloud.google.com/appengine/docs/flexible/python/configuring-your-app-with-app-yaml
resources:
  cpu: @@FLEXIBLE_CPU@@
  memory_gb: @@FLEXIBLE_MEMORY@@
  disk_size_gb: @@FLEXIBLE_DISK@@

liveness_check:
  path: "/healthcheck"
  timeout_sec: 20
  check_interval_sec: 60
  failure_threshold: 10
  success_threshold: 2
  initial_delay_sec: 20

readiness_check:
  path: "/healthcheck"
  check_interval_sec: 60
  timeout_sec: 20
  failure_threshold: 10
  success_threshold: 2
  app_start_timeout_sec: 180
