import argparse
import subprocess
import shutil, errno
from datetime import datetime
from distutils.dir_util import copy_tree
import os, fnmatch

# replace @@REPLACE_ME@@ with a directory other than /tmp since it is purged periodically
TEMP_FOLDER = "/Users/<USER>"

DEFAULT_LOCATION_REGION = "europe-west1"
LOCATION_REGIONS = {"yieldplanet": "us-central1",
					"innsist": "us-central1",
                    "paratytech": "us-central1",
					"paratytech": "us-central1",
					"omnibeespull": "us-central1",
					"avalon":"us-central1"}


def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)

def print_line(message):
	print()
	print("-------------------------------------------------------------")
	print((message.upper()))
	print("-------------------------------------------------------------")


def create_temp_folder(params):

	os.chdir(TEMP_FOLDER)

	new_folder = "build_%s_%s" % (params['application'], datetime.now().strftime("%Y_%m_%d_%H_%M"))

	if os.path.exists(new_folder):
		shutil.rmtree(new_folder)
	os.makedirs(new_folder)

	os.chdir(TEMP_FOLDER + "/" + new_folder)

	print_line("Created temp folder: %s" % (TEMP_FOLDER + "/" + new_folder))


def copy_files(src, dest):
	src_files = os.listdir(src)
	for file_name in src_files:
		full_file_name = os.path.join(src, file_name)
		if os.path.isfile(full_file_name):
			shutil.copy(full_file_name, dest)
		else:
			copy_anything(full_file_name, dest)


def copy_anything(src, dst):

	try:
		shutil.copytree(src, dst, dirs_exist_ok=True)
	except OSError as exc: # python >2.5
		if exc.errno == errno.ENOTDIR:
			shutil.copy(src, dst)
		else: raise


def download_stable_version_of_project(build_temp_folder, project_name, git_path, branch_name=None):

	if project_name == "base-integration":
		print('ooooooooooooo')
		branch_name = 'python3-flexible'

	project_temp_dir = "%s/%s" % (TEMP_FOLDER, project_name)

	if branch_name:
		project_temp_dir = "%s_%s" % (project_temp_dir, branch_name)

	#If it is already downloaded we just update it
	if os.path.exists(project_temp_dir):
		try:
			print(("Updating source code of %s" % project_name))
			pull_cmd = "cd %s && git pull && git submodule init && git submodule update" % project_temp_dir
			execute_command(pull_cmd)
			if project_name == "base-integration":
				executo_update_submodule = "cd %s && cd src/paraty_commons_3 && git checkout master && git pull" % (project_temp_dir)
				execute_command(executo_update_submodule)
		except:
			print('Removing temporary directory and downloading it clean')
			rm_cmd = "rm -rf %s" % project_temp_dir
			execute_command(rm_cmd)

	#If it doesn't exist we clone it into the project temp dir
	if not os.path.exists(project_temp_dir):
		print(("Cloning source code of %s" % project_name))

		clone_cmd = "git clone --recursive %s %s" % (git_path, project_temp_dir)
		execute_command(clone_cmd)

		if branch_name:
			change_branch_cmd = "cd %s && git checkout %s && cd ." % (project_temp_dir, branch_name)
			execute_command(change_branch_cmd)
			if project_name == "base-integration":
				executo_update_submodule = "cd %s && git submodule init && git submodule update && cd src/paraty_commons_3 && git checkout master && git pull" % (project_temp_dir)
				execute_command(executo_update_submodule)


	#
	# #We copy the project to the temporary build folder
	# print 'Copying %s to %s' % (project_temp_dir, build_temp_folder)
	# result = "%s/%s" % (build_temp_folder, project_name)
	# shutil.copytree(project_temp_dir, result)


def download_projects(params):

	print_line('Cloning from repository')

	download_stable_version_of_project(TEMP_FOLDER, 'base-integration', '*****************:paraty/base-integration.git', params['branch'])

	download_stable_version_of_project(TEMP_FOLDER, params['application'], '*****************:paraty/%s.git' % params['application'],  params['branch_adapter'])


def create_stage_folder(params):
	print_line("Creating stage folder")
	os.makedirs("stage")
	os.chdir("stage")

	base_integration_path = "%s/base-integration/src/" % TEMP_FOLDER
	if params.get('branch'):
		base_integration_path = "%s/base-integration_%s/src/" % (TEMP_FOLDER, params.get('branch'))

	copy_tree(base_integration_path, ".")

	project_folder = "%s/%s" % (TEMP_FOLDER, params['application'])

	if params['branch_adapter']:
		project_folder = "%s_%s" % (project_folder, params['branch'])

	if not os.path.exists(project_folder):
		os.makedirs(project_folder)

	if not os.path.exists(project_folder+"/src"):
		os.makedirs(project_folder+"/src")

	copy_tree("%s/src/" % project_folder, ".")


def execute_unit_tests():

	print_line('Executing unit tests')

	cmd_base = "nosetests tests"
	execute_command(cmd_base)

	cmd_specific = "nosetests test"
	execute_command(cmd_specific)


def _replace_in_file(path, original, changed):

	with open(path) as f:
		file_str = f.read()

	# do stuff with file_str
	if not original in file_str:
		raise Exception("Expected code to be changed: %s  not found at: %s" % (original, path))

	file_str = file_str.replace(original, changed)

	with open(path, "w") as f:
		f.write(file_str)


def find(pattern, path):
	result = []
	for root, dirs, files in os.walk(path):
		for name in files:
			if fnmatch.fnmatch(name, pattern):
				result.append(os.path.join(root, name))
	return result




def configure_project(params):

	print_line('Configuring project to use specific integration')

	integration_name = params['application'].split("-")[0]

	if params['module']:
		_replace_in_file("app.yaml", "@@MODULE@@", params['module'])
		_replace_in_file("app-flexible.yaml", "@@MODULE@@", params['module'])

		_replace_in_file("app-flexible.yaml", "@@FLEXIBLE_CPU@@", params.get('cpu', '1'))
		_replace_in_file("app-flexible.yaml", "@@FLEXIBLE_MEMORY@@", params.get('memory', '3.75'))
		_replace_in_file("app-flexible.yaml", "@@FLEXIBLE_DISK@@", params.get('disk', '10'))

	try:
		_replace_in_file("app.yaml", "@@MACHINE_TYPE@@", params.get('machine_type', 'F2'))
		_replace_in_file("app-flexible.yaml", "@@MACHINE_TYPE@@", params.get('machine_type', 'F2'))

		_replace_in_file("app-flexible.yaml", "@@FLEXIBLE_CPU@@", params.get('cpu', '1'))
		_replace_in_file("app-flexible.yaml", "@@FLEXIBLE_MEMORY@@", params.get('memory', '3.75'))
		_replace_in_file("app-flexible.yaml", "@@FLEXIBLE_DISK@@", params.get('disk', '10'))

	except Exception as e:
		print("MACHINE_TYPE not found")

	_replace_in_file("paraty/config.py", "@@INTEGRATION_NAME@@", integration_name)
	_replace_in_file("paraty/config.py", "@@LOCATON_REGION@@", LOCATION_REGIONS.get(integration_name, DEFAULT_LOCATION_REGION))

	#Move templates from specific integration to common folder
	copy_files('%s/templates' % integration_name, 'templates')

	if os.path.exists("./%s/integration_requirements.txt" % integration_name):
		with open("./%s/integration_requirements.txt" % integration_name, "r") as source_req:
			with open("./requirements.txt", "a") as dest_req:
				dest_req.write("\n#requirements from integration_requirements.txt\n")
				dest_req.write(source_req.read())

def update_to_gae(params):
	print_line('Updating to GAE')

	if params['flexible']:
		# execute_command('gcloud config set project %s' % params['application'])
		# update_cmd = 'gcloud app deploy app-flexible.yaml --version=%s --no-promote' % params['version']
		update_cmd = "gcloud app deploy app-flexible.yaml --project %s -q --no-promote" % params['application']
	else:
		update_cmd = "gcloud app deploy --project %s -q --no-promote" % params['application']
		if params['version']:
			update_cmd += " --version=%s " % params['version']

	execute_command(update_cmd)


def deploy_integration(params):

	create_temp_folder(params)

	download_projects(params)

	create_stage_folder(params)

	if not params['ignore_tests']:
		execute_unit_tests()

	configure_project(params)

	update_to_gae(params)


if __name__ == '__main__':

	parser = argparse.ArgumentParser(description=
	'''
	Update a integration
	i.e. python update_project_3.py -a yieldplanet-adapter -t -branch python3-flexible
	
	i.e. python update_project.py -a rategain-adapter -v 1
	i.e. python update_project.py -a webcamp-adapter -v 1
	i.e. python update_project.py -a tripadvisor-adapter -v 2018-06-21 -m v7 -b v7 -t
	i.e. python update_project_3.py -a yieldplanet-adapter -m background -t -branch python3-flexible -s F4 -z python3-flexible -f True

	INFORMATION FOR SCALING TYPES:
	https://cloud.google.com/appengine/docs/standard/python/config/appref


	''', formatter_class=argparse.RawTextHelpFormatter)

	parser.add_argument('-version', '-v', required=False, help="Indicates the new version name")
	parser.add_argument('-module', '-m', required=False, default="default", help="Indicates the module to use")
	parser.add_argument('-branch', '-b', required=False, help="Indicates the branch in git to use")
	parser.add_argument('-branch_adapter', '-z', required=False, help="Indicates the branch in git to use")
	parser.add_argument('-application', '-a', required=False, help="Indicates the applicationId")
	parser.add_argument('-ignore_tests', '-t', action='store_true', help="If present we dont execute unit tests")
	parser.add_argument('-flexible', '-f', required=False, help="If present we upload it as a Flexible version")
	parser.add_argument('-type', '-s', required=False, default="F2", help="If present it defines the machine type (i.e. F4)")
	parser.add_argument('-scaling', '-c', required=False, default="automatic_scaling", help="Indicates the type of scaling (i.e. automatic_scaling or basic_scaling or manual_scaling)")
	parser.add_argument('-cpu', '-p', required=False, default="1", help="Indicates the number of cpu for the flexible computer")
	parser.add_argument('-memory', '-e', required=False, default="3.75", help="Indicates the amount of memory for the flexible computer")
	parser.add_argument('-disk', '-d', required=False, default="10", help="Indicates the amount of disk for the flexible computer")

	args = parser.parse_args()

	params = {
		'version': args.version,
		'application': args.application,
		'ignore_tests': args.ignore_tests,
		'module': args.module,
		'flexible': args.flexible,
		'machine_type': args.type,
		'scaling_type': args.scaling,
		'branch': args.branch,
		'branch_adapter': args.branch_adapter,
		'cpu': args.cpu,
		'memory': args.memory,
		'disk': args.disk
	}

	deploy_integration(params)
	print_line("FINISHED!")
