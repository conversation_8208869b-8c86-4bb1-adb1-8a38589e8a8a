import logging
import os
import subprocess
import sys
import unittest

class MyTestResult(unittest.TextTestResult):
    def addSuccess(self, test):
        super().addSuccess(test)
        message = f'\033[92mTest {test} PASSED\033[0m\n'  # Verde
        self.stream.write(message)
        self.stream.flush()

    def addError(self, test, err):
        super().addError(test, err)
        message = f'\033[93mTest {test} ERROR\033[0m\n'  # Amarillo
        self.stream.write(message)
        self.stream.flush()

    def addFailure(self, test, err):
        super().addFailure(test, err)
        message = f'\033[91mTest {test} FAILED\033[0m\n'  # Rojo
        self.stream.write(message)
        self.stream.flush()

    def addSubTest(self, test, subtest, err):
        super().addSubTest(test, subtest, err)
        if err is None:
            message = f'\033[92mSubtest {subtest} of {test} PASSED\033[0m\n'  # Verde
        else:
            message = f'\033[91mSubtest {subtest} of {test} FAILED\033[0m\n'  # Rojo
        self.stream.write(message)
        self.stream.flush()


def perform_tests():
    # Run unittests with actual venv activated
    folder_abs_path = os.path.abspath(os.path.dirname(__file__))
    source_path = os.path.join(folder_abs_path, "../src")

    # Run tests with custom result class
    logging.disable(logging.CRITICAL)

    sys.path.append(source_path)

    loader = CustomTestLoader(exclude_dir='paraty_commons_3')
    tests = loader.discover(start_dir=source_path, pattern='test_*')
    runner = unittest.TextTestRunner(resultclass=MyTestResult)
    status_default = runner.run(tests)

    # Reenable logging
    logging.disable(logging.NOTSET)

    if not status_default.wasSuccessful():
        raise Exception('Tests failed')


class CustomTestLoader(unittest.TestLoader):
    def __init__(self, exclude_dir=None):
        self.exclude_dir = exclude_dir
        super().__init__()

    def _match_path(self, path, full_path, pattern):
        if self.exclude_dir and self.exclude_dir in full_path:
            return False

        return super()._match_path(path, full_path, pattern)


if __name__ == '__main__':
    perform_tests()
