import json
import logging
import os
from paraty import app, Config

from handlers.redo_handler import _get_start_and_end_dates, _update_limits
from paraty.integration import modify_prices_batch
from paraty.integration.integration_data_cache import IntegrationDataCache
from paraty_commons_3.datastore import datastore_communicator
from tools.audit.convert_xml_to_availabilities import _convert_xml_to_availability_items
from tools.audit import download_audits, convert_xml_to_availabilities

from paraty.utils import hotel_manager_utils as local_hotel_manager_utils
from tools.audit.download_audits import HOTEL_CODE, NUM_DAYS, FROM_HOTEL_CODE_MIRROR, PATH

'''
This allows you to do a redo locally
'''

def _redo_from_date(save_changes=True):

	_download_xmls()

	_convert_xml_to_items()

	if not save_changes:
		return

	hotel = local_hotel_manager_utils.get_hotel_by_application_id(HOTEL_CODE)

	#Read availability items
	directory = os.listdir(download_audits.OUTPUT_DIR)

	print("")
	print(('Found %s XML files' % len(directory)))
	print("")

	# Note that we want to analyze starting from the most recent one (maybe we find soon what we are looking for)
	sorted_files = sorted(directory)

	# Let's use an intermediate structure to avoid having to access the datastore continually
	cache = IntegrationDataCache(hotel, "MANUAL REDO")

	# Limits
	first_modified_date, last_modified_date = None, None

	for availability_items_file in sorted_files:

		if '.xml' not in availability_items_file:
			continue

		with open(download_audits.OUTPUT_DIR + "/" + availability_items_file) as f:
			try:
				availability_items = json.loads(f.read())
			except Exception as e:
				logging.error("Error reading %s", availability_items_file)
				logging.error("Error reading MESSAGE: %s POSSIBLE FIELD EMPTY: ", str(e), download_audits.OUTPUT_DIR + "/" + availability_items_file)
				availability_items = None

		if not availability_items:
			continue
		start_date, end_date = _get_start_and_end_dates(availability_items)

		first_modified_date, last_modified_date = _update_limits(first_modified_date, last_modified_date, start_date, end_date)

		logging.info("_redo_operation_in_memory start_date %s end_date %s", start_date, end_date)

		modify_prices_batch.execute_modify_prices_batch(HOTEL_CODE, availability_items, start_date, end_date, "TEST REQUEST ID", cache)

	# We use the request of the first one
	cache.save_data_to_datastore()

	# Make Sure that the cache of hotel-manager is cleaned (if something has changed)
	if first_modified_date and last_modified_date:
		local_hotel_manager_utils.reset_hotel_manager_cache(hotel, first_modified_date, last_modified_date)


def _convert_xml_to_items():
	logging.info("Converting xml to Availability items")
	_convert_xml_to_availability_items()


def _download_xmls():
	params = {
		'hotel': HOTEL_CODE,
		'days': NUM_DAYS
	}

	if FROM_HOTEL_CODE_MIRROR:
		params['from_hotel_code_mirror'] = FROM_HOTEL_CODE_MIRROR

	download_audits.download_audits(params)


if __name__ == '__main__':

	# Change here the project you want to do the manual redo
	Config.PROJECT = 'dingus-adapter'

	entities = datastore_communicator.get_using_entity_and_params("EndpointCallAuditEvent", [('hotel_code', '=', HOTEL_CODE), ('path', '=', PATH)], return_cursor=True, order_by='-timestamp')

	convert_xml_to_availabilities.GET_AVAILABILITY_ENDPOINT = f'https://{Config.PROJECT}.appspot.com/get_availability_items'
	convert_xml_to_availabilities.MANUAL_REDO = True

	_redo_from_date(save_changes=False)