import json
import os

from tools.audit.download_audits import OUTPUT_DIR, HOTEL_CODE, INPUT_DIR

'''
Identify the related availability items
'''

from paraty.utils import date_utils

def find_files_with_string(directory_path, strings_to_find):

	all_files = [x for x in os.listdir(directory_path)]
	all_files.sort(reverse=True)

	for fname in all_files:
		# Full path
		with open(directory_path + os.sep + fname, 'r') as f:

			content = f.read()
			found = True
			for current_string in strings_to_find:
				if not current_string in content:
					found = False
					break

			if found:
				print(fname)
				print(content)
				print()


def find_items_with_condition(directory_path, filter_function):
	all_files = [x for x in os.listdir(directory_path)]
	all_files.sort(reverse=True)

	with open('./results/results_%s.json' % date_utils.get_timestamp(format="%Y-%m-%d_%H_%M"), 'w') as result_file:

		num_entities = 0
		for fname in all_files:
			# Full path
			with open(directory_path + os.sep + fname, 'r') as f:

				content = f.read()

				current_items = json.loads(content)

				valid_items = list(filter(filter_function, current_items))

				if valid_items:
					for valid_item in valid_items:
						valid_item['file'] = INPUT_DIR + "/" + fname

						#Only valid fields
						valid_item = {k: v for k, v in list(valid_item.items()) if v is not None}

						print(json.dumps(valid_item))
						result_file.write(json.dumps(valid_item) + ",\n")
					print()

				num_entities += 1

			if num_entities % 10000 == 0:
				print('Analyzed: %s, %s' % (num_entities, fname))



'''
What items are we looking for?

Use XML_Mapping at Footer of Hotel Manager to identify Room, Rates and Boards

Available fields (note, some fields are strings and some a integers):

		'day': self.day,
		'roomId': self.room_id,
		'boardId': self.board_id,
		'rateId': self.rate_id,
		'capacity': self.capacity,
		'price': self.price,
		'minimumStay': self.minimum_stay,
		'maximumStay': self.maximum_stay,
		'closedToArrival': self.closed_to_arrival,
		'closedToDeparture': self.closed_to_departure,
		'quantity': self.quantity,
		'status': self.status,
		'request_id': self.request_id,
		'operation_id': self.operation_id,
		'release': self.release,
'''

def _my_filter(x):

	return \
		x['day'] == '2019-08-15' and x['minimumStay']


if __name__ == '__main__':

	#NOTE, Put here the hotel code of the hotel you are looking for
	find_items_with_condition(OUTPUT_DIR, _my_filter)