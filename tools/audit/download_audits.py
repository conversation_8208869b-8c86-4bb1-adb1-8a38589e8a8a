import zlib

import os

import errno

from datetime import datetime

import pytz

from paraty.utils.hotel_manager_utils import get_properties_at_integration_configuration
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id

BASE_PATH = '/Users/<USER>/tmp/audits'

SELECTION_INTEGRATION = 'dingus'

# ------ MODIFY THIS TO ADAPT IT TO DIFFERENT INTEGRATIONS --------

#This import is mandatory
exec('from %s.%s_integration import %sIntegration' %(SELECTION_INTEGRATION, SELECTION_INTEGRATION, SELECTION_INTEGRATION.title()))

from paraty import Config
from paraty.utils import date_utils
from paraty_commons_3.datastore import datastore_communicator

PROJECT = '%s-adapter' % 'dingus'
HOTEL_CODE = 'fives-beach'
NUM_DAYS = 2

FROM_HOTEL_CODE_MIRROR = ""
ADAPTER_FOR_MIRROR = ""

INPUT_DIR = '%s/%s' % (BASE_PATH, HOTEL_CODE)
OUTPUT_DIR = '/Users/<USER>/tmp/items/%s' % HOTEL_CODE
# -----------------------------------------------------------------

# from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE
# PATH = SPECIFIC_INTEGRATION_INTERFACE.get_modify_url()
#PATH = '/update'
#siteminder:
#PATH = '/inventory'
#dingus
PATH = '/dingus/HotelAvailRateUpdate'
#availpro
#PATH = '/update'




def _transform_xml_content_to_mirror(xml_body, from_hotel_code, mirror_hotel):

	adapter_name = ADAPTER_FOR_MIRROR

	#siteminder mirror mapping
	adapter_mirror_config = "%s mirror mapping %s" % (adapter_name, mirror_hotel)

	mapping_rooms_mirror = get_properties_at_integration_configuration(from_hotel_code, adapter_mirror_config, field="roomMap")
	mapping_rooms_mirror = {str(alphanumeric_to_id(x)): y for x, y in mapping_rooms_mirror.items()}
	mapping_rates_mirror = get_properties_at_integration_configuration(from_hotel_code, adapter_mirror_config, field="rateMap")
	mapping_rates_mirror = {str(alphanumeric_to_id(x)): y for x, y in mapping_rates_mirror.items()}
	mapping_boards_mirror = get_properties_at_integration_configuration(from_hotel_code, adapter_mirror_config, field="boardMap")
	mapping_boards_mirror = {str(alphanumeric_to_id(x)): y for x, y in mapping_boards_mirror.items()}
	mirror_xml = xml_body.decode('utf-8')

	#if this XML hasn't room-rate-board mapped it's stupid to process it in mirror hotel
	change_made = False

	for room_from, room_to in mapping_rooms_mirror.items():
		if room_from in mirror_xml:
			change_made = True
			mirror_xml = mirror_xml.replace(room_from, room_to)

	if change_made:
		change_made = False
		for rates_from, rates_to in mapping_rates_mirror.items():
			if rates_from in mirror_xml:
				change_made = True
				mirror_xml = mirror_xml.replace(rates_from, rates_to)

	if change_made:
		change_made = False
		for boards_from, boards_to in mapping_boards_mirror.items():
			if boards_from in mirror_xml:
				change_made = True
				mirror_xml = mirror_xml.replace(boards_from, boards_to)

	if change_made:
		mirror_xml = mirror_xml.replace(from_hotel_code, mirror_hotel)

		return mirror_xml

	return ""



def _get_file_path(entity, hotel_code):

	timestamp = date_utils.date_to_string(entity['timestamp'], format="%Y_%m_%d__%H_%M_%S")
	file_name = '%s_%s.xml' % (timestamp, entity.id)

	return "%s/%s/%s" % (BASE_PATH, hotel_code, file_name)


def _save_content(new_path, xml_content):
	with open(new_path, "w") as text_file:
		text_file.write(xml_content)


def _create_destination_folder(hotel_code):

	new_directory = "%s/%s" % (BASE_PATH, hotel_code)

	try:
		os.makedirs(new_directory)
	except OSError as e:
		if e.errno != errno.EEXIST:
			raise

def download_audits(params):

	_create_destination_folder(params['hotel'])

	hotel_code_audits = params['hotel']
	if params.get("from_hotel_code_mirror"):
		hotel_code_audits = params.get("from_hotel_code_mirror")

	#Download from now to the past
	entities = datastore_communicator.get_using_entity_and_params("EndpointCallAuditEvent", [('hotel_code', '=', hotel_code_audits), ('path', '=', PATH)], return_cursor=True, order_by='-timestamp')

	TODAY = datetime.now(pytz.UTC)

	MAX_DAYS = 20
	if params.get('days'):
		MAX_DAYS = int(params.get('days'))

	num_entities = 0
	for entity in entities:

		new_path = _get_file_path(entity, params['hotel'])
		if os.path.exists(new_path):
			#We are not going to download the same thing twice
			print('Path already exists, stopping process')
			break

		else:

			# If the file already exists, stop.
			xml_content = zlib.decompress(entity['request'])
			if params.get('from_hotel_code_mirror'):
				xml_content = _transform_xml_content_to_mirror(xml_content, params.get('from_hotel_code_mirror'), params.get('hotel'))

				if xml_content:
					_save_content(new_path, xml_content)
			else:
				_save_content(new_path, xml_content.decode('utf8'))
			num_entities += 1

		if num_entities % 10000 == 0:
			print('%s, %s' % (num_entities, date_utils.date_to_string(entity['timestamp'], format="%Y_%m_%d__%H_%M_%S")))

		if (TODAY - entity['timestamp']).days > MAX_DAYS:
			print('Reached max days: %s' % MAX_DAYS)
			break


	print("Created %s new files" % num_entities)


# NOTE: PLEASE CHANGE THE PROJECT AND PATH TO CHANGE IT TO THE DIFFERENT ADAPTERS
# Note that it will stop as soon as it finds a file with the same name, so the first time you do it, do it for many days (i.e. 15 or 30)
if __name__ == '__main__':
	Config.PROJECT = 'availpro-adapter'
	Config.NAMESPACE = ''

	params = {
		'hotel': HOTEL_CODE,
		'days': NUM_DAYS
	}

	download_audits(params)