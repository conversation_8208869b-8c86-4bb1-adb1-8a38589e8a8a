import json
import logging
import os

import errno

import requests
from google.cloud import ndb

from paraty.constants.queue_names import LONG_REQUEST_QUEUE
from paraty_commons_3.decorators.retry import retry
from tools.audit import download_audits
from model.audit import EndpointCallAuditEvent
from paraty_commons_3.concurrency import concurrency_utils
from paraty_commons_3.hotel_manager import hotel_manager_utils



# ------ MODIFY THIS TO CHANGE DEFAULT CONFIGURATION --------
from tools.audit.download_audits import PROJECT

LOG_LEVEL = logging.INFO

MANUAL_REDO = False

#Let's speed things up by doing multiple in parallel
MAX_PARALLEL = 20
# -----------------------------------------------------------------

#Based on https://stackoverflow.com/questions/2130016/splitting-a-list-into-n-parts-of-approximately-equal-length
def _chunk_it(seq, num):
	avg = len(seq) / float(num)
	out = []
	last = 0.0

	while last < len(seq):
		out.append(seq[int(last):int(last + avg)])
		last += avg

	return out


def _convert_xml_to_availability_items():
	try:
		os.makedirs(download_audits.OUTPUT_DIR)
	except OSError as e:
		if e.errno != errno.EEXIST:
			raise

	directory = os.listdir(download_audits.INPUT_DIR)

	print()
	print('Found %s XML files' % len(directory))
	print()

	#Note that we want to analyze starting from the most recent one (maybe we find soon what we are looking for)
	sorted_files = sorted(directory, reverse=True)

	# TODO, remove it
	existing_paths = _get_paths_in_folder(download_audits.OUTPUT_DIR)
	sorted_files = [x for x in sorted_files if not x in existing_paths]

	print('Converting %s files' % len(sorted_files))

	chunks = _chunk_it(sorted_files, 20)

	params = [(x,) for x in chunks]

	# #Use this method if you want to profile it
	# for chunk in chunks:
	# 	cprofile.run('_process_files(sorted_files)', {'_process_files': _process_files, 'sorted_files': chunk[0:3000]}, {})

	# this might take quite a while
	concurrency_utils.execute_in_parallel(_process_files_in_ndb_context, params, max_waiting_time=18000, max_concurrency=20)

	"""
	This code enable the possibility to debug get_availability_items if it is necessary 
	for p in params:
		_process_files(p[0])
	"""

INTEGRATION_HOST = 'https://%s.appspot.com' % download_audits.PROJECT
GET_AVAILABILITY_ENDPOINT = '{}/get_availability_items'.format(INTEGRATION_HOST)

@retry(Exception, tries=5, delay=3)
def _get_availability_items_from_production(name, body):

	body = body.replace("\n", "").replace("\r", "")

	params = {
		'hotel_code': download_audits.HOTEL_CODE,
		'xml_files': [[name, body]]
	}

	endpoint = GET_AVAILABILITY_ENDPOINT
	headers = {'Content-Type': 'application/json', 'Accept-enconding': 'gzip'}
	response = requests.post(endpoint, json=params, headers=headers, timeout=60)

	try:
		return json.loads(response.content)[0][1]
	except Exception as e:
		return json.loads(response.content)[0][1]


def _compare_availability(items_1, items_2):

	for i, d1 in enumerate(items_1):

		d2 = items_2[i]
		d1_keys = set(d1.keys())
		d2_keys = set(d2.keys())
		intersect_keys = d1_keys.intersection(d2_keys)
		added = d1_keys - d2_keys
		removed = d2_keys - d1_keys
		modified = {o: (d1[o], d2[o]) for o in intersect_keys if d1[o] != d2[o]}
		same = set(o for o in intersect_keys if d1[o] == d2[o])

		#Operation Id is not present locally as we read it from a file
		modified.pop('operation_id', None)

		if added or removed or modified:
			logging.error("Something has changed: %s, %s", d1, d2)
		else:
			logging.info("OK")


def _process_files_in_ndb_context(sorted_files):
	client = ndb.Client(namespace='', project=PROJECT)
	with client.context():
		_process_files(sorted_files)


def _get_paths_in_folder(directory_path):
	files = [f for f in os.listdir(directory_path)]
	return set(files)


def _process_files(sorted_files):
	processed = 0

	print('Converting %s files' % len(sorted_files))
	
	# existing_paths = _get_paths_in_folder(download_audits.OUTPUT_DIR)

	for my_file in sorted_files:

		processed = processed + 1

		if processed % 500 == 0:
			print('Converted %s files' % processed)

		# new_path = download_audits.OUTPUT_DIR + "/" + my_file
		# if my_file in existing_paths:
		# 	continue

		operation = EndpointCallAuditEvent()

		with open(download_audits.INPUT_DIR + "/" + my_file) as f:
			file_content = f.read()

		operation.request = file_content
		operation.hotel_code = download_audits.HOTEL_CODE
		operation.key = ndb.Key('EndpointCallAuditEvent', None)

		from interface_to_implement import SPECIFIC_INTEGRATION_INTERFACE

		# #For test reasons, if we want to compare it with production:
		availability_items = _get_availability_items_from_production(my_file, file_content)

		if not MANUAL_REDO:
			local_availability_items = SPECIFIC_INTEGRATION_INTERFACE.get_availability_items(operation, "audit_assistant", LONG_REQUEST_QUEUE)
			_compare_availability(local_availability_items, availability_items)
			availability_items = local_availability_items


		new_path = download_audits.OUTPUT_DIR + "/" + my_file

		with open(new_path, 'w') as f:

			result = json.dumps(availability_items)
			#result = json.dumps(production_availability_items)
			f.write(result)


if __name__ == '__main__':
	GET_AVAILABILITY_ENDPOINT = 'https://availpro-adapter.appspot.com/get_availability_items'
	download_audits.HOTEL_CODE = 'cidade-olhao'

	_convert_xml_to_availability_items()