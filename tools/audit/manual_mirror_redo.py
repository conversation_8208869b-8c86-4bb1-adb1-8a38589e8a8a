import json
import logging
import os

from handlers.redo_handler import _get_start_and_end_dates, _update_limits
from paraty.integration import modify_prices_batch
from paraty.integration.integration_data_cache import IntegrationDataCache
from tools.audit.convert_xml_to_availabilities import _setup_environment, _convert_xml_to_availability_items
from tools.audit import download_audits

from paraty.utils import hotel_manager_utils as local_hotel_manager_utils
from tools.audit.download_audits import HOTEL_CODE, NUM_DAYS, HOTEL_CODE_MIRROR

'''
This allows you to do a redo locally from ORIGIN hotel to a MIRROR (target) hotel

'''

def _redo_from_date():

	_download_xmls()

	_convert_xml_to_items()

	hotel = local_hotel_manager_utils.get_hotel_by_application_id(HOTEL_CODE)[0]

	#Read availability items
	directory = os.listdir(download_audits.OUTPUT_DIR)

	print
	print 'Found %s XML files' % len(directory)
	print

	# Note that we want to analyze starting from the most recent one (maybe we find soon what we are looking for)
	sorted_files = sorted(directory, reverse=True)

	# Let's use an intermediate structure to avoid having to access the datastore continually
	cache = IntegrationDataCache(hotel, "MANUAL REDO")

	# Limits
	first_modified_date, last_modified_date = None, None

	for availability_items_file in sorted_files:

		with open(download_audits.OUTPUT_DIR + "/" + availability_items_file) as f:
			availability_items = json.loads(f.read())

		# --------------------------------------------------------------------
		# fmatheis, filter here only what you are interested in
		availability_items = [item for item in availability_items if
							  item['day'] == '2022-09-09' and
							  item['quantity_rate_board'] and
							  item['roomId'] == 5629499534213120 and
							  item['rateId'] == 6388353817640960
							  ]
		if not availability_items:
			continue
		# --------------------------------------------------------------------

		start_date, end_date = _get_start_and_end_dates(availability_items)

		first_modified_date, last_modified_date = _update_limits(first_modified_date, last_modified_date, start_date, end_date)

		# logging.info("_redo_operation_in_memory start_date %s end_date %s", start_date, end_date)

		modify_prices_batch.execute_modify_prices_batch(HOTEL_CODE, availability_items, start_date, end_date, "TEST REQUEST ID", cache)

	# We use the request of the first one
	cache.save_data_to_datastore()

	# Make Sure that the cache of hotel-manager is cleaned (if something has changed)
	if first_modified_date and last_modified_date:
		local_hotel_manager_utils.reset_hotel_manager_cache(hotel, first_modified_date, last_modified_date)


def _convert_xml_to_items():
	logging.info("Converting xml to Availability items")
	_setup_environment()
	_convert_xml_to_availability_items()


def _download_xmls():
	params = {
		'hotel': HOTEL_CODE,
		'hotel_mirror': HOTEL_CODE_MIRROR,
		'days': NUM_DAYS
	}
	download_audits.download_audits(params)


if __name__ == '__main__':

	_redo_from_date()