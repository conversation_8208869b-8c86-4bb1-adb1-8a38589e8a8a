import os

from google.appengine.ext import ndb
from model.audit import EndpointCallAuditEvent
from paraty_common.dev.profiler import cprofile
from siteminder.siteminder_integration import SiteminderIntegration

import logging
logging.basicConfig(level=logging.INFO)
import lxml

def measure_performance():
    # Read the file contents in test_data
    all_files = os.listdir(os.path.join(os.path.dirname(__file__), "test_data/port-azafata"))[0:1000]
    for file in all_files:
        if file.endswith(".xml"):
            with open(os.path.join(os.path.dirname(__file__), "test_data/port-azafata", file)) as f:
                file_content = f.read()
                operation = EndpointCallAuditEvent()
                operation.request = file_content
                operation.hotel_code = "port-azafata"
                operation.key = ndb.Key('EndpointCallAuditEvent', 2028383)
                availability_items = SiteminderIntegration().get_availability_items(operation, "test")
                print("Created %s availability items" % len(availability_items))


if __name__ == '__main__':
    result = cprofile.run('measure_performance()', globals(), locals())
    print(result)
