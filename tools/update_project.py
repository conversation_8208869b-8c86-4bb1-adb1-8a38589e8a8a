import argparse
import subprocess
import shutil, errno
from datetime import datetime
from distutils.dir_util import copy_tree
import os, fnmatch

TEMP_FOLDER = "/temp2"

def execute_command(cmd, print_it=True):
	if print_it:
		print(cmd)

	subprocess.check_call(cmd, shell=True)

def print_line(message):
	print()
	print("-------------------------------------------------------------")
	print(message.upper())
	print("-------------------------------------------------------------")


def create_temp_folder(params):

	os.chdir(TEMP_FOLDER)

	new_folder = "build_%s_%s" % (params['application'], datetime.now().strftime("%Y_%m_%d_%H_%M"))

	if os.path.exists(new_folder):
		shutil.rmtree(new_folder)
	os.makedirs(new_folder)

	os.chdir(TEMP_FOLDER + "/" + new_folder)

	print_line("Created temp folder: %s" % (TEMP_FOLDER + "/" + new_folder))



def copy_anything(src, dst):

	try:
		shutil.copytree(src, dst)
	except OSError as exc: # python >2.5
		if exc.errno == errno.ENOTDIR:
			shutil.copy(src, dst)
		else: raise


def download_stable_version_of_project(build_temp_folder, project_name, git_path, branch_name=None):

	project_temp_dir = "%s/%s" % (TEMP_FOLDER, project_name)

	if branch_name:
		project_temp_dir = "%s_%s" % (project_temp_dir, branch_name)

	#If it is already downloaded we just update it
	if os.path.exists(project_temp_dir):
		try:
			print("Updating source code of %s" % project_name)
			pull_cmd = "cd %s && git pull" % project_temp_dir
			execute_command(pull_cmd)
		except:
			print('Removing temporary directory and downloading it clean')
			rm_cmd = "rm -rf %s" % project_temp_dir
			execute_command(rm_cmd)

	#If it doesn't exist we clone it into the project temp dir
	if not os.path.exists(project_temp_dir):
		print("Cloning source code of %s" % project_name)

		clone_cmd = "git clone --recursive %s %s" % (git_path, project_temp_dir)
		execute_command(clone_cmd)

		if branch_name:
			change_branch_cmd = "cd %s && git checkout %s && cd ." % (project_temp_dir, branch_name)
			execute_command(change_branch_cmd)


	#
	# #We copy the project to the temporary build folder
	# print 'Copying %s to %s' % (project_temp_dir, build_temp_folder)
	# result = "%s/%s" % (build_temp_folder, project_name)
	# shutil.copytree(project_temp_dir, result)


def download_projects(params):

	print_line('Cloning from repository')

	download_stable_version_of_project(TEMP_FOLDER, 'base-integration', '*****************:paraty/base-integration.git')

	download_stable_version_of_project(TEMP_FOLDER, params['application'], '*****************:paraty/%s.git' % params['application'], params['branch'])


def create_stage_folder(params):
	print_line("Creating stage folder")
	os.makedirs("stage")
	os.chdir("stage")

	copy_tree("%s/base-integration/src/" % TEMP_FOLDER, ".")

	project_folder = "%s/%s" % (TEMP_FOLDER, params['application'])

	if params['branch']:
		project_folder = "%s_%s" % (project_folder, params['branch'])

	copy_tree("%s/src/" % project_folder, ".")


def execute_unit_tests():

	print_line('Executing unit tests')

	cmd_base = "nosetests tests"
	execute_command(cmd_base)

	cmd_specific = "nosetests test"
	execute_command(cmd_specific)


def _replace_in_file(path, original, changed):

	with open(path) as f:
		file_str = f.read()

	# do stuff with file_str
	file_str = file_str.replace(original, changed)

	with open(path, "w") as f:
		f.write(file_str)


def find(pattern, path):
	result = []
	for root, dirs, files in os.walk(path):
		for name in files:
			if fnmatch.fnmatch(name, pattern):
				result.append(os.path.join(root, name))
	return result




def configure_project(params):

	YAML_FILE = 'app.yaml'
	CRON_FILE = 'cron.yaml'

	if params['flexible']:
		YAML_FILE = 'app-flexible.yaml'


	scaling_param = params.get('scaling_type', 'automatic_scaling')

	scaling_property_1 = ""
	scaling_property_2 = ""
	scaling_property_3 = ""
	scaling_property_4 = ""
	scaling_property_5 = ""

	#extract from here: https://cloud.google.com/appengine/docs/standard/python/config/appref
	if scaling_param == "automatic_scaling":
		scaling_property_1 = "min_idle_instances: 0"
		scaling_property_2 = "max_idle_instances: 2  # default value"
		scaling_property_3 = "min_pending_latency: 30ms  # default value"
		scaling_property_4 = "max_pending_latency: automatic"
		scaling_property_5 = "max_concurrent_requests: 40"
	elif scaling_param == "basic_scaling":
		scaling_property_1 = "max_instances: 2"
		scaling_property_2 = "idle_timeout: 5m # default value"
	elif scaling_param == "manual_scaling":
		scaling_property_1 = "instances: 2"



	print_line('Configuring project to use specific integration')


	#Modify app.yaml
	_replace_in_file(YAML_FILE, "@@APPLICATION@@", params['application'])
	_replace_in_file(YAML_FILE, "@@VERSION@@", params['version'])
	_replace_in_file(YAML_FILE, "@@MODULE@@", params.get('module', 'default'))
	_replace_in_file(CRON_FILE, "@@MODULE@@", params.get('module', 'default'))
	_replace_in_file(YAML_FILE, "@@MACHINE_TYPE@@", params.get('machine_type', 'F2'))
	_replace_in_file(YAML_FILE, "@@SCALING_TYPE@@", scaling_param)
	_replace_in_file(YAML_FILE, "@@SCALING_PROPERTY_1@@", scaling_property_1)
	_replace_in_file(YAML_FILE, "@@SCALING_PROPERTY_2@@", scaling_property_2)
	_replace_in_file(YAML_FILE, "@@SCALING_PROPERTY_3@@", scaling_property_3)
	_replace_in_file(YAML_FILE, "@@SCALING_PROPERTY_4@@", scaling_property_4)
	_replace_in_file(YAML_FILE, "@@SCALING_PROPERTY_5@@", scaling_property_5)

	#Set the integration
	integration_file = find('*_integration.py', '.')[0]
	import_text = integration_file.replace("/", ".").replace(".py", "").replace("..","import ") + " # @UnusedImport"

	_replace_in_file("main.py", "#@@IMPORT@@", import_text)


def update_to_gae(params):
	print_line('Updating to GAE')

	if params['flexible']:
		execute_command('gcloud config set project %s' % params['application'])
		update_cmd = 'gcloud app deploy app-flexible.yaml --version=%s --no-promote' % params['version']
	else:
		update_cmd = "appcfg.py update ."

	execute_command(update_cmd)


def deploy_integration(params):

	create_temp_folder(params)

	download_projects(params)

	create_stage_folder(params)

	if not params['ignore_tests']:
		execute_unit_tests()

	configure_project(params)

	update_to_gae(params)


if __name__ == '__main__':

	parser = argparse.ArgumentParser(description=
	'''
	Update a integration
	i.e. python update_project.py -a availpro-adapter -v 1
	i.e. python update_project.py -a rategain-adapter -v 1
	i.e. python update_project.py -a webcamp-adapter -v 1
	i.e. python update_project.py -a tripadvisor-adapter -v 2018-06-21 -m v7 -b v7 -t


	INFORMATION FOR SCALING TYPES:
	https://cloud.google.com/appengine/docs/standard/python/config/appref


	''', formatter_class=argparse.RawTextHelpFormatter)
	
	parser.add_argument('-version', '-v', required=True, help="Indicates the new version name")
	parser.add_argument('-module', '-m', required=False, default="default", help="Indicates the module to use")
	parser.add_argument('-branch', '-b', required=False, help="Indicates the branch in git to use")
	parser.add_argument('-application', '-a', required=False, help="Indicates the applicationId")
	parser.add_argument('-ignore_tests', '-t', action='store_true', help="If present we dont execute unit tests")
	parser.add_argument('-flexible', '-f', required=False, help="If present we upload it as a Flexible version")
	parser.add_argument('-type', '-s', required=False, default="F2", help="If present it defines the machine type (i.e. F4)")
	parser.add_argument('-scaling', '-c', required=False, default="automatic_scaling", help="Indicates the type of scaling (i.e. automatic_scaling or basic_scaling or manual_scaling)")

	args = parser.parse_args()

	params = {
		'version': args.version,
		'application': args.application,
		'ignore_tests': args.ignore_tests,
		'module': args.module,
		'flexible': args.flexible,
		'machine_type': args.type,
		'scaling_type': args.scaling,
		'branch': args.branch
	}

	deploy_integration(params)
	print_line("FINISHED!")

#python update_project.py -v 1 -application rategain-adapter
#python update_project.py -v 1 -application availpro-adapter
