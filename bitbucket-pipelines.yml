pipelines:
  default:
    - step:
        name: Build and test
        image: python:3.10
        caches:
          - pip
        script:
          - mkdir -p ~/.ssh
          - echo $BITBUCKET_PIPELINES_SSH
          - echo $BITBUCKET_PIPELINES_SSH | base64 -d > ~/.ssh/id_rsa
          - chmod 600 ~/.ssh/id_rsa
          - ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts
          - apt-get install -y git
          - git submodule update --init --recursive
          - cd src
          - pip install -r requirements.txt
          - pip install vulture
          - echo $BUILD_TOOLS_2_SERVICE_ACCOUNT | base64 -d > /tmp/build-tools-2-service-account.json
          - export GOOGLE_APPLICATION_CREDENTIALS="/tmp/build-tools-2-service-account.json"
          - cd ../scripts
          - python test_project.py
