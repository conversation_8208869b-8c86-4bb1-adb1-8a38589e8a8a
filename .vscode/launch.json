{"version": "0.2.0", "configurations": [{"name": "Python: Main Application", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/src/main.py", "console": "integratedTerminal", "justMyCode": false, "python": "${workspaceFolder}/venv/bin/python", "cwd": "${workspaceFolder}/src", "env": {"PYTHONUNBUFFERED": "1", "PYTHONPATH": "${workspaceFolder}/src", "AVOID_BROWSER_ON_START": "true", "FLASK_ENV": "development", "FLASK_DEBUG": "1"}, "args": [], "stopOnEntry": false}, {"name": "Python: Main Application (External Terminal)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/src/main.py", "console": "externalTerminal", "justMyCode": false, "python": "${workspaceFolder}/venv/bin/python", "cwd": "${workspaceFolder}/src", "env": {"PYTHONUNBUFFERED": "1", "PYTHONPATH": "${workspaceFolder}/src", "AVOID_BROWSER_ON_START": "true", "FLASK_ENV": "development", "FLASK_DEBUG": "1"}, "args": []}]}