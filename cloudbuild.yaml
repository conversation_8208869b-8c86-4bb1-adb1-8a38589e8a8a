steps:
  - name: 'gcr.io/cloud-builders/git'
    id: 'Install SSH KEY for Bitbucket'
    args:
      - '-c'
      - |
        ls -la /
        echo "$$SSHKEY" > /root/.ssh/id_rsa
        chmod 400 /root/.ssh/id_rsa
        ssh-keyscan bitbucket.org >> /root/.ssh/known_hosts
    entrypoint: bash
    secretEnv: [ 'SSHKEY' ]
    volumes:
      - name: ssh
        path: /root/.ssh


  - name: 'gcr.io/cloud-builders/git'
    id: 'Init submodules and clone adapter project'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        git fetch
        git checkout ${_BASE_INTEGRATION_BRANCH}
        git pull origin ${_BASE_INTEGRATION_BRANCH}
        git submodule update --init --recursive
        
        cd /tmp
        <NAME_EMAIL>:paraty/${_ADAPTER_GIT_REPOSITORY}.git
        cd ${_ADAPTER_GIT_REPOSITORY}
        git fetch
        git checkout ${_ADAPTER_BRANCH}
        git pull origin ${_ADAPTER_BRANCH}
        git submodule update --init --recursive
        find . -type f ! -name '*.git*' -exec cp --parents {} /workspace/ \;

    volumes:
      - name: ssh
        path: /root/.ssh

  - name: 'ubuntu'
    id: 'Set config files and copy templates of adapter'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd src
        sed -i 's/@@INTEGRATION_NAME@@/${_ADAPTER_NAME}/g' paraty/config.py
        sed -i 's/@@INTEGRATION_NAME@@/${_ADAPTER_LOCATION}/g' paraty/config.py
        
        if [ -d "${_ADAPTER_NAME}/templates" ]; then
          cp -a ${_ADAPTER_NAME}/templates/. templates
        else
          echo "Directory ${_ADAPTER_NAME}/templates does not exist."
        fi
        

  - name: 'ubuntu'
    id: 'Specific requirements.txt of adapter'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd src
        if [ -f "${_ADAPTER_NAME}/integration_requirements.txt" ]; then
          echo "File integration_requirements.txt exists so is adding."
          cat ${_ADAPTER_NAME}/integration_requirements.txt >> requirements.txt
        else
            echo "File integration_requirements.txt not exists."
        fi

  - name: 'gcr.io/build-tools-2/adapters-base-image:latest'
    id: 'Unit test of project'
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        cd src
        pip install -r requirements.txt
        export TIMESTAMP_CHECK_INTERVAL=1
        python -m unittest discover -s tests_adapter -p 'test_*.py'

  - name: 'gcr.io/build-tools-2/adapters-base-image:latest'
    id: 'Prepare queues for cloud run'
    entrypoint: 'sh'
    args:
      - '-c'
      - |
        cd scripts/deploy
        python duplicate_queues.py
        

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'Deploy queues of project'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd src
        gcloud app deploy queue.yaml --project ${_GOOGLE_PROJECT}
      

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'Build project image'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd src
        gcloud config set builds/use_kaniko True
        gcloud builds submit --project ${_GOOGLE_PROJECT} --tag gcr.io/${_GOOGLE_PROJECT}/${_GOOGLE_PROJECT}
      

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'Build cloud run url of project'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd src
        
        CLOUD_RUN_URL=$(gcloud run services describe ${_GOOGLE_PROJECT} --format 'value(status.url)' --project ${_GOOGLE_PROJECT} --region ${_ADAPTER_LOCATION})
        if [ -z "$$CLOUD_RUN_URL" ]; then
          gcloud run deploy ${_GOOGLE_PROJECT} --allow-unauthenticated \
          --region ${_ADAPTER_LOCATION} \
          --project ${_GOOGLE_PROJECT} \
          --image gcr.io/${_GOOGLE_PROJECT}/${_GOOGLE_PROJECT} \
          --service-account=${_GOOGLE_PROJECT}@appspot.gserviceaccount.com \
          --set-env-vars=CLOUD_RUN_URL=$$CLOUD_RUN_URL \
          --set-env-vars=GOOGLE_CLOUD_PROJECT=${_GOOGLE_PROJECT} \
          --set-env-vars=PROJECT_LOCATION=${_ADAPTER_LOCATION} \
          --set-env-vars=INTEGRATION_NAME=${_ADAPTER_NAME} \
          --labels=developer_mail=${_DEVELOPER_EMAIL}
          CLOUD_RUN_URL=$(gcloud run services describe ${_GOOGLE_PROJECT} --format 'value(status.url)' --project ${_GOOGLE_PROJECT} --region ${_ADAPTER_LOCATION})
        fi
        
        echo "CLOUD_RUN_URL=$$CLOUD_RUN_URL" >> /data-bridger/configs.properties
        

    volumes:
      - name: data-bridger
        path: /data-bridger


  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'Deploy cloud run of project'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd src
        source /data-bridger/configs.properties

        if [ "${_DEPLOY_BACKGROUND}" = "y" ] || [ "${_DEPLOY_BACKGROUND}" = "only" ]; then
          gcloud run deploy ${_GOOGLE_PROJECT} --allow-unauthenticated \
            --region ${_ADAPTER_LOCATION} \
            --project ${_GOOGLE_PROJECT} \
            --image gcr.io/${_GOOGLE_PROJECT}/${_GOOGLE_PROJECT} \
            --service-account=${_GOOGLE_PROJECT}@appspot.gserviceaccount.com \
            --set-env-vars=CLOUD_RUN_URL=$$CLOUD_RUN_URL \
            --set-env-vars=GOOGLE_CLOUD_PROJECT=${_GOOGLE_PROJECT} \
            --set-env-vars=PROJECT_LOCATION=${_ADAPTER_LOCATION} \
            --set-env-vars=INTEGRATION_NAME=${_ADAPTER_NAME} \
            --labels=developer_mail=${_DEVELOPER_EMAIL}
        fi

    volumes:
      - name: data-bridger
        path: /data-bridger


  - name: 'ubuntu'
    id: 'Prepare appengine config at app.yaml'
    waitFor: ['Build cloud run url of project']
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd src
        source /data-bridger/configs.properties
        
        sed -i 's/@@MODULE@@/default/g' app.yaml
        sed -i "s/@@MACHINE_TYPE@@/${_APPENGINE_MACHINE_TYPE:-F4}/g" app.yaml
        sed -i "/handlers:/i env_variables:\n  CLOUD_RUN_URL: $$CLOUD_RUN_URL\n  GOOGLE_CLOUD_PROJECT: ${_GOOGLE_PROJECT}\n  PROJECT_LOCATION: ${_ADAPTER_LOCATION}\n  INTEGRATION_NAME: ${_ADAPTER_NAME}" app.yaml

    volumes:
      - name: data-bridger
        path: /data-bridger

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'Deploy appengine default service of project'
    waitFor: ['Prepare appengine config at app.yaml']
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd src
        source /data-bridger/configs.properties
        
        TARGET_VERSION=$(date -d "+1 hour" +%Y-%m-%d-%H-%M)
        if [ -n "${_CUSTOM_ADAPTER_VERSION_NAME}" ]; then
          TARGET_VERSION=${_CUSTOM_ADAPTER_VERSION_NAME}
        fi
        
        if [ "${_DEPLOY_BACKGROUND}" != "only" ]; then
          gcloud app deploy app.yaml --project ${_GOOGLE_PROJECT} \
          --version $$TARGET_VERSION \
          --no-promote
        fi

    volumes:
      - name: data-bridger
        path: /data-bridger

tags: ['${_DEVELOPER_EMAIL}', '${_ADAPTER_NAME}']

availableSecrets:
  secretManager:
  - versionName: projects/security-seeker/secrets/google-cloud-build/versions/latest
    env: SSHKEY

options:
  logging: CLOUD_LOGGING_ONLY